
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface PatternResult {
  id: string;
  symbol: string;
  pattern: string;
  reliability: number;
  expected: string;
  timeframe: string;
  detected_at: string;
}

export function usePatternRecognition() {
  return useQuery({
    queryKey: ['analytics_patterns'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('analytics_patterns')
        .select('*')
        .order('detected_at', { ascending: false });
      if (error) throw error;
      return data ?? [];
    }
  });
}
