 

/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import BacktestForm from './BacktestForm';
import BacktestResultCard from './BacktestResultCard';

export interface BacktestResult {
  id: string;
  strategy_name: string;
  symbol: string;
  timeframe: string;
  status: string;
  started_at: string;
  finished_at: string | null;
  parameters: any;
  result_summary: any | null;
  result_trades?: any | null;
}

const fetchResults = async (): Promise<BacktestResult[]> => {
  const { data, error } = await supabase
    .from('analytics_backtests')
    .select('*')
    .order('started_at', { ascending: false });
  if (error) throw error;
  return (data as BacktestResult[]) ?? [];
};

const BacktestingEngine = () => {
  const queryClient = useQueryClient();
  const { data: results = [], isLoading, error } = useQuery({
    queryKey: ['backtests'],
    queryFn: fetchResults,
    refetchInterval: 8000,
    staleTime: 7000,
  });

  return (
    <Card className="border-2 border-emerald-200 bg-gradient-to-br from-emerald-50 to-green-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-emerald-800">
          <BarChart3 className="h-5 w-5" />
          محرك اختبار الاستراتيجيات (Backend)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <BacktestForm
          afterSubmit={() => {
            // Refetch after test is added
            queryClient.invalidateQueries({ queryKey: ['backtests'] });
          }}
        />
        <div className="space-y-4">
          {isLoading && (
            <div className="text-center py-8 text-emerald-600">جاري التحميل...</div>
          )}
          {error && (
            <div className="text-center py-8 text-red-600">فشل تحميل نتائج الباكتيست</div>
          )}
          {results && results.length > 0 ? (
            results.map(result => (
              <BacktestResultCard key={result.id} result={result} />
            ))
          ) : (
            !isLoading && (
              <div className="text-center py-12 text-muted-foreground">
                <BarChart3 className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-xl font-semibold mb-2">ابدأ أول اختبار لك</h3>
                <p>اختبر استراتيجياتك على البيانات التاريخية وتعرف على أدائها</p>
              </div>
            )
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default BacktestingEngine;
