-- 💎 إنشاء قاعدة البيانات المخصصة للأسهم المصرية
-- Custom Database Creation for Egyptian Stock Market Data Treasure
-- Date: 2025-06-16

-- Note: Database creation will be handled by setup script
-- This file contains table and index definitions

-- إنشاء Extensions مطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";  -- Requires superuser
-- CREATE EXTENSION IF NOT EXISTS "btree_gin";  -- Requires superuser

-- ==========================================
-- 1. جدول الأسهم الرئيسي
-- ==========================================
CREATE TABLE stocks_master (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) UNIQUE NOT NULL,
    name_ar VARCHAR(200),
    name_en VARCHAR(200),
    sector VARCHAR(100),
    industry VARCHAR(150),
    isin VARCHAR(20),
    total_shares BIGINT,
    free_shares BIGINT,
    face_value DECIMAL(8,2),
    listing_date DATE,
    is_active BOOLEAN DEFAULT true,
    is_suspended BOOLEAN DEFAULT false,
    suspension_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_stocks_master_symbol ON stocks_master(symbol);
CREATE INDEX idx_stocks_master_sector ON stocks_master(sector);
CREATE INDEX idx_stocks_master_active ON stocks_master(is_active) WHERE is_active = true;

-- ==========================================
-- 2. البيانات التاريخية (من ملفات TXT)
-- ==========================================
CREATE TABLE stocks_historical (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL REFERENCES stocks_master(symbol),
    trade_date DATE NOT NULL,
    period VARCHAR(1) DEFAULT 'D', -- D for Daily
    
    -- أسعار OHLC
    open_price DECIMAL(12,4),
    high_price DECIMAL(12,4),
    low_price DECIMAL(12,4),
    close_price DECIMAL(12,4),
    
    -- حجم التداول
    volume BIGINT DEFAULT 0,
    turnover DECIMAL(15,2),
    trades_count INTEGER,
    
    -- مؤشرات محسوبة
    price_change DECIMAL(12,4),
    price_change_pct DECIMAL(8,4),
    
    -- معلومات إضافية
    open_interest BIGINT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- قيود فريدة
    UNIQUE(symbol, trade_date, period)
);

-- فهارس محسنة للأداء
CREATE INDEX idx_historical_symbol_date ON stocks_historical(symbol, trade_date DESC);
CREATE INDEX idx_historical_date ON stocks_historical(trade_date DESC);
CREATE INDEX idx_historical_volume ON stocks_historical(volume DESC) WHERE volume > 0;
CREATE INDEX idx_historical_symbol_date_volume ON stocks_historical(symbol, trade_date, volume);

-- تقسيم الجدول حسب التاريخ للأداء (Partitioning)
-- يمكن تنفيذه لاحقاً عند نمو البيانات

-- ==========================================
-- 3. البيانات الحية (من stock_synco.xlsx)
-- ==========================================
CREATE TABLE stocks_realtime (
    symbol VARCHAR(10) PRIMARY KEY REFERENCES stocks_master(symbol),
    
    -- أسعار أساسية
    open_price DECIMAL(12,4),
    high_price DECIMAL(12,4),
    low_price DECIMAL(12,4),
    current_price DECIMAL(12,4),
    previous_close DECIMAL(12,4),
    change_amount DECIMAL(12,4),
    change_percent DECIMAL(8,4),
    
    -- حجم وقيمة التداول
    volume BIGINT DEFAULT 0,
    turnover DECIMAL(15,2) DEFAULT 0,
    trades_count INTEGER DEFAULT 0,
    
    -- السيولة المتقدمة
    liquidity_ratio DECIMAL(8,4),
    net_liquidity DECIMAL(15,2),
    liquidity_inflow DECIMAL(15,2),
    liquidity_outflow DECIMAL(15,2),
    volume_inflow BIGINT,
    volume_outflow BIGINT,
    liquidity_flow DECIMAL(8,4),
    
    -- المتوسطات المتحركة
    ma5 DECIMAL(12,4),
    ma10 DECIMAL(12,4),
    ma20 DECIMAL(12,4),
    ma50 DECIMAL(12,4),
    ma100 DECIMAL(12,4),
    ma200 DECIMAL(12,4),
    
    -- مؤشرات تكنيكال
    tk_indicator DECIMAL(12,4),
    kj_indicator DECIMAL(12,4),
    
    -- الأهداف والمقاومات
    target_1 DECIMAL(12,4),
    target_2 DECIMAL(12,4),
    target_3 DECIMAL(12,4),
    stop_loss DECIMAL(12,4),
    
    -- تصنيفات السهم
    stock_status INTEGER CHECK (stock_status BETWEEN 1 AND 5), -- 1=ممتاز, 5=ضعيف
    speculation_opportunity BOOLEAN DEFAULT false,
    price_range DECIMAL(8,4),
    
    -- إحصائيات متقدمة
    avg_net_volume_3d DECIMAL(15,2),
    avg_net_volume_5d DECIMAL(15,2),
    
    -- نسب أساسية سريعة
    eps_annual DECIMAL(8,4),
    book_value DECIMAL(8,4),
    pe_ratio DECIMAL(8,4),
    dividend_yield DECIMAL(8,4),
    
    -- معلومات التوقيت
    last_trade_date DATE,
    last_trade_time TIME,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للبيانات الحية
CREATE INDEX idx_realtime_change_percent ON stocks_realtime(change_percent DESC);
CREATE INDEX idx_realtime_volume ON stocks_realtime(volume DESC);
CREATE INDEX idx_realtime_turnover ON stocks_realtime(turnover DESC);
CREATE INDEX idx_realtime_status ON stocks_realtime(stock_status);
CREATE INDEX idx_realtime_opportunities ON stocks_realtime(speculation_opportunity) WHERE speculation_opportunity = true;

-- ==========================================
-- 4. البيانات المالية الشاملة (من financial_data.csv)
-- ==========================================
CREATE TABLE stocks_financials (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL REFERENCES stocks_master(symbol),
    fiscal_year INTEGER NOT NULL,
    fiscal_quarter INTEGER CHECK (fiscal_quarter BETWEEN 1 AND 4),
    reporting_date DATE,
    
    -- تقييم السوق
    market_cap DECIMAL(15,2),
    enterprise_value DECIMAL(15,2),
    shares_outstanding BIGINT,
    
    -- نسب التقييم
    pe_ratio DECIMAL(10,4),
    pb_ratio DECIMAL(10,4),
    ps_ratio DECIMAL(10,4),
    pcf_ratio DECIMAL(10,4),
    peg_ratio DECIMAL(10,4),
    
    -- الربحية لكل سهم
    eps_basic DECIMAL(10,4),
    eps_diluted DECIMAL(10,4),
    eps_growth_yoy DECIMAL(8,4),
    eps_growth_qoq DECIMAL(8,4),
    
    -- الأرباح والتوزيعات
    dividend_yield DECIMAL(8,4),
    dividend_payout_ratio DECIMAL(8,4),
    dividends_per_share DECIMAL(8,4),
    
    -- قائمة الدخل
    total_revenue DECIMAL(15,2),
    gross_profit DECIMAL(15,2),
    operating_income DECIMAL(15,2),
    net_income DECIMAL(15,2),
    ebitda DECIMAL(15,2),
    ebit DECIMAL(15,2),
    
    -- هوامش الربح
    gross_margin DECIMAL(8,4),
    operating_margin DECIMAL(8,4),
    net_margin DECIMAL(8,4),
    ebitda_margin DECIMAL(8,4),
    
    -- الميزانية العمومية
    total_assets DECIMAL(15,2),
    current_assets DECIMAL(15,2),
    non_current_assets DECIMAL(15,2),
    total_liabilities DECIMAL(15,2),
    current_liabilities DECIMAL(15,2),
    long_term_debt DECIMAL(15,2),
    total_equity DECIMAL(15,2),
    retained_earnings DECIMAL(15,2),
    cash_and_equivalents DECIMAL(15,2),
    inventory DECIMAL(15,2),
    accounts_receivable DECIMAL(15,2),
    
    -- التدفقات النقدية
    operating_cash_flow DECIMAL(15,2),
    investing_cash_flow DECIMAL(15,2),
    financing_cash_flow DECIMAL(15,2),
    free_cash_flow DECIMAL(15,2),
    capital_expenditures DECIMAL(15,2),
    
    -- نسب السيولة
    current_ratio DECIMAL(8,4),
    quick_ratio DECIMAL(8,4),
    cash_ratio DECIMAL(8,4),
    operating_cash_flow_ratio DECIMAL(8,4),
    
    -- نسب الرافعة المالية
    debt_to_equity DECIMAL(8,4),
    debt_to_assets DECIMAL(8,4),
    debt_to_ebitda DECIMAL(8,4),
    equity_ratio DECIMAL(8,4),
    
    -- نسب الربحية
    roe DECIMAL(8,4), -- العائد على حقوق الملكية
    roa DECIMAL(8,4), -- العائد على الأصول
    roic DECIMAL(8,4), -- العائد على رأس المال المستثمر
    
    -- نسب النشاط
    asset_turnover DECIMAL(8,4),
    inventory_turnover DECIMAL(8,4),
    receivables_turnover DECIMAL(8,4),
    payables_turnover DECIMAL(8,4),
    
    -- معلومات متقدمة
    beta DECIMAL(8,4),
    analyst_rating VARCHAR(50),
    target_price DECIMAL(12,4),
    target_performance_1y DECIMAL(8,4),
    
    -- تواريخ مهمة
    recent_earnings_date DATE,
    upcoming_earnings_date DATE,
    dividend_ex_date DATE,
    dividend_pay_date DATE,
    
    -- معلومات إضافية
    industry_comparison_score DECIMAL(8,4),
    financial_strength_score DECIMAL(8,4),
    growth_score DECIMAL(8,4),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(symbol, fiscal_year, fiscal_quarter)
);

-- فهارس للبيانات المالية
CREATE INDEX idx_financials_symbol_year ON stocks_financials(symbol, fiscal_year DESC);
CREATE INDEX idx_financials_pe_ratio ON stocks_financials(pe_ratio) WHERE pe_ratio > 0;
CREATE INDEX idx_financials_roe ON stocks_financials(roe DESC);
CREATE INDEX idx_financials_market_cap ON stocks_financials(market_cap DESC);

-- ==========================================
-- 5. المؤشرات الفنية المتقدمة
-- ==========================================
CREATE TABLE technical_indicators (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL REFERENCES stocks_master(symbol),
    calculation_date DATE NOT NULL,
    
    -- متوسطات متحركة أسية
    ema_12 DECIMAL(12,4),
    ema_26 DECIMAL(12,4),
    ema_50 DECIMAL(12,4),
    ema_200 DECIMAL(12,4),
    
    -- MACD
    macd DECIMAL(12,4),
    macd_signal DECIMAL(12,4),
    macd_histogram DECIMAL(12,4),
    
    -- RSI
    rsi_14 DECIMAL(8,4),
    rsi_30 DECIMAL(8,4),
    
    -- Bollinger Bands
    bb_upper DECIMAL(12,4),
    bb_middle DECIMAL(12,4),
    bb_lower DECIMAL(12,4),
    bb_width DECIMAL(8,4),
    bb_position DECIMAL(8,4),
    
    -- Stochastic
    stochastic_k DECIMAL(8,4),
    stochastic_d DECIMAL(8,4),
    
    -- مؤشرات أخرى
    williams_r DECIMAL(8,4),
    cci DECIMAL(8,4),
    atr_14 DECIMAL(12,4),
    adx_14 DECIMAL(8,4),
    
    -- مستويات الدعم والمقاومة
    resistance_1 DECIMAL(12,4),
    resistance_2 DECIMAL(12,4),
    resistance_3 DECIMAL(12,4),
    support_1 DECIMAL(12,4),
    support_2 DECIMAL(12,4),
    support_3 DECIMAL(12,4),
    
    -- إشارات التداول
    buy_signal BOOLEAN DEFAULT false,
    sell_signal BOOLEAN DEFAULT false,
    hold_signal BOOLEAN DEFAULT false,
    trend_direction VARCHAR(10) CHECK (trend_direction IN ('UP', 'DOWN', 'SIDEWAYS')),
    trend_strength DECIMAL(8,4),
    
    -- نقاط القوة والضعف
    bullish_signals_count INTEGER DEFAULT 0,
    bearish_signals_count INTEGER DEFAULT 0,
    overall_score DECIMAL(8,4),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, calculation_date)
);

-- فهارس للمؤشرات الفنية
CREATE INDEX idx_technical_symbol_date ON technical_indicators(symbol, calculation_date DESC);
CREATE INDEX idx_technical_buy_signals ON technical_indicators(buy_signal) WHERE buy_signal = true;
CREATE INDEX idx_technical_sell_signals ON technical_indicators(sell_signal) WHERE sell_signal = true;

-- ==========================================
-- 6. إحصائيات السوق اليومية
-- ==========================================
CREATE TABLE market_statistics (
    id SERIAL PRIMARY KEY,
    trade_date DATE UNIQUE NOT NULL,
    
    -- إحصائيات عامة
    total_stocks INTEGER,
    active_stocks INTEGER,
    suspended_stocks INTEGER,
    
    -- الأداء
    gainers_count INTEGER,
    losers_count INTEGER,
    unchanged_count INTEGER,
    strong_gainers_count INTEGER, -- أكثر من 5%
    strong_losers_count INTEGER, -- أقل من -5%
    
    -- الحجم والقيمة
    total_volume BIGINT,
    total_turnover DECIMAL(15,2),
    avg_volume_per_stock DECIMAL(15,2),
    
    -- المؤشرات
    market_trend VARCHAR(10) CHECK (market_trend IN ('BULLISH', 'BEARISH', 'NEUTRAL')),
    avg_change_percent DECIMAL(8,4),
    median_change_percent DECIMAL(8,4),
    volatility_index DECIMAL(8,4),
    
    -- قطاعات
    best_performing_sector VARCHAR(100),
    worst_performing_sector VARCHAR(100),
    most_active_sector VARCHAR(100),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للإحصائيات
CREATE INDEX idx_market_stats_date ON market_statistics(trade_date DESC);

-- ==========================================
-- 7. سجل التحديثات
-- ==========================================
CREATE TABLE data_sync_log (
    id SERIAL PRIMARY KEY,
    sync_type VARCHAR(50) NOT NULL, -- 'historical', 'realtime', 'financial'
    source_file VARCHAR(500),
    records_processed INTEGER,
    records_inserted INTEGER,
    records_updated INTEGER,
    records_failed INTEGER,
    sync_status VARCHAR(20) CHECK (sync_status IN ('SUCCESS', 'FAILED', 'PARTIAL')),
    error_message TEXT,
    sync_duration_seconds INTEGER,
    started_at TIMESTAMP,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس لسجل التحديثات
CREATE INDEX idx_sync_log_type_date ON data_sync_log(sync_type, completed_at DESC);

-- ==========================================
-- إعداد الـ Triggers للتحديث التلقائي
-- ==========================================

-- Trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق الـ trigger على الجداول المطلوبة
CREATE TRIGGER update_stocks_master_updated_at BEFORE UPDATE ON stocks_master FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_stocks_realtime_updated_at BEFORE UPDATE ON stocks_realtime FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_stocks_financials_updated_at BEFORE UPDATE ON stocks_financials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==========================================
-- Comments للتوثيق
-- ==========================================
COMMENT ON TABLE stocks_master IS 'الجدول الرئيسي لمعلومات الأسهم الأساسية';
COMMENT ON TABLE stocks_historical IS 'البيانات التاريخية اليومية من ملفات TXT (321 سهم × 8 سنوات)';
COMMENT ON TABLE stocks_realtime IS 'البيانات الحية من ملف Excel مع 45+ مؤشر';
COMMENT ON TABLE stocks_financials IS 'البيانات المالية الشاملة من CSV مع 130+ مؤشر مالي';
COMMENT ON TABLE technical_indicators IS 'المؤشرات الفنية المحسوبة والإشارات';
COMMENT ON TABLE market_statistics IS 'إحصائيات السوق اليومية';
COMMENT ON TABLE data_sync_log IS 'سجل عمليات تحديث البيانات';

-- ==========================================
-- إعداد المستخدمين والصلاحيات (will be handled by setup script)
-- ==========================================

-- Note: User creation will be handled by the setup script
-- to avoid permission issues

-- GRANT statements for when users are created:
-- GRANT CONNECT ON DATABASE egx_stock_oracle TO egx_api_user;
-- GRANT USAGE ON SCHEMA public TO egx_api_user;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO egx_api_user;

-- ==========================================
-- إحصائيات ومعلومات البيانات المتوقعة
-- ==========================================

/*
المتوقع بعد الاستيراد:
- stocks_master: ~321 سجل (الأسهم المصرية)
- stocks_historical: ~642,000 سجل (321 × 2000 يوم)
- stocks_realtime: ~321 سجل (محدث يومياً)
- stocks_financials: ~1,284 سجل (321 × 4 أرباع سنوية)
- technical_indicators: ~321 سجل يومياً (محسوب)
- market_statistics: ~365 سجل سنوياً

إجمالي البيانات المتوقع: ~644,000 سجل تاريخي + بيانات حية ومالية
حجم قاعدة البيانات المتوقع: ~500 MB - 1 GB (مع الفهارس)
*/
