#!/usr/bin/env python3
"""
EGX Stock AI Oracle - Database Inspection Script
سكريبت فحص قاعدة البيانات بالتفصيل
"""

import os
import sys
import json
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_SERVICE_ROLE_KEY:
    print("❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def print_section(title):
    """Print a formatted section header"""
    print("\n" + "=" * 70)
    print(f"📊 {title}")
    print("=" * 70)

def inspect_stocks_master():
    """فحص جدول stocks_master"""
    print_section("فحص جدول stocks_master")
    
    try:
        # Get total count
        total_result = supabase.table("stocks_master").select("*", count="exact").execute()
        total_count = total_result.count
        print(f"📈 إجمالي الأسهم في stocks_master: {total_count}")
        
        # Get sample data
        sample_result = supabase.table("stocks_master").select("*").limit(5).execute()
        
        print(f"\n📋 عينة من البيانات (أول 5 أسهم):")
        for i, stock in enumerate(sample_result.data, 1):
            print(f"\n{i}. رمز السهم: {stock.get('symbol', 'N/A')}")
            print(f"   الاسم: {stock.get('name', 'N/A')}")
            print(f"   الاسم بالعربية: {stock.get('name_ar', 'N/A')}")
            print(f"   القطاع: {stock.get('sector', 'N/A')}")
            print(f"   السوق: {stock.get('market', 'N/A')}")
            print(f"   نشط: {stock.get('is_active', 'N/A')}")
        
        # Check for missing data
        missing_names = supabase.table("stocks_master").select("symbol").is_("name", "null").execute()
        missing_sectors = supabase.table("stocks_master").select("symbol").is_("sector", "null").execute()
        
        print(f"\n⚠️ إحصائيات البيانات المفقودة:")
        print(f"   أسهم بدون أسماء: {len(missing_names.data)}")
        print(f"   أسهم بدون قطاعات: {len(missing_sectors.data)}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص stocks_master: {e}")

def inspect_stocks_realtime():
    """فحص جدول stocks_realtime"""
    print_section("فحص جدول stocks_realtime")
    
    try:
        # Get total count
        total_result = supabase.table("stocks_realtime").select("*", count="exact").execute()
        total_count = total_result.count
        print(f"⚡ إجمالي الأسهم في stocks_realtime: {total_count}")
        
        # Get sample data with all fields
        sample_result = supabase.table("stocks_realtime").select("*").limit(3).execute()
        
        print(f"\n📋 عينة تفصيلية من البيانات الحقيقية:")
        for i, stock in enumerate(sample_result.data, 1):
            print(f"\n{i}. رمز السهم: {stock.get('symbol', 'N/A')}")
            print(f"   السعر الحالي: {stock.get('current_price', 'N/A')} جنيه")
            print(f"   سعر الافتتاح: {stock.get('open_price', 'N/A')} جنيه")
            print(f"   أعلى سعر: {stock.get('high_price', 'N/A')} جنيه")
            print(f"   أدنى سعر: {stock.get('low_price', 'N/A')} جنيه")
            print(f"   الإغلاق السابق: {stock.get('previous_close', 'N/A')} جنيه")
            print(f"   مقدار التغيير: {stock.get('change_amount', 'N/A')} جنيه")
            print(f"   نسبة التغيير: {stock.get('change_percent', 'N/A')}%")
            print(f"   الحجم: {stock.get('volume', 'N/A'):,}" if stock.get('volume') else "   الحجم: N/A")
            print(f"   القيمة المتداولة: {stock.get('turnover', 'N/A'):,.2f}" if stock.get('turnover') else "   القيمة المتداولة: N/A")
            print(f"   عدد الصفقات: {stock.get('trades_count', 'N/A')}")
            print(f"   آخر تحديث: {stock.get('updated_at', 'N/A')}")
        
        # Check data quality
        stocks_with_prices = supabase.table("stocks_realtime").select("symbol").not_.is_("current_price", "null").execute()
        stocks_with_volume = supabase.table("stocks_realtime").select("symbol").not_.is_("volume", "null").execute()
        stocks_with_change = supabase.table("stocks_realtime").select("symbol").not_.is_("change_percent", "null").execute()
        
        print(f"\n📊 جودة البيانات:")
        print(f"   أسهم لها أسعار: {len(stocks_with_prices.data)}/{total_count}")
        print(f"   أسهم لها حجم تداول: {len(stocks_with_volume.data)}/{total_count}")
        print(f"   أسهم لها نسبة تغيير: {len(stocks_with_change.data)}/{total_count}")
        
        # Show price ranges
        all_stocks = supabase.table("stocks_realtime").select("current_price").not_.is_("current_price", "null").execute()
        prices = [float(s['current_price']) for s in all_stocks.data if s.get('current_price')]
        
        if prices:
            print(f"\n💰 نطاق الأسعار:")
            print(f"   أعلى سعر: {max(prices):.2f} جنيه")
            print(f"   أدنى سعر: {min(prices):.2f} جنيه")
            print(f"   متوسط السعر: {sum(prices)/len(prices):.2f} جنيه")
        
    except Exception as e:
        print(f"❌ خطأ في فحص stocks_realtime: {e}")

def inspect_market_indices():
    """فحص جدول market_indices"""
    print_section("فحص جدول market_indices")
    
    try:
        # Get all indices
        indices_result = supabase.table("market_indices").select("*").execute()
        
        print(f"📈 إجمالي المؤشرات: {len(indices_result.data)}")
        
        print(f"\n📋 جميع مؤشرات السوق:")
        for i, index in enumerate(indices_result.data, 1):
            print(f"\n{i}. رمز المؤشر: {index.get('symbol', 'N/A')}")
            print(f"   الاسم: {index.get('name', 'N/A')}")
            print(f"   الاسم بالعربية: {index.get('name_ar', 'N/A')}")
            print(f"   القيمة الحالية: {index.get('current_value', 'N/A')}")
            print(f"   قيمة الافتتاح: {index.get('open_value', 'N/A')}")
            print(f"   أعلى قيمة: {index.get('high_value', 'N/A')}")
            print(f"   أدنى قيمة: {index.get('low_value', 'N/A')}")
            print(f"   الإغلاق السابق: {index.get('previous_close', 'N/A')}")
            print(f"   مقدار التغيير: {index.get('change_amount', 'N/A')}")
            print(f"   نسبة التغيير: {index.get('change_percent', 'N/A')}%")
            print(f"   القيمة السوقية: {index.get('market_cap', 'N/A')}")
            print(f"   الحجم: {index.get('volume', 'N/A')}")
            print(f"   عدد الأسهم: {index.get('constituents_count', 'N/A')}")
            print(f"   آخر تحديث: {index.get('updated_at', 'N/A')}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص market_indices: {e}")

def inspect_historical_data():
    """فحص جدول stocks_historical"""
    print_section("فحص جدول stocks_historical")
    
    try:
        # Get total count
        total_result = supabase.table("stocks_historical").select("*", count="exact").execute()
        total_count = total_result.count
        print(f"📊 إجمالي السجلات التاريخية: {total_count:,}")
        
        # Get unique symbols count
        symbols_result = supabase.table("stocks_historical").select("symbol").execute()
        unique_symbols = len(set(s['symbol'] for s in symbols_result.data))
        print(f"📈 عدد الأسهم التي لها بيانات تاريخية: {unique_symbols}")
        
        # Get date range
        latest_date = supabase.table("stocks_historical").select("date").order("date", desc=True).limit(1).execute()
        earliest_date = supabase.table("stocks_historical").select("date").order("date", desc=False).limit(1).execute()
        
        if latest_date.data and earliest_date.data:
            print(f"📅 النطاق الزمني للبيانات:")
            print(f"   أقدم تاريخ: {earliest_date.data[0]['date']}")
            print(f"   أحدث تاريخ: {latest_date.data[0]['date']}")
        
        # Sample data
        sample_result = supabase.table("stocks_historical").select("*").limit(3).execute()
        
        print(f"\n📋 عينة من البيانات التاريخية:")
        for i, record in enumerate(sample_result.data, 1):
            print(f"\n{i}. رمز السهم: {record.get('symbol', 'N/A')}")
            print(f"   التاريخ: {record.get('date', 'N/A')}")
            print(f"   الافتتاح: {record.get('open', 'N/A')}")
            print(f"   الأعلى: {record.get('high', 'N/A')}")
            print(f"   الأدنى: {record.get('low', 'N/A')}")
            print(f"   الإغلاق: {record.get('close', 'N/A')}")
            print(f"   الحجم: {record.get('volume', 'N/A'):,}" if record.get('volume') else "   الحجم: N/A")
        
    except Exception as e:
        print(f"❌ خطأ في فحص stocks_historical: {e}")

def test_frontend_data_queries():
    """اختبار الاستعلامات التي يستخدمها الفرونت اند"""
    print_section("اختبار استعلامات الفرونت اند")
    
    try:
        # Test market overview query (like useMarketOverview)
        print("🔍 اختبار استعلام نظرة عامة على السوق...")
        market_overview = supabase.table("market_indices").select("*").in_("symbol", ["EGX30", "EGX70"]).execute()
        print(f"   نتائج EGX30/EGX70: {len(market_overview.data)} مؤشرات")
        
        for index in market_overview.data:
            print(f"   - {index.get('symbol')}: {index.get('current_value')} ({index.get('change_percent', 0):+.2f}%)")
        
        # Test top gainers query
        print(f"\n🔍 اختبار استعلام أفضل الأسهم أداءً...")
        top_gainers = supabase.table("stocks_realtime").select("symbol, current_price, change_percent").not_.is_("change_percent", "null").order("change_percent", desc=True).limit(5).execute()
        print(f"   أفضل 5 أسهم أداءً:")
        
        for stock in top_gainers.data:
            change = stock.get('change_percent', 0)
            print(f"   - {stock.get('symbol')}: {stock.get('current_price')} ({change:+.2f}%)")
        
        # Test most active by volume
        print(f"\n🔍 اختبار استعلام أكثر الأسهم تداولاً...")
        most_active = supabase.table("stocks_realtime").select("symbol, current_price, volume").not_.is_("volume", "null").order("volume", desc=True).limit(5).execute()
        print(f"   أكثر 5 أسهم تداولاً:")
        
        for stock in most_active.data:
            volume = stock.get('volume', 0)
            print(f"   - {stock.get('symbol')}: {volume:,} سهم")
        
        # Test StockTicker query
        print(f"\n🔍 اختبار استعلام شريط الأسعار...")
        ticker_data = supabase.table("stocks_realtime").select("symbol, current_price, change_percent").not_.is_("current_price", "null").limit(10).execute()
        print(f"   بيانات شريط الأسعار: {len(ticker_data.data)} أسهم")
        
        # Show JSON structure for frontend
        if ticker_data.data:
            print(f"\n📋 هيكل البيانات JSON للفرونت اند:")
            sample_stock = ticker_data.data[0]
            print(json.dumps(sample_stock, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استعلامات الفرونت اند: {e}")

def generate_summary():
    """إنشاء ملخص شامل"""
    print_section("الملخص الشامل")
    
    try:
        # Count all tables
        counts = {}
        tables = ["stocks_master", "stocks_realtime", "stocks_historical", "market_indices"]
        
        for table in tables:
            result = supabase.table(table).select("*", count="exact").execute()
            counts[table] = result.count
        
        print("📊 إحصائيات قاعدة البيانات:")
        print(f"   🏢 stocks_master: {counts.get('stocks_master', 0):,} سجل")
        print(f"   ⚡ stocks_realtime: {counts.get('stocks_realtime', 0):,} سجل")
        print(f"   📈 stocks_historical: {counts.get('stocks_historical', 0):,} سجل")
        print(f"   📊 market_indices: {counts.get('market_indices', 0):,} سجل")
        
        # Data freshness
        latest_update = supabase.table("stocks_realtime").select("updated_at").order("updated_at", desc=True).limit(1).execute()
        if latest_update.data:
            print(f"\n🕒 آخر تحديث للبيانات: {latest_update.data[0]['updated_at']}")
        
        # Status assessment
        print(f"\n✅ تقييم جودة البيانات:")
        
        if counts.get('stocks_realtime', 0) > 200:
            print("   ✅ بيانات الوقت الفعلي: ممتازة")
        elif counts.get('stocks_realtime', 0) > 100:
            print("   ⚠️ بيانات الوقت الفعلي: جيدة")
        else:
            print("   ❌ بيانات الوقت الفعلي: ناقصة")
        
        if counts.get('market_indices', 0) >= 5:
            print("   ✅ مؤشرات السوق: متوفرة")
        else:
            print("   ⚠️ مؤشرات السوق: ناقصة")
        
        if counts.get('stocks_historical', 0) > 10000:
            print("   ✅ البيانات التاريخية: ممتازة")
        elif counts.get('stocks_historical', 0) > 1000:
            print("   ⚠️ البيانات التاريخية: جيدة")
        else:
            print("   ❌ البيانات التاريخية: ناقصة")
        
        print(f"\n🎯 التوصيات:")
        
        if counts.get('stocks_realtime', 0) < 200:
            print("   📋 تحديث البيانات الحقيقية من ملف Excel")
        
        if counts.get('market_indices', 0) < 5:
            print("   📈 إضافة المزيد من مؤشرات السوق")
        
        print("   🌐 الآن يمكن فحص الفرونت اند للتأكد من عرض البيانات")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملخص: {e}")

def main():
    print("🔍 EGX Stock AI Oracle - فحص شامل لقاعدة البيانات")
    print("🕒 الوقت:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # Run all inspections
    inspect_stocks_master()
    inspect_stocks_realtime()
    inspect_market_indices()
    inspect_historical_data()
    test_frontend_data_queries()
    generate_summary()
    
    print("\n" + "=" * 70)
    print("✅ انتهى فحص قاعدة البيانات!")
    print("🔧 الخطوة التالية: فحص ملفات الفرونت اند")
    print("=" * 70)

if __name__ == "__main__":
    main()
