from typing import Optional, List
from datetime import datetime, date
from pydantic import BaseModel, Field
from decimal import Decimal


# Base response model
class ResponseBase(BaseModel):
    """Base response model with common fields"""
    success: bool = True
    message: Optional[str] = None
    
    class Config:
        from_attributes = True


# Stock Master Models
class StockBase(BaseModel):
    """Base stock model"""
    symbol: str = Field(..., max_length=15, description="Stock symbol")
    name_ar: str = Field(..., max_length=200, description="Arabic stock name")
    name_en: Optional[str] = Field(None, max_length=200, description="English stock name")
    sector: Optional[str] = Field(None, max_length=100, description="Stock sector")


class StockDetail(StockBase):
    """Detailed stock information"""
    is_active: bool = Field(default=True, description="Is stock actively traded")
    is_suspended: bool = Field(default=False, description="Is stock suspended")
    listing_date: Optional[datetime] = Field(None, description="Stock listing date")
    isin: Optional[str] = Field(None, max_length=20, description="ISIN code")
    market_cap: Optional[str] = Field(None, description="Market capitalization")
    shares_outstanding: Optional[str] = Field(None, description="Shares outstanding")
    free_float: Optional[str] = Field(None, description="Free float percentage")
    description: Optional[str] = Field(None, description="Company description")
    website: Optional[str] = Field(None, description="Company website")
    phone: Optional[str] = Field(None, description="Company phone")
    address: Optional[str] = Field(None, description="Company address")
    created_at: Optional[datetime] = Field(None, description="Record creation time")
    updated_at: Optional[datetime] = Field(None, description="Record update time")
    
    class Config:
        from_attributes = True


class StockList(BaseModel):
    """Stock list response"""
    stocks: List[StockBase]
    total: int = Field(..., description="Total number of stocks")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    has_next: bool = Field(..., description="Has next page")
    has_prev: bool = Field(..., description="Has previous page")


class StockResponse(ResponseBase):
    """Single stock response"""
    data: Optional[StockDetail] = None


class StockListResponse(ResponseBase):
    """Stock list response"""
    data: Optional[StockList] = None


# Search Models
class StockSearchParams(BaseModel):
    """Stock search parameters"""
    q: Optional[str] = Field(None, description="Search query (name or symbol)")
    sector: Optional[str] = Field(None, description="Filter by sector")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Items per page")


# Sector Models
class SectorInfo(BaseModel):
    """Sector information"""
    sector: str = Field(..., description="Sector name")
    stock_count: int = Field(..., description="Number of stocks in sector")
    
    class Config:
        from_attributes = True


class SectorListResponse(ResponseBase):
    """Sector list response"""
    data: Optional[List[SectorInfo]] = None
