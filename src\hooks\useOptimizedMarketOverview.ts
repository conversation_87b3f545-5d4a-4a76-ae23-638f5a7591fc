import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// نوع البيانات من قاعدة البيانات
type StocksRealtimeRow = Database['public']['Tables']['stocks_realtime']['Row'];

export interface OptimizedOverviewData {
  marketSummary: {
    totalValue: number;
    totalVolume: number;
    gainersCount: number;
    losersCount: number;
    unchangedCount: number;
    avgChange: number;
    marketTrend: 'bullish' | 'bearish' | 'neutral';
  };
  topStocks: {
    gainers: Array<{
      symbol: string;
      name: string | null;
      change_percent: number;
      current_price: number;
      volume: number;
    }>;
    losers: Array<{
      symbol: string;
      name: string | null;  
      change_percent: number;
      current_price: number;
      volume: number;
    }>;
    mostActive: Array<{
      symbol: string;
      name: string | null;
      volume: number;
      current_price: number;
      change_percent: number;
    }>;
  };
  technicalSignals: {
    bullishCount: number;
    bearishCount: number;
    strongTrendCount: number;
    targetsCount: number;
  };
  lastUpdated: string;
}

/**
 * Hook محسن للنظرة العامة على السوق
 * يستخدم استعلامات محددة وبيانات محسوبة مسبقاً
 */
export function useOptimizedMarketOverview() {
  return useQuery<OptimizedOverviewData>({
    queryKey: ['optimized-market-overview'],
    queryFn: async () => {
      console.log('🔍 جاري جلب النظرة العامة المحسنة...');

      // استعلام واحد محسن للبيانات الأساسية - تجنب مشاكل TypeScript
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select('*')
        .not('symbol', 'like', '%EGX%')
        .not('current_price', 'is', null)
        .order('volume', { ascending: false });

      if (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        throw new Error('لا توجد بيانات متاحة');
      }      // تصفية البيانات وتحويلها إلى النوع المطلوب
      const marketData = data.filter(item => 
        item.name && item.current_price !== null
      ) as StocksRealtimeRow[];

      console.log(`✅ تم جلب ${marketData.length} سهم للنظرة العامة`);

      // === حساب الملخص العام ===
      const validStocks = marketData.filter(s => s.change_percent !== null);
      const gainers = validStocks.filter(s => (s.change_percent || 0) > 0);
      const losers = validStocks.filter(s => (s.change_percent || 0) < 0);
      const unchanged = validStocks.filter(s => (s.change_percent || 0) === 0);
      
      const totalValue = marketData.reduce((sum, s) => sum + (s.turnover || 0), 0);
      const totalVolume = marketData.reduce((sum, s) => sum + (s.volume || 0), 0);
      const avgChange = validStocks.length > 0 
        ? validStocks.reduce((sum, s) => sum + (s.change_percent || 0), 0) / validStocks.length
        : 0;

      // تحديد اتجاه السوق
      let marketTrend: 'bullish' | 'bearish' | 'neutral' = 'neutral';
      if (gainers.length > losers.length * 1.2) {
        marketTrend = 'bullish';
      } else if (losers.length > gainers.length * 1.2) {
        marketTrend = 'bearish';
      }

      // === أفضل الأسهم (استخدام ترتيب قاعدة البيانات) ===
      const topGainers = gainers
        .sort((a, b) => (b.change_percent || 0) - (a.change_percent || 0))
        .slice(0, 5);

      const topLosers = losers
        .sort((a, b) => (a.change_percent || 0) - (b.change_percent || 0))
        .slice(0, 5);

      const mostActive = marketData
        .filter(s => (s.volume || 0) > 0)
        .slice(0, 5); // مرتبة بالفعل حسب الحجم

      // === الإشارات الفنية (استخدام البيانات المحسوبة) ===
      const bullishCount = marketData.filter(s => 
        (s.ma5 || 0) > (s.ma20 || 0) && s.ma5 && s.ma20
      ).length;

      const bearishCount = marketData.filter(s => 
        (s.ma5 || 0) < (s.ma20 || 0) && s.ma5 && s.ma20
      ).length;

      const strongTrendCount = marketData.filter(s => 
        (s.ma5 || 0) > (s.ma20 || 0) && 
        (s.ma20 || 0) > (s.ma50 || 0) && 
        s.ma5 && s.ma20 && s.ma50
      ).length;

      const targetsCount = marketData.filter(s => 
        s.target_1 && s.target_1 > 0
      ).length;

      console.log('📊 ملخص النظرة العامة:');
      console.log(`  📈 صاعدة: ${gainers.length}, 📉 هابطة: ${losers.length}`);
      console.log(`  🎯 اتجاه السوق: ${marketTrend}`);
      console.log(`  📊 إشارات فنية: صاعدة ${bullishCount}, هابطة ${bearishCount}`);

      return {
        marketSummary: {
          totalValue,
          totalVolume,
          gainersCount: gainers.length,
          losersCount: losers.length,
          unchangedCount: unchanged.length,
          avgChange,
          marketTrend
        },
        topStocks: {
          gainers: topGainers.map(s => ({
            symbol: s.symbol,
            name: s.name,
            change_percent: s.change_percent || 0,
            current_price: s.current_price || 0,
            volume: s.volume || 0
          })),
          losers: topLosers.map(s => ({
            symbol: s.symbol,
            name: s.name,
            change_percent: s.change_percent || 0,
            current_price: s.current_price || 0,
            volume: s.volume || 0
          })),
          mostActive: mostActive.map(s => ({
            symbol: s.symbol,
            name: s.name,
            volume: s.volume || 0,
            current_price: s.current_price || 0,
            change_percent: s.change_percent || 0
          }))
        },
        technicalSignals: {
          bullishCount,
          bearishCount,
          strongTrendCount,
          targetsCount
        },
        lastUpdated: new Date().toISOString()
      };
    },
    refetchInterval: 30000, // تحديث كل 30 ثانية
    staleTime: 15000, // البيانات صالحة لمدة 15 ثانية
  });
}
