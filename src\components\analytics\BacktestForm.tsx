
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Play } from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

type NewTest = {
  strategy: string;
  symbol: string;
  period: string;
  capital: string;
};

const STRATEGY_NAMES: Record<string, string> = {
  'ma-crossover': 'تقاطع المتوسطات المتحركة',
  'rsi': 'استراتيجية RSI',
  'bollinger': 'نطاقات بولينجر',
  'macd': 'مؤشر الماكد',
  'candlestick': 'نمط الشموع'
};

const DEFAULT_TEST: NewTest = {
  strategy: 'ma-crossover',
  symbol: 'COMI',
  period: '1Y',
  capital: '100000'
};

const BacktestForm = ({
  afterSubmit
}: {
  afterSubmit?: () => void;
}) => {
  const [newTest, setNewTest] = useState<NewTest>(DEFAULT_TEST);

  const mutation = useMutation({
    mutationFn: async (variables: NewTest) => {
      // Simulate a backend test: calculate mock result_summary
      const totalTrades = Math.floor(20 + Math.random() * 50);
      const winRate = 40 + Math.random() * 40;
      const totalReturn = (Math.random() - 0.3) * 30;
      const result_summary = {
        total_return: totalReturn,
        win_rate: winRate,
        max_drawdown: -Math.random() * 20,
        sharpe_ratio: (Math.random() - 0.5) * 2,
        total_trades: totalTrades,
        winning_trades: Math.floor(totalTrades * winRate / 100),
        losing_trades: totalTrades - Math.floor(totalTrades * winRate / 100),
        avg_win: 1 + Math.random() * 3,
        avg_loss: -(1 + Math.random() * 2),
      };
      const { error } = await supabase.from('analytics_backtests').insert([
        {
          strategy_name: STRATEGY_NAMES[variables.strategy] || variables.strategy,
          symbol: variables.symbol,
          timeframe: variables.period,
          status: 'completed',
          parameters: variables,
          result_summary
        }
      ]);
      if (error) throw error;
    },
    onSuccess: () => {
      if (afterSubmit) afterSubmit();
    }
  });

  const isRunning = mutation.isPending;

  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-white/60 rounded-lg">
      <select
        value={newTest.strategy}
        onChange={e => setNewTest(prev => ({ ...prev, strategy: e.target.value }))}
        className="px-3 py-2 border rounded-md"
      >
        <option value="ma-crossover">تقاطع المتوسطات</option>
        <option value="rsi">استراتيجية RSI</option>
        <option value="bollinger">نطاقات بولينجر</option>
        <option value="macd">مؤشر الماكد</option>
        <option value="candlestick">نمط الشموع</option>
      </select>
      <select
        value={newTest.symbol}
        onChange={e => setNewTest(prev => ({ ...prev, symbol: e.target.value }))}
        className="px-3 py-2 border rounded-md"
      >
        <option value="COMI">COMI</option>
        <option value="TALAAT">TALAAT</option>
        <option value="ETEL">ETEL</option>
        <option value="OTMT">OTMT</option>
      </select>
      <select
        value={newTest.period}
        onChange={e => setNewTest(prev => ({ ...prev, period: e.target.value }))}
        className="px-3 py-2 border rounded-md"
      >
        <option value="1M">شهر واحد</option>
        <option value="3M">3 أشهر</option>
        <option value="6M">6 أشهر</option>
        <option value="1Y">سنة واحدة</option>
        <option value="2Y">سنتان</option>
      </select>
      <Input
        type="number"
        placeholder="رأس المال"
        value={newTest.capital}
        onChange={e => setNewTest(prev => ({ ...prev, capital: e.target.value }))}
      />
      <Button
        onClick={() => mutation.mutate(newTest)}
        disabled={isRunning}
        className="bg-emerald-600 hover:bg-emerald-700"
      >
        {isRunning ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            جاري التشغيل...
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            تشغيل الاختبار
          </div>
        )}
      </Button>
    </div>
  );
};

export default BacktestForm;
