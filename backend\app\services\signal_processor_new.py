# Enhanced Signal Processing Service with Advanced PnL Logic
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from datetime import datetime
import logging

from ..database import get_db
from ..models.trading_signals import TradingSignal, LiveRecommendations, SignalPerformance

logger = logging.getLogger(__name__)

class SignalProcessor:
    """
    Enhanced signal processor with advanced PnL logic for trailing stops
    and protected profit calculations
    """
    
    def __init__(self):
        self.db_session = None
    
    async def process_buy_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """Process buy signal and create live recommendation"""
        try:
            # Get database session
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            buy_price = signal_data.get('buy_price')
            tp1 = signal_data.get('tp1')
            tp2 = signal_data.get('tp2')
            tp3 = signal_data.get('tp3')
            stop_loss = signal_data.get('sl')
            
            # Calculate risk-reward ratio
            risk_reward_ratio = None
            if buy_price and stop_loss and tp1:
                risk = buy_price - stop_loss
                reward = tp1 - buy_price
                risk_reward_ratio = reward / risk if risk > 0 else None
            
            # Check if there's already an active recommendation for this stock
            existing_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if existing_rec:
                # Update existing recommendation
                existing_rec.entry_price = buy_price
                existing_rec.tp1 = tp1
                existing_rec.tp2 = tp2
                existing_rec.tp3 = tp3
                existing_rec.stop_loss = stop_loss
                existing_rec.risk_reward_ratio = risk_reward_ratio
                existing_rec.updated_at = datetime.utcnow()
                existing_rec.highest_price = buy_price  # Initialize highest price
                
                logger.info(f"Updated existing recommendation for {stock_code}")
            else:
                # Create new live recommendation
                live_rec = LiveRecommendations(
                    stock_code=stock_code,
                    action='buy',
                    entry_price=buy_price,
                    tp1=tp1,
                    tp2=tp2,
                    tp3=tp3,
                    stop_loss=stop_loss,
                    risk_reward_ratio=risk_reward_ratio,
                    status='active',
                    highest_price=buy_price  # Initialize highest price
                )
                
                db.add(live_rec)
                logger.info(f"Created new buy recommendation for {stock_code}")
            
            db.commit()
            
            # Send notifications
            await self._send_buy_notification(signal_data)
            
        except Exception as e:
            logger.error(f"Error processing buy signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def process_sell_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """Process sell signal and close recommendation with advanced PnL logic"""
        try:
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            sell_price = signal_data.get('sell_price')
            
            # Find active recommendation
            active_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if active_rec:
                # Calculate final P&L
                if sell_price and active_rec.entry_price:
                    final_pnl = active_rec.calculate_pnl(sell_price)
                    
                    # Update highest price if this sell price is higher
                    if not active_rec.highest_price or sell_price > active_rec.highest_price:
                        active_rec.highest_price = sell_price
                    
                    # Determine if this is a protected profit scenario
                    targets_hit = sum([active_rec.tp1_hit or False, active_rec.tp2_hit or False, active_rec.tp3_hit or False])
                    is_protected_profit = False
                    
                    # Check if we have trailing stop scenario with achieved targets
                    if targets_hit > 0 and active_rec.highest_price:
                        # Calculate max profit that was achieved
                        max_profit_pnl = active_rec.calculate_pnl(active_rec.highest_price)
                        
                        # If current sell price gives positive return but less than max achieved
                        if final_pnl > 0 and final_pnl < max_profit_pnl:
                            # This is a trailing stop scenario - we're protecting profit
                            is_protected_profit = True
                            active_rec.protected_profit = final_pnl
                    
                    # Update recommendation
                    active_rec.current_price = sell_price
                    active_rec.current_pnl = final_pnl
                    active_rec.status = 'closed'
                    active_rec.close_reason = 'protected_profit' if is_protected_profit else 'sell_signal'
                    active_rec.closed_at = datetime.utcnow()
                    
                    # Create performance record with enhanced logic
                    outcome = 'profit' if final_pnl > 0 else 'loss' if final_pnl < 0 else 'breakeven'
                    if is_protected_profit:
                        outcome = 'protected_profit'
                    
                    performance = SignalPerformance(
                        signal_id=signal_id,
                        stock_code=stock_code,
                        total_return=final_pnl,
                        max_profit=active_rec.max_pnl or final_pnl,
                        outcome=outcome,
                        targets_hit=targets_hit
                    )
                    
                    db.add(performance)
                    
                    logger.info(f"Closed recommendation for {stock_code} with {final_pnl:.2f}% P&L (Protected: {is_protected_profit})")
                
                db.commit()
                
                # Send notifications with enhanced info
                await self._send_sell_notification(signal_data, active_rec.current_pnl, 
                                                 targets_hit, active_rec.close_reason == 'protected_profit')
            else:
                logger.warning(f"No active recommendation found for sell signal: {stock_code}")
                
        except Exception as e:
            logger.error(f"Error processing sell signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def process_tp_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """Process take profit signal with enhanced tracking"""
        try:
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            signal_type = signal_data['report']
            new_sl = signal_data.get('sl')
            
            # Find active recommendation
            active_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if active_rec:
                tp_price = None
                tp_number = None
                
                # Update TP achievement and stop loss
                if signal_type == 'tp1done':
                    active_rec.tp1_hit = True
                    active_rec.tp1_hit_at = datetime.utcnow()
                    tp_price = active_rec.tp1
                    tp_number = '1'
                elif signal_type == 'tp2done':
                    active_rec.tp2_hit = True
                    active_rec.tp2_hit_at = datetime.utcnow()
                    tp_price = active_rec.tp2
                    tp_number = '2'
                elif signal_type == 'tp3done':
                    active_rec.tp3_hit = True
                    active_rec.tp3_hit_at = datetime.utcnow()
                    tp_price = active_rec.tp3
                    tp_number = '3'
                
                # Update stop loss to breakeven or profit zone
                if new_sl:
                    active_rec.stop_loss = new_sl
                
                # Calculate current P&L and update highest price
                if tp_price and active_rec.entry_price:
                    current_pnl = active_rec.calculate_pnl(tp_price)
                    active_rec.current_pnl = current_pnl
                    active_rec.current_price = tp_price
                    
                    # Update highest price if this TP price is higher
                    if not active_rec.highest_price or tp_price > active_rec.highest_price:
                        active_rec.highest_price = tp_price
                    
                    # Update max profit
                    if not active_rec.max_pnl or current_pnl > active_rec.max_pnl:
                        active_rec.max_pnl = current_pnl
                
                active_rec.updated_at = datetime.utcnow()
                db.commit()
                
                logger.info(f"TP{tp_number} hit for {stock_code}, new SL: {new_sl}, highest price: {active_rec.highest_price}")
                
                # Send notifications
                await self._send_tp_notification(signal_data, tp_number, tp_price)
            else:
                logger.warning(f"No active recommendation found for TP signal: {stock_code}")
                
        except Exception as e:
            logger.error(f"Error processing TP signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def process_stop_loss_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """Process stop loss signal with enhanced logic"""
        try:
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            sl_price = signal_data.get('sl')
            
            # Find active recommendation
            active_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if active_rec:
                # Mark as stopped out
                active_rec.stop_loss_hit = True
                active_rec.stop_loss_hit_at = datetime.utcnow()
                active_rec.current_price = sl_price
                active_rec.status = 'closed'
                active_rec.closed_at = datetime.utcnow()
                
                # Calculate final P&L
                if sl_price and active_rec.entry_price:
                    final_pnl = active_rec.calculate_pnl(sl_price)
                    active_rec.current_pnl = final_pnl
                    
                    # Determine if this was a trailing stop (profit protection)
                    targets_hit = sum([active_rec.tp1_hit or False, active_rec.tp2_hit or False, active_rec.tp3_hit or False])
                    is_trailing_stop = False
                    
                    if targets_hit > 0 and final_pnl > 0:
                        # This is a trailing stop protecting profit
                        is_trailing_stop = True
                        active_rec.close_reason = 'trailing_stop'
                        active_rec.protected_profit = final_pnl
                    else:
                        # Regular stop loss
                        active_rec.close_reason = 'stop_loss'
                    
                    # Create performance record
                    outcome = 'protected_profit' if is_trailing_stop else 'loss'
                    performance = SignalPerformance(
                        signal_id=signal_id,
                        stock_code=stock_code,
                        total_return=final_pnl,
                        max_profit=active_rec.max_pnl or final_pnl,
                        outcome=outcome,
                        targets_hit=targets_hit
                    )
                    
                    db.add(performance)
                    
                    logger.info(f"Stop {'trailing' if is_trailing_stop else 'loss'} hit for {stock_code} at {sl_price}")
                
                db.commit()
                
                # Send notifications
                await self._send_stop_loss_notification(signal_data, active_rec.close_reason == 'trailing_stop')
            else:
                logger.warning(f"No active recommendation found for stop loss signal: {stock_code}")
                
        except Exception as e:
            logger.error(f"Error processing stop loss signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def _send_buy_notification(self, signal_data: Dict[str, Any]):
        """Send buy signal notification"""
        try:
            from .telegram_service import TelegramService
            from .notification_service import NotificationService
            
            stock_code = signal_data['stock_code']
            buy_price = signal_data.get('buy_price', 0)
            tp1 = signal_data.get('tp1', 0)
            tp2 = signal_data.get('tp2', 0)
            tp3 = signal_data.get('tp3', 0)
            sl = signal_data.get('sl', 0)
            
            # Calculate risk-reward ratio
            risk = buy_price - sl if buy_price and sl else 0
            reward = tp1 - buy_price if tp1 and buy_price else 0
            rr_ratio = reward / risk if risk > 0 else 0
            
            # Telegram message
            telegram_message = f"""
🟢 إشارة شراء جديدة

📈 السهم: {stock_code}
💰 سعر الشراء: {buy_price:.2f} جنيه
🎯 الأهداف:
   الأول: {tp1:.2f} جنيه
   الثاني: {tp2:.2f} جنيه  
   الثالث: {tp3:.2f} جنيه
⛔ وقف الخسارة: {sl:.2f} جنيه
📊 نسبة المخاطرة/العائد: 1:{rr_ratio:.2f}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # Send via Telegram
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
            # Send web notifications
            notification_service = NotificationService()
            await notification_service.send_signal_notification({
                'type': 'buy_signal',
                'stock_code': stock_code,
                'message': f'إشارة شراء جديدة - {stock_code}',
                'data': signal_data
            })
            
        except Exception as e:
            logger.error(f"Error sending buy notification: {str(e)}")
    
    async def _send_sell_notification(self, signal_data: Dict[str, Any], pnl: Optional[float] = None, 
                                    targets_hit: int = 0, is_protected: bool = False):
        """Send sell signal notification with enhanced info"""
        try:
            from .telegram_service import TelegramService
            
            stock_code = signal_data['stock_code']
            sell_price = signal_data.get('sell_price', 0)
            
            pnl_text = f"\n💰 الربح/الخسارة: {pnl:.2f}%" if pnl else ""
            pnl_emoji = "📈" if pnl and pnl > 0 else "📉" if pnl and pnl < 0 else "🔄"
            
            # Enhanced message for protected profit
            protection_text = ""
            if is_protected:
                protection_text = f"\n🛡️ ربح محمي بعد تحقيق {targets_hit} هدف"
            
            telegram_message = f"""
🔴 إشارة بيع

📈 السهم: {stock_code}
💰 سعر البيع: {sell_price:.2f} جنيه{pnl_text}{protection_text}
{pnl_emoji} تم إغلاق الصفقة

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
        except Exception as e:
            logger.error(f"Error sending sell notification: {str(e)}")
    
    async def _send_tp_notification(self, signal_data: Dict[str, Any], tp_number: str, tp_price: Optional[float] = None):
        """Send take profit notification"""
        try:
            from .telegram_service import TelegramService
            
            stock_code = signal_data['stock_code']
            new_sl = signal_data.get('sl', 0)
            
            telegram_message = f"""
🎯 تحقق الهدف {tp_number}

📈 السهم: {stock_code}
💰 سعر الهدف: {tp_price:.2f} جنيه
🔄 وقف الخسارة الجديد: {new_sl:.2f} جنيه
✅ نقل الصفقة لمنطقة الأمان

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
        except Exception as e:
            logger.error(f"Error sending TP notification: {str(e)}")
    
    async def _send_stop_loss_notification(self, signal_data: Dict[str, Any], is_trailing: bool = False):
        """Send stop loss notification with trailing stop info"""
        try:
            from .telegram_service import TelegramService
            
            stock_code = signal_data['stock_code']
            sl_price = signal_data.get('sl', 0)
            
            # Different message for trailing stop vs regular stop loss
            if is_trailing:
                message_title = "🛡️ تفعيل وقف الخسارة المتحرك"
                message_subtitle = "✅ تم حماية الأرباح المحققة"
            else:
                message_title = "⛔ تفعيل وقف الخسارة"
                message_subtitle = "❌ تم إغلاق الصفقة"
            
            telegram_message = f"""
{message_title}

📈 السهم: {stock_code}
💰 سعر التنفيذ: {sl_price:.2f} جنيه
{message_subtitle}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
        except Exception as e:
            logger.error(f"Error sending stop loss notification: {str(e)}")
