import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';

const mockStockData = [
  {
    symbol: 'COMI',
    current_price: 45.50,
    change_amount: 2.30,
    change_percent: 5.33,
    volume: 1250000,
  },
  {
    symbol: 'ETEL',
    current_price: 18.75,
    change_amount: -0.85,
    change_percent: -4.34,
    volume: 890000,
  },
  {
    symbol: 'ORWE',
    current_price: 12.40,
    change_amount: 0.60,
    change_percent: 5.08,
    volume: 650000,
  },
];

// Mock Supabase client - define all mock functions at the top level
vi.mock('@/integrations/supabase/client', () => {
  const mockSubscribe = vi.fn(() => ({
    unsubscribe: vi.fn(),
  }));
  
  const mockChannel = vi.fn(() => ({
    on: vi.fn(() => ({
      subscribe: mockSubscribe,
    })),
    unsubscribe: vi.fn(),
  }));

  return {
    supabase: {
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          order: vi.fn(() => ({
            limit: vi.fn(() => Promise.resolve({
              data: mockStockData,
              error: null,
            })),
          })),
        })),
      })),
      channel: mockChannel,
    },
  };
});

import StockTicker from '../../components/StockTicker';

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
    },
  });
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('StockTicker Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  it('renders the live indicator', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('EGX Live')).toBeInTheDocument();
    });
  });

  it('displays loading state initially', () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );

    expect(screen.getByText('جاري تحميل البيانات...')).toBeInTheDocument();
  });  it('renders stock data after loading', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );

    await waitFor(() => {
      // Use getAllByText to handle multiple occurrences
      const comiElements = screen.getAllByText('COMI');
      expect(comiElements.length).toBeGreaterThan(0);
      expect(screen.getByText('45.50')).toBeInTheDocument();
      expect(screen.getByText('*****')).toBeInTheDocument(); // Change amount includes + sign for positive values
      expect(screen.getByText('5.33%')).toBeInTheDocument();
    });
  });
  it('displays multiple stocks', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );

    await waitFor(() => {
      // Use getAllByText to handle multiple occurrences of stock symbols
      const comiElements = screen.getAllByText('COMI');
      const etelElements = screen.getAllByText('ETEL');
      const orweElements = screen.getAllByText('ORWE');
      
      expect(comiElements.length).toBeGreaterThan(0);
      expect(etelElements.length).toBeGreaterThan(0);
      expect(orweElements.length).toBeGreaterThan(0);
    });
  });

  it('formats positive changes correctly', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );    await waitFor(() => {
      // Check that percentage values appear (they are formatted as absolute values in badges)
      expect(screen.getByText('5.33%')).toBeInTheDocument();
      expect(screen.getByText('5.08%')).toBeInTheDocument();
    });
  });

  it('formats negative changes correctly', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );    await waitFor(() => {
      // Check negative change formatting - percentage is shown as absolute value in badge
      expect(screen.getByText('4.34%')).toBeInTheDocument();
    });
  });
  it('formats volume correctly', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );

    await waitFor(() => {
      // Volume should be formatted by the formatVolume function in the component
      // 1250000 > 1000000 so it becomes 1.3M
      // 890000 < 1000000 so it becomes 890K  
      // 650000 < 1000000 so it becomes 650K
      expect(screen.getByText('1.3M')).toBeInTheDocument();
      expect(screen.getByText('890K')).toBeInTheDocument();
      expect(screen.getByText('650K')).toBeInTheDocument();
    });
  });

  it('applies correct styling for positive changes', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );    await waitFor(() => {
      // Check that stocks with positive changes are rendered - use getAllByText to avoid multiple element error
      const comiElements = screen.getAllByText('COMI');
      expect(comiElements.length).toBeGreaterThan(0);
      const orweElements = screen.getAllByText('ORWE');
      expect(orweElements.length).toBeGreaterThan(0);
    });
  });

  it('applies correct styling for negative changes', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );    await waitFor(() => {
      // Check that stock with negative change is rendered - use getAllByText to avoid multiple element error
      const etelElements = screen.getAllByText('ETEL');
      expect(etelElements.length).toBeGreaterThan(0);
    });
  });

  it('handles error state gracefully', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );

    await waitFor(() => {
      // Component should still render the header even in error states
      expect(screen.getByText('EGX Live')).toBeInTheDocument();
    });
  });

  it('handles empty data gracefully', async () => {
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );
    
    await waitFor(() => {
      // Component should still render the live indicator even with empty data
      expect(screen.getByText('EGX Live')).toBeInTheDocument();
    });
  });

  it('subscribes to real-time updates on mount', async () => {
    const { supabase } = await import('@/integrations/supabase/client');
    
    render(
      <TestWrapper>
        <StockTicker />
      </TestWrapper>
    );

    // Wait for component to mount and call the channel function
    await waitFor(() => {
      expect(supabase.channel).toHaveBeenCalledWith('stock_prices');
    });
  });
});
