#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 محدث الأسماء العربية الحقيقية - استخراج من Excel
Arabic Names Updater - Extract Real Names from Excel

📅 Created: 2025-06-16
🎯 Purpose: استخراج الأسماء العربية الحقيقية من ملف Excel وتحديث قاعدة البيانات
"""

import pandas as pd
import subprocess
import logging
import sys
from pathlib import Path
from datetime import datetime
import re

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('arabic_names_update.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ArabicNamesUpdater:
    """محدث الأسماء العربية الحقيقية"""
    
    def __init__(self):
        self.excel_file_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx")
        self.stats = {
            'processed': 0,
            'updated': 0,
            'skipped': 0,
            'failed': 0,
            'not_found_in_excel': 0,
            'not_found_in_db': 0
        }
    
    def execute_sql(self, sql_command: str, description: str = ""):
        """تنفيذ أمر SQL باستخدام sudo postgres"""
        try:
            cmd = ['sudo', '-u', 'postgres', 'psql', '-d', 'egx_stock_oracle', '-c', sql_command]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            if description:
                logger.info(f"✅ {description}")
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ SQL Error in {description}: {e.stderr}")
            return False, e.stderr
    
    def clean_arabic_name(self, name: str) -> str:
        """تنظيف الاسم العربي"""
        if pd.isna(name):
            return None
            
        name = str(name).strip()
        
        # إزالة البادئات الغير مرغوبة
        name = re.sub(r'^(مؤشر\s+)', '', name)  # Remove "مؤشر " prefix for indices
        
        # تنظيف المسافات الزائدة
        name = re.sub(r'\s+', ' ', name)
        
        return name if name and len(name) > 0 else None
    
    def load_excel_mapping(self):
        """تحميل خريطة الرموز والأسماء من Excel"""
        try:
            logger.info("📥 تحميل خريطة الأسماء من Excel...")
            
            if not self.excel_file_path.exists():
                logger.error(f"❌ ملف Excel غير موجود: {self.excel_file_path}")
                return None
            
            df = pd.read_excel(self.excel_file_path)
            logger.info(f"📊 تم تحميل {len(df)} سجل من Excel")
            
            # إنشاء خريطة الرموز والأسماء
            symbol_to_name = {}
            
            for idx, row in df.iterrows():
                symbol = row.get('الرمز')
                arabic_name = row.get('الاسم')
                
                if pd.notna(symbol) and pd.notna(arabic_name):
                    symbol = str(symbol).strip().upper()
                    arabic_name = self.clean_arabic_name(arabic_name)
                    
                    if symbol and arabic_name:
                        # تجاهل المؤشرات
                        if not arabic_name.startswith('مؤشر'):
                            symbol_to_name[symbol] = arabic_name
            
            logger.info(f"🎯 تم العثور على {len(symbol_to_name)} اسم أسهم صالح (باستثناء المؤشرات)")
            
            # عرض عينة من البيانات
            logger.info("📋 عينة من الخريطة:")
            for i, (symbol, name) in enumerate(list(symbol_to_name.items())[:10]):
                logger.info(f"  {symbol:10} -> {name}")
            
            return symbol_to_name
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل ملف Excel: {e}")
            return None
    
    def get_database_symbols(self):
        """الحصول على رموز الأسهم من قاعدة البيانات"""
        try:
            logger.info("🗃️ جلب رموز الأسهم من قاعدة البيانات...")
            
            sql = """
                SELECT symbol, name_ar 
                FROM stocks_master 
                WHERE symbol NOT LIKE 'EGX%'
                ORDER BY symbol;
            """
            
            success, result = self.execute_sql(sql, "جلب رموز الأسهم")
            if not success:
                return None
            
            # تحليل النتيجة
            db_symbols = {}
            lines = result.strip().split('\n')
            
            # تجاهل العناوين والخطوط الفاصلة
            data_lines = [line for line in lines if '|' in line and not line.startswith(' symbol')]
            
            for line in data_lines:
                if '|' in line:
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 2 and parts[0] and parts[1]:
                        symbol = parts[0]
                        current_name = parts[1]
                        db_symbols[symbol] = current_name
            
            logger.info(f"🎯 تم العثور على {len(db_symbols)} رمز في قاعدة البيانات")
            return db_symbols
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب البيانات من قاعدة البيانات: {e}")
            return None
    
    def update_arabic_names(self):
        """تحديث الأسماء العربية في قاعدة البيانات"""
        try:
            logger.info("🚀 بدء عملية تحديث الأسماء العربية...")
            
            # تحميل خريطة Excel
            excel_mapping = self.load_excel_mapping()
            if not excel_mapping:
                logger.error("❌ فشل في تحميل خريطة Excel")
                return False
            
            # جلب رموز قاعدة البيانات
            db_symbols = self.get_database_symbols()
            if not db_symbols:
                logger.error("❌ فشل في جلب رموز قاعدة البيانات")
                return False
            
            logger.info(f"🔄 بدء تحديث {len(db_symbols)} رمز...")
            
            updates_made = []
            
            for symbol, current_name in db_symbols.items():
                self.stats['processed'] += 1
                
                # التحقق من وجود الرمز في Excel
                if symbol not in excel_mapping:
                    logger.debug(f"⚠️ الرمز {symbol} غير موجود في Excel")
                    self.stats['not_found_in_excel'] += 1
                    continue
                
                new_arabic_name = excel_mapping[symbol]
                
                # التحقق من الحاجة للتحديث
                if current_name == new_arabic_name:
                    logger.debug(f"⏭️ تم تجاهل {symbol} - الاسم مطابق بالفعل")
                    self.stats['skipped'] += 1
                    continue
                
                # تحديث الاسم
                escaped_name = new_arabic_name.replace("'", "''")  # Escape single quotes
                update_sql = f"""
                    UPDATE stocks_master 
                    SET name_ar = '{escaped_name}',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE symbol = '{symbol}';
                """
                
                success, _ = self.execute_sql(update_sql, f"تحديث {symbol}")
                
                if success:
                    logger.info(f"✅ تم تحديث {symbol}: '{current_name}' -> '{new_arabic_name}'")
                    self.stats['updated'] += 1
                    updates_made.append({
                        'symbol': symbol,
                        'old_name': current_name,
                        'new_name': new_arabic_name
                    })
                else:
                    logger.error(f"❌ فشل في تحديث {symbol}")
                    self.stats['failed'] += 1
            
            # كتابة تقرير التحديثات
            self.write_update_report(updates_made)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في عملية التحديث: {e}")
            return False
    
    def write_update_report(self, updates_made):
        """كتابة تقرير التحديثات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"arabic_names_update_report_{timestamp}.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("🔄 تقرير تحديث الأسماء العربية\n")
                f.write("=" * 50 + "\n")
                f.write(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("📊 الإحصائيات:\n")
                f.write(f"  • معالجة: {self.stats['processed']}\n")
                f.write(f"  • تم التحديث: {self.stats['updated']}\n")
                f.write(f"  • تم التجاهل: {self.stats['skipped']}\n")
                f.write(f"  • فشل: {self.stats['failed']}\n")
                f.write(f"  • غير موجود في Excel: {self.stats['not_found_in_excel']}\n\n")
                
                if updates_made:
                    f.write("📝 التحديثات المُنجزة:\n")
                    f.write("-" * 30 + "\n")
                    for update in updates_made:
                        f.write(f"{update['symbol']:10} | {update['old_name']} -> {update['new_name']}\n")
            
            logger.info(f"📄 تم حفظ التقرير في: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في كتابة التقرير: {e}")
    
    def verify_updates(self):
        """التحقق من التحديثات"""
        try:
            logger.info("🔍 التحقق من التحديثات...")
            
            sql = """
                SELECT symbol, name_ar 
                FROM stocks_master 
                WHERE symbol NOT LIKE 'EGX%'
                AND name_ar NOT LIKE 'سهم %'
                ORDER BY symbol
                LIMIT 20;
            """
            
            success, result = self.execute_sql(sql, "التحقق من الأسماء المحدثة")
            if success:
                logger.info("📋 عينة من الأسماء المحدثة:")
                logger.info(result)
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق: {e}")
    
    def run(self):
        """تشغيل عملية التحديث الكاملة"""
        try:
            start_time = datetime.now()
            logger.info("🏁 بدء عملية تحديث الأسماء العربية...")
            
            # تحديث الأسماء
            success = self.update_arabic_names()
            
            if success:
                # التحقق من النتائج
                self.verify_updates()
                
                # طباعة الإحصائيات النهائية
                duration = datetime.now() - start_time
                logger.info("=" * 60)
                logger.info("🎉 اكتملت عملية تحديث الأسماء العربية!")
                logger.info(f"⏱️ المدة الزمنية: {duration}")
                logger.info("📊 الإحصائيات النهائية:")
                for key, value in self.stats.items():
                    logger.info(f"  • {key}: {value}")
                logger.info("=" * 60)
                
                return True
            else:
                logger.error("❌ فشلت عملية التحديث")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ عام في التشغيل: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    try:
        updater = ArabicNamesUpdater()
        success = updater.run()
        
        if success:
            logger.info("✅ تمت العملية بنجاح!")
            sys.exit(0)
        else:
            logger.error("❌ فشلت العملية!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.warning("⚠️ تم إيقاف العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
