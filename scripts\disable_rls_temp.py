#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def disable_rls_temporarily():
    """Temporarily disable RLS on public data tables using Supabase SQL Editor approach"""
    print("🔧 EGX Stock AI Oracle - Temporary RLS Disabler")
    print("=" * 60)
    
    supabase_url = os.getenv('SUPABASE_URL')
    service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not service_key:
        print("❌ Error: Environment variables not set")
        return False
    
    print("🚨 WARNING: Temporarily disabling RLS on public data tables")
    print("   This allows anonymous access to all data in these tables")
    print("   Use this only for development/testing purposes\n")
    
    # Tables to disable RLS on
    tables = [
        'market_indices',
        'stocks_realtime', 
        'stocks_master',
        'stocks_historical',
        'stocks_financials',
        'technical_indicators',
        'market_news'
    ]
    
    success_count = 0
    
    for table in tables:
        # Try to execute ALTER TABLE command via a different approach
        # We'll use the database URL directly with proper authentication
        
        print(f"🔓 Disabling RLS on {table}...")
        
        # Since we can't execute ALTER TABLE via REST API directly,
        # let's create a database function to do this
        sql_command = f"ALTER TABLE public.{table} DISABLE ROW LEVEL SECURITY;"
        
        # For now, we'll output the SQL commands that need to be run
        print(f"   SQL needed: {sql_command}")
        success_count += 1
    
    print(f"\n📋 Manual Steps Required:")
    print("   1. Go to your Supabase Dashboard")
    print("   2. Navigate to SQL Editor")
    print("   3. Run the following SQL commands:\n")
    
    for table in tables:
        print(f"   ALTER TABLE public.{table} DISABLE ROW LEVEL SECURITY;")
    
    print(f"\n   4. Or run all at once:")
    all_sql = " ".join([f"ALTER TABLE public.{table} DISABLE ROW LEVEL SECURITY;" for table in tables])
    print(f"   {all_sql}")
    
    return True

def test_access():
    """Test access with anon key"""
    print("\n🧪 Testing current access with anon key...")
    
    supabase_url = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    tables_to_test = ['market_indices', 'stocks_realtime']
    
    for table in tables_to_test:
        response = requests.get(
            f"{supabase_url}/rest/v1/{table}?select=*&limit=1",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        if response.status_code == 200:
            data = response.json()
            if len(data) > 0:
                print(f"  ✅ {table}: {len(data)} records accessible")
            else:
                print(f"  ❌ {table}: Accessible but no data (RLS blocking)")
        else:
            print(f"  ❌ {table}: Error {response.status_code}")

if __name__ == "__main__":
    print("🔍 Current Access Status:")
    test_access()
    
    print("\n" + "="*60)
    disable_rls_temporarily()
    
    print("\n💡 After running the SQL commands, test again with:")
    print("   python scripts/diagnose_rls.py")
    
    print("\n🔒 To re-enable RLS later (recommended for production):")
    tables = ['market_indices', 'stocks_realtime', 'stocks_master', 'stocks_historical', 'stocks_financials', 'technical_indicators', 'market_news']
    for table in tables:
        print(f"   ALTER TABLE public.{table} ENABLE ROW LEVEL SECURITY;")
        print(f"   CREATE POLICY \"Allow public read access to {table}\" ON public.{table} FOR SELECT TO anon USING (true);")
