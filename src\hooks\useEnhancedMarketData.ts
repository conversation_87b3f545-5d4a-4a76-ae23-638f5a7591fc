 
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Enhanced interfaces for better data handling
export interface EnhancedStock {
  symbol: string;
  name: string | null;
  current_price: number | null;
  change_percent: number | null;
  change_amount: number | null;
  volume: number | null;
  turnover: number | null;
  market_cap: number | null;
  sector: string | null;
  ma5: number | null;
  ma10: number | null;
  ma20: number | null;
  ma50: number | null;
  ma100: number | null;
  ma200: number | null;
  high_price: number | null;
  low_price: number | null;
  open_price: number | null;
  previous_close: number | null;
  updated_at: string | null;
  // Enhanced financial data
  pe_ratio: number | null;
  eps_annual: number | null;
  dividend_yield: number | null;
  book_value: number | null;
  liquidity_ratio: number | null;
  target_1: number | null;
  target_2: number | null;
  target_3: number | null;
  stop_loss: number | null;
  speculation_opportunity: boolean | null;
  volume_inflow: number | null;
  volume_outflow: number | null;
  liquidity_inflow: number | null;
  liquidity_outflow: number | null;  trades_count: number | null;
  last_trade_date: string | null;
  price_range: number | null;
  tk_indicator: number | null;
  kj_indicator: number | null;
  avg_net_volume_3d: number | null;
  avg_net_volume_5d: number | null;
}

// Define the exact return type from Supabase query
type StockQueryResult = {
  symbol: string;
  name: string | null;
  current_price: number | null;
  change_percent: number | null;
  change_amount: number | null;
  volume: number | null;
  turnover: number | null;
  market_cap: number | null;
  sector: string | null;
  ma5: number | null;
  ma10: number | null;
  ma20: number | null;
  ma50: number | null;
  ma100: number | null;
  ma200: number | null;
  high_price: number | null;
  low_price: number | null;
  open_price: number | null;
  previous_close: number | null;
  updated_at: string | null;
  pe_ratio: number | null;
  eps_annual: number | null;
  dividend_yield: number | null;
  book_value: number | null;
  liquidity_ratio: number | null;
  target_1: number | null;
  target_2: number | null;
  target_3: number | null;
  stop_loss: number | null;
  speculation_opportunity: boolean | null;
  volume_inflow: number | null;
  volume_outflow: number | null;
  liquidity_inflow: number | null;
  liquidity_outflow: number | null;
  trades_count: number | null;
  last_trade_date: string | null;
  price_range: number | null;
  tk_indicator: number | null;
  kj_indicator: number | null;
};

export interface MarketSummary {
  totalStocks: number;
  totalVolume: number;
  totalTurnover: number;
  totalMarketCap: number;
  avgVolume: number;
  avgChange: number;
  gainers: number;
  losers: number;
  unchanged: number;
}

export interface TopPerformers {
  topGainer: EnhancedStock | null;
  topLoser: EnhancedStock | null;
  mostActive: EnhancedStock | null;
  highestTurnover: EnhancedStock | null;
}

export interface SectorAnalysis {
  sector: string;
  stockCount: number;
  totalVolume: number;
  totalTurnover: number;
  avgChange: number;
  topStock: string;
  performance: 'positive' | 'negative' | 'neutral';
}

export interface TechnicalSignals {
  breakoutsAboveMA20: number;
  bullishCrossover: number; // MA5 > MA20
  bearishCrossover: number; // MA5 < MA20
  oversoldStocks: number; // Price < MA5 * 0.95
  overboughtStocks: number; // Price > MA5 * 1.05
  strongUptrend: number; // MA5 > MA20 > MA50
  strongDowntrend: number; // MA5 < MA20 < MA50
}

export interface EnhancedMarketData {
  summary: MarketSummary;
  performers: TopPerformers;
  sectors: SectorAnalysis[];
  technical: TechnicalSignals;
  stocks: EnhancedStock[];
  lastUpdated: string;
}

export function useEnhancedMarketData() {
  return useQuery<EnhancedMarketData>({
    queryKey: ['enhanced-market-data'],
    queryFn: async () => {
      console.log('🔄 Fetching enhanced market data...');      const { data: stocks, error } = await supabase
        .from('stocks_realtime')
        .select('*')
        .not('symbol', 'in', '("EGX30","EGX100EWI","EGX70EWI","EGX30ETF","EGX30Capped")')
        .not('name', 'is', null)
        .order('volume', { ascending: false });if (error) {
        console.error('❌ Error fetching stocks:', error);
        throw error;
      }

      if (!stocks || stocks.length === 0) {
        console.warn('⚠️ No stock data found');
        throw new Error('No stock data available');
      }      // Cast to any to bypass TypeScript issues temporarily
      const stockData = stocks as any[];
      console.log(`✅ Fetched ${stockData.length} stocks`);

      // Calculate market summary
      const summary: MarketSummary = {
        totalStocks: stockData.length,
        totalVolume: stockData.reduce((sum: number, stock: any) => sum + (stock.volume || 0), 0),
        totalTurnover: stockData.reduce((sum: number, stock: any) => sum + (stock.turnover || 0), 0),
        totalMarketCap: stockData.reduce((sum: number, stock: any) => sum + (stock.market_cap || 0), 0),
        avgVolume: 0,
        avgChange: 0,
        gainers: 0,
        losers: 0,
        unchanged: 0
      };

      // Calculate averages and distribution
      let totalChange = 0;
      let validChangeCount = 0;
      
      stockData.forEach((stock: any) => {
        const change = stock.change_percent || 0;
        if (stock.change_percent !== null) {
          totalChange += change;
          validChangeCount++;
        }
        
        if (change > 0) summary.gainers++;
        else if (change < 0) summary.losers++;
        else summary.unchanged++;
      });

      summary.avgVolume = summary.totalVolume / stockData.length;
      summary.avgChange = validChangeCount > 0 ? totalChange / validChangeCount : 0;

      // Find top performers
      const validStocks = stockData.filter((s: any) => s.change_percent !== null);
      
      const performers: TopPerformers = {
        topGainer: validStocks.length > 0 
          ? validStocks.reduce((max: any, stock: any) => 
              (stock.change_percent || 0) > (max.change_percent || 0) ? stock : max
            ) as EnhancedStock
          : null,
        topLoser: validStocks.length > 0 
          ? validStocks.reduce((min: any, stock: any) => 
              (stock.change_percent || 0) < (min.change_percent || 0) ? stock : min
            ) as EnhancedStock
          : null,
        mostActive: stockData.reduce((max: any, stock: any) => 
          (stock.volume || 0) > (max.volume || 0) ? stock : max
        ) as EnhancedStock,
        highestTurnover: stockData.reduce((max: any, stock: any) => 
          (stock.turnover || 0) > (max.turnover || 0) ? stock : max
        ) as EnhancedStock
      };

      // Analyze sectors
      const sectorMap = new Map<string, {
        stocks: any[];
        totalVolume: number;
        totalTurnover: number;
        totalChange: number;
        validChangeCount: number;
      }>();

      stockData.forEach((stock: any) => {
        if (stock.sector) {
          const existing = sectorMap.get(stock.sector) || {
            stocks: [],
            totalVolume: 0,
            totalTurnover: 0,
            totalChange: 0,
            validChangeCount: 0
          };
          
          existing.stocks.push(stock);
          existing.totalVolume += (stock.volume || 0);
          existing.totalTurnover += (stock.turnover || 0);
          
          if (stock.change_percent !== null) {
            existing.totalChange += stock.change_percent;
            existing.validChangeCount++;
          }
          
          sectorMap.set(stock.sector, existing);
        }
      });      const sectors: SectorAnalysis[] = Array.from(sectorMap.entries()).map(([sector, data]) => {
        const avgChange = data.validChangeCount > 0 ? data.totalChange / data.validChangeCount : 0;
        const topStock = data.stocks.reduce((best: any, stock: any) => 
          (stock.change_percent || 0) > (best.change_percent || 0) ? stock : best
        );

        let performance: 'positive' | 'negative' | 'neutral' = 'neutral';
        if (avgChange > 0.5) performance = 'positive';
        else if (avgChange < -0.5) performance = 'negative';

        return {
          sector,
          stockCount: data.stocks.length,
          totalVolume: data.totalVolume,
          totalTurnover: data.totalTurnover,
          avgChange,
          topStock: topStock.symbol,
          performance
        };
      }).sort((a, b) => b.totalTurnover - a.totalTurnover);

      // Calculate technical signals using any type
      const technical: TechnicalSignals = {
        breakoutsAboveMA20: stockData.filter((s: any) => 
          (s.current_price || 0) > (s.ma20 || 0) && s.ma20 !== null
        ).length,
        bullishCrossover: stockData.filter((s: any) => 
          (s.ma5 || 0) > (s.ma20 || 0) && s.ma5 !== null && s.ma20 !== null
        ).length,
        bearishCrossover: stockData.filter((s: any) => 
          (s.ma5 || 0) < (s.ma20 || 0) && s.ma5 !== null && s.ma20 !== null
        ).length,
        oversoldStocks: stockData.filter((s: any) => 
          (s.current_price || 0) < (s.ma5 || 0) * 0.95 && s.ma5 !== null
        ).length,
        overboughtStocks: stockData.filter((s: any) => 
          (s.current_price || 0) > (s.ma5 || 0) * 1.05 && s.ma5 !== null
        ).length,
        strongUptrend: stockData.filter((s: any) => 
          (s.ma5 || 0) > (s.ma20 || 0) && 
          (s.ma20 || 0) > (s.ma50 || 0) && 
          s.ma5 !== null && s.ma20 !== null && s.ma50 !== null
        ).length,
        strongDowntrend: stockData.filter((s: any) => 
          (s.ma5 || 0) < (s.ma20 || 0) && 
          (s.ma20 || 0) < (s.ma50 || 0) && 
          s.ma5 !== null && s.ma20 !== null && s.ma50 !== null
        ).length
      };

      const result: EnhancedMarketData = {
        summary,
        performers,
        sectors,
        technical,
        stocks: stockData as EnhancedStock[],
        lastUpdated: new Date().toISOString()
      };

      console.log('📊 Market data summary:', {
        totalStocks: summary.totalStocks,
        gainers: summary.gainers,
        losers: summary.losers,
        sectorsCount: sectors.length,
        topGainer: performers.topGainer?.symbol,
        topLoser: performers.topLoser?.symbol
      });

      return result;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 25000, // Consider data stale after 25 seconds
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Helper hook for specific market metrics
export function useMarketMetrics() {
  const { data: marketData, isLoading, error } = useEnhancedMarketData();
  
  return {
    data: marketData?.summary || null,
    isLoading,
    error
  };
}

// Helper hook for top performers
export function useTopPerformers() {
  const { data: marketData, isLoading, error } = useEnhancedMarketData();
  
  return {
    data: marketData?.performers || null,
    isLoading,
    error
  };
}

// Helper hook for sector analysis
export function useSectorAnalysis() {
  const { data: marketData, isLoading, error } = useEnhancedMarketData();
  
  return {
    data: marketData?.sectors || [],
    isLoading,
    error
  };
}

// Helper hook for technical signals
export function useTechnicalSignals() {
  const { data: marketData, isLoading, error } = useEnhancedMarketData();
  
  return {
    data: marketData?.technical || null,
    isLoading,
    error
  };
}
