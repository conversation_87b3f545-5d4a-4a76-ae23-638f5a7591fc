#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 مستورد البيانات الحية - نظام الأسهم المصرية
Realtime Data Importer - Egyptian Stock Market System

📅 Created: 2025-06-16
🎯 Purpose: استيراد البيانات الحية من stock_synco.xlsx (45+ عمود)
📁 Source: /mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx
"""

import pandas as pd
import psycopg2
from pathlib import Path
import logging
from datetime import datetime
import sys
from typing import Dict, Optional, Tuple
import numpy as np

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('realtime_data_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EGXRealtimeDataImporter:
    """مستورد البيانات الحية للأسهم المصرية"""
    
    def __init__(self, db_config: Dict[str, str]):
        """تهيئة المستورد مع إعدادات قاعدة البيانات"""
        self.db_config = db_config
        self.connection = None
        self.excel_file_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx")
        
        # إحصائيات العملية
        self.stats = {
            'total_records': 0,
            'inserted_records': 0,
            'updated_records': 0,
            'failed_records': 0,
            'start_time': None,
            'end_time': None
        }
        
        # خريطة تطابق أعمدة Excel مع قاعدة البيانات
        self.column_mapping = {
            # أعمدة أساسية
            'الرمز': 'symbol',
            'الافتتاح': 'open_price',
            'الأعلى': 'high_price',
            'الأدنى': 'low_price',
            'السعر': 'current_price',
            'الإغلاق السابق': 'previous_close',
            'التغير': 'change_amount',
            'التغير%': 'change_percent',
            
            # حجم وقيمة
            'الحجم': 'volume',
            'القيمة': 'turnover',
            'الصفقات': 'trades_count',
            
            # السيولة
            'نسبة السيولة': 'liquidity_ratio',
            'صافي السيولة': 'net_liquidity',
            'تدفق السيولة الداخل': 'liquidity_inflow',
            'تدفق السيولة الخارج': 'liquidity_outflow',
            'حجم الداخل': 'volume_inflow',
            'حجم الخارج': 'volume_outflow',
            'تدفق السيولة': 'liquidity_flow',
            
            # المتوسطات المتحركة
            'MA5': 'ma5',
            'MA10': 'ma10',
            'MA20': 'ma20',
            'MA50': 'ma50',
            'MA100': 'ma100',
            'MA200': 'ma200',
            
            # مؤشرات تكنيكال
            'TK': 'tk_indicator',
            'KJ': 'kj_indicator',
            
            # الأهداف
            'الهدف 1': 'target_1',
            'الهدف 2': 'target_2',
            'الهدف 3': 'target_3',
            'وقف الخسارة': 'stop_loss',
            
            # تصنيفات
            'حالة السهم': 'stock_status',
            'فرصة مضاربة': 'speculation_opportunity',
            'نطاق السعر': 'price_range',
            
            # إحصائيات متقدمة
            'متوسط صافي الحجم 3 أيام': 'avg_net_volume_3d',
            'متوسط صافي الحجم 5 أيام': 'avg_net_volume_5d',
            
            # نسب مالية أساسية
            'ربحية السهم': 'eps_annual',
            'القيمة الدفترية': 'book_value',
            'مضاعف السعر/الربح': 'pe_ratio',
            'عائد التوزيع': 'dividend_yield'
        }
    
    def connect_db(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.connection.autocommit = False
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close_db(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            logger.info("🔒 تم إغلاق الاتصال بقاعدة البيانات")
    
    def validate_excel_file(self) -> Tuple[bool, str]:
        """التحقق من صحة ملف Excel"""
        try:
            if not self.excel_file_path.exists():
                return False, "ملف Excel غير موجود"
            
            if self.excel_file_path.stat().st_size == 0:
                return False, "ملف Excel فارغ"
            
            # محاولة قراءة أول سطر
            df_test = pd.read_excel(self.excel_file_path, nrows=1)
            if df_test.empty:
                return False, "ملف Excel فارغ من البيانات"
            
            return True, "صحيح"
            
        except Exception as e:
            return False, f"خطأ في التحقق: {str(e)}"
    
    def load_excel_data(self) -> pd.DataFrame:
        """تحميل البيانات من ملف Excel"""
        try:
            logger.info(f"📖 قراءة ملف Excel: {self.excel_file_path.name}")
            
            # قراءة الملف مع معالجة الترميز
            df = pd.read_excel(self.excel_file_path, engine='openpyxl')
            
            logger.info(f"📊 تم تحميل {len(df)} سجل من ملف Excel")
            logger.info(f"📋 الأعمدة المتاحة: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ خطأ في قراءة ملف Excel: {e}")
            return pd.DataFrame()
    
    def clean_and_transform_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """تنظيف وتحويل البيانات"""
        if df.empty:
            return df
        
        try:
            logger.info("🧹 بدء تنظيف وتحويل البيانات")
            
            # نسخ البيانات
            clean_df = df.copy()
            
            # إعادة تسمية الأعمدة حسب التطابق
            rename_dict = {}
            for excel_col in clean_df.columns:
                if excel_col in self.column_mapping:
                    rename_dict[excel_col] = self.column_mapping[excel_col]
            
            clean_df = clean_df.rename(columns=rename_dict)
            
            # التأكد من وجود عمود الرمز
            if 'symbol' not in clean_df.columns:
                # البحث عن عمود يحتوي على رموز الأسهم
                possible_symbol_cols = ['الرمز', 'Symbol', 'SYMBOL', 'ticker', 'TICKER']
                for col in possible_symbol_cols:
                    if col in clean_df.columns:
                        clean_df['symbol'] = clean_df[col]
                        break
            
            if 'symbol' not in clean_df.columns:
                logger.error("❌ لم يتم العثور على عمود الرمز")
                return pd.DataFrame()
            
            # تنظيف رموز الأسهم
            clean_df['symbol'] = clean_df['symbol'].astype(str).str.strip().str.upper()
            clean_df = clean_df[clean_df['symbol'] != '']
            clean_df = clean_df[clean_df['symbol'] != 'NAN']
            clean_df = clean_df.dropna(subset=['symbol'])
            
            # تحويل الأعمدة الرقمية
            numeric_columns = [
                'open_price', 'high_price', 'low_price', 'current_price', 'previous_close',
                'change_amount', 'change_percent', 'volume', 'turnover', 'trades_count',
                'liquidity_ratio', 'net_liquidity', 'liquidity_inflow', 'liquidity_outflow',
                'volume_inflow', 'volume_outflow', 'liquidity_flow',
                'ma5', 'ma10', 'ma20', 'ma50', 'ma100', 'ma200',
                'tk_indicator', 'kj_indicator',
                'target_1', 'target_2', 'target_3', 'stop_loss',
                'stock_status', 'price_range',
                'avg_net_volume_3d', 'avg_net_volume_5d',
                'eps_annual', 'book_value', 'pe_ratio', 'dividend_yield'
            ]
            
            for col in numeric_columns:
                if col in clean_df.columns:
                    clean_df[col] = pd.to_numeric(clean_df[col], errors='coerce')
            
            # تحويل الأعمدة المنطقية
            boolean_columns = ['speculation_opportunity']
            for col in boolean_columns:
                if col in clean_df.columns:
                    clean_df[col] = clean_df[col].astype(bool)
            
            # إضافة تاريخ ووقت آخر تحديث
            clean_df['last_trade_date'] = datetime.now().date()
            clean_df['updated_at'] = datetime.now()
            
            # إزالة الصفوف التي لا تحتوي على بيانات أساسية
            essential_columns = ['current_price']
            for col in essential_columns:
                if col in clean_df.columns:
                    clean_df = clean_df.dropna(subset=[col])
                    clean_df = clean_df[clean_df[col] > 0]
            
            logger.info(f"✅ تم تنظيف البيانات - المتبقي: {len(clean_df)} سجل")
            
            return clean_df            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف البيانات: {e}")
            return pd.DataFrame()
    
    def ensure_stocks_exist(self, symbols: list, df: pd.DataFrame = None) -> bool:
        """التأكد من وجود الأسهم في جدول stocks_master مع استخدام الأسماء العربية الحقيقية"""
        try:
            cursor = self.connection.cursor()
            
            # الحصول على الرموز الموجودة
            cursor.execute("SELECT symbol FROM stocks_master WHERE symbol = ANY(%s)", (symbols,))
            existing_symbols = {row[0] for row in cursor.fetchall()}
            
            # إضافة الرموز المفقودة
            new_symbols = set(symbols) - existing_symbols
            
            if new_symbols:
                logger.info(f"➕ إضافة {len(new_symbols)} رمز جديد إلى stocks_master")
                
                # إنشاء خريطة الرموز إلى الأسماء العربية من DataFrame
                symbol_to_arabic_name = {}
                if df is not None and 'الاسم' in df.columns and 'symbol' in df.columns:
                    for _, row in df.iterrows():
                        symbol = str(row['symbol']).strip().upper()
                        arabic_name = str(row['الاسم']).strip()
                        if symbol and arabic_name and not arabic_name.startswith('مؤشر'):
                            symbol_to_arabic_name[symbol] = arabic_name
                
                for symbol in new_symbols:
                    # استخدام الاسم العربي الحقيقي إذا كان متاحاً، وإلا استخدام اسم مؤقت
                    arabic_name = symbol_to_arabic_name.get(symbol, f"سهم {symbol}")
                    
                    cursor.execute("""
                        INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT (symbol) DO NOTHING
                    """, (symbol, arabic_name, symbol, True))
            
            cursor.close()
            self.connection.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في التأكد من وجود الأسهم: {e}")
            return False
    
    def upsert_realtime_data(self, df: pd.DataFrame) -> Tuple[int, int, int]:
        """إدراج أو تحديث البيانات الحية"""
        if df.empty:
            return 0, 0, 0
        
        try:
            cursor = self.connection.cursor()
            
            inserted_count = 0
            updated_count = 0
            failed_count = 0
            
            # إعداد قائمة الأعمدة للإدراج
            db_columns = [
                'symbol', 'open_price', 'high_price', 'low_price', 'current_price',
                'previous_close', 'change_amount', 'change_percent', 'volume',
                'turnover', 'trades_count', 'liquidity_ratio', 'net_liquidity',
                'liquidity_inflow', 'liquidity_outflow', 'volume_inflow',
                'volume_outflow', 'liquidity_flow', 'ma5', 'ma10', 'ma20',
                'ma50', 'ma100', 'ma200', 'tk_indicator', 'kj_indicator',
                'target_1', 'target_2', 'target_3', 'stop_loss', 'stock_status',
                'speculation_opportunity', 'price_range', 'avg_net_volume_3d',
                'avg_net_volume_5d', 'eps_annual', 'book_value', 'pe_ratio',
                'dividend_yield', 'last_trade_date', 'updated_at'
            ]
            
            # إعداد استعلام UPSERT
            columns_str = ', '.join(db_columns)
            values_str = ', '.join(['%s'] * len(db_columns))
            update_str = ', '.join([f"{col} = EXCLUDED.{col}" for col in db_columns if col != 'symbol'])
            
            upsert_query = f"""
                INSERT INTO stocks_realtime ({columns_str})
                VALUES ({values_str})
                ON CONFLICT (symbol) DO UPDATE SET
                {update_str}
            """
            
            # معالجة كل سجل
            for _, row in df.iterrows():
                try:
                    # تحضير البيانات للإدراج
                    values = []
                    for col in db_columns:
                        value = row.get(col)
                        if pd.isna(value):
                            values.append(None)
                        elif col in ['speculation_opportunity']:
                            values.append(bool(value) if value is not None else False)
                        else:
                            values.append(value)
                    
                    # تنفيذ الاستعلام
                    cursor.execute(upsert_query, values)
                    
                    # تحديد نوع العملية (إدراج أم تحديث)
                    if cursor.rowcount == 1:
                        inserted_count += 1
                    else:
                        updated_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    logger.warning(f"⚠️ فشل في معالجة السهم {row.get('symbol', 'غير معروف')}: {e}")
            
            cursor.close()
            
            logger.info(f"💾 إدراج: {inserted_count}, تحديث: {updated_count}, فشل: {failed_count}")
            
            return inserted_count, updated_count, failed_count
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في إدراج البيانات الحية: {e}")
            return 0, 0, len(df)
    
    def log_sync_operation(self, records_processed: int, records_inserted: int, 
                          records_updated: int, records_failed: int, 
                          duration: int, status: str, error_msg: str = None):
        """تسجيل عملية المزامنة"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO data_sync_log (
                    sync_type, source_file, records_processed, records_inserted,
                    records_updated, records_failed, sync_status, error_message, 
                    sync_duration_seconds, started_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                'realtime', self.excel_file_path.name, records_processed, 
                records_inserted, records_updated, records_failed, status, 
                error_msg, duration, datetime.now()
            ))
            cursor.close()
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل عملية المزامنة: {e}")
    
    def import_realtime_data(self) -> Dict:
        """استيراد البيانات الحية الرئيسية"""
        self.stats['start_time'] = datetime.now()
        logger.info("🚀 بدء استيراد البيانات الحية للأسهم المصرية")
        
        if not self.connect_db():
            return self.stats
        
        try:
            # التحقق من صحة الملف
            is_valid, validation_msg = self.validate_excel_file()
            if not is_valid:
                logger.error(f"❌ ملف Excel غير صحيح: {validation_msg}")
                return self.stats
            
            # تحميل البيانات
            df = self.load_excel_data()
            if df.empty:
                logger.error("❌ لا توجد بيانات في ملف Excel")
                return self.stats
            
            self.stats['total_records'] = len(df)
            
            # تنظيف البيانات
            clean_df = self.clean_and_transform_data(df)
            if clean_df.empty:
                logger.error("❌ لا توجد بيانات صحيحة بعد التنظيف")
                return self.stats
              # التأكد من وجود الأسهم في stocks_master
            symbols = clean_df['symbol'].unique().tolist()
            if not self.ensure_stocks_exist(symbols, df):
                logger.error("❌ فشل في التأكد من وجود الأسهم")
                return self.stats
            
            # إدراج البيانات الحية
            inserted, updated, failed = self.upsert_realtime_data(clean_df)
            
            self.stats['inserted_records'] = inserted
            self.stats['updated_records'] = updated
            self.stats['failed_records'] = failed
            
            if inserted + updated > 0:
                self.connection.commit()
                status = 'SUCCESS'
                logger.info("✅ تم حفظ البيانات الحية بنجاح")
            else:
                self.connection.rollback()
                status = 'FAILED'
                logger.error("❌ فشل في حفظ البيانات الحية")
            
            self.stats['end_time'] = datetime.now()
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            # تسجيل العملية
            self.log_sync_operation(
                len(clean_df), inserted, updated, failed, 
                int(duration), status
            )
            
            # تقرير نهائي
            logger.info("=" * 80)
            logger.info("📋 تقرير استيراد البيانات الحية")
            logger.info("=" * 80)
            logger.info(f"📊 إجمالي السجلات: {self.stats['total_records']}")
            logger.info(f"➕ سجلات جديدة: {self.stats['inserted_records']}")
            logger.info(f"🔄 سجلات محدثة: {self.stats['updated_records']}")
            logger.info(f"❌ سجلات فاشلة: {self.stats['failed_records']}")
            logger.info(f"⏱️ المدة: {duration:.1f} ثانية")
            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في عملية الاستيراد: {e}")
            self.connection.rollback()
        
        finally:
            self.close_db()
        
        return self.stats

def main():
    """الدالة الرئيسية"""    # إعدادات قاعدة البيانات
    db_config = {
        'host': 'localhost',
        'database': 'egx_stock_oracle',
        'user': 'postgres',
        'password': '',
        'port': 5432
    }
    
    # إنشاء المستورد
    importer = EGXRealtimeDataImporter(db_config)
    
    # بدء عملية الاستيراد
    stats = importer.import_realtime_data()
    
    print("\n🎉 اكتملت عملية استيراد البيانات الحية!")
    print(f"📊 تم معالجة {stats['inserted_records'] + stats['updated_records']} سجل")

if __name__ == "__main__":
    main()
