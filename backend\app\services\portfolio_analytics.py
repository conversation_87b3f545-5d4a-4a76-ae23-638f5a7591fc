"""
Portfolio Analytics Service - خدمة تحليل المحفظة المتقدمة
تحتوي على جميع الحسابات والتحليلات المتقدمة للمحفظة الذكية
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_

from ..models.smart_portfolio import (
    SmartPortfolioPosition, 
    SmartPortfolioTransaction, 
    SmartPortfolioMetrics,
    PortfolioPerformanceSnapshot
)
from ..schemas.smart_portfolio import PortfolioPerformanceMetrics, RiskMetrics, PerformanceComparison

logger = logging.getLogger(__name__)

class PortfolioAnalytics:
    """خدمة تحليل المحفظة المتقدمة"""
    
    def __init__(self, db: Session):
        self.db = db
        self.initial_capital = 1000000.0  # رأس المال الأولي
        self.risk_free_rate = 0.10  # معدل خالي من المخاطر 10%
        self.trading_days_per_year = 252
    
    async def get_performance_metrics(self, timeframe: str = "1M") -> PortfolioPerformanceMetrics:
        """الحصول على مقاييس الأداء للفترة المحددة"""
        try:
            # حساب التواريخ
            end_date = datetime.now()
            start_date = self._get_start_date(timeframe, end_date)
            
            # الحصول على البيانات
            positions = await self._get_positions_in_period(start_date, end_date)
            transactions = await self._get_transactions_in_period(start_date, end_date)
            
            # حساب قيم المحفظة
            portfolio_values = await self._calculate_portfolio_values(positions)
            
            # حساب مقاييس العائد
            return_metrics = await self._calculate_return_metrics(transactions, start_date, end_date)
            
            # حساب مقاييس المخاطر
            risk_metrics = await self._calculate_risk_metrics(transactions, positions)
            
            # حساب إحصائيات التداول
            trading_stats = await self._calculate_trading_statistics(transactions)
            
            # حساب إحصائيات الإشارات
            signal_stats = await self._calculate_signal_statistics(start_date, end_date)
            
            # حساب توزيع المحفظة
            portfolio_distribution = await self._calculate_portfolio_distribution(positions)
            
            return PortfolioPerformanceMetrics(
                # قيم المحفظة
                total_portfolio_value=portfolio_values['total_value'],
                cash_balance=portfolio_values['cash_balance'],
                invested_value=portfolio_values['invested_value'],
                unrealized_pnl=portfolio_values['unrealized_pnl'],
                realized_pnl=portfolio_values['realized_pnl'],
                
                # مقاييس العائد
                daily_return=return_metrics['daily_return'],
                total_return=return_metrics['total_return'],
                total_return_percentage=return_metrics['total_return_percentage'],
                annualized_return=return_metrics['annualized_return'],
                
                # مقاييس المخاطر
                sharpe_ratio=risk_metrics['sharpe_ratio'],
                max_drawdown=risk_metrics['max_drawdown'],
                current_drawdown=risk_metrics['current_drawdown'],
                volatility=risk_metrics['volatility'],
                var_95=risk_metrics['var_95'],
                expected_shortfall=risk_metrics['expected_shortfall'],
                
                # إحصائيات التداول
                total_trades=trading_stats['total_trades'],
                winning_trades=trading_stats['winning_trades'],
                losing_trades=trading_stats['losing_trades'],
                win_rate=trading_stats['win_rate'],
                profit_factor=trading_stats['profit_factor'],
                average_win=trading_stats['average_win'],
                average_loss=trading_stats['average_loss'],
                
                # إحصائيات الإشارات
                signals_processed_today=signal_stats['processed_today'],
                signals_executed_today=signal_stats['executed_today'],
                signals_rejected_today=signal_stats['rejected_today'],
                signal_acceptance_rate=signal_stats['acceptance_rate'],
                
                # توزيع المحفظة
                active_positions_count=portfolio_distribution['active_positions'],
                sector_distribution=portfolio_distribution['sector_distribution']
            )
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            # إرجاع قيم افتراضية في حالة الخطأ
            return PortfolioPerformanceMetrics(
                total_portfolio_value=self.initial_capital,
                cash_balance=self.initial_capital,
                invested_value=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                daily_return=0.0,
                total_return=0.0,
                total_return_percentage=0.0,
                annualized_return=0.0,
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                current_drawdown=0.0,
                volatility=0.0,
                var_95=0.0,
                expected_shortfall=0.0,
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0.0,
                profit_factor=0.0,
                average_win=0.0,
                average_loss=0.0,
                signals_processed_today=0,
                signals_executed_today=0,
                signals_rejected_today=0,
                signal_acceptance_rate=0.0,
                active_positions_count=0,
                sector_distribution={}
            )
    
    async def calculate_risk_metrics(self) -> RiskMetrics:
        """حساب مقاييس المخاطر المتقدمة"""
        try:
            # الحصول على عوائد المحفظة اليومية
            daily_returns = await self._get_daily_returns()
            
            if len(daily_returns) < 30:  # نحتاج على الأقل 30 نقطة بيانات
                return self._get_default_risk_metrics()
            
            returns_array = np.array(daily_returns)
            
            # Value at Risk حسابات
            var_1d_95 = np.percentile(returns_array, 5)
            var_1d_99 = np.percentile(returns_array, 1)
            var_1w_95 = var_1d_95 * np.sqrt(5)  # VaR أسبوعي
            
            # Expected Shortfall
            es_1d_95 = returns_array[returns_array <= var_1d_95].mean()
            es_1d_99 = returns_array[returns_array <= var_1d_99].mean()
            
            # Maximum Drawdown
            cumulative_returns = (1 + pd.Series(returns_array)).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            current_drawdown = drawdown.iloc[-1]
            
            # Volatility
            volatility_annualized = np.std(returns_array) * np.sqrt(self.trading_days_per_year)
            
            # Downside Deviation
            negative_returns = returns_array[returns_array < 0]
            downside_deviation = np.std(negative_returns) * np.sqrt(self.trading_days_per_year)
            
            # Sortino Ratio
            excess_return = np.mean(returns_array) * self.trading_days_per_year - self.risk_free_rate
            sortino_ratio = excess_return / downside_deviation if downside_deviation > 0 else 0
            
            # مخاطر التركيز
            concentration_risk = await self._calculate_concentration_risk()
            
            # مخاطر القطاعات
            sector_risk = await self._calculate_sector_risk()
            
            # مخاطر الارتباط
            correlation_risk = await self._calculate_correlation_risk()
            
            return RiskMetrics(
                var_1d_95=abs(var_1d_95) * 100,
                var_1d_99=abs(var_1d_99) * 100,
                var_1w_95=abs(var_1w_95) * 100,
                es_1d_95=abs(es_1d_95) * 100,
                es_1d_99=abs(es_1d_99) * 100,
                maximum_drawdown=abs(max_drawdown) * 100,
                current_drawdown=abs(current_drawdown) * 100,
                volatility_annualized=volatility_annualized * 100,
                downside_deviation=downside_deviation * 100,
                sortino_ratio=sortino_ratio,
                concentration_risk=concentration_risk,
                sector_risk=sector_risk,
                correlation_risk=correlation_risk
            )
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return self._get_default_risk_metrics()
    
    async def compare_with_benchmark(self, timeframe: str, benchmark: str = "EGX30") -> PerformanceComparison:
        """مقارنة أداء المحفظة بالمؤشر"""
        try:
            end_date = datetime.now()
            start_date = self._get_start_date(timeframe, end_date)
            
            # الحصول على أداء المحفظة
            portfolio_performance = await self._get_portfolio_performance_history(start_date, end_date)
            
            # الحصول على أداء المؤشر (سيتم ربطه بمصدر البيانات لاحقاً)
            benchmark_performance = await self._get_benchmark_performance(benchmark, start_date, end_date)
            
            # حساب الإحصائيات
            portfolio_returns = np.array([p['return'] for p in portfolio_performance])
            benchmark_returns = np.array([b['return'] for b in benchmark_performance])
            
            # العوائد والتقلبات
            portfolio_total_return = (1 + portfolio_returns).prod() - 1
            benchmark_total_return = (1 + benchmark_returns).prod() - 1
            
            portfolio_volatility = np.std(portfolio_returns) * np.sqrt(self.trading_days_per_year)
            benchmark_volatility = np.std(benchmark_returns) * np.sqrt(self.trading_days_per_year)
            
            # نسب شارب
            portfolio_sharpe = (np.mean(portfolio_returns) * self.trading_days_per_year - self.risk_free_rate) / portfolio_volatility
            benchmark_sharpe = (np.mean(benchmark_returns) * self.trading_days_per_year - self.risk_free_rate) / benchmark_volatility
            
            # Alpha و Beta
            covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
            beta = covariance / np.var(benchmark_returns)
            alpha = np.mean(portfolio_returns) - beta * np.mean(benchmark_returns)
            
            # الارتباط
            correlation = np.corrcoef(portfolio_returns, benchmark_returns)[0, 1]
            
            # تاريخ الأداء للرسم البياني
            performance_history = []
            portfolio_cum_return = 1.0
            benchmark_cum_return = 1.0
            
            for i, (p_ret, b_ret) in enumerate(zip(portfolio_returns, benchmark_returns)):
                portfolio_cum_return *= (1 + p_ret)
                benchmark_cum_return *= (1 + b_ret)
                
                performance_history.append({
                    'date': (start_date + timedelta(days=i)).isoformat(),
                    'portfolio_value': portfolio_cum_return,
                    'benchmark_value': benchmark_cum_return,
                    'portfolio_return': (portfolio_cum_return - 1) * 100,
                    'benchmark_return': (benchmark_cum_return - 1) * 100
                })
            
            return PerformanceComparison(
                timeframe=timeframe,
                smart_portfolio_return=portfolio_total_return * 100,
                smart_portfolio_volatility=portfolio_volatility * 100,
                smart_portfolio_sharpe=portfolio_sharpe,
                benchmark_return=benchmark_total_return * 100,
                benchmark_volatility=benchmark_volatility * 100,
                benchmark_sharpe=benchmark_sharpe,
                alpha=alpha * 100,
                beta=beta,
                correlation=correlation,
                performance_history=performance_history
            )
            
        except Exception as e:
            logger.error(f"Error comparing with benchmark: {e}")
            return PerformanceComparison(
                timeframe=timeframe,
                smart_portfolio_return=0.0,
                smart_portfolio_volatility=0.0,
                smart_portfolio_sharpe=0.0,
                benchmark_return=0.0,
                benchmark_volatility=0.0,
                benchmark_sharpe=0.0,
                alpha=0.0,
                beta=1.0,
                correlation=0.0,
                performance_history=[]
            )
    
    async def get_detailed_analysis(self, timeframe: str) -> Dict:
        """الحصول على تحليل مفصل للمحفظة"""
        try:
            end_date = datetime.now()
            start_date = self._get_start_date(timeframe, end_date)
            
            # تحليل الأداء حسب الفترات الزمنية
            period_analysis = await self._analyze_performance_by_periods(start_date, end_date)
            
            # تحليل الأداء حسب الأسهم
            stock_analysis = await self._analyze_performance_by_stocks(start_date, end_date)
            
            # تحليل أنماط التداول
            trading_patterns = await self._analyze_trading_patterns(start_date, end_date)
            
            # تحليل أوقات الدخول والخروج
            timing_analysis = await self._analyze_entry_exit_timing(start_date, end_date)
            
            # تحليل فعالية الإشارات
            signal_effectiveness = await self._analyze_signal_effectiveness(start_date, end_date)
            
            return {
                'timeframe': timeframe,
                'analysis_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days
                },
                'period_analysis': period_analysis,
                'stock_analysis': stock_analysis,
                'trading_patterns': trading_patterns,
                'timing_analysis': timing_analysis,
                'signal_effectiveness': signal_effectiveness,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting detailed analysis: {e}")
            return {'error': str(e)}
    
    async def get_trading_statistics(self) -> Dict:
        """الحصول على إحصائيات التداول"""
        try:
            # إحصائيات المراكز
            total_positions = self.db.query(SmartPortfolioPosition).count()
            active_positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.status == 'ACTIVE').count()
            closed_positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.status == 'CLOSED').count()
            
            # إحصائيات المعاملات
            total_transactions = self.db.query(SmartPortfolioTransaction).count()
            buy_transactions = self.db.query(SmartPortfolioTransaction)\
                .filter(SmartPortfolioTransaction.transaction_type == 'BUY').count()
            sell_transactions = self.db.query(SmartPortfolioTransaction)\
                .filter(SmartPortfolioTransaction.transaction_type.like('SELL%')).count()
            
            # إحصائيات الربحية
            profitable_positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.realized_pnl > 0).count()
            losing_positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.realized_pnl < 0).count()
            
            win_rate = (profitable_positions / closed_positions * 100) if closed_positions > 0 else 0
            
            # متوسط مدة الاحتفاظ
            avg_holding_period = await self._calculate_average_holding_period()
            
            # أكبر ربح وخسارة
            max_profit = self.db.query(func.max(SmartPortfolioPosition.realized_pnl)).scalar() or 0
            max_loss = self.db.query(func.min(SmartPortfolioPosition.realized_pnl)).scalar() or 0
            
            return {
                'positions': {
                    'total': total_positions,
                    'active': active_positions,
                    'closed': closed_positions,
                    'profitable': profitable_positions,
                    'losing': losing_positions,
                    'win_rate': win_rate
                },
                'transactions': {
                    'total': total_transactions,
                    'buy_orders': buy_transactions,
                    'sell_orders': sell_transactions
                },
                'performance': {
                    'max_profit': max_profit,
                    'max_loss': max_loss,
                    'avg_holding_period_days': avg_holding_period
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting trading statistics: {e}")
            return {}
    
    async def get_optimization_suggestions(self) -> Dict:
        """الحصول على اقتراحات تحسين المحفظة"""
        try:
            suggestions = []
            optimization_score = 100
            
            # تحليل توزيع المخاطر
            risk_analysis = await self._analyze_risk_distribution()
            if risk_analysis['concentration_risk'] > 0.3:
                suggestions.append("تقليل التركيز في الأسهم الفردية - الحد الأقصى 30% لكل سهم")
                optimization_score -= 10
            
            # تحليل نسبة شارب
            current_metrics = await self.get_performance_metrics()
            if current_metrics.sharpe_ratio < 1.0:
                suggestions.append("تحسين نسبة المخاطرة/العائد - استهداف نسبة شارب أعلى من 1.0")
                optimization_score -= 15
            
            # تحليل معدل الربح
            if current_metrics.win_rate < 60:
                suggestions.append("تحسين معايير قبول الإشارات لزيادة معدل الربح")
                optimization_score -= 20
            
            # تحليل الانسحاب الأقصى
            if current_metrics.max_drawdown > 8:
                suggestions.append("تشديد إدارة المخاطر لتقليل الانسحاب الأقصى")
                optimization_score -= 25
            
            # اقتراحات إعادة التوازن
            rebalancing_suggestions = await self._get_rebalancing_suggestions()
            
            # اقتراحات تعديل المعايير
            parameter_suggestions = await self._get_parameter_optimization_suggestions()
            
            return {
                'optimization_score': max(0, optimization_score),
                'suggestions': suggestions,
                'rebalancing_needed': len(rebalancing_suggestions) > 0,
                'suggested_changes': rebalancing_suggestions,
                'risk_adjustments': await self._get_risk_adjustment_suggestions(),
                'parameter_optimizations': parameter_suggestions
            }
            
        except Exception as e:
            logger.error(f"Error getting optimization suggestions: {e}")
            return {
                'optimization_score': 50,
                'suggestions': ['خطأ في تحليل التحسينات'],
                'rebalancing_needed': False,
                'suggested_changes': [],
                'risk_adjustments': [],
                'parameter_optimizations': {}
            }
    
    # Helper Methods
    def _get_start_date(self, timeframe: str, end_date: datetime) -> datetime:
        """حساب تاريخ البداية حسب الإطار الزمني"""
        timeframe_map = {
            '1D': timedelta(days=1),
            '1W': timedelta(weeks=1),
            '1M': timedelta(days=30),
            '3M': timedelta(days=90),
            '6M': timedelta(days=180),
            '1Y': timedelta(days=365)
        }
        return end_date - timeframe_map.get(timeframe, timedelta(days=30))
    
    async def _get_positions_in_period(self, start_date: datetime, end_date: datetime) -> List:
        """الحصول على المراكز في الفترة المحددة"""
        return self.db.query(SmartPortfolioPosition)\
            .filter(and_(
                SmartPortfolioPosition.created_at >= start_date,
                SmartPortfolioPosition.created_at <= end_date
            )).all()
    
    async def _get_transactions_in_period(self, start_date: datetime, end_date: datetime) -> List:
        """الحصول على المعاملات في الفترة المحددة"""
        return self.db.query(SmartPortfolioTransaction)\
            .filter(and_(
                SmartPortfolioTransaction.timestamp >= start_date,
                SmartPortfolioTransaction.timestamp <= end_date
            )).all()
    
    async def _calculate_portfolio_values(self, positions: List) -> Dict:
        """حساب قيم المحفظة"""
        total_investment = sum(pos.initial_shares * pos.entry_price for pos in positions if pos.status == 'ACTIVE')
        total_realized_pnl = sum(pos.realized_pnl for pos in positions)
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in positions if pos.status == 'ACTIVE')
        
        cash_balance = self.initial_capital - total_investment + total_realized_pnl
        total_value = cash_balance + total_investment + total_unrealized_pnl
        
        return {
            'total_value': total_value,
            'cash_balance': cash_balance,
            'invested_value': total_investment,
            'realized_pnl': total_realized_pnl,
            'unrealized_pnl': total_unrealized_pnl
        }
    
    async def _calculate_return_metrics(self, transactions: List, start_date: datetime, end_date: datetime) -> Dict:
        """حساب مقاييس العائد"""
        if not transactions:
            return {
                'daily_return': 0.0,
                'total_return': 0.0,
                'total_return_percentage': 0.0,
                'annualized_return': 0.0
            }
        
        total_pnl = sum(t.pnl for t in transactions)
        total_return_percentage = (total_pnl / self.initial_capital) * 100
        
        days = (end_date - start_date).days
        daily_return = total_return_percentage / days if days > 0 else 0
        
        # العائد المركب السنوي
        if days > 0:
            annualized_return = ((1 + total_return_percentage/100) ** (365/days) - 1) * 100
        else:
            annualized_return = 0
        
        return {
            'daily_return': daily_return,
            'total_return': total_pnl,
            'total_return_percentage': total_return_percentage,
            'annualized_return': annualized_return
        }
    
    async def _calculate_risk_metrics(self, transactions: List, positions: List) -> Dict:
        """حساب مقاييس المخاطر الأساسية"""
        if not transactions:
            return {
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'current_drawdown': 0.0,
                'volatility': 0.0,
                'var_95': 0.0,
                'expected_shortfall': 0.0
            }
        
        # حساب العوائد اليومية
        daily_returns = [t.pnl_percentage for t in transactions if t.pnl_percentage is not None]
        
        if not daily_returns:
            return self._get_default_risk_metrics()
        
        returns_array = np.array(daily_returns) / 100  # تحويل إلى كسور عشرية
        
        # التقلبات
        volatility = np.std(returns_array) * np.sqrt(self.trading_days_per_year)
        
        # نسبة شارب
        mean_return = np.mean(returns_array) * self.trading_days_per_year
        excess_return = mean_return - self.risk_free_rate
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0
        
        # VaR و ES
        var_95 = np.percentile(returns_array, 5) if len(returns_array) > 20 else 0
        expected_shortfall = returns_array[returns_array <= var_95].mean() if len(returns_array) > 20 else 0
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': 0.0,  # سيتم حسابها من البيانات التاريخية
            'current_drawdown': 0.0,
            'volatility': volatility,
            'var_95': abs(var_95),
            'expected_shortfall': abs(expected_shortfall)
        }
    
    def _get_default_risk_metrics(self) -> RiskMetrics:
        """الحصول على مقاييس مخاطر افتراضية"""
        return RiskMetrics(
            var_1d_95=0.0,
            var_1d_99=0.0,
            var_1w_95=0.0,
            es_1d_95=0.0,
            es_1d_99=0.0,
            maximum_drawdown=0.0,
            current_drawdown=0.0,
            volatility_annualized=0.0,
            downside_deviation=0.0,
            sortino_ratio=0.0,
            concentration_risk=0.0,
            sector_risk={},
            correlation_risk=0.0
        )
    
    async def _get_daily_returns(self) -> List[float]:
        """الحصول على العوائد اليومية للمحفظة"""
        # سيتم تنفيذها بالتفصيل لاحقاً
        return []
    
    async def _calculate_concentration_risk(self) -> float:
        """حساب مخاطر التركيز"""
        # سيتم تنفيذها بالتفصيل لاحقاً
        return 0.0
    
    async def _calculate_sector_risk(self) -> Dict[str, float]:
        """حساب مخاطر القطاعات"""
        # سيتم تنفيذها بالتفصيل لاحقاً
        return {}
    
    async def _calculate_correlation_risk(self) -> float:
        """حساب مخاطر الارتباط"""
        # سيتم تنفيذها بالتفصيل لاحقاً
        return 0.0
    
    # المزيد من المساعدات سيتم إضافتها حسب الحاجة
    async def _calculate_trading_statistics(self, transactions: List) -> Dict:
        """حساب إحصائيات التداول"""
        if not transactions:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'average_win': 0.0,
                'average_loss': 0.0
            }
        
        winning_trades = [t for t in transactions if t.pnl > 0]
        losing_trades = [t for t in transactions if t.pnl < 0]
        
        total_trades = len(transactions)
        win_count = len(winning_trades)
        loss_count = len(losing_trades)
        
        win_rate = (win_count / total_trades * 100) if total_trades > 0 else 0
        
        total_profits = sum(t.pnl for t in winning_trades)
        total_losses = abs(sum(t.pnl for t in losing_trades))
        
        profit_factor = total_profits / total_losses if total_losses > 0 else 0
        
        average_win = total_profits / win_count if win_count > 0 else 0
        average_loss = total_losses / loss_count if loss_count > 0 else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': win_count,
            'losing_trades': loss_count,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'average_win': average_win,
            'average_loss': average_loss
        }
    
    async def _calculate_signal_statistics(self, start_date: datetime, end_date: datetime) -> Dict:
        """حساب إحصائيات الإشارات"""
        try:
            from ..models.smart_portfolio import SmartPortfolioSignalProcessing
            
            today = datetime.now().date()
            
            processed_today = self.db.query(SmartPortfolioSignalProcessing)\
                .filter(func.date(SmartPortfolioSignalProcessing.processed_at) == today)\
                .count()
            
            executed_today = self.db.query(SmartPortfolioSignalProcessing)\
                .filter(and_(
                    func.date(SmartPortfolioSignalProcessing.processed_at) == today,
                    SmartPortfolioSignalProcessing.processing_result == 'EXECUTED'
                )).count()
            
            rejected_today = self.db.query(SmartPortfolioSignalProcessing)\
                .filter(and_(
                    func.date(SmartPortfolioSignalProcessing.processed_at) == today,
                    SmartPortfolioSignalProcessing.processing_result == 'REJECTED'
                )).count()
            
            acceptance_rate = (executed_today / processed_today * 100) if processed_today > 0 else 0
            
            return {
                'processed_today': processed_today,
                'executed_today': executed_today,
                'rejected_today': rejected_today,
                'acceptance_rate': acceptance_rate
            }
            
        except Exception as e:
            logger.error(f"Error calculating signal statistics: {e}")
            return {
                'processed_today': 0,
                'executed_today': 0,
                'rejected_today': 0,
                'acceptance_rate': 0.0
            }
    
    async def _calculate_portfolio_distribution(self, positions: List) -> Dict:
        """حساب توزيع المحفظة"""
        active_positions = [p for p in positions if p.status == 'ACTIVE']
        
        # توزيع القطاعات (سيتم تطويره مع ربط بيانات الأسهم)
        sector_distribution = {
            'BANKING': 30.0,
            'TELECOM': 25.0,
            'REAL_ESTATE': 20.0,
            'INDUSTRIAL': 15.0,
            'OTHER': 10.0
        }
        
        return {
            'active_positions': len(active_positions),
            'sector_distribution': sector_distribution
        }
    
    async def _get_portfolio_performance_history(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """الحصول على تاريخ أداء المحفظة"""
        # سيتم تطويرها لاحقاً مع ربط البيانات التاريخية
        return []
    
    async def _get_benchmark_performance(self, benchmark: str, start_date: datetime, end_date: datetime) -> List[Dict]:
        """الحصول على أداء المؤشر المرجعي"""
        # سيتم تطويرها لاحقاً مع ربط بيانات المؤشر
        return []
    
    async def _calculate_average_holding_period(self) -> float:
        """حساب متوسط فترة الاحتفاظ بالأسهم"""
        closed_positions = self.db.query(SmartPortfolioPosition)\
            .filter(SmartPortfolioPosition.status == 'CLOSED')\
            .all()
        
        if not closed_positions:
            return 0.0
        
        total_days = 0
        for position in closed_positions:
            if position.closed_at:
                days = (position.closed_at - position.created_at).days
                total_days += days
        
        return total_days / len(closed_positions)
      # المزيد من المساعدات للتحليل المتقدم
    async def _analyze_risk_distribution(self) -> Dict:
        """تحليل توزيع المخاطر المتقدم"""
        try:
            active_positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.status == 'ACTIVE').all()
            
            if not active_positions:
                return {'concentration_risk': 0.0, 'position_sizes': {}, 'risk_breakdown': {}}
            
            # حساب القيمة الإجمالية للمحفظة
            total_portfolio_value = sum(pos.remaining_shares * pos.entry_price for pos in active_positions)
            
            # تحليل التركيز لكل سهم
            position_concentrations = {}
            for pos in active_positions:
                position_value = pos.remaining_shares * pos.entry_price
                concentration = position_value / total_portfolio_value
                position_concentrations[pos.stock_code] = {
                    'concentration': concentration * 100,
                    'value': position_value,
                    'risk_level': 'HIGH' if concentration > 0.15 else 'MEDIUM' if concentration > 0.08 else 'LOW'
                }
            
            # حساب مؤشر هيرفيندال-هيرشمان للتركيز
            hhi = sum(conc['concentration']**2 for conc in position_concentrations.values())
            
            # تصنيف مستوى التركيز
            if hhi > 2500:
                concentration_level = 'HIGH'
            elif hhi > 1500:
                concentration_level = 'MEDIUM'  
            else:
                concentration_level = 'LOW'
            
            return {
                'concentration_risk': hhi / 10000,  # تحويل إلى نسبة من 0 إلى 1
                'concentration_level': concentration_level,
                'position_sizes': position_concentrations,
                'herfindahl_index': hhi,
                'max_position_size': max(conc['concentration'] for conc in position_concentrations.values()) if position_concentrations else 0,
                'positions_over_10_percent': len([p for p in position_concentrations.values() if p['concentration'] > 10])
            }
            
        except Exception as e:
            logger.error(f"Error analyzing risk distribution: {e}")
            return {'concentration_risk': 0.0, 'error': str(e)}
    
    async def _get_rebalancing_suggestions(self) -> List[Dict]:
        """الحصول على اقتراحات إعادة التوازن المتقدمة"""
        try:
            suggestions = []
            risk_analysis = await self._analyze_risk_distribution()
            
            # فحص التركيز المفرط
            if risk_analysis.get('concentration_risk', 0) > 0.25:
                over_concentrated = [
                    stock for stock, data in risk_analysis.get('position_sizes', {}).items()
                    if data['concentration'] > 15
                ]
                
                for stock in over_concentrated:
                    suggestions.append({
                        'type': 'REDUCE_POSITION',
                        'stock_code': stock,
                        'current_weight': risk_analysis['position_sizes'][stock]['concentration'],
                        'suggested_weight': 12.0,
                        'reason': 'تقليل التركيز المفرط - يُنصح بعدم تجاوز 12% لكل سهم',
                        'priority': 'HIGH',
                        'action': f'تقليل مركز {stock} من {risk_analysis["position_sizes"][stock]["concentration"]:.1f}% إلى 12%'
                    })
            
            # فحص التوزيع القطاعي
            sector_analysis = await self._analyze_sector_distribution()
            for sector, weight in sector_analysis.items():
                if weight > 40:  # إذا تجاوز قطاع 40% من المحفظة
                    suggestions.append({
                        'type': 'SECTOR_REBALANCE',
                        'sector': sector,
                        'current_weight': weight,
                        'suggested_weight': 35.0,
                        'reason': 'تقليل التركيز القطاعي',
                        'priority': 'MEDIUM',
                        'action': f'تقليل الاستثمار في قطاع {sector} من {weight:.1f}% إلى 35%'
                    })
            
            # فحص الأسهم منخفضة الأداء
            underperforming = await self._identify_underperforming_positions()
            for stock in underperforming:
                suggestions.append({
                    'type': 'REVIEW_POSITION',
                    'stock_code': stock['stock_code'],
                    'performance': stock['performance'],
                    'reason': 'مراجعة الأسهم ضعيفة الأداء',
                    'priority': 'MEDIUM',
                    'action': f'مراجعة مركز {stock["stock_code"]} - أداء {stock["performance"]:.1f}%'
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting rebalancing suggestions: {e}")
            return []
    
    async def _get_parameter_optimization_suggestions(self) -> Dict:
        """الحصول على اقتراحات تحسين المعايير المتقدمة"""
        try:
            current_performance = await self.get_performance_metrics()
            
            suggestions = {
                'risk_management': [],
                'position_sizing': [],
                'signal_filtering': [],
                'profit_taking': []
            }
            
            # تحليل إدارة المخاطر
            if current_performance.max_drawdown > 8:
                suggestions['risk_management'].extend([
                    {
                        'parameter': 'max_position_risk',
                        'current': '2%',
                        'suggested': '1.5%',
                        'reason': 'تقليل المخاطرة لكل صفقة لتقليل الانسحاب الأقصى',
                        'expected_impact': 'تقليل الانسحاب الأقصى بنسبة 20-25%'
                    },
                    {
                        'parameter': 'trailing_stop_factor',
                        'current': '2.0',
                        'suggested': '1.5',
                        'reason': 'تشديد الـ trailing stop للحفاظ على الأرباح',
                        'expected_impact': 'حماية أفضل للأرباح المحققة'
                    }
                ])
            
            # تحليل حجم المراكز
            if current_performance.win_rate < 55:
                suggestions['position_sizing'].extend([
                    {
                        'parameter': 'kelly_fraction_adjustment',
                        'current': 'Full Kelly',
                        'suggested': 'Half Kelly',
                        'reason': 'تقليل حجم المراكز عند انخفاض معدل الربح',
                        'expected_impact': 'تقليل التقلبات وتحسين الاستقرار'
                    }
                ])
            
            # تحليل فلترة الإشارات
            if current_performance.signal_acceptance_rate > 80:
                suggestions['signal_filtering'].extend([
                    {
                        'parameter': 'min_signal_confidence',
                        'current': '0.65',
                        'suggested': '0.75',
                        'reason': 'رفع معايير قبول الإشارات لتحسين الجودة',
                        'expected_impact': 'زيادة معدل الربح وتقليل عدد الصفقات الخاسرة'
                    },
                    {
                        'parameter': 'volume_filter',
                        'current': 'Disabled',
                        'suggested': 'Min 1M EGP daily volume',
                        'reason': 'إضافة فلتر حجم التداول لضمان السيولة',
                        'expected_impact': 'تحسين جودة التنفيذ وتقليل الانزلاق'
                    }
                ])
            
            # تحليل جني الأرباح
            avg_profit = current_performance.average_win
            avg_loss = abs(current_performance.average_loss)
            if avg_profit / avg_loss < 1.5:  # نسبة ربح/خسارة منخفضة
                suggestions['profit_taking'].extend([
                    {
                        'parameter': 'profit_targets',
                        'current': '3%, 6%, 10%',
                        'suggested': '2%, 4%, 8%',
                        'reason': 'تقليل أهداف الربح لتحسين معدل التحقق',
                        'expected_impact': 'زيادة معدل تحقق الأهداف'
                    }
                ])
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting parameter optimization suggestions: {e}")
            return {}
    
    async def _get_risk_adjustment_suggestions(self) -> List[str]:
        """الحصول على اقتراحات تعديل المخاطر المتقدمة"""
        try:
            suggestions = []
            current_metrics = await self.get_performance_metrics()
            risk_metrics = await self.calculate_risk_metrics()
            
            # تحليل نسبة شارب
            if current_metrics.sharpe_ratio < 1.0:
                suggestions.append("تحسين نسبة المخاطرة/العائد - تقليل التقلبات أو زيادة العوائد")
            
            # تحليل الانسحاب الأقصى
            if current_metrics.max_drawdown > 10:
                suggestions.append("تشديد إدارة المخاطر - الانسحاب الأقصى مرتفع جداً")
                suggestions.append("تطبيق stop loss أكثر صرامة")
                suggestions.append("تقليل حجم المراكز في الأسواق المتقلبة")
            
            # تحليل VaR
            if risk_metrics.var_1d_95 > 3:
                suggestions.append("مخاطر VaR مرتفعة - تقليل التعرض للمخاطر")
                suggestions.append("تنويع أكبر في المحفظة")
            
            # تحليل التركيز
            risk_dist = await self._analyze_risk_distribution()
            if risk_dist.get('concentration_risk', 0) > 0.3:
                suggestions.append("تقليل التركيز في الأسهم الفردية")
                suggestions.append("توزيع الاستثمارات على عدد أكبر من الأسهم")
            
            # تحليل الارتباط
            if risk_metrics.correlation_risk > 0.7:
                suggestions.append("تقليل الارتباط بين الأسهم - الاستثمار في قطاعات مختلفة")
            
            # تحليل السيولة
            liquidity_risk = await self._assess_liquidity_risk()
            if liquidity_risk > 0.2:
                suggestions.append("تحسين السيولة - التركيز على الأسهم عالية التداول")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting risk adjustment suggestions: {e}")
            return ["خطأ في تحليل المخاطر"]
    
    # دوال مساعدة إضافية للتحليل المتقدم
    async def _analyze_sector_distribution(self) -> Dict[str, float]:
        """تحليل التوزيع القطاعي للمحفظة"""
        try:
            # ستحتاج لربط بيانات القطاعات من جدول الأسهم
            # هذا مثال مؤقت
            return {
                'BANKING': 35.0,
                'TELECOM': 25.0, 
                'REAL_ESTATE': 20.0,
                'INDUSTRIAL': 15.0,
                'OTHER': 5.0
            }
        except Exception as e:
            logger.error(f"Error analyzing sector distribution: {e}")
            return {}
    
    async def _identify_underperforming_positions(self) -> List[Dict]:
        """تحديد الأسهم ضعيفة الأداء"""
        try:
            positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.status == 'ACTIVE').all()
            
            underperforming = []
            for pos in positions:
                # حساب الأداء الحالي
                performance = (pos.unrealized_pnl / (pos.initial_shares * pos.entry_price)) * 100
                
                # إذا كانت الخسارة أكثر من 8% أو المركز راكد لأكثر من 30 يوم
                days_held = (datetime.now() - pos.created_at).days
                if performance < -8 or (days_held > 30 and abs(performance) < 2):
                    underperforming.append({
                        'stock_code': pos.stock_code,
                        'performance': performance,
                        'days_held': days_held,
                        'reason': 'خسارة كبيرة' if performance < -8 else 'أداء راكد'
                    })
            
            return underperforming
            
        except Exception as e:
            logger.error(f"Error identifying underperforming positions: {e}")
            return []
    
    async def _assess_liquidity_risk(self) -> float:
        """تقييم مخاطر السيولة"""
        try:
            # سيتم ربطها ببيانات حجم التداول الفعلية
            # هذا مثال مؤقت
            return 0.15  # 15% مخاطر سيولة
        except Exception as e:
            logger.error(f"Error assessing liquidity risk: {e}")
            return 0.0
    
    # تحليلات متقدمة إضافية
    async def _analyze_performance_by_periods(self, start_date: datetime, end_date: datetime) -> Dict:
        """تحليل الأداء حسب الفترات الزمنية"""
        try:
            periods = {
                'daily': [],
                'weekly': [], 
                'monthly': []
            }
            
            # تحليل يومي
            current_date = start_date
            while current_date <= end_date:
                next_date = current_date + timedelta(days=1)
                daily_transactions = await self._get_transactions_in_period(current_date, next_date)
                daily_pnl = sum(t.pnl for t in daily_transactions)
                
                periods['daily'].append({
                    'date': current_date.isoformat(),
                    'pnl': daily_pnl,
                    'trades_count': len(daily_transactions)
                })
                
                current_date = next_date
            
            # حساب الإحصائيات
            daily_pnls = [p['pnl'] for p in periods['daily']]
            
            return {
                'daily_analysis': {
                    'data': periods['daily'][-30:],  # آخر 30 يوم
                    'avg_daily_pnl': np.mean(daily_pnls) if daily_pnls else 0,
                    'best_day': max(daily_pnls) if daily_pnls else 0,
                    'worst_day': min(daily_pnls) if daily_pnls else 0,
                    'positive_days': len([p for p in daily_pnls if p > 0]),
                    'negative_days': len([p for p in daily_pnls if p < 0])
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing performance by periods: {e}")
            return {}
    
    async def _analyze_performance_by_stocks(self, start_date: datetime, end_date: datetime) -> Dict:
        """تحليل الأداء حسب الأسهم"""
        try:
            positions = await self._get_positions_in_period(start_date, end_date)
            
            stock_performance = {}
            for pos in positions:
                if pos.stock_code not in stock_performance:
                    stock_performance[pos.stock_code] = {
                        'total_pnl': 0,
                        'trades_count': 0,
                        'win_rate': 0,
                        'avg_holding_days': 0
                    }
                
                stock_performance[pos.stock_code]['total_pnl'] += pos.realized_pnl or 0
                stock_performance[pos.stock_code]['trades_count'] += 1
                
                if pos.closed_at:
                    holding_days = (pos.closed_at - pos.created_at).days
                    stock_performance[pos.stock_code]['avg_holding_days'] += holding_days
            
            # حساب المتوسطات
            for stock, data in stock_performance.items():
                if data['trades_count'] > 0:
                    data['avg_holding_days'] /= data['trades_count']
                    data['avg_pnl_per_trade'] = data['total_pnl'] / data['trades_count']
            
            # ترتيب حسب الأداء
            sorted_stocks = sorted(
                stock_performance.items(),
                key=lambda x: x[1]['total_pnl'],
                reverse=True
            )
            
            return {
                'best_performers': sorted_stocks[:5],
                'worst_performers': sorted_stocks[-5:],
                'all_stocks': dict(sorted_stocks)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing performance by stocks: {e}")
            return {}
    
    async def _analyze_trading_patterns(self, start_date: datetime, end_date: datetime) -> Dict:
        """تحليل أنماط التداول"""
        try:
            transactions = await self._get_transactions_in_period(start_date, end_date)
            
            # تحليل حسب أيام الأسبوع
            weekday_performance = {i: {'pnl': 0, 'count': 0} for i in range(7)}
            
            # تحليل حسب ساعات اليوم
            hour_performance = {i: {'pnl': 0, 'count': 0} for i in range(24)}
            
            for transaction in transactions:
                weekday = transaction.timestamp.weekday()
                hour = transaction.timestamp.hour
                
                weekday_performance[weekday]['pnl'] += transaction.pnl
                weekday_performance[weekday]['count'] += 1
                
                hour_performance[hour]['pnl'] += transaction.pnl
                hour_performance[hour]['count'] += 1
            
            # العثور على أفضل الأوقات
            best_weekday = max(weekday_performance.items(), key=lambda x: x[1]['pnl'])
            best_hour = max(hour_performance.items(), key=lambda x: x[1]['pnl'])
            
            return {
                'weekday_analysis': weekday_performance,
                'hour_analysis': hour_performance,
                'best_trading_day': {
                    'day': best_weekday[0],
                    'day_name': ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'][best_weekday[0]],
                    'avg_pnl': best_weekday[1]['pnl'] / max(best_weekday[1]['count'], 1)
                },
                'best_trading_hour': {
                    'hour': best_hour[0],
                    'avg_pnl': best_hour[1]['pnl'] / max(best_hour[1]['count'], 1)
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing trading patterns: {e}")
            return {}
    
    async def _analyze_entry_exit_timing(self, start_date: datetime, end_date: datetime) -> Dict:
        """تحليل توقيت الدخول والخروج"""
        try:
            positions = await self._get_positions_in_period(start_date, end_date)
            
            entry_analysis = {
                'quick_profits': 0,  # أرباح في أقل من 3 أيام
                'medium_profits': 0,  # أرباح في 3-10 أيام
                'long_profits': 0,   # أرباح في أكثر من 10 أيام
                'avg_profit_time': 0
            }
            
            profitable_positions = [p for p in positions if p.realized_pnl and p.realized_pnl > 0]
            
            if profitable_positions:
                profit_times = []
                for pos in profitable_positions:
                    if pos.closed_at:
                        days = (pos.closed_at - pos.created_at).days
                        profit_times.append(days)
                        
                        if days <= 3:
                            entry_analysis['quick_profits'] += 1
                        elif days <= 10:
                            entry_analysis['medium_profits'] += 1
                        else:
                            entry_analysis['long_profits'] += 1
                
                entry_analysis['avg_profit_time'] = np.mean(profit_times)
            
            return {
                'timing_analysis': entry_analysis,
                'optimal_holding_period': entry_analysis['avg_profit_time'],
                'timing_distribution': {
                    'quick': entry_analysis['quick_profits'],
                    'medium': entry_analysis['medium_profits'], 
                    'long': entry_analysis['long_profits']
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing entry/exit timing: {e}")
            return {}
    
    async def _analyze_signal_effectiveness(self, start_date: datetime, end_date: datetime) -> Dict:
        """تحليل فعالية الإشارات"""
        try:
            # ربط الإشارات بالمراكز لتحليل الأداء
            positions = await self._get_positions_in_period(start_date, end_date)
            
            signal_performance = {
                'high_confidence': {'count': 0, 'successful': 0, 'avg_pnl': 0},
                'medium_confidence': {'count': 0, 'successful': 0, 'avg_pnl': 0},
                'low_confidence': {'count': 0, 'successful': 0, 'avg_pnl': 0}
            }
            
            for pos in positions:
                if pos.signal_confidence:
                    confidence_level = 'high_confidence' if pos.signal_confidence > 0.8 else \
                                     'medium_confidence' if pos.signal_confidence > 0.6 else 'low_confidence'
                    
                    signal_performance[confidence_level]['count'] += 1
                    
                    if pos.realized_pnl and pos.realized_pnl > 0:
                        signal_performance[confidence_level]['successful'] += 1
                    
                    if pos.realized_pnl:
                        signal_performance[confidence_level]['avg_pnl'] += pos.realized_pnl
            
            # حساب المتوسطات
            for level, data in signal_performance.items():
                if data['count'] > 0:
                    data['success_rate'] = (data['successful'] / data['count']) * 100
                    data['avg_pnl'] /= data['count']
                else:
                    data['success_rate'] = 0
            
            return {
                'signal_performance': signal_performance,
                'overall_signal_accuracy': sum(d['successful'] for d in signal_performance.values()) / 
                                         max(sum(d['count'] for d in signal_performance.values()), 1) * 100
            }
            
        except Exception as e:
            logger.error(f"Error analyzing signal effectiveness: {e}")
            return {}
