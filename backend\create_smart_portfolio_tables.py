#!/usr/bin/env python3
"""
Smart Portfolio Database Tables Creator
إنشاء جداول المحفظة الذكية في قاعدة البيانات
"""

import sys
import os
sys.path.append('.')

from sqlalchemy import inspect, text
from app.database import engine
from app.models.smart_portfolio import (
    SmartPortfolioPosition, 
    SmartPortfolioTransaction, 
    SmartPortfolioMetrics,
    CopyTradingSubscription,
    CopiedTrade,
    SmartPortfolioSignalProcessing,
    PortfolioPerformanceSnapshot,
    Base
)

def check_and_create_tables():
    """فحص وإنشاء الجداول المطلوبة"""
    print("🔍 فحص جداول المحفظة الذكية...")
    print("=" * 50)
    
    # Get database inspector
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    
    # Define required tables
    required_tables = {
        'smart_portfolio_positions': SmartPortfolioPosition,
        'smart_portfolio_transactions': SmartPortfolioTransaction,
        'smart_portfolio_metrics': SmartPortfolioMetrics,
        'copy_trading_subscriptions': CopyTradingSubscription,
        'copied_trades': CopiedTrade,
        'smart_portfolio_signal_processing': SmartPortfolioSignalProcessing,
        'portfolio_performance_snapshots': PortfolioPerformanceSnapshot
    }
    
    # Check which tables are missing
    missing_tables = []
    for table_name in required_tables:
        if table_name in existing_tables:
            print(f"✅ {table_name} - موجود")
        else:
            print(f"❌ {table_name} - غير موجود")
            missing_tables.append(table_name)
    
    print(f"\n📊 الجداول الموجودة: {len(required_tables) - len(missing_tables)}/{len(required_tables)}")
    print(f"⚠️  الجداول المطلوب إنشاؤها: {len(missing_tables)}")
    
    if missing_tables:
        print(f"\n🚀 إنشاء الجداول المفقودة...")
        try:
            # Use the Base from smart_portfolio models
            Base.metadata.create_all(bind=engine)
            print("✅ تم إنشاء جميع جداول المحفظة الذكية بنجاح!")
            
            # Verify creation
            print("\n🔍 التحقق من إنشاء الجداول...")
            inspector = inspect(engine)
            new_tables = inspector.get_table_names()
            
            for table_name in missing_tables:
                if table_name in new_tables:
                    print(f"✅ {table_name} - تم إنشاؤه بنجاح")
                else:
                    print(f"❌ {table_name} - فشل في الإنشاء")
                    
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")
            return False
    else:
        print("✅ جميع الجداول موجودة بالفعل!")
    
    return True

def add_indexes_and_constraints():
    """إضافة الفهارس والقيود المطلوبة"""
    print("\n🔧 إضافة الفهارس والقيود المطلوبة...")
    
    indexes_sql = [
        # Smart Portfolio Positions indexes
        "CREATE INDEX IF NOT EXISTS idx_positions_stock_code ON smart_portfolio_positions(stock_code);",
        "CREATE INDEX IF NOT EXISTS idx_positions_status ON smart_portfolio_positions(status);",
        "CREATE INDEX IF NOT EXISTS idx_positions_entry_date ON smart_portfolio_positions(entry_date);",
          # Smart Portfolio Transactions indexes  
        "CREATE INDEX IF NOT EXISTS idx_transactions_position_id ON smart_portfolio_transactions(position_id);",
        "CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON smart_portfolio_transactions(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_transactions_type ON smart_portfolio_transactions(transaction_type);",
          # Copy Trading Subscriptions indexes
        "CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON copy_trading_subscriptions(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_subscriptions_is_active ON copy_trading_subscriptions(is_active);",
        
        # Performance snapshots indexes
        "CREATE INDEX IF NOT EXISTS idx_snapshots_date ON portfolio_performance_snapshots(snapshot_date);",
    ]
    
    try:
        with engine.connect() as conn:
            for sql in indexes_sql:
                conn.execute(text(sql))
                print(f"✅ تم تنفيذ: {sql.split('ON')[0]}...")
            conn.commit()
        print("✅ تم إنشاء جميع الفهارس بنجاح!")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفهارس: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إعداد قاعدة بيانات المحفظة الذكية")
    print("=" * 60)
    
    # Check and create tables
    if check_and_create_tables():
        # Add indexes and constraints
        if add_indexes_and_constraints():
            print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
            print("✅ جميع جداول المحفظة الذكية جاهزة للاستخدام")
        else:
            print("\n⚠️  تم إنشاء الجداول ولكن فشل في إنشاء بعض الفهارس")
    else:
        print("\n❌ فشل في إعداد قاعدة البيانات")
        sys.exit(1)
