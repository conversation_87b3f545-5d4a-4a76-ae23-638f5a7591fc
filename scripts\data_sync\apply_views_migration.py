#!/usr/bin/env python3
"""
تطبيق migration للـ Views المحسوبة - تطبيق مباشر على قاعدة البيانات
Apply migration for computed views - direct database application
"""

import os
import sys
from datetime import datetime
from supabase import create_client, Client

# إعداد البيئة
load_dotenv = lambda: None
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# إعداد الاتصال بقاعدة البيانات
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Missing Supabase configuration")
    print("Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("✅ Connected to Supabase successfully")
except Exception as e:
    print(f"❌ Error connecting to Supabase: {e}")
    sys.exit(1)

def read_migration_file():
    """قراءة ملف migration"""
    migration_path = "supabase/migrations/20250616_create_optimized_views.sql"
    
    try:
        with open(migration_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        print(f"❌ Migration file not found: {migration_path}")
        return None
    except Exception as e:
        print(f"❌ Error reading migration file: {e}")
        return None

def split_sql_statements(sql_content):
    """تقسيم محتوى SQL إلى استعلامات منفصلة"""
    # إزالة التعليقات والأسطر الفارغة
    lines = []
    for line in sql_content.split('\n'):
        line = line.strip()
        if line and not line.startswith('--'):
            lines.append(line)
    
    # دمج السطور وتقسيم حسب semicolon
    content = ' '.join(lines)
    statements = [stmt.strip() for stmt in content.split(';') if stmt.strip()]
    
    return statements

def execute_migration():
    """تنفيذ migration"""
    print("🚀 بدء تطبيق migration للـ Views المحسوبة...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # قراءة ملف migration
    sql_content = read_migration_file()
    if not sql_content:
        return False
    
    # تقسيم الاستعلامات
    statements = split_sql_statements(sql_content)
    print(f"📊 وُجد {len(statements)} استعلام SQL")
    
    success_count = 0
    
    for i, statement in enumerate(statements, 1):
        if not statement:
            continue
            
        print(f"\n🔧 تنفيذ الاستعلام {i}/{len(statements)}...")
        
        try:
            # استخدام rpc لتنفيذ SQL مباشرة (إذا كان متاحاً)
            # بدلاً من ذلك، سنحاول استخدام raw SQL
            response = supabase.rpc('exec_sql', {'sql_query': statement}).execute()
            
            if response.data is not None:
                print(f"✅ نجح الاستعلام {i}")
                success_count += 1
            else:
                print(f"⚠️  الاستعلام {i} لم يُرجع بيانات")
                success_count += 1
                
        except Exception as e:
            print(f"❌ خطأ في الاستعلام {i}: {e}")
            # محاولة طباعة بداية الاستعلام للتشخيص
            preview = statement[:100] + "..." if len(statement) > 100 else statement
            print(f"   الاستعلام: {preview}")
    
    print(f"\n🎯 تم تنفيذ {success_count}/{len(statements)} استعلام بنجاح")
    
    if success_count == len(statements):
        print("✅ تم تطبيق migration بنجاح!")
        return True
    else:
        print("❌ فشل في تطبيق migration كاملة")
        return False

def test_views():
    """اختبار الـ Views المُنشأة"""
    print("\n🧪 اختبار الـ Views المُنشأة...")
    
    views_to_test = [
        'market_summary_view',
        'technical_analysis_view', 
        'advanced_metrics_view',
        'top_performers_view',
        'sector_performance_view',
        'market_liquidity_view'
    ]
    
    working_views = []
    
    for view in views_to_test:
        try:
            response = supabase.table(view).select('*').limit(1).execute()
            if response.data is not None:
                print(f"✅ {view} - يعمل بشكل صحيح")
                working_views.append(view)
            else:
                print(f"⚠️  {view} - لا يُرجع بيانات")
        except Exception as e:
            print(f"❌ {view} - خطأ: {e}")
    
    print(f"\n📊 {len(working_views)}/{len(views_to_test)} views تعمل بشكل صحيح")
    return working_views

def create_exec_sql_function():
    """إنشاء دالة exec_sql إذا لم تكن موجودة"""
    print("🔧 محاولة إنشاء دالة exec_sql...")
    
    create_function_sql = """
    CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
    RETURNS text
    SECURITY DEFINER
    LANGUAGE plpgsql
    AS $$
    BEGIN
        EXECUTE sql_query;
        RETURN 'OK';
    EXCEPTION WHEN OTHERS THEN
        RETURN SQLSTATE || ': ' || SQLERRM;
    END;
    $$;
    """
    
    try:
        # محاولة إنشاء الدالة مباشرة (قد لا تعمل مع Supabase)
        response = supabase.rpc('exec_sql', {'sql_query': create_function_sql}).execute()
        print("✅ تم إنشاء دالة exec_sql")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء دالة exec_sql: {e}")
        print("💡 يجب إنشاء الـ Views من لوحة تحكم Supabase مباشرة")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تطبيق Migration للـ Views المحسوبة...")
    
    # محاولة إنشاء دالة exec_sql أولاً
    if not create_exec_sql_function():
        print("\n💡 يُنصح بتطبيق Migration من لوحة تحكم Supabase:")
        print("1. اذهب إلى https://supabase.com/dashboard")
        print("2. اختر مشروعك")
        print("3. اذهب إلى SQL Editor")
        print("4. انسخ محتوى ملف: supabase/migrations/20250616_create_optimized_views.sql")
        print("5. اضغط 'Run' لتنفيذ الاستعلامات")
        return
    
    # تطبيق migration
    success = execute_migration()
    
    if success:
        # اختبار الـ Views
        working_views = test_views()
        
        if working_views:
            print(f"\n🎉 تم تطبيق Migration بنجاح!")
            print(f"✅ {len(working_views)} views جاهزة للاستخدام")
        else:
            print("\n⚠️  Migration تم تطبيقه لكن الـ Views لا تعمل")
    
    print("\n📝 الخطوة التالية: تحديث الـ hooks لاستخدام الـ Views الجديدة")

if __name__ == "__main__":
    main()
