@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 213 94% 68%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 45 93% 58%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 213 94% 68%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* EGX Brand Colors */
    --egx-blue-50: 239 100% 97%;
    --egx-blue-100: 239 100% 95%;
    --egx-blue-500: 213 94% 68%;
    --egx-blue-700: 213 94% 58%;
    --egx-blue-800: 213 94% 48%;
    --egx-blue-900: 213 94% 38%;
    
    --egx-gold-50: 48 100% 96%;
    --egx-gold-100: 48 96% 89%;
    --egx-gold-400: 45 93% 58%;
    --egx-gold-500: 45 93% 47%;
    --egx-gold-600: 45 93% 38%;
    --egx-gold-900: 45 100% 15%;

    --market-green: 142 71% 45%;
    --market-red: 0 84% 60%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 213 94% 68%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 45 93% 58%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-cairo antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Enhanced Arabic text styling */
  .arabic {
    font-family: 'Cairo', system-ui, sans-serif;
    direction: rtl;
    text-align: right;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }

  /* Magical animations */
  .market-ticker {
    animation: slide-up 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .price-up {
    @apply text-market-green;
    animation: pulse-glow-green 1.5s ease-in-out;
  }

  .price-down {
    @apply text-market-red;
    animation: pulse-glow-red 1.5s ease-in-out;
  }

  /* Enhanced magical gradient backgrounds */
  .egx-gradient {
    background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 25%, #2563eb 50%, #3b82f6 75%, #60a5fa 100%);
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite;
  }

  .gold-gradient {
    background: linear-gradient(135deg, #78350f 0%, #92400e 25%, #b45309 50%, #d97706 75%, #f59e0b 100%);
    background-size: 200% 200%;
    animation: gradient-shift 6s ease infinite;
  }

  .magical-gradient {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 400% 400%;
    animation: gradient-shift 10s ease infinite;
  }

  /* Enhanced glass morphism effect */
  .glass-effect {
    background: rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.15);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Magical scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-muted/50 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--accent)));
    @apply rounded-full;
    transition: all 0.3s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, hsl(var(--accent)), hsl(var(--primary)));
    transform: scale(1.1);
  }

  /* Enhanced card hover effects with magic */
  .card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .card-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s;
    z-index: 1;
  }

  .card-hover:hover::before {
    left: 100%;
  }

  .card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 30px rgba(59, 130, 246, 0.15);
  }

  /* Magical button animations */
  .btn-animate {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    position: relative;
    overflow: hidden;
  }

  .btn-animate::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    z-index: 0;
  }

  .btn-animate:hover::before {
    width: 300px;
    height: 300px;
  }

  .btn-animate:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  .btn-animate:active {
    transform: scale(0.98);
  }

  /* Floating magical elements */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  .float-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: -2s;
  }

  .magical-float {
    animation: magical-float 8s ease-in-out infinite;
  }

  /* Typewriter effect with magic */
  .typewriter {
    overflow: hidden;
    border-right: 3px solid hsl(var(--accent));
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
  }

  /* Particle effect background */
  .particle-bg {
    position: relative;
    overflow: hidden;
  }

  .particle-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
    animation: particle-move 20s ease-in-out infinite;
    z-index: -1;
  }

  /* Enhanced Stock Ticker Animations */
  @keyframes scroll-right {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(calc(100vw));
    }
  }

  @keyframes float-slow {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(180deg);
    }
  }

  @keyframes float-medium {
    0%, 100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-15px) scale(1.2);
    }
  }

  @keyframes float-fast {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-10px) rotate(90deg);
    }
    50% {
      transform: translateY(-20px) rotate(180deg);
    }
    75% {
      transform: translateY(-10px) rotate(270deg);
    }
  }

  @keyframes bounce-slow {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-3px);
    }
  }

  .animate-scroll-right {
    animation: scroll-right 30s linear infinite;
  }

  .animate-float-slow {
    animation: float-slow 6s ease-in-out infinite;
  }

  .animate-float-medium {
    animation: float-medium 4s ease-in-out infinite;
  }

  .animate-float-fast {
    animation: float-fast 3s ease-in-out infinite;
  }

  .animate-bounce-slow {
    animation: bounce-slow 2s ease-in-out infinite;
  }

  /* Enhanced glass effect for ticker */
  .glass-effect {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced shimmer effect for ticker highlights */
  @keyframes shimmer-enhanced {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .animate-shimmer {
    animation: shimmer-enhanced 2s infinite;
  }

  /* Dynamic width for stock ticker progress bars */
  [data-width="0"] { width: 0%; }
  [data-width="1"] { width: 1%; }
  [data-width="2"] { width: 2%; }
  [data-width="3"] { width: 3%; }
  [data-width="4"] { width: 4%; }
  [data-width="5"] { width: 5%; }
  [data-width="10"] { width: 10%; }
  [data-width="15"] { width: 15%; }
  [data-width="20"] { width: 20%; }
  [data-width="25"] { width: 25%; }
  [data-width="30"] { width: 30%; }
  [data-width="35"] { width: 35%; }
  [data-width="40"] { width: 40%; }
  [data-width="45"] { width: 45%; }
  [data-width="50"] { width: 50%; }
  [data-width="60"] { width: 60%; }
  [data-width="70"] { width: 70%; }
  [data-width="80"] { width: 80%; }
  [data-width="90"] { width: 90%; }
  [data-width="100"] { width: 100%; }
}

@layer utilities {
  /* Magical text gradients */
  .text-gradient-primary {
    background: linear-gradient(135deg, #2563eb, #3b82f6, #60a5fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .text-gradient-gold {
    background: linear-gradient(135deg, #f59e0b, #fbbf24, #fcd34d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .text-gradient-magical {
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: gradient-shift 4s ease infinite;
  }

  /* Enhanced shadows with magic */
  .shadow-magical {
    box-shadow: 
      0 10px 25px rgba(0, 0, 0, 0.1),
      0 20px 60px rgba(59, 130, 246, 0.1),
      0 0 60px rgba(251, 191, 36, 0.05);
  }

  .shadow-elevated {
    box-shadow: 
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 30px rgba(59, 130, 246, 0.1);
  }

  .shadow-soft {
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.1), 
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 20px rgba(59, 130, 246, 0.05);
  }

  /* Brand color utilities */
  .text-egx-blue-500 { color: hsl(var(--egx-blue-500)); }
  .text-egx-blue-700 { color: hsl(var(--egx-blue-700)); }
  .text-egx-blue-800 { color: hsl(var(--egx-blue-800)); }
  .text-egx-blue-900 { color: hsl(var(--egx-blue-900)); }
  .text-egx-gold-400 { color: hsl(var(--egx-gold-400)); }
  .text-egx-gold-500 { color: hsl(var(--egx-gold-500)); }
  .text-egx-gold-900 { color: hsl(var(--egx-gold-900)); }
  .text-market-green { color: hsl(var(--market-green)); }
  .text-market-red { color: hsl(var(--market-red)); }

  .bg-egx-blue-500 { background-color: hsl(var(--egx-blue-500)); }
  .bg-egx-gold-400 { background-color: hsl(var(--egx-gold-400)); }
  .bg-egx-gold-500 { background-color: hsl(var(--egx-gold-500)); }
  .bg-market-green { background-color: hsl(var(--market-green)); }
  .bg-market-red { background-color: hsl(var(--market-red)); }
}

/* Enhanced magical keyframes */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes pulse-glow-green {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
  50% {
    opacity: 0.9;
    box-shadow: 
      0 0 30px rgba(34, 197, 94, 0.6),
      0 0 60px rgba(34, 197, 94, 0.2);
  }
}

@keyframes pulse-glow-red {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }
  50% {
    opacity: 0.9;
    box-shadow: 
      0 0 30px rgba(239, 68, 68, 0.6),
      0 0 60px rgba(239, 68, 68, 0.2);
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes magical-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: currentColor;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes particle-move {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(10px, -10px) scale(1.1);
  }
  66% {
    transform: translate(-5px, 5px) scale(0.9);
  }
}

@keyframes magical-glow {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(251, 191, 36, 0.2);
  }
  50% {
    box-shadow: 
      0 0 40px rgba(59, 130, 246, 0.5),
      0 0 80px rgba(251, 191, 36, 0.3);
  }
}

/* Enhanced loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.shimmer-dark {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Stagger animation delays for magical entrance */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }

/* Interactive elements with magic */
.interactive-card {
  @apply transition-all duration-300 cursor-pointer;
}

.interactive-card:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 40px rgba(59, 130, 246, 0.1);
}

.magical-border {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, #667eea, #764ba2) border-box;
}

.pulse-ring {
  @apply absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-full;
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  opacity: 0.75;
}
