-- Add missing columns to stocks_realtime table
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS avg_net_volume_3d DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS avg_net_volume_5d DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS tk_indicator DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS kj_indicator DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS opening_value DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS speculation_opportunity BOOLEAN;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS stock_status VARCHAR(100);
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_flow DECIMAL;

-- Add technical analysis columns
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma5 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma10 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma20 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma50 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma100 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma200 DECIMAL;

-- Add target and stop loss columns
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS target_1 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS target_2 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS target_3 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS stop_loss DECIMAL;

-- Add liquidity analysis columns
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_ratio DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS net_liquidity DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_inflow DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_outflow DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS volume_inflow BIGINT;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS volume_outflow BIGINT;

-- Add financial metrics columns
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS free_shares BIGINT;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS eps_annual DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS book_value DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS pe_ratio DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS dividend_yield DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS sector VARCHAR(100);

-- Add additional analysis columns
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS price_range DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS name VARCHAR(200);
