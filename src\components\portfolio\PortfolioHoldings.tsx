import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trash2 } from 'lucide-react';
import StockAnalysisLink from '@/components/analysis/StockAnalysisLink';
import { useAnalysis } from '@/hooks/useAnalysis';

interface PortfolioItem {
  id: string;
  symbol: string;
  name: string;
  quantity: number;
  buyPrice: number;
  currentPrice: number;
  totalValue: number;
  profitLoss: number;
  profitLossPercent: number;
}

interface PortfolioHoldingsProps {
  portfolio: PortfolioItem[];
  onRemoveStock: (id: string) => void;
}

const PortfolioHoldings = ({ portfolio, onRemoveStock }: PortfolioHoldingsProps) => {
  const { openAnalysis } = useAnalysis();
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-right arabic" dir="rtl">أسهم المحفظة</CardTitle>
      </CardHeader>
      <CardContent>
        {portfolio.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            لا توجد أسهم في المحفظة. قم بإضافة أول سهم!
          </div>
        ) : (
          <div className="space-y-4">
            {portfolio.map(item => (
              <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="flex flex-col gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onRemoveStock(item.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                    <StockAnalysisLink 
                      symbol={item.symbol} 
                      size="sm" 
                      onAnalyze={openAnalysis}
                    />
                  </div>
                  <div>
                    <div className="font-bold text-lg">{item.symbol}</div>
                    <div className="text-sm text-muted-foreground">{item.name}</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-xs text-muted-foreground">الكمية</div>
                    <div className="font-semibold">{item.quantity}</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">سعر الشراء</div>
                    <div className="font-semibold">{item.buyPrice.toFixed(2)} ج.م</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">السعر الحالي</div>
                    <div className="font-semibold">{item.currentPrice.toFixed(2)} ج.م</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">الربح/الخسارة</div>
                    <div className={`font-bold ${item.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {item.profitLoss >= 0 ? '+' : ''}{item.profitLoss.toFixed(2)} ج.م
                    </div>
                    <Badge variant={item.profitLoss >= 0 ? 'default' : 'destructive'} className="text-xs">
                      {item.profitLossPercent >= 0 ? '+' : ''}{item.profitLossPercent.toFixed(2)}%
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PortfolioHoldings;
