 

/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useRef } from 'react';

interface WebSocketMessage {
  type: string;
  symbol?: string;
  price?: number;
  change?: number;
  volume?: number;
  timestamp?: string;
}

export const useWebSocket = (url: string) => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const ws = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Mock WebSocket connection for demo
    const mockConnection = () => {
      setIsConnected(true);
      
      // Simulate real-time price updates
      const interval = setInterval(() => {
        const symbols = ['COMI', 'ETEL', 'TALAAT', 'OTMT', 'HRHO'];
        const randomSymbol = symbols[Math.floor(Math.random() * symbols.length)];
        const basePrice = 50 + Math.random() * 50;
        const change = (Math.random() - 0.5) * 4;
        
        setLastMessage({
          type: 'price_update',
          symbol: randomSymbol,
          price: basePrice,
          change: change,
          volume: Math.floor(Math.random() * 1000000),
          timestamp: new Date().toISOString()
        });
      }, 2000);

      return () => {
        clearInterval(interval);
        setIsConnected(false);
      };
    };

    const cleanup = mockConnection();
    return cleanup;
  }, [url]);

  const sendMessage = (message: any) => {
    console.log('Sending message:', message);
  };

  return { isConnected, lastMessage, sendMessage };
};
