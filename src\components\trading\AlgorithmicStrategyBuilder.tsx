import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Bot, Plus, Play, Save, Settings, Code, BarChart3 } from 'lucide-react';

interface Strategy {
  id: string;
  name: string;
  description: string;
  conditions: string[];
  actions: string[];
  status: 'draft' | 'testing' | 'live';
  performance: number;
}

const AlgorithmicStrategyBuilder = () => {
  const [strategies, setStrategies] = useState<Strategy[]>([
    {
      id: '1',
      name: 'RSI الانعكاس',
      description: 'شراء عند RSI أقل من 30، بيع عند أكبر من 70',
      conditions: ['RSI < 30', 'حجم التداول > متوسط 10 أيام'],
      actions: ['شراء 100 سهم', 'وقف الخسارة 5%'],
      status: 'testing',
      performance: 12.5
    }
  ]);

  const [newStrategy, setNewStrategy] = useState({
    name: '',
    description: '',
    condition: '',
    action: ''
  });

  const [conditions, setConditions] = useState<string[]>([]);
  const [actions, setActions] = useState<string[]>([]);

  const addCondition = () => {
    if (newStrategy.condition) {
      setConditions([...conditions, newStrategy.condition]);
      setNewStrategy({...newStrategy, condition: ''});
    }
  };

  const addAction = () => {
    if (newStrategy.action) {
      setActions([...actions, newStrategy.action]);
      setNewStrategy({...newStrategy, action: ''});
    }
  };

  const createStrategy = () => {
    if (newStrategy.name && conditions.length > 0 && actions.length > 0) {
      const strategy: Strategy = {
        id: Date.now().toString(),
        name: newStrategy.name,
        description: newStrategy.description,
        conditions,
        actions,
        status: 'draft',
        performance: 0
      };
      setStrategies([...strategies, strategy]);
      setNewStrategy({ name: '', description: '', condition: '', action: '' });
      setConditions([]);
      setActions([]);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'live': return 'bg-green-100 text-green-800';
      case 'testing': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800">
          <Bot className="h-5 w-5" />
          منشئ الاستراتيجيات الخوارزمية
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Strategy Builder Form */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white/60 rounded-lg">
          <div className="space-y-4">
            <Input
              placeholder="اسم الاستراتيجية"
              value={newStrategy.name}
              onChange={(e) => setNewStrategy({...newStrategy, name: e.target.value})}
              className="text-right"
            />
            <Textarea
              placeholder="وصف الاستراتيجية"
              value={newStrategy.description}
              onChange={(e) => setNewStrategy({...newStrategy, description: e.target.value})}
              className="text-right"
            />
            
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  placeholder="إضافة شرط (مثل: RSI < 30)"
                  value={newStrategy.condition}
                  onChange={(e) => setNewStrategy({...newStrategy, condition: e.target.value})}
                  className="text-right"
                />
                <Button onClick={addCondition} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {conditions.map((condition, index) => (
                  <Badge key={index} className="bg-blue-100 text-blue-800">
                    {condition}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  placeholder="إضافة إجراء (مثل: شراء 100 سهم)"
                  value={newStrategy.action}
                  onChange={(e) => setNewStrategy({...newStrategy, action: e.target.value})}
                  className="text-right"
                />
                <Button onClick={addAction} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {actions.map((action, index) => (
                  <Badge key={index} className="bg-green-100 text-green-800">
                    {action}
                  </Badge>
                ))}
              </div>
            </div>

            <Button onClick={createStrategy} className="w-full">
              <Save className="h-4 w-4 mr-2" />
              إنشاء الاستراتيجية
            </Button>
          </div>

          <div className="space-y-4">
            <h4 className="font-medium">المؤشرات المتاحة:</h4>
            <div className="grid grid-cols-2 gap-2">
              {['RSI', 'MACD', 'SMA', 'EMA', 'البولينجر', 'حجم التداول'].map(indicator => (
                <Button
                  key={indicator}
                  variant="outline"
                  size="sm"
                  onClick={() => setNewStrategy({...newStrategy, condition: indicator})}
                >
                  {indicator}
                </Button>
              ))}
            </div>

            <h4 className="font-medium">الإجراءات المتاحة:</h4>
            <div className="grid grid-cols-2 gap-2">
              {['شراء', 'بيع', 'وقف الخسارة', 'جني الأرباح'].map(action => (
                <Button
                  key={action}
                  variant="outline"
                  size="sm"
                  onClick={() => setNewStrategy({...newStrategy, action: action})}
                >
                  {action}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Existing Strategies */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">الاستراتيجيات المحفوظة</h3>
          {strategies.map(strategy => (
            <div key={strategy.id} className="p-4 bg-white/80 rounded-lg space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{strategy.name}</h4>
                  <p className="text-sm text-gray-600">{strategy.description}</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(strategy.status)}>
                    {strategy.status === 'live' ? 'مباشر' : strategy.status === 'testing' ? 'اختبار' : 'مسودة'}
                  </Badge>
                  {strategy.performance !== 0 && (
                    <Badge className="bg-green-100 text-green-800">
                      +{strategy.performance}%
                    </Badge>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="text-sm font-medium mb-2">الشروط:</h5>
                  <div className="space-y-1">
                    {strategy.conditions.map((condition, index) => (
                      <Badge key={index} variant="outline" className="block w-fit">
                        {condition}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h5 className="text-sm font-medium mb-2">الإجراءات:</h5>
                  <div className="space-y-1">
                    {strategy.actions.map((action, index) => (
                      <Badge key={index} variant="outline" className="block w-fit">
                        {action}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button size="sm" variant="outline">
                  <Play className="h-4 w-4 mr-1" />
                  اختبار
                </Button>
                <Button size="sm" variant="outline">
                  <Settings className="h-4 w-4 mr-1" />
                  تعديل
                </Button>
                <Button size="sm" variant="outline">
                  <BarChart3 className="h-4 w-4 mr-1" />
                  الأداء
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default AlgorithmicStrategyBuilder;
