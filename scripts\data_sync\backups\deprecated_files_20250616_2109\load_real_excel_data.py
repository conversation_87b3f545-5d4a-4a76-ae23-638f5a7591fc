#!/usr/bin/env python3
"""
EGX Stock AI Oracle - Load Real Data from Excel
Reads real-time stock data from the Excel file with correct Arabic columns
"""

import pandas as pd
from supabase import create_client, Client
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
import sys

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('realtime_data_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
EXCEL_PATH = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx"

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def calculate_change_metrics(current_price, change_percent):
    """Calculate change amount from percentage"""
    try:
        if change_percent and current_price:
            # If change_percent is provided, calculate change_amount
            change_amount = (current_price * change_percent) / 100
            return change_amount, change_percent
        return 0, 0
    except:
        return 0, 0

def safe_float(value, default=None):
    """Safely convert to float"""
    try:
        if pd.isna(value) or value in ['', '-', 'FALSE', 'TRUE']:
            return default
        return float(value)
    except:
        return default

def safe_int(value, default=None):
    """Safely convert to int"""
    try:
        if pd.isna(value) or value in ['', '-', 'FALSE', 'TRUE']:
            return default
        return int(float(value))
    except:
        return default

def process_realtime_data_from_excel():
    """Process real-time data from the actual Excel file with correct structure"""
    try:
        if not os.path.exists(EXCEL_PATH):
            logger.error(f"Excel file not found: {EXCEL_PATH}")
            return
        
        logger.info(f"📊 قراءة ملف Excel: {EXCEL_PATH}")
        
        # Read Excel file
        df = pd.read_excel(EXCEL_PATH, sheet_name=0)
        
        if df.empty:
            logger.warning("ملف Excel فارغ")
            return
        
        logger.info(f"📈 تم تحميل {len(df)} صف من ملف Excel")
        logger.info(f"الأعمدة المتاحة: {list(df.columns)}")
        
        processed_count = 0
        batch_payload = []
        indices_data = []
        
        for index, row in df.iterrows():
            try:
                # Get symbol
                symbol = str(row.get('الرمز', '')).strip()
                if not symbol or symbol == 'nan':
                    continue
                
                # Get stock name
                name = str(row.get('الاسم', symbol)).strip()
                
                # Get prices
                current_price = safe_float(row.get('الاغلاق'))
                open_price = safe_float(row.get('إفتتاح'))
                high_price = safe_float(row.get('أعلى'))
                low_price = safe_float(row.get('أدنى'))
                
                if current_price is None:
                    logger.warning(f"لا يوجد سعر للرمز {symbol}")
                    continue
                
                # Get change percentage and calculate change amount
                change_percent = safe_float(row.get('التغير %'))
                previous_close = current_price
                if change_percent:
                    # Calculate previous close from current price and change %
                    previous_close = current_price / (1 + (change_percent / 100))
                
                change_amount, change_percent = calculate_change_metrics(current_price, change_percent)
                
                # Get volume and other data
                volume = safe_int(row.get('الحجم', 0))
                turnover = safe_float(row.get('القيمة', 0))
                trades_count = safe_int(row.get('الصفقات', 0))
                
                # Get sector info
                sector = str(row.get('القطاع', '')).strip()
                
                # Check if this is an index (like EGX30Capped)
                if 'EGX' in symbol.upper() or 'مؤشر' in name:
                    # This is a market index
                    index_data = {
                        "symbol": symbol,
                        "name": name,
                        "name_ar": name if any(c > '\u0600' for c in name) else f"مؤشر {symbol}",
                        "current_value": current_price,
                        "open_value": open_price,
                        "high_value": high_price,
                        "low_value": low_price,
                        "previous_close": previous_close,
                        "change_amount": change_amount,
                        "change_percent": change_percent,
                        "volume": volume,
                        "turnover": turnover,
                        "updated_at": datetime.now(timezone.utc).isoformat()
                    }
                    indices_data.append(index_data)
                    logger.info(f"🔍 وجد مؤشر: {symbol} = {current_price:.2f} ({change_percent:+.2f}%)")
                else:
                    # This is a regular stock
                    payload = {
                        "symbol": symbol,
                        "current_price": current_price,
                        "open_price": open_price,
                        "high_price": high_price,
                        "low_price": low_price,
                        "previous_close": previous_close,
                        "change_amount": change_amount,
                        "change_percent": change_percent,
                        "volume": volume,
                        "turnover": turnover,
                        "trades_count": trades_count,
                        "updated_at": datetime.now(timezone.utc).isoformat()
                    }
                    
                    batch_payload.append(payload)
                    processed_count += 1
                    
                    # Also update stocks_master with name and sector
                    try:
                        master_payload = {
                            "symbol": symbol,
                            "name": name,
                            "sector": sector if sector and sector != 'nan' else None,
                            "market": "EGX",
                            "is_active": True,
                            "updated_at": datetime.now(timezone.utc).isoformat()
                        }
                        supabase.table("stocks_master").upsert(master_payload).execute()
                    except Exception as e:
                        logger.warning(f"خطأ في تحديث stocks_master للرمز {symbol}: {e}")
                
            except Exception as e:
                logger.error(f"خطأ في معالجة الصف {index}: {e}")
                continue
        
        # Insert stock data in batches
        if batch_payload:
            try:
                batch_size = 50
                for i in range(0, len(batch_payload), batch_size):
                    batch = batch_payload[i:i+batch_size]
                    result = supabase.table("stocks_realtime").upsert(batch).execute()
                    logger.info(f"✅ تم تحديث {len(batch)} سهم")
                
                logger.info(f"🎉 تم تحديث {processed_count} سهم بنجاح")
                
            except Exception as e:
                logger.error(f"خطأ في تحديث بيانات الأسهم: {e}")
        
        # Insert indices data
        if indices_data:
            try:
                for index_data in indices_data:
                    result = supabase.table("market_indices").upsert(index_data).execute()
                    logger.info(f"✅ تم تحديث مؤشر {index_data['symbol']}: {index_data['current_value']:.2f} ({index_data['change_percent']:+.2f}%)")
                
            except Exception as e:
                logger.error(f"خطأ في تحديث المؤشرات: {e}")
          # Calculate market statistics
        if batch_payload:
            gaining_stocks = len([s for s in batch_payload if (s.get('change_percent') or 0) > 0])
            losing_stocks = len([s for s in batch_payload if (s.get('change_percent') or 0) < 0])
            unchanged_stocks = processed_count - gaining_stocks - losing_stocks
            
            total_volume = sum(s.get('volume') or 0 for s in batch_payload)
            total_turnover = sum(s.get('turnover') or 0 for s in batch_payload)
            
            logger.info(f"📊 إحصائيات السوق:")
            logger.info(f"   📈 ارتفاع: {gaining_stocks}")
            logger.info(f"   📉 انخفاض: {losing_stocks}")
            logger.info(f"   ➡️  ثبات: {unchanged_stocks}")
            logger.info(f"   💹 الحجم الإجمالي: {total_volume:,}")
            logger.info(f"   💰 القيمة الإجمالية: {total_turnover:,.2f}")
            
    except Exception as e:
        logger.error(f"خطأ في معالجة البيانات: {e}")

def main():
    logger.info("🚀 بدء تحديث البيانات الحقيقية من ملف Excel")
    process_realtime_data_from_excel()
    logger.info("✅ انتهى تحديث البيانات!")

if __name__ == "__main__":
    main()
