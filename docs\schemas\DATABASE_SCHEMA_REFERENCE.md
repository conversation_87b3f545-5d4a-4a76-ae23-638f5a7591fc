# EGX Stock AI Oracle - Database Schema Reference

*Generated on: 2025-06-15 21:45:46*

## 📊 Database Overview

This document provides a comprehensive reference for all database tables and columns in the EGX Stock AI Oracle system.

### 🎯 Quick Stats
- **Total Tables**: 5
- **Total Records**: 442737
- **Main Stock Data Tables**: 3

---

## 📋 Table: `stocks_realtime`

**Records**: 225 | **Columns**: 51

Database table for EGX Stock AI Oracle system

### Columns (51)

| Column | Type | Sample Value | Description |
|--------|------|--------------|-------------|
| `symbol` | str | EGX30 | Stock symbol identifier |
| `current_price` | float | 31016.0 | Current trading price in EGP |
| `open_price` | float | 32511.68 | Data field |
| `high_price` | float | 32511.68 | Data field |
| `low_price` | float | 30002.46 | Data field |
| `previous_close` | float | 31016.0 | Data field |
| `change_amount` | float | 0.0 | Data field |
| `change_percent` | float | 0.0 | Data field |
| `volume` | int | 273272187 | Trading volume |
| `turnover` | float | 2475209404.0 | Data field |
| `trades_count` | null | None | Data field |
| `bid_price` | null | None | Data field |
| `ask_price` | null | None | Data field |
| `bid_volume` | null | None | Data field |
| `ask_volume` | null | None | Data field |
| `last_trade_time` | null | None | Data field |
| `market_cap` | null | None | Market capitalization |
| `updated_at` | str | 2025-06-15T15:39:37.678742+00:00 | Last update timestamp |
| `ma5` | null | None | 5-day moving average |
| `ma10` | null | None | Data field |
| `ma20` | null | None | 20-day moving average |
| `ma50` | null | None | Data field |
| `ma100` | null | None | Data field |
| `ma200` | null | None | Data field |
| `tk_indicator` | null | None | Data field |
| `kj_indicator` | null | None | Data field |
| `target_1` | null | None | First price target |
| `target_2` | null | None | Data field |
| `target_3` | null | None | Data field |
| `stop_loss` | null | None | Stop loss price level |
| `stock_status` | null | None | Data field |
| `speculation_opportunity` | null | None | Trading opportunity flag |
| `liquidity_ratio` | null | None | Data field |
| `net_liquidity` | null | None | Data field |
| `liquidity_inflow` | null | None | Data field |
| `liquidity_outflow` | null | None | Data field |
| `volume_inflow` | null | None | Data field |
| `volume_outflow` | null | None | Data field |
| `liquidity_flow` | null | None | Data field |
| `free_shares` | null | None | Data field |
| `eps_annual` | null | None | Data field |
| `book_value` | null | None | Data field |
| `pe_ratio` | null | None | Price-to-Earnings ratio |
| `dividend_yield` | null | None | Annual dividend yield percentage |
| `sector` | null | None | Industry sector classification |
| `price_range` | null | None | Data field |
| `avg_net_volume_3d` | null | None | Data field |
| `avg_net_volume_5d` | null | None | Data field |
| `opening_value` | null | None | Data field |
| `name` | null | None | Data field |
| `last_trade_date` | null | None | Data field |

---

## 📋 Table: `stocks_financials`

**Records**: 252 | **Columns**: 95

Database table for EGX Stock AI Oracle system

### Columns (95)

| Column | Type | Sample Value | Description |
|--------|------|--------------|-------------|
| `id` | int | 7 | Data field |
| `symbol` | str | EGAL | Stock symbol identifier |
| `fiscal_year` | int | 2025 | Data field |
| `fiscal_quarter` | null | None | Data field |
| `market_cap` | float | 67212751007.0 | Market capitalization |
| `pe_ratio` | null | None | Price-to-Earnings ratio |
| `eps_ttm` | null | None | Data field |
| `eps_growth_yoy` | null | None | Data field |
| `dividend_yield` | float | 4.2961 | Annual dividend yield percentage |
| `book_value_per_share` | null | None | Data field |
| `price_to_book` | null | None | Data field |
| `revenue_ttm` | float | 32815674548.0 | Data field |
| `net_income_ttm` | float | 9324234387.0 | Data field |
| `total_assets` | null | None | Data field |
| `total_debt` | float | 5759164.0 | Data field |
| `free_cash_flow` | float | 11384757680.0 | Data field |
| `roe` | null | None | Data field |
| `roa` | null | None | Data field |
| `debt_to_equity` | null | None | Data field |
| `current_ratio` | null | None | Data field |
| `gross_margin` | null | None | Data field |
| `operating_margin` | null | None | Data field |
| `net_margin` | null | None | Data field |
| `beta` | float | 1.5277 | Data field |
| `analyst_rating` | str | Strong buy | Data field |
| `target_price` | null | None | Data field |
| `created_at` | str | 2025-06-15T15:23:22.421637+00:00 | Record creation timestamp |
| `updated_at` | str | 2025-06-15T18:36:08.382104+00:00 | Last update timestamp |
| `description` | str | EGYPT ALUMINUM | Data field |
| `industry` | str | Aluminum | Data field |
| `isin` | str | EGS3E181C010 | Data field |
| `index_membership` | str | EGX 30 | Data field |
| `market_cap_currency` | str | EGP | Data field |
| `total_shares_outstanding` | int | 412500000 | Data field |
| `float_shares_outstanding` | int | 41921550 | Data field |
| `float_shares_percent` | float | 10.1628 | Data field |
| `number_of_shareholders` | null | None | Data field |
| `assets_to_equity` | float | 1.31645748127211 | Data field |
| `assets_turnover` | null | None | Data field |
| `cash_ratio` | float | 0.995843760975687 | Data field |
| `cash_to_debt_ratio` | float | 823.1831484569635 | Data field |
| `debt_to_assets` | float | 0.0002810375269884 | Data field |
| `price_to_cash_flow` | null | None | Data field |
| `price_to_free_cash_flow` | null | None | Data field |
| `price_to_sales` | null | None | Data field |
| `enterprise_value` | null | None | Data field |
| `ev_to_ebitda` | null | None | Data field |
| `ev_to_revenue` | null | None | Data field |
| `total_revenue` | float | 32815674548.000004 | Data field |
| `gross_profit` | float | 12853618550.0 | Data field |
| `operating_income` | null | None | Data field |
| `net_income` | float | 9324234387.0 | Data field |
| `ebitda` | null | None | Data field |
| `total_equity` | null | None | Data field |
| `total_liabilities` | float | 4926104958.0 | Data field |
| `total_current_assets` | null | None | Data field |
| `total_current_liabilities` | null | None | Data field |
| `cash_and_equivalents` | float | 2048122229.0 | Data field |
| `cash_short_term_investments` | float | 4740846754.0 | Data field |
| `net_debt` | null | None | Data field |
| `goodwill` | null | None | Data field |
| `operating_cash_flow` | null | None | Data field |
| `investing_cash_flow` | null | None | Data field |
| `financing_cash_flow` | null | None | Data field |
| `capital_expenditures` | null | None | Data field |
| `eps_basic_ttm` | null | None | Data field |
| `eps_diluted_ttm` | null | None | Data field |
| `eps_reported_annual` | float | 22.6 | Data field |
| `eps_estimate_quarterly` | null | None | Data field |
| `eps_growth_ttm` | null | None | Data field |
| `revenue_growth` | null | None | Data field |
| `net_income_growth` | null | None | Data field |
| `ebitda_growth` | null | None | Data field |
| `gross_profit_growth` | null | None | Data field |
| `free_cash_flow_growth` | null | None | Data field |
| `capex_growth` | null | None | Data field |
| `total_assets_growth` | float | 15.020225411534712 | Data field |
| `dividend_yield_indicated` | float | 4.29606 | Data field |
| `dividend_payout_ratio` | null | None | Data field |
| `dividends_per_share` | float | 7.0 | Data field |
| `dividends_paid_annual` | float | -3051986735.0 | Data field |
| `continuous_dividend_growth` | int | 2 | Data field |
| `forward_pe` | float | 5.0 | Data field |
| `target_price_1y` | float | 315.0 | Data field |
| `target_performance_1y` | float | 89.75903614457832 | Data field |
| `performance_ytd` | float | 43.77273514637105 | Data field |
| `beta_5_years` | float | 1.527714 | Data field |
| `average_volume_10d` | int | 282567 | Data field |
| `relative_volume` | float | 1.0209318382976489 | Data field |
| `volatility_1_day` | float | 2.921320731557623 | Data field |
| `volume_change` | float | 279.95424025018394 | Data field |
| `turnover_1_day` | float | 54857854.0 | Data field |
| `recent_earnings_date` | str | 2025-01-06 | Data field |
| `upcoming_earnings_date` | null | None | Data field |
| `sector` | str | Non-energy minerals | Industry sector classification |

---

## 📋 Table: `stocks_historical`

**Records**: 442260 | **Columns**: 11

Database table for EGX Stock AI Oracle system

### Columns (11)

| Column | Type | Sample Value | Description |
|--------|------|--------------|-------------|
| `id` | int | 1 | Data field |
| `symbol` | str | AALR | Stock symbol identifier |
| `date` | str | 2017-03-12 | Data field |
| `open` | float | 11.43 | Data field |
| `high` | float | 11.7 | Data field |
| `low` | float | 11.3 | Data field |
| `close` | float | 11.4 | Data field |
| `volume` | int | 3095 | Trading volume |
| `open_interest` | int | 0 | Data field |
| `adjusted_close` | null | None | Data field |
| `created_at` | str | 2025-06-15T15:08:14.627507+00:00 | Record creation timestamp |

---

## 📋 Table: `user_portfolios`

**Records**: 1 | **Columns**: 11

Database table for EGX Stock AI Oracle system

### Columns (11)

| Column | Type | Sample Value | Description |
|--------|------|--------------|-------------|
| `id` | str | d0cd6cdb-7cb4-4666-9c8d-d99081bffd7d | Data field |
| `user_id` | str | 27260eb4-29e1-48e1-9154-e65229bdda7b | Data field |
| `name` | str | test3 | Data field |
| `description` | str |  | Data field |
| `is_default` | bool | False | Data field |
| `total_value` | float | 0.0 | Data field |
| `total_cost` | float | 0.0 | Data field |
| `total_profit_loss` | float | 0.0 | Data field |
| `currency` | str | EGP | Data field |
| `created_at` | str | 2025-06-14T17:32:18.673346+00:00 | Record creation timestamp |
| `updated_at` | str | 2025-06-14T17:32:18.673346+00:00 | Last update timestamp |

---

## 📋 Table: `paper_trades`

**Records**: 2 | **Columns**: 16

Database table for EGX Stock AI Oracle system

### Columns (16)

| Column | Type | Sample Value | Description |
|--------|------|--------------|-------------|
| `id` | str | 9fb9232d-8f57-43bd-9b96-11ae2323b63a | Data field |
| `account_id` | str | 01119f21-88a4-4e44-b453-0d603221585a | Data field |
| `symbol` | str | COMI | Stock symbol identifier |
| `trade_type` | str | buy | Data field |
| `quantity` | float | 1000.0 | Data field |
| `entry_price` | float | 62.0 | Data field |
| `exit_price` | null | None | Data field |
| `stop_loss` | null | None | Stop loss price level |
| `take_profit` | null | None | Data field |
| `profit_loss` | null | None | Data field |
| `commission` | float | 0.0 | Data field |
| `status` | str | open | Data field |
| `entry_time` | str | 2025-06-14T16:13:09.483975+00:00 | Data field |
| `exit_time` | null | None | Data field |
| `notes` | null | None | Data field |
| `strategy_name` | null | None | Data field |

---

## 🚀 Frontend Integration Guide

### TypeScript Interfaces
Use the generated `database_types.ts` file for type-safe database queries.

### Common Query Patterns

#### 1. Get Active Stocks for Dashboard
```typescript
const { data: activeStocks } = await supabase
  .from('stocks_realtime')
  .select('symbol, current_price, change_percent, volume')
  .gt('volume', 0)
  .order('volume', { ascending: false })
  .limit(50);
```

#### 2. Get Stock Details with Financials
```typescript
const { data: stockDetails } = await supabase
  .from('stocks_realtime')
  .select(`
    *,
    stocks_financials (
      pe_ratio,
      market_cap,
      dividend_yield,
      sector
    )
  `)
  .eq('symbol', stockSymbol)
  .single();
```

#### 3. Get Sector Performance
```typescript
const { data: sectorData } = await supabase
  .from('stocks_realtime')
  .select(`
    symbol,
    current_price,
    change_percent,
    stocks_financials!inner (sector)
  `)
  .eq('stocks_financials.sector', sectorName);
```

### Key Fields for Frontend Components

#### Quick Stats Grid
- `current_price`, `change_percent`, `volume`, `market_cap`

#### Stock Screener
- `pe_ratio`, `dividend_yield`, `sector`, `market_cap`, `volume`

#### Technical Analysis
- `ma5`, `ma10`, `ma20`, `ma50`, `ma100`, `ma200`
- `tk_indicator`, `kj_indicator`

#### Trading Signals
- `target_1`, `target_2`, `target_3`, `stop_loss`
- `speculation_opportunity`, `stock_status`

---

*📄 This documentation is automatically generated. For updates, re-run the schema inspection script.*
