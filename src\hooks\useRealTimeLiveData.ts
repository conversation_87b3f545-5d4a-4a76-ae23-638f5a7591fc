import { useState, useEffect, useRef, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';

export interface RealTimeStockData {
  symbol: string;
  name?: string;
  current_price: number;
  previous_close?: number;
  change_amount?: number;
  change_percent: number;
  volume: number;
  turnover: number;
  high_price?: number;
  low_price?: number;
  updated_at: string;
  
  // بيانات إضافية للرسوم البيانية
  price_history?: number[];
  volume_history?: number[];
  
  // حالات التغيير للتأثيرات البصرية
  priceDirection?: 'up' | 'down' | 'neutral';
  volumeChange?: 'up' | 'down' | 'neutral';
  isNewUpdate?: boolean;
  flashColor?: 'green' | 'red' | 'none';
  isHighlighted?: boolean;
  lastUpdateTime?: number;
}

export interface RealTimeMarketStats {
  totalStocks: number;
  gainers: number;
  losers: number;
  unchanged: number;
  totalVolume: number;
  totalTurnover: number;
  mostActiveStock?: RealTimeStockData;
  topGainer?: RealTimeStockData;
  topLoser?: RealTimeStockData;
  lastUpdate: string;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  updatesPerMinute: number;
}

export const useRealTimeLiveData = () => {
  const [stocks, setStocks] = useState<RealTimeStockData[]>([]);
  const [marketStats, setMarketStats] = useState<RealTimeMarketStats | null>(null);
  const [previousData, setPreviousData] = useState<Map<string, RealTimeStockData>>(new Map());
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [updatesCount, setUpdatesCount] = useState(0);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const flashTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const updateCounterRef = useRef(0);
  const { toast } = useToast();

  // جلب البيانات الأولية
  const {
    data: initialData,
    isLoading,
    error: initialError,
    refetch
  } = useQuery({
    queryKey: ['realtime_initial_data'],
    queryFn: async () => {
      try {
        console.log('🔄 Fetching initial real-time data...');
        
        const { data: stocks, error: stocksError } = await supabase
          .from('stocks_realtime')
          .select(`
            symbol,
            current_price,
            previous_close,
            change_amount,
            change_percent,
            volume,
            turnover,
            high_price,
            low_price,
            updated_at
          `)
          .not('symbol', 'like', '*EGX*')
          .not('symbol', 'like', '*INDEX*')
          .gt('current_price', 0)
          .order('turnover', { ascending: false })
          .limit(150);

        if (stocksError) throw stocksError;

        // جلب أسماء الأسهم
        const symbols = stocks?.map(s => s.symbol) || [];
        const { data: masterData } = await supabase
          .from('stocks_master')
          .select('symbol, name, name_ar')
          .in('symbol', symbols);

        // دمج البيانات مع إضافة تاريخ الأسعار
        const enrichedStocks = stocks?.map(stock => {
          const master = masterData?.find(m => m.symbol === stock.symbol);
          return {
            ...stock,
            name: master?.name_ar || master?.name || stock.symbol,
            price_history: [stock.current_price], // بداية التاريخ
            volume_history: [stock.volume || 0],
            lastUpdateTime: Date.now(),
          };
        }) || [];

        console.log(`✅ Loaded ${enrichedStocks.length} stocks for real-time tracking`);
        return enrichedStocks;
      } catch (error) {
        console.error('❌ Error fetching initial data:', error);
        throw error;
      }
    },
    retry: 2,
    retryDelay: 2000,
  });
  // حساب إحصائيات السوق
  const calculateMarketStats = useCallback((stockData: RealTimeStockData[]) => {
    if (!stockData || stockData.length === 0) return;

    const gainers = stockData.filter(s => s.change_percent > 0);
    const losers = stockData.filter(s => s.change_percent < 0);
    const unchanged = stockData.filter(s => s.change_percent === 0);
    
    const totalVolume = stockData.reduce((sum, s) => sum + (s.volume || 0), 0);
    const totalTurnover = stockData.reduce((sum, s) => sum + (s.turnover || 0), 0);

    const mostActiveStock = stockData.reduce((max, stock) => 
      (stock.turnover || 0) > (max.turnover || 0) ? stock : max
    );

    const topGainer = gainers.length > 0 ? gainers.reduce((max, stock) => 
      stock.change_percent > max.change_percent ? stock : max
    ) : undefined;

    const topLoser = losers.length > 0 ? losers.reduce((min, stock) => 
      stock.change_percent < min.change_percent ? stock : min
    ) : undefined;

    // حساب التحديثات في الدقيقة
    const updatesPerMinute = Math.round(updateCounterRef.current / ((Date.now() - (Date.now() % 60000)) / 60000 + 1));

    const stats: RealTimeMarketStats = {
      totalStocks: stockData.length,
      gainers: gainers.length,
      losers: losers.length,
      unchanged: unchanged.length,
      totalVolume,
      totalTurnover,
      mostActiveStock,
      topGainer,
      topLoser,
      lastUpdate: new Date().toLocaleTimeString('ar-EG'),
      connectionStatus,
      updatesPerMinute,
    };

    setMarketStats(stats);
  }, [connectionStatus]);

  // تحديث البيانات مع التأثيرات البصرية
  const updateStockData = useCallback((newData: RealTimeStockData[]) => {
    const now = Date.now();
    
    const processedStocks = newData.map(stock => {
      const previous = previousData.get(stock.symbol);
      let priceDirection: 'up' | 'down' | 'neutral' = 'neutral';
      let volumeChange: 'up' | 'down' | 'neutral' = 'neutral';
      let flashColor: 'green' | 'red' | 'none' = 'none';
      let isNewUpdate = false;
      let isHighlighted = false;

      // إضافة للتاريخ
      let price_history = stock.price_history || [stock.current_price];
      let volume_history = stock.volume_history || [stock.volume || 0];

      if (previous) {
        // مقارنة الأسعار
        if (stock.current_price > previous.current_price) {
          priceDirection = 'up';
          flashColor = 'green';
          isNewUpdate = true;
          isHighlighted = true;
        } else if (stock.current_price < previous.current_price) {
          priceDirection = 'down';
          flashColor = 'red';
          isNewUpdate = true;
          isHighlighted = true;
        }

        // مقارنة الحجم
        if (stock.volume > previous.volume) {
          volumeChange = 'up';
        } else if (stock.volume < previous.volume) {
          volumeChange = 'down';
        }

        // تحديث التاريخ (آخر 20 نقطة)
        price_history = [...(previous.price_history || []), stock.current_price].slice(-20);
        volume_history = [...(previous.volume_history || []), stock.volume || 0].slice(-20);

        // إدارة تأثير الوميض
        if (isNewUpdate) {
          const existingTimer = flashTimers.current.get(stock.symbol);
          if (existingTimer) clearTimeout(existingTimer);

          const timer = setTimeout(() => {
            flashTimers.current.delete(stock.symbol);
          }, 2000);
          
          flashTimers.current.set(stock.symbol, timer);
        }
      }

      return {
        ...stock,
        price_history,
        volume_history,
        priceDirection,
        volumeChange,
        isNewUpdate,
        flashColor,
        isHighlighted,
        lastUpdateTime: now,
      };
    });

    setStocks(processedStocks);
    
    // تحديث البيانات السابقة
    const newPreviousData = new Map<string, RealTimeStockData>();
    processedStocks.forEach(stock => {
      newPreviousData.set(stock.symbol, { ...stock });
    });
    setPreviousData(newPreviousData);

    // حساب الإحصائيات
    calculateMarketStats(processedStocks);
    
    // تحديث عداد التحديثات
    updateCounterRef.current += 1;
    setUpdatesCount(updateCounterRef.current);
    
  }, [previousData, calculateMarketStats]);

  // إعداد WebSocket للتحديث الفوري
  const initializeWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) return;

    setConnectionStatus('connecting');
    console.log('🔌 Connecting to WebSocket...');

    // محاكاة WebSocket - في التطبيق الحقيقي ستكون اتصال حقيقي
    const mockWebSocket = {
      readyState: WebSocket.OPEN,
      close: () => {},
      send: () => {},
    };

    wsRef.current = mockWebSocket as unknown as WebSocket;
    setConnectionStatus('connected');
    
    toast({
      title: "✅ تم الاتصال",
      description: "تم الاتصال بالخادم للتحديث الفوري",
    });

    // تحديث دوري كل ثانية (محاكاة WebSocket)
    const interval = setInterval(async () => {
      if (connectionStatus === 'connected') {
        try {
          // جلب بيانات محدثة (في الواقع ستأتي من WebSocket)
          const { data: updatedStocks } = await supabase
            .from('stocks_realtime')
            .select('*')
            .not('symbol', 'like', '*EGX*')
            .gt('current_price', 0)
            .limit(150);

          if (updatedStocks) {
            updateStockData(updatedStocks);
          }
        } catch (error) {
          console.error('❌ WebSocket update error:', error);
        }
      }
    }, 2000); // كل ثانيتين للاختبار

    return () => {
      clearInterval(interval);
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [connectionStatus, updateStockData, toast]);

  // إعادة الاتصال عند انقطاع الاتصال
  const reconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    reconnectTimeoutRef.current = setTimeout(() => {
      console.log('🔄 Attempting to reconnect...');
      initializeWebSocket();
    }, 3000);
  }, [initializeWebSocket]);

  // تهيئة البيانات الأولية
  useEffect(() => {
    if (initialData && initialData.length > 0) {
      updateStockData(initialData);
      initializeWebSocket();
    }
  }, [initialData, updateStockData, initializeWebSocket]);
  // تنظيف الموارد
  useEffect(() => {
    const timers = flashTimers.current;
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      timers.forEach(timer => clearTimeout(timer));
      timers.clear();
    };
  }, []);

  return {
    stocks,
    marketStats,
    isLoading,
    error: initialError,
    refetch,
    connectionStatus,
    reconnect,
    updatesCount,
  };
};
