import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Activity, Zap, Sparkles, Award } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
}

const StockTicker = React.memo(() => {
  const [highlightedStock, setHighlightedStock] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  
  // Fetch real stock data from Supabase - Best performers of the session
  const { data: stocksData, isLoading, error } = useQuery({
    queryKey: ['stocks_realtime_best_performers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select(`
          symbol,
          current_price,
          change_amount,
          change_percent,
          volume
        `)
        .not('symbol', 'like', '*EGX*') // Exclude market indices
        .gt('volume', 1000) // Only stocks with reasonable volume
        .gt('change_percent', 0) // Only gaining stocks
        .order('change_percent', { ascending: false }) // Best performers first
        .limit(8); // Show top 8 best performers
      
      if (error) throw error;
      return data;
    },
    refetchInterval: 15000, // Refetch every 15 seconds for best performers
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  // Memoize transformed data to prevent unnecessary recalculations
  const stocks: StockData[] = React.useMemo(() => 
    stocksData?.map(stock => ({
      symbol: stock.symbol,
      name: stock.symbol, // You can enhance this by joining with stocks_master table
      price: stock.current_price || 0,
      change: stock.change_amount || 0,
      changePercent: stock.change_percent || 0,
      volume: stock.volume || 0,
    })) || [], [stocksData]
  );

  // Memoize formatters to prevent recreation on each render
  const formatNumber = React.useCallback((num: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  }, []);

  const formatVolume = React.useCallback((volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    }
    return `${(volume / 1000).toFixed(0)}K`;
  }, []);

  // Set up real-time subscription for live updates
  useEffect(() => {
    setIsLoaded(true);
    
    // Subscribe to real-time stock updates
    const subscription = supabase
      .channel('stock_prices')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'stocks_realtime'
        },
        (payload) => {
          // Highlight stock when updated
          const updatedSymbol = payload.new.symbol;
          if (Math.abs(payload.new.change_percent) > 3) {
            setHighlightedStock(updatedSymbol);
            setTimeout(() => setHighlightedStock(null), 2000);
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  if (isLoading) {
    return (
      <div className="w-full bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-egx-gold-500/20 shadow-lg relative overflow-hidden h-16">
        <div className="container mx-auto px-4 py-2 h-full">
          <div className="flex items-center gap-4 text-white h-full">
            <Activity className="w-4 h-4 animate-pulse" />
            <span className="text-sm">جاري تحميل البيانات...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error || stocks.length === 0) {
    return (
      <div className="w-full bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-egx-gold-500/20 shadow-lg relative overflow-hidden h-16">
        <div className="container mx-auto px-4 py-2 h-full">
          <div className="flex items-center gap-4 text-red-400 h-full">
            <Activity className="w-4 h-4" />
            <span className="text-sm">خطأ في تحميل البيانات</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-egx-gold-500/20 shadow-lg relative overflow-hidden h-16">
      {/* Subtle background effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-egx-gold-500/3 via-egx-gold-400/5 to-egx-gold-500/3 opacity-50"></div>
      
      <div className="container mx-auto px-4 py-2 relative h-full">
        <div className="flex items-center gap-4 h-full">
          {/* Compact EGX Live section */}
          <div className="flex-shrink-0">
            <div className="flex items-center gap-2 text-white font-bold whitespace-nowrap glass-effect px-3 py-1 rounded-full bg-gradient-to-r from-egx-gold-600 to-egx-gold-500 shadow-lg border border-egx-gold-400/50">
              <Award className="w-4 h-4 text-yellow-300 animate-pulse" />
              <Activity className="w-4 h-4 animate-pulse text-white" />
              <span className="text-sm font-bold">EGX Live - أفضل الأداء</span>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </div>
          
          {/* Compact scrolling stocks container */}
          <div className="flex-1 overflow-hidden relative">
            <div className="flex items-center gap-3 animate-scroll-right">
              {/* Display limited stocks for compact view */}
              {[...stocks.slice(0, 6), ...stocks.slice(0, 6)].map((stock, index) => (
                <div 
                  key={`${stock.symbol}-${index}`} 
                  className={`flex items-center gap-2 px-3 py-1 min-w-[180px] border transition-all duration-300 bg-gradient-to-r backdrop-blur-sm relative overflow-hidden shrink-0 rounded-lg
                    ${stock.change >= 0 
                      ? 'border-green-400/30 from-green-900/20 to-green-800/10 text-green-300' 
                      : 'border-red-400/30 from-red-900/20 to-red-800/10 text-red-300'
                    } ${highlightedStock === stock.symbol ? 'ring-1 ring-egx-gold-400/50 animate-pulse' : ''}`}
                >
                  {/* Compact stock information */}
                  <div className="flex items-center gap-2 flex-1">
                    <span className="font-bold text-sm text-white">
                      {stock.symbol}
                    </span>
                    <span className={`text-sm font-semibold ${
                      stock.change >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {formatNumber(stock.price)}
                    </span>
                    <div className={`flex items-center gap-1 text-xs ${
                      stock.change >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {stock.change >= 0 ? 
                        <TrendingUp className="w-3 h-3" /> : 
                        <TrendingDown className="w-3 h-3" />
                      }
                      <span>{formatNumber(Math.abs(stock.changePercent))}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

StockTicker.displayName = 'StockTicker';

export default StockTicker;
