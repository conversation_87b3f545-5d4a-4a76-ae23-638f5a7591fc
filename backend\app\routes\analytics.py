from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_, or_, text
from datetime import datetime, date, timedelta
from decimal import Decimal

from ..database import get_db
from ..models.stocks import StockMaster
from ..models.realtime import StockRealtime
from ..models.historical import StockHistorical
from ..models.financial import StockFinancial
from ..models.schemas import ResponseBase
from pydantic import BaseModel, Field

# Pydantic models for analytics
class MarketOverview(BaseModel):
    """Market overview statistics"""
    total_stocks: int = Field(..., description="Total number of stocks")
    active_stocks: int = Field(..., description="Number of active stocks")
    suspended_stocks: int = Field(..., description="Number of suspended stocks")
    market_cap_total: Optional[Decimal] = Field(None, description="Total market capitalization")
    avg_change_percent: Optional[float] = Field(None, description="Average price change percentage")
    gainers_count: int = Field(default=0, description="Number of gaining stocks")
    losers_count: int = Field(default=0, description="Number of losing stocks")
    unchanged_count: int = Field(default=0, description="Number of unchanged stocks")
    total_volume: Optional[Decimal] = Field(None, description="Total trading volume")
    last_updated: Optional[datetime] = Field(None, description="Last update time")
    
    class Config:
        from_attributes = True


class SectorPerformance(BaseModel):
    """Sector performance statistics"""
    sector: str = Field(..., description="Sector name")
    stock_count: int = Field(..., description="Number of stocks in sector")
    avg_price: Optional[float] = Field(None, description="Average stock price")
    avg_change_percent: Optional[float] = Field(None, description="Average change percentage")
    total_volume: Optional[Decimal] = Field(None, description="Total sector volume")
    market_cap: Optional[Decimal] = Field(None, description="Sector market capitalization")
    gainers: int = Field(default=0, description="Number of gaining stocks")
    losers: int = Field(default=0, description="Number of losing stocks")
    
    class Config:
        from_attributes = True


class StockPerformanceMetrics(BaseModel):
    """Stock performance metrics"""
    symbol: str
    name_ar: str
    current_price: Optional[float] = None
    change_percent: Optional[float] = None
    volume: Optional[Decimal] = None
    market_cap: Optional[Decimal] = None
    
    # Technical indicators
    rsi: Optional[float] = None
    moving_avg_20: Optional[float] = None
    moving_avg_50: Optional[float] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    
    # Performance metrics
    volatility_30d: Optional[float] = None
    performance_1d: Optional[float] = None
    performance_7d: Optional[float] = None
    performance_30d: Optional[float] = None
    performance_ytd: Optional[float] = None
    
    class Config:
        from_attributes = True


class TradingStatistics(BaseModel):
    """Trading statistics"""
    date: date
    total_trades: Optional[int] = None
    total_volume: Optional[Decimal] = None
    total_value: Optional[Decimal] = None
    avg_trade_size: Optional[float] = None
    active_stocks: int = Field(default=0, description="Number of actively traded stocks")
    
    class Config:
        from_attributes = True


# Create router
router = APIRouter()


@router.get("/market-overview", response_model=ResponseBase)
async def get_market_overview(db: Session = Depends(get_db)):
    """Get comprehensive market overview statistics"""
    try:
        # Get basic stock counts
        total_stocks = db.query(StockMaster).count()
        active_stocks = db.query(StockMaster).filter(StockMaster.is_active == True).count()
        suspended_stocks = db.query(StockMaster).filter(StockMaster.is_suspended == True).count()
        
        # Get latest realtime data statistics
        realtime_stats = db.query(
            func.avg(StockRealtime.change_percent).label('avg_change'),
            func.sum(StockRealtime.volume).label('total_volume'),
            func.count().label('total_records'),
            func.sum(func.case([(StockRealtime.change_percent > 0, 1)], else_=0)).label('gainers'),
            func.sum(func.case([(StockRealtime.change_percent < 0, 1)], else_=0)).label('losers'),
            func.sum(func.case([(StockRealtime.change_percent == 0, 1)], else_=0)).label('unchanged')
        ).first()
        
        # Get latest update time
        latest_update = db.query(func.max(StockRealtime.updated_at)).scalar()
        
        overview = MarketOverview(
            total_stocks=total_stocks,
            active_stocks=active_stocks,
            suspended_stocks=suspended_stocks,
            avg_change_percent=float(realtime_stats.avg_change) if realtime_stats.avg_change else None,
            gainers_count=int(realtime_stats.gainers) if realtime_stats.gainers else 0,
            losers_count=int(realtime_stats.losers) if realtime_stats.losers else 0,
            unchanged_count=int(realtime_stats.unchanged) if realtime_stats.unchanged else 0,
            total_volume=realtime_stats.total_volume,
            last_updated=latest_update
        )
        
        return ResponseBase(
            success=True,
            message="Retrieved market overview",
            data=overview
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving market overview: {str(e)}")


@router.get("/sector-performance", response_model=ResponseBase)
async def get_sector_performance(db: Session = Depends(get_db)):
    """Get performance statistics by sector"""
    try:
        # Query sector performance
        sector_stats = db.query(
            StockMaster.sector,
            func.count(StockMaster.symbol).label('stock_count'),
            func.avg(StockRealtime.current_price).label('avg_price'),
            func.avg(StockRealtime.change_percent).label('avg_change'),
            func.sum(StockRealtime.volume).label('total_volume'),
            func.sum(func.case([(StockRealtime.change_percent > 0, 1)], else_=0)).label('gainers'),
            func.sum(func.case([(StockRealtime.change_percent < 0, 1)], else_=0)).label('losers')
        ).outerjoin(
            StockRealtime, StockMaster.symbol == StockRealtime.symbol
        ).filter(
            StockMaster.sector.isnot(None)
        ).group_by(
            StockMaster.sector
        ).order_by(
            desc(func.count(StockMaster.symbol))
        ).all()
        
        sectors = []
        for stat in sector_stats:
            sectors.append(SectorPerformance(
                sector=stat.sector,
                stock_count=stat.stock_count,
                avg_price=float(stat.avg_price) if stat.avg_price else None,
                avg_change_percent=float(stat.avg_change) if stat.avg_change else None,
                total_volume=stat.total_volume,
                gainers=int(stat.gainers) if stat.gainers else 0,
                losers=int(stat.losers) if stat.losers else 0
            ))
        
        return ResponseBase(
            success=True,
            message=f"Retrieved performance data for {len(sectors)} sectors",
            data=sectors
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving sector performance: {str(e)}")


@router.get("/top-performers", response_model=ResponseBase)
async def get_top_performers(
    metric: str = Query("change_percent", description="Performance metric (change_percent, volume, market_cap)"),
    limit: int = Query(10, ge=1, le=50, description="Number of top performers to return"),
    period: str = Query("1d", description="Time period (1d, 7d, 30d, ytd)"),
    db: Session = Depends(get_db)
):
    """Get top performing stocks by various metrics"""
    try:
        valid_metrics = ['change_percent', 'volume', 'market_cap', 'price']
        if metric not in valid_metrics:
            raise HTTPException(status_code=400, detail=f"Invalid metric. Valid options: {', '.join(valid_metrics)}")
        
        # Base query joining stocks with realtime data
        query = db.query(
            StockMaster.symbol,
            StockMaster.name_ar,
            StockRealtime.current_price,
            StockRealtime.change_percent,
            StockRealtime.volume,
            StockRealtime.market_cap
        ).join(
            StockRealtime, StockMaster.symbol == StockRealtime.symbol
        ).filter(
            StockMaster.is_active == True,
            StockRealtime.current_price.isnot(None)
        )
        
        # Order by the specified metric
        if metric == "change_percent":
            query = query.order_by(desc(StockRealtime.change_percent))
        elif metric == "volume":
            query = query.order_by(desc(StockRealtime.volume))
        elif metric == "market_cap":
            query = query.order_by(desc(StockRealtime.market_cap))
        elif metric == "price":
            query = query.order_by(desc(StockRealtime.current_price))
        
        performers = query.limit(limit).all()
        
        results = []
        for p in performers:
            results.append({
                "symbol": p.symbol,
                "name_ar": p.name_ar,
                "current_price": float(p.current_price) if p.current_price else None,
                "change_percent": float(p.change_percent) if p.change_percent else None,
                "volume": p.volume,
                "market_cap": p.market_cap
            })
        
        return ResponseBase(
            success=True,
            message=f"Retrieved top {len(results)} performers by {metric}",
            data=results
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving top performers: {str(e)}")


@router.get("/trading-statistics", response_model=ResponseBase)
async def get_trading_statistics(
    start_date: Optional[date] = Query(None, description="Start date for statistics"),
    end_date: Optional[date] = Query(None, description="End date for statistics"),
    db: Session = Depends(get_db)
):
    """Get trading statistics for a date range"""
    try:
        # Default to last 30 days if no dates provided
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # Query historical data for trading statistics
        stats = db.query(
            StockHistorical.trade_date,
            func.count(StockHistorical.symbol).label('active_stocks'),
            func.sum(StockHistorical.volume).label('total_volume'),
            func.sum(StockHistorical.volume * StockHistorical.close_price).label('total_value')
        ).filter(
            and_(
                StockHistorical.trade_date >= start_date,
                StockHistorical.trade_date <= end_date,
                StockHistorical.volume > 0
            )
        ).group_by(
            StockHistorical.trade_date
        ).order_by(
            desc(StockHistorical.trade_date)
        ).all()
        
        results = []
        for stat in stats:
            avg_trade_size = None
            if stat.total_volume and stat.active_stocks:
                avg_trade_size = float(stat.total_volume) / stat.active_stocks
                
            results.append(TradingStatistics(
                date=stat.trade_date,
                total_volume=stat.total_volume,
                total_value=stat.total_value,
                avg_trade_size=avg_trade_size,
                active_stocks=stat.active_stocks
            ))
        
        return ResponseBase(
            success=True,
            message=f"Retrieved trading statistics for {len(results)} days",
            data=results
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving trading statistics: {str(e)}")


@router.get("/correlation-matrix", response_model=ResponseBase)
async def get_correlation_matrix(
    symbols: str = Query(..., description="Comma-separated list of stock symbols"),
    period: int = Query(30, ge=7, le=365, description="Number of days for correlation calculation"),
    db: Session = Depends(get_db)
):
    """Calculate correlation matrix for selected stocks"""
    try:
        symbol_list = [s.strip().upper() for s in symbols.split(",")]
        
        if len(symbol_list) > 20:
            raise HTTPException(status_code=400, detail="Maximum 20 stocks allowed for correlation matrix")
        
        # Calculate start date
        end_date = date.today()
        start_date = end_date - timedelta(days=period)
        
        # Get historical data for all symbols
        historical_data = db.query(
            StockHistorical.symbol,
            StockHistorical.trade_date,
            StockHistorical.close_price
        ).filter(
            and_(
                StockHistorical.symbol.in_(symbol_list),
                StockHistorical.trade_date >= start_date,
                StockHistorical.trade_date <= end_date,
                StockHistorical.close_price.isnot(None)
            )
        ).order_by(
            StockHistorical.symbol,
            StockHistorical.trade_date
        ).all()
        
        # Organize data by symbol
        price_data = {}
        for row in historical_data:
            if row.symbol not in price_data:
                price_data[row.symbol] = []
            price_data[row.symbol].append({
                'date': row.trade_date,
                'price': float(row.close_price)
            })
        
        # Calculate daily returns for each stock
        returns_data = {}
        for symbol, prices in price_data.items():
            if len(prices) < 2:
                continue
                
            returns = []
            for i in range(1, len(prices)):
                prev_price = prices[i-1]['price']
                curr_price = prices[i]['price']
                if prev_price > 0:
                    daily_return = (curr_price - prev_price) / prev_price
                    returns.append(daily_return)
            
            if returns:
                returns_data[symbol] = returns
        
        # Simple correlation calculation (Pearson correlation coefficient)
        correlations = {}
        for symbol1 in returns_data:
            correlations[symbol1] = {}
            for symbol2 in returns_data:
                if symbol1 == symbol2:
                    correlations[symbol1][symbol2] = 1.0
                else:
                    # Calculate correlation between two return series
                    returns1 = returns_data[symbol1]
                    returns2 = returns_data[symbol2]
                    
                    # Align data lengths
                    min_len = min(len(returns1), len(returns2))
                    if min_len < 5:  # Need minimum data points
                        correlations[symbol1][symbol2] = None
                        continue
                    
                    r1 = returns1[:min_len]
                    r2 = returns2[:min_len]
                    
                    # Calculate correlation
                    n = len(r1)
                    sum_r1 = sum(r1)
                    sum_r2 = sum(r2)
                    sum_r1_sq = sum(x**2 for x in r1)
                    sum_r2_sq = sum(x**2 for x in r2)
                    sum_r1_r2 = sum(x*y for x, y in zip(r1, r2))
                    
                    denominator = ((n * sum_r1_sq - sum_r1**2) * (n * sum_r2_sq - sum_r2**2)) ** 0.5
                    if denominator == 0:
                        correlations[symbol1][symbol2] = None
                    else:
                        correlation = (n * sum_r1_r2 - sum_r1 * sum_r2) / denominator
                        correlations[symbol1][symbol2] = round(correlation, 4)
        
        return ResponseBase(
            success=True,
            message=f"Calculated correlation matrix for {len(correlations)} stocks over {period} days",
            data={
                "correlations": correlations,
                "symbols": list(correlations.keys()),
                "period_days": period,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating correlation matrix: {str(e)}")


@router.get("/volatility-ranking", response_model=ResponseBase)
async def get_volatility_ranking(
    period: int = Query(30, ge=7, le=365, description="Number of days for volatility calculation"),
    limit: int = Query(20, ge=1, le=100, description="Number of stocks to return"),
    order: str = Query("desc", description="Sort order (asc or desc)"),
    db: Session = Depends(get_db)
):
    """Get stocks ranked by volatility (standard deviation of returns)"""
    try:
        # Calculate start date
        end_date = date.today()
        start_date = end_date - timedelta(days=period)
        
        # Get active stocks
        active_stocks = db.query(StockMaster.symbol, StockMaster.name_ar).filter(
            StockMaster.is_active == True
        ).all()
        
        volatility_results = []
        
        for stock in active_stocks:
            # Get historical prices for the stock
            prices = db.query(StockHistorical.close_price).filter(
                and_(
                    StockHistorical.symbol == stock.symbol,
                    StockHistorical.trade_date >= start_date,
                    StockHistorical.trade_date <= end_date,
                    StockHistorical.close_price.isnot(None)
                )
            ).order_by(StockHistorical.trade_date).all()
            
            if len(prices) < 5:  # Need minimum data points
                continue
            
            # Calculate daily returns
            returns = []
            for i in range(1, len(prices)):
                prev_price = float(prices[i-1].close_price)
                curr_price = float(prices[i].close_price)
                if prev_price > 0:
                    daily_return = (curr_price - prev_price) / prev_price
                    returns.append(daily_return)
            
            if len(returns) < 5:
                continue
            
            # Calculate volatility (standard deviation of returns)
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = variance ** 0.5
            
            # Annualize volatility (assuming 252 trading days per year)
            annualized_volatility = volatility * (252 ** 0.5)
            
            volatility_results.append({
                "symbol": stock.symbol,
                "name_ar": stock.name_ar,
                "volatility": round(annualized_volatility, 6),
                "volatility_percent": round(annualized_volatility * 100, 2),
                "data_points": len(returns),
                "period_days": period
            })
        
        # Sort by volatility
        reverse_order = order.lower() == "desc"
        volatility_results.sort(key=lambda x: x["volatility"], reverse=reverse_order)
        
        # Limit results
        volatility_results = volatility_results[:limit]
        
        return ResponseBase(
            success=True,
            message=f"Retrieved volatility ranking for {len(volatility_results)} stocks",
            data=volatility_results
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating volatility ranking: {str(e)}")


@router.get("/performance-summary/{symbol}", response_model=ResponseBase)
async def get_performance_summary(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Get comprehensive performance summary for a specific stock"""
    try:
        # Verify stock exists
        stock = db.query(StockMaster).filter(StockMaster.symbol == symbol.upper()).first()
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        # Get current data
        current_data = db.query(StockRealtime).filter(
            StockRealtime.symbol == symbol.upper()
        ).first()
        
        # Calculate performance periods
        today = date.today()
        dates = {
            "7d": today - timedelta(days=7),
            "30d": today - timedelta(days=30),
            "90d": today - timedelta(days=90),
            "1y": today - timedelta(days=365)
        }
        
        performance = {}
        
        # Get historical prices for performance calculation
        for period, start_date in dates.items():
            historical_price = db.query(StockHistorical.close_price).filter(
                and_(
                    StockHistorical.symbol == symbol.upper(),
                    StockHistorical.trade_date >= start_date
                )
            ).order_by(StockHistorical.trade_date).first()
            
            if historical_price and current_data and current_data.current_price:
                old_price = float(historical_price.close_price)
                current_price = float(current_data.current_price)
                if old_price > 0:
                    perf = ((current_price - old_price) / old_price) * 100
                    performance[period] = round(perf, 2)
                else:
                    performance[period] = None
            else:
                performance[period] = None
        
        # Get latest financial metrics
        latest_financial = db.query(StockFinancial).filter(
            StockFinancial.symbol == symbol.upper()
        ).order_by(desc(StockFinancial.report_date)).first()
        
        summary = {
            "symbol": symbol.upper(),
            "name_ar": stock.name_ar,
            "current_price": float(current_data.current_price) if current_data and current_data.current_price else None,
            "change_percent": float(current_data.change_percent) if current_data and current_data.change_percent else None,
            "volume": current_data.volume if current_data else None,
            "market_cap": current_data.market_cap if current_data else None,
            "performance": performance,
            "financial_metrics": {
                "pe_ratio": float(latest_financial.pe_ratio) if latest_financial and latest_financial.pe_ratio else None,
                "pb_ratio": float(latest_financial.pb_ratio) if latest_financial and latest_financial.pb_ratio else None,
                "roe": float(latest_financial.roe) if latest_financial and latest_financial.roe else None,
                "dividend_yield": float(latest_financial.dividend_yield) if latest_financial and latest_financial.dividend_yield else None,
                "latest_report_date": latest_financial.report_date.isoformat() if latest_financial and latest_financial.report_date else None
            },
            "last_updated": current_data.updated_at.isoformat() if current_data and current_data.updated_at else None
        }
        
        return ResponseBase(
            success=True,
            message=f"Retrieved comprehensive performance summary for {symbol}",
            data=summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving performance summary for {symbol}: {str(e)}")
