#!/bin/bash

# 🧹 سكريبت تنظيف المشروع
# يقوم بحذف الملفات المؤقتة وإعادة تنظيم الهيكل

echo "🧹 بدء تنظيف المشروع..."

# إنشاء المجلدات المطلوبة إذا لم تكن موجودة
mkdir -p docs/{guides,schemas,completion-reports}
mkdir -p tests
mkdir -p scripts/data_sync

echo "📁 تم إنشاء المجلدات المطلوبة"

# حذف الملفات المؤقتة
echo "🗑️ حذف الملفات المؤقتة..."

# حذف ملفات النظام المؤقتة
find . -name ".DS_Store" -type f -delete 2>/dev/null
find . -name "Thumbs.db" -type f -delete 2>/dev/null
find . -name "*.tmp" -type f -delete 2>/dev/null
find . -name "*.temp" -type f -delete 2>/dev/null
find . -name "*~" -type f -delete 2>/dev/null

# حذف ملفات Python المؤقتة
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null
find . -name "*.pyc" -type f -delete 2>/dev/null
find . -name "*.pyo" -type f -delete 2>/dev/null
find . -name "*.pyd" -type f -delete 2>/dev/null

# حذف ملفات Node.js المؤقتة
rm -f npm-debug.log* 2>/dev/null
rm -f yarn-debug.log* 2>/dev/null
rm -f yarn-error.log* 2>/dev/null

# تنظيف مجلد dist إذا كان موجوداً
if [ -d "dist" ]; then
    echo "🔄 تنظيف مجلد البناء..."
    rm -rf dist/*
fi

echo "✅ تم تنظيف الملفات المؤقتة"

# التحقق من وجود ملفات في المكان الخطأ
echo "🔍 البحث عن ملفات في أماكن خاطئة..."

# البحث عن ملفات التوثيق في الجذر
docs_in_root=$(find . -maxdepth 1 -name "*.md" -not -name "README.md" -not -name "PROJECT_STRUCTURE.md" | wc -l)
if [ $docs_in_root -gt 0 ]; then
    echo "⚠️  وُجدت ملفات توثيق في الجذر، سيتم نقلها..."
    find . -maxdepth 1 -name "*.md" -not -name "README.md" -not -name "PROJECT_STRUCTURE.md" -exec mv {} docs/ \;
fi

# البحث عن ملفات اختبار خارج مجلد tests
test_files=$(find . -maxdepth 1 -name "test_*.py" -o -name "*_test.py" -o -name "check_*.py" | wc -l)
if [ $test_files -gt 0 ]; then
    echo "⚠️  وُجدت ملفات اختبار خارج مجلد tests، سيتم نقلها..."
    find . -maxdepth 1 \( -name "test_*.py" -o -name "*_test.py" -o -name "check_*.py" \) -exec mv {} tests/ \;
fi

# تحديث الفهارس
echo "📋 تحديث فهارس المجلدات..."

# إنشاء فهرس للتوثيق
cat > docs/README.md << 'EOF'
# 📚 فهرس التوثيق

## 📖 الأدلة (Guides)
- [دليل تكامل البيانات](guides/DATA_INTEGRATION_COMPLETE.md)
- [دليل نظام تحديث البيانات](guides/DATA_UPDATE_SYSTEM_GUIDE.md)
- [دليل الشاشة اللحظية](guides/LIVE_TRADING_SCREEN_GUIDE.md)
- [دليل رسوم TradingView](guides/TRADINGVIEW_CHART_GUIDE.md)

## 🗄️ مخططات قاعدة البيانات (Schemas)
- [المخطط الحالي](schemas/CURRENT_DATABASE_SCHEMA.md)
- [دليل قاعدة البيانات](schemas/DATABASE_README.md)
- [خطة المخطط](schemas/DATABASE_SCHEMA_PLAN.md)
- [مرجع JSON](schemas/database_schema_reference.json)

## 📋 تقارير الإكمال (Completion Reports)
- [تقرير EGX30](completion-reports/EGX30_COMPLETION_SUMMARY.md)
- [تقرير الشاشة اللحظية](completion-reports/LIVE_TRADING_COMPLETION_SUMMARY.md)
- [تقرير المرحلة الثالثة](completion-reports/PHASE_3_COMPLETION_SUMMARY.md)

## 📄 ملفات أخرى
- [الميزات المتقدمة](ADVANCED_FEATURES.md)
- [التحليل العميق والتوصيات](DEEP_ANALYSIS_AND_RECOMMENDATIONS.md)
EOF

# إنشاء فهرس للاختبارات
cat > tests/README.md << 'EOF'
# 🧪 فهرس الاختبارات

## 🔍 اختبارات النظام
- `final_system_test.py` - الاختبار النهائي الشامل للنظام
- `final_stocks_test.py` - اختبار بيانات الأسهم النهائية
- `final_success_test.py` - اختبار النجاح النهائي

## 🌐 اختبارات الواجهة الأمامية
- `test_frontend_api.js` - اختبار API الواجهة الأمامية
- `simple_frontend_test.mjs` - اختبار بسيط للواجهة

## 📈 اختبارات البيانات اللحظية
- `test_realtime_updates.py` - اختبار التحديثات اللحظية
- `test_active_stocks.py` - اختبار الأسهم النشطة
- `test_change_percent_fix.py` - اختبار إصلاح نسب التغيير

## 🔧 تشغيل الاختبارات

### اختبارات Python
```bash
cd tests
python final_system_test.py
```

### اختبارات JavaScript
```bash
npm test
```
EOF

echo "📁 تم إنشاء فهارس المجلدات"

# عرض إحصائيات نهائية
echo ""
echo "📊 إحصائيات المشروع بعد التنظيف:"
echo "📚 التوثيق: $(find docs -name "*.md" | wc -l) ملف"
echo "🧪 الاختبارات: $(find tests -name "*.py" -o -name "*.js" -o -name "*.mjs" | wc -l) ملف"
echo "🔧 السكريبتات: $(find scripts -name "*.py" | wc -l) ملف"
echo "⚛️  مكونات React: $(find src -name "*.tsx" -o -name "*.ts" | wc -l) ملف"

echo ""
echo "✅ تم الانتهاء من تنظيف المشروع بنجاح!"
echo "📋 راجع ملف PROJECT_STRUCTURE.md للهيكل الجديد"
