
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  Briefcase, 
  Bell, 
  TrendingUp, 
  Calendar,
  Settings,
  Save,
  RotateCcw,
  Grid3X3,
  Eye
} from 'lucide-react';

interface Widget {
  id: string;
  title: string;
  icon: React.ElementType;
  type: string;
  size: 'small' | 'medium' | 'large';
  enabled: boolean;
  position: { x: number; y: number };
}

const DashboardCustomizer = () => {
  const [editMode, setEditMode] = useState(false);
  const [widgets, setWidgets] = useState<Widget[]>([
    { id: '1', title: 'نظرة عامة على السوق', icon: BarChart3, type: 'market-overview', size: 'large', enabled: true, position: { x: 0, y: 0 } },
    { id: '2', title: 'محفظتي', icon: Briefcase, type: 'portfolio', size: 'medium', enabled: true, position: { x: 1, y: 0 } },
    { id: '3', title: 'أفضل الأسهم', icon: TrendingUp, type: 'top-stocks', size: 'small', enabled: true, position: { x: 2, y: 0 } },
    { id: '4', title: 'التنبيهات', icon: Bell, type: 'alerts', size: 'small', enabled: true, position: { x: 0, y: 1 } },
    { id: '5', title: 'التقويم المالي', icon: Calendar, type: 'calendar', size: 'medium', enabled: false, position: { x: 1, y: 1 } },
  ]);

  const availableWidgets = [
    { type: 'market-overview', title: 'نظرة عامة على السوق', icon: BarChart3 },
    { type: 'portfolio', title: 'محفظتي', icon: Briefcase },
    { type: 'top-stocks', title: 'أفضل الأسهم', icon: TrendingUp },
    { type: 'alerts', title: 'التنبيهات', icon: Bell },
    { type: 'calendar', title: 'التقويم المالي', icon: Calendar },
    { type: 'news', title: 'الأخبار', icon: BarChart3 },
    { type: 'screener', title: 'مرشح الأسهم', icon: Settings },
  ];

  const toggleWidget = (widgetId: string) => {
    setWidgets(widgets.map(widget => 
      widget.id === widgetId 
        ? { ...widget, enabled: !widget.enabled }
        : widget
    ));
  };

  const changeWidgetSize = (widgetId: string, newSize: 'small' | 'medium' | 'large') => {
    setWidgets(widgets.map(widget => 
      widget.id === widgetId 
        ? { ...widget, size: newSize }
        : widget
    ));
  };

  const resetLayout = () => {
    setWidgets(widgets.map((widget, index) => ({
      ...widget,
      position: { x: index % 3, y: Math.floor(index / 3) }
    })));
  };

  const saveLayout = () => {
    localStorage.setItem('dashboard-layout', JSON.stringify(widgets));
    setEditMode(false);
  };

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-violet-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-purple-800">
              <Grid3X3 className="h-5 w-5" />
              تخصيص لوحة التحكم
            </div>
            <div className="flex gap-2">
              <Button
                variant={editMode ? "default" : "outline"}
                size="sm"
                onClick={() => setEditMode(!editMode)}
                className="gap-2"
              >
                {editMode ? <Save className="h-4 w-4" /> : <Settings className="h-4 w-4" />}
                {editMode ? 'حفظ' : 'تعديل'}
              </Button>
              {editMode && (
                <Button variant="outline" size="sm" onClick={resetLayout} className="gap-2">
                  <RotateCcw className="h-4 w-4" />
                  إعادة تعيين
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        {editMode && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableWidgets.map((widget, index) => {
                const isEnabled = widgets.find(w => w.type === widget.type)?.enabled || false;
                const currentWidget = widgets.find(w => w.type === widget.type);
                
                return (
                  <div key={index} className="p-4 bg-white/60 rounded-lg border border-purple-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <widget.icon className="h-4 w-4" />
                        <span className="font-medium">{widget.title}</span>
                      </div>
                      <Button
                        variant={isEnabled ? "default" : "outline"}
                        size="sm"
                        onClick={() => currentWidget && toggleWidget(currentWidget.id)}
                      >
                        {isEnabled ? <Eye className="h-4 w-4" /> : <Eye className="h-4 w-4 opacity-50" />}
                      </Button>
                    </div>
                    
                    {isEnabled && currentWidget && (
                      <div className="flex gap-1">
                        {(['small', 'medium', 'large'] as const).map(size => (
                          <Button
                            key={size}
                            variant={currentWidget.size === size ? "default" : "outline"}
                            size="sm"
                            onClick={() => changeWidgetSize(currentWidget.id, size)}
                            className="text-xs"
                          >
                            {size === 'small' ? 'صغير' : size === 'medium' ? 'متوسط' : 'كبير'}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Dashboard Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            معاينة لوحة التحكم
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {widgets
              .filter(widget => widget.enabled)
              .map(widget => {
                const IconComponent = widget.icon;
                return (
                  <div
                    key={widget.id}
                    className={`
                      p-4 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg border-2 border-blue-200
                      ${widget.size === 'large' ? 'md:col-span-2 lg:col-span-3' : ''}
                      ${widget.size === 'medium' ? 'md:col-span-2 lg:col-span-2' : ''}
                      ${editMode ? 'cursor-move hover:shadow-lg transition-shadow' : ''}
                    `}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{widget.title}</span>
                      </div>
                      <Badge className="text-xs">
                        {widget.size === 'small' ? 'صغير' : widget.size === 'medium' ? 'متوسط' : 'كبير'}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      محتوى العنصر سيظهر هنا...
                    </div>
                  </div>
                );
              })}
          </div>
        </CardContent>
      </Card>

      {/* Layout Templates */}
      <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Grid3X3 className="h-5 w-5" />
            قوالب التخطيط
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { name: 'المتداول النشط', description: 'مخصص للمتداولين اليوميين' },
              { name: 'المستثمر طويل المدى', description: 'مناسب للاستثمار طويل المدى' },
              { name: 'المحلل المالي', description: 'أدوات تحليل متقدمة' }
            ].map((template, index) => (
              <div key={index} className="p-4 bg-white/60 rounded-lg border border-green-200 hover:bg-white/80 transition-colors cursor-pointer">
                <div className="font-medium text-green-800">{template.name}</div>
                <div className="text-sm text-muted-foreground mt-1">{template.description}</div>
                <Button variant="outline" size="sm" className="mt-2 w-full">
                  تطبيق القالب
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardCustomizer;
