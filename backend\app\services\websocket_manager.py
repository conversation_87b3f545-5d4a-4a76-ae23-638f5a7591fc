# WebSocket Manager for Real-time Signal Broadcasting
import asyncio
import json
import logging
from typing import Set, Dict, Any, Optional, List
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

logger = logging.getLogger(__name__)

class WebSocketManager:
    """
    WebSocket manager for real-time communication
    Handles signal broadcasting, market updates, and notifications
    """
    
    def __init__(self):
        # Active connections organized by channel
        self.active_connections: Dict[str, Set[WebSocket]] = {
            'signals': set(),      # Trading signals
            'market': set(),       # Market data updates
            'notifications': set(), # User notifications
            'alerts': set(),       # Price and technical alerts
            'chat': set()          # User chat/discussions
        }
        
        # Connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
        
        # Channel subscriptions per connection
        self.subscriptions: Dict[WebSocket, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, channels: List[str] = None):
        """Accept WebSocket connection and subscribe to channels"""
        try:
            await websocket.accept()
            
            # Default to signals channel if none specified
            if not channels:
                channels = ['signals']
            
            # Add to subscribed channels
            self.subscriptions[websocket] = set(channels)
            
            for channel in channels:
                if channel not in self.active_connections:
                    self.active_connections[channel] = set()
                
                self.active_connections[channel].add(websocket)
            
            # Store connection metadata
            self.connection_metadata[websocket] = {
                'connected_at': datetime.utcnow(),
                'channels': channels,
                'last_activity': datetime.utcnow()
            }
            
            logger.info(f"WebSocket connected to channels: {channels}")
            
            # Send welcome message
            await self.send_personal_message(websocket, {
                'type': 'connection_established',
                'channels': channels,
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error connecting WebSocket: {str(e)}")
            raise
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection from all channels"""
        try:
            # Remove from all channels
            for channel_connections in self.active_connections.values():
                channel_connections.discard(websocket)
            
            # Clean up metadata
            if websocket in self.connection_metadata:
                del self.connection_metadata[websocket]
            
            if websocket in self.subscriptions:
                del self.subscriptions[websocket]
            
            logger.info("WebSocket disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {str(e)}")
    
    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send message to specific WebSocket connection"""
        try:
            message_str = json.dumps(message, ensure_ascii=False, default=str)
            await websocket.send_text(message_str)
            
            # Update last activity
            if websocket in self.connection_metadata:
                self.connection_metadata[websocket]['last_activity'] = datetime.utcnow()
                
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during message send")
            self.disconnect(websocket)
        except Exception as e:
            logger.error(f"Error sending personal message: {str(e)}")
            self.disconnect(websocket)
    
    async def broadcast_to_channel(self, message: str, channel: str):
        """Broadcast message to all connections in a channel"""
        if channel not in self.active_connections:
            logger.warning(f"Channel '{channel}' does not exist")
            return
        
        disconnected = set()
        
        for connection in self.active_connections[channel].copy():
            try:
                await connection.send_text(message)
                
                # Update last activity
                if connection in self.connection_metadata:
                    self.connection_metadata[connection]['last_activity'] = datetime.utcnow()
                    
            except WebSocketDisconnect:
                logger.info("WebSocket disconnected during broadcast")
                disconnected.add(connection)
            except Exception as e:
                logger.error(f"Error broadcasting to connection: {str(e)}")
                disconnected.add(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def broadcast_signal(self, signal_data: Dict[str, Any]):
        """Broadcast trading signal to all signal subscribers"""
        try:
            message = {
                'type': 'trading_signal',
                'data': signal_data,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            message_str = json.dumps(message, ensure_ascii=False, default=str)
            await self.broadcast_to_channel(message_str, 'signals')
            
            logger.info(f"Broadcasted signal for {signal_data.get('stock_code', 'unknown')}")
            
        except Exception as e:
            logger.error(f"Error broadcasting signal: {str(e)}")
    
    async def broadcast_market_update(self, market_data: Dict[str, Any]):
        """Broadcast market data update"""
        try:
            message = {
                'type': 'market_update',
                'data': market_data,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            message_str = json.dumps(message, ensure_ascii=False, default=str)
            await self.broadcast_to_channel(message_str, 'market')
            
        except Exception as e:
            logger.error(f"Error broadcasting market update: {str(e)}")
    
    async def broadcast_price_alert(self, alert_data: Dict[str, Any]):
        """Broadcast price alert"""
        try:
            message = {
                'type': 'price_alert',
                'data': alert_data,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            message_str = json.dumps(message, ensure_ascii=False, default=str)
            await self.broadcast_to_channel(message_str, 'alerts')
            
        except Exception as e:
            logger.error(f"Error broadcasting price alert: {str(e)}")
    
    async def broadcast_notification(self, notification_data: Dict[str, Any]):
        """Broadcast notification to users"""
        try:
            message = {
                'type': 'notification',
                'data': notification_data,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            message_str = json.dumps(message, ensure_ascii=False, default=str)
            await self.broadcast_to_channel(message_str, 'notifications')
            
        except Exception as e:
            logger.error(f"Error broadcasting notification: {str(e)}")
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """Send message to specific user (if we track user connections)"""
        # This would require user authentication and connection tracking
        # For now, we'll broadcast to the appropriate channel
        try:
            message_str = json.dumps(message, ensure_ascii=False, default=str)
            
            # Determine appropriate channel based on message type
            message_type = message.get('type', 'notification')
            
            if message_type.startswith('signal'):
                await self.broadcast_to_channel(message_str, 'signals')
            elif message_type.startswith('alert'):
                await self.broadcast_to_channel(message_str, 'alerts')
            else:
                await self.broadcast_to_channel(message_str, 'notifications')
                
        except Exception as e:
            logger.error(f"Error sending message to user {user_id}: {str(e)}")
    
    def get_connection_count(self, channel: Optional[str] = None) -> int:
        """Get number of active connections"""
        if channel:
            return len(self.active_connections.get(channel, set()))
        else:
            # Return total unique connections
            all_connections = set()
            for channel_connections in self.active_connections.values():
                all_connections.update(channel_connections)
            return len(all_connections)
    
    def get_channel_stats(self) -> Dict[str, int]:
        """Get statistics for all channels"""
        return {
            channel: len(connections)
            for channel, connections in self.active_connections.items()
        }
    
    async def ping_connections(self):
        """Send ping to all connections to keep them alive"""
        ping_message = {
            'type': 'ping',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        message_str = json.dumps(ping_message, ensure_ascii=False)
        
        for channel in self.active_connections:
            await self.broadcast_to_channel(message_str, channel)
    
    async def cleanup_stale_connections(self, timeout_minutes: int = 30):
        """Remove stale connections that haven't been active"""
        try:
            from datetime import timedelta
            
            cutoff_time = datetime.utcnow() - timedelta(minutes=timeout_minutes)
            stale_connections = []
            
            for connection, metadata in self.connection_metadata.items():
                if metadata['last_activity'] < cutoff_time:
                    stale_connections.append(connection)
            
            for connection in stale_connections:
                logger.info("Removing stale WebSocket connection")
                self.disconnect(connection)
            
            if stale_connections:
                logger.info(f"Cleaned up {len(stale_connections)} stale connections")
                
        except Exception as e:
            logger.error(f"Error cleaning up stale connections: {str(e)}")
    
    async def handle_client_message(self, websocket: WebSocket, message: str):
        """Handle incoming message from client"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'subscribe':
                # Handle channel subscription
                channels = data.get('channels', [])
                await self._handle_subscription(websocket, channels)
                
            elif message_type == 'unsubscribe':
                # Handle channel unsubscription
                channels = data.get('channels', [])
                await self._handle_unsubscription(websocket, channels)
                
            elif message_type == 'pong':
                # Handle pong response
                if websocket in self.connection_metadata:
                    self.connection_metadata[websocket]['last_activity'] = datetime.utcnow()
                    
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received from client")
        except Exception as e:
            logger.error(f"Error handling client message: {str(e)}")
    
    async def _handle_subscription(self, websocket: WebSocket, channels: List[str]):
        """Handle channel subscription request"""
        try:
            for channel in channels:
                if channel not in self.active_connections:
                    self.active_connections[channel] = set()
                
                self.active_connections[channel].add(websocket)
                
                if websocket not in self.subscriptions:
                    self.subscriptions[websocket] = set()
                
                self.subscriptions[websocket].add(channel)
            
            # Send confirmation
            await self.send_personal_message(websocket, {
                'type': 'subscription_confirmed',
                'channels': channels,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            logger.info(f"WebSocket subscribed to channels: {channels}")
            
        except Exception as e:
            logger.error(f"Error handling subscription: {str(e)}")
    
    async def _handle_unsubscription(self, websocket: WebSocket, channels: List[str]):
        """Handle channel unsubscription request"""
        try:
            for channel in channels:
                if channel in self.active_connections:
                    self.active_connections[channel].discard(websocket)
                
                if websocket in self.subscriptions:
                    self.subscriptions[websocket].discard(channel)
            
            # Send confirmation
            await self.send_personal_message(websocket, {
                'type': 'unsubscription_confirmed',
                'channels': channels,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            logger.info(f"WebSocket unsubscribed from channels: {channels}")
            
        except Exception as e:
            logger.error(f"Error handling unsubscription: {str(e)}")

# Global WebSocket manager instance
signal_manager = WebSocketManager()
