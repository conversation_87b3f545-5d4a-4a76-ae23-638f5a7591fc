-- Enhanced EGX Stock AI Oracle - Financial Data Schema Update
-- Execute this script in Supabase SQL editor

ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS industry VARCHAR(200);
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS isin VARCHAR(20);
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS index_membership VARCHAR(100);
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS market_cap_currency VARCHAR(10);
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS total_shares_outstanding BIGINT;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS float_shares_outstanding BIGINT;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS float_shares_percent DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS number_of_shareholders INTEGER;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS assets_to_equity DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS assets_turnover DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS cash_ratio DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS cash_to_debt_ratio DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS debt_to_assets DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS price_to_cash_flow DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS price_to_free_cash_flow DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS price_to_sales DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS enterprise_value DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS ev_to_ebitda DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS ev_to_revenue DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS total_revenue DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS gross_profit DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS operating_income DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS net_income DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS ebitda DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS total_equity DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS total_liabilities DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS total_current_assets DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS total_current_liabilities DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS cash_and_equivalents DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS cash_short_term_investments DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS net_debt DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS goodwill DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS operating_cash_flow DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS investing_cash_flow DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS financing_cash_flow DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS capital_expenditures DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS eps_basic_ttm DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS eps_diluted_ttm DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS eps_reported_annual DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS eps_estimate_quarterly DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS eps_growth_ttm DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS revenue_growth DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS net_income_growth DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS ebitda_growth DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS gross_profit_growth DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS free_cash_flow_growth DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS capex_growth DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS total_assets_growth DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS dividend_yield_indicated DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS dividend_payout_ratio DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS dividends_per_share DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS dividends_paid_annual DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS continuous_dividend_growth INTEGER;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS forward_pe DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS target_price_1y DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS target_performance_1y DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS performance_ytd DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS beta_5_years DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS average_volume_10d BIGINT;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS relative_volume DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS volatility_1_day DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS volume_change DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS turnover_1_day DECIMAL;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS recent_earnings_date DATE;
ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS upcoming_earnings_date DATE;

-- Create index on symbol for better performance
CREATE INDEX IF NOT EXISTS idx_stocks_financials_symbol ON stocks_financials(symbol);

-- Update RLS policies
ALTER TABLE stocks_financials ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Enable read access for all users" ON stocks_financials;
CREATE POLICY "Enable read access for all users" ON stocks_financials FOR SELECT USING (true);