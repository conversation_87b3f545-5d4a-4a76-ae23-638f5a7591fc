import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Structure for data coming from Supabase
export interface EgxIndex {
  symbol: string;
  name: string;
  current_value: number;
  change_amount: number;
  change_percent: number;
}

export interface MarketOverviewData {
  egx30: EgxIndex | null;
  egx70: EgxIndex | null;
  totalVolume: number | null;
  totalValue: number | null;
  gainers: number | null;
  losers: number | null;
  unchanged: number | null;
}

export function useMarketOverview() {
  return useQuery<MarketOverviewData>({
    queryKey: ['market_overview'],
    queryFn: async () => {
      // Fetch main indices data (egx30, egx70)
      const { data: indices, error: indicesError } = await supabase
        .from('market_indices')
        .select('*')
        .in('symbol', ['EGX30', 'EGX70']);

      if (indicesError) throw indicesError;

      // Find index data
      const findIndexData = (symbol: string) => (indices ?? []).find(idx => idx.symbol === symbol) || null;
      const egx30 = findIndexData('EGX30');
      const egx70 = findIndexData('EGX70');      // Fetch real-time stocks data to calculate actual market distribution
      const { data: stocks, error: stocksError } = await supabase
        .from('stocks_realtime')
        .select('symbol, change_percent')
        .not('symbol', 'like', '*EGX*'); // Exclude market indices

      if (stocksError) {
        console.warn('Could not fetch stocks for market distribution:', stocksError);
      }

      // Calculate real market distribution
      let gainers = 0;
      let losers = 0;
      let unchanged = 0;

      if (stocks && stocks.length > 0) {
        stocks.forEach(stock => {
          const changePercent = Number(stock.change_percent || 0);
          if (changePercent > 0) {
            gainers++;
          } else if (changePercent < 0) {
            losers++;
          } else {
            unchanged++;
          }
        });
      } else {
        // Fallback to default values if no stock data available
        gainers = 18;
        losers = 9;
        unchanged = 3;
      }

      return {
        egx30: egx30
          ? {
              symbol: egx30.symbol,
              name: egx30.name,
              current_value: Number(egx30.current_value ?? 0),
              change_amount: Number(egx30.change_amount ?? 0),
              change_percent: Number(egx30.change_percent ?? 0)
            }
          : null,
        egx70: egx70
          ? {
              symbol: egx70.symbol,
              name: egx70.name,
              current_value: Number(egx70.current_value ?? 0),
              change_amount: Number(egx70.change_amount ?? 0),
              change_percent: Number(egx70.change_percent ?? 0)
            }
          : null,
        totalVolume: egx30?.volume ? Number(egx30.volume) / 1000000000 : null, // Convert to billions
        totalValue: egx30?.turnover ? Number(egx30.turnover) / 1000000 : null, // Convert to millions
        gainers,
        losers,
        unchanged,
      };
    },
    refetchInterval: 10000,
    staleTime: 9000,
  });
}
