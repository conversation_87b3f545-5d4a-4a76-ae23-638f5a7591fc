from sqlalchemy import Column, String, Numeric, DateTime, Date, ForeignKey, Integer, Text
from sqlalchemy.sql import func
from ..database import Base


class StockFinancial(Base):
    """Financial statements and ratios model"""
    __tablename__ = "stocks_financials"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(15), ForeignKey("stocks_master.symbol"), index=True, nullable=False)
    
    # Report metadata
    report_date = Column(Date, nullable=False, index=True)
    report_period = Column(String(20))  # Q1, Q2, Q3, Q4, Annual
    report_year = Column(Integer, index=True)
    currency = Column(String(10), default="EGP")
    
    # Income Statement (in thousands)
    revenue = Column(Numeric(20, 2))
    gross_profit = Column(Numeric(20, 2))
    operating_profit = Column(Numeric(20, 2))
    net_profit = Column(Numeric(20, 2))
    ebitda = Column(Numeric(20, 2))
    
    # Balance Sheet (in thousands)
    total_assets = Column(Numeric(20, 2))
    total_liabilities = Column(Numeric(20, 2))
    shareholders_equity = Column(Numeric(20, 2))
    cash_and_equivalents = Column(Numeric(20, 2))
    total_debt = Column(Numeric(20, 2))
    
    # Cash Flow (in thousands)
    operating_cash_flow = Column(Numeric(20, 2))
    investing_cash_flow = Column(Numeric(20, 2))
    financing_cash_flow = Column(Numeric(20, 2))
    free_cash_flow = Column(Numeric(20, 2))
    
    # Per Share Data
    earnings_per_share = Column(Numeric(8, 4))
    book_value_per_share = Column(Numeric(8, 4))
    dividends_per_share = Column(Numeric(8, 4))
    shares_outstanding = Column(Numeric(15, 0))
    
    # Financial Ratios
    pe_ratio = Column(Numeric(12, 4))
    pb_ratio = Column(Numeric(8, 4))
    roe = Column(Numeric(8, 4))  # Return on Equity
    roa = Column(Numeric(8, 4))  # Return on Assets
    debt_to_equity = Column(Numeric(8, 4))
    current_ratio = Column(Numeric(8, 4))
    quick_ratio = Column(Numeric(8, 4))
    gross_margin = Column(Numeric(8, 4))
    operating_margin = Column(Numeric(8, 4))
    net_margin = Column(Numeric(8, 4))
    
    # Additional metrics
    market_cap = Column(Numeric(20, 2))
    enterprise_value = Column(Numeric(20, 2))
    dividend_yield = Column(Numeric(8, 4))
    payout_ratio = Column(Numeric(8, 4))
    
    # Notes and commentary
    notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Composite index for efficient querying
    __table_args__ = (
        {'schema': None}  # Default schema
    )
