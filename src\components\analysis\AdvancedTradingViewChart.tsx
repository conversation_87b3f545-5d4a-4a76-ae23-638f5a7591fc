import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  Settings, 
  Monitor, 
  Moon,
  Sun,
  Clock,
  TrendingUp,
  Eye,
  PenTool,
  Target
} from 'lucide-react';
import TradingViewChart from './TradingViewChart';

interface AdvancedTradingViewChartProps {
  symbol: string;
  className?: string;
}

const AdvancedTradingViewChart: React.FC<AdvancedTradingViewChartProps> = ({ 
  symbol, 
  className = '' 
}) => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [interval, setInterval] = useState('D');
  const [height, setHeight] = useState(600);

  const intervals = [
    { value: '1', label: '1 دقيقة' },
    { value: '5', label: '5 دقائق' },
    { value: '15', label: '15 دقيقة' },
    { value: '1H', label: '1 ساعة' },
    { value: '4H', label: '4 ساعات' },
    { value: 'D', label: 'يومي' },
    { value: 'W', label: 'أسبوعي' },
    { value: 'M', label: 'شهري' }
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Chart Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              شارت {symbol} الاحترافي
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
                className="flex items-center gap-1"
              >
                {theme === 'light' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                {theme === 'light' ? 'وضع ليلي' : 'وضع نهاري'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            {/* Time Intervals */}
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-600" />
              <span className="text-sm text-gray-600">الإطار الزمني:</span>
              <div className="flex gap-1">
                {intervals.map((int) => (
                  <Button
                    key={int.value}
                    variant={interval === int.value ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setInterval(int.value)}
                    className="text-xs px-2 py-1"
                  >
                    {int.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>      {/* TradingView Chart */}
      <Card>
        <CardContent className="p-0">
          <div className="w-full h-full">            <TradingViewChart 
              symbol={symbol}
              height={height}
              theme={theme}
              interval={interval}
              timezone="Africa/Cairo"
              locale="ar"
              range="12M"
              allow_symbol_change={false}
              hide_side_toolbar={false}
              autosize={false}
            />
          </div>
        </CardContent>
      </Card>

      {/* Chart Features Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <div>
                <h4 className="font-semibold text-blue-800">مؤشرات فنية</h4>
                <p className="text-xs text-blue-600">أكثر من 100 مؤشر</p>
              </div>
            </div>
            <ul className="text-xs text-blue-700 mt-3 space-y-1">
              <li>• RSI, MACD, Stochastic</li>
              <li>• Bollinger Bands, ATR</li>
              <li>• Volume Profile</li>
              <li>• Ichimoku Cloud</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <PenTool className="h-8 w-8 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-800">أدوات الرسم</h4>
                <p className="text-xs text-green-600">أدوات احترافية</p>
              </div>
            </div>
            <ul className="text-xs text-green-700 mt-3 space-y-1">
              <li>• خطوط الاتجاه</li>
              <li>• مستويات فيبوناتشي</li>
              <li>• Elliott Wave Tools</li>
              <li>• Gann Analysis</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Eye className="h-8 w-8 text-purple-600" />
              <div>
                <h4 className="font-semibold text-purple-800">كشف الأنماط</h4>
                <p className="text-xs text-purple-600">تلقائي ومتقدم</p>
              </div>
            </div>
            <ul className="text-xs text-purple-700 mt-3 space-y-1">
              <li>• Head & Shoulders</li>
              <li>• Triangles & Flags</li>
              <li>• Double Top/Bottom</li>
              <li>• Harmonic Patterns</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Target className="h-8 w-8 text-orange-600" />
              <div>
                <h4 className="font-semibold text-orange-800">SMC Analysis</h4>
                <p className="text-xs text-orange-600">Smart Money</p>
              </div>
            </div>
            <ul className="text-xs text-orange-700 mt-3 space-y-1">
              <li>• Order Blocks</li>
              <li>• Fair Value Gaps</li>
              <li>• Market Structure</li>
              <li>• Liquidity Sweeps</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Additional Info */}
      <Card className="bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-semibold text-gray-800 mb-1">شارت احترافي من TradingView</h4>
              <p className="text-sm text-gray-600">
                يوفر جميع أدوات التحليل الفني المتقدمة المستخدمة من قبل المحترفين حول العالم
              </p>
            </div>
            <Badge variant="outline" className="bg-white">
              Professional
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdvancedTradingViewChart;
