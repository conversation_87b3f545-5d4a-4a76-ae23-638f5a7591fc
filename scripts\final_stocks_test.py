#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def format_volume(volume):
    """Format volume for display"""
    if volume is None or volume == 0:
        return "0"
    elif volume >= 1000000:
        return f"{volume/1000000:.1f}M"
    elif volume >= 1000:
        return f"{volume/1000:.0f}K"
    else:
        return str(volume)

def test_final_stock_lists():
    """Final test of all stock lists to ensure they show active stocks only"""
    print("🎯 اختبار نهائي - قوائم الأسهم النشطة فقط")
    print("=" * 60)
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}"
    }
    
    tests = [
        {
            "name": "🎯 أكثر الأسهم تداولاً (النهائي)",
            "query": "stocks_realtime?select=symbol,volume&symbol=not.like.*EGX*&volume=gt.0&order=volume.desc&limit=3",
            "check_field": "volume"
        },
        {
            "name": "📈 شريط الأسعار (النهائي)",
            "query": "stocks_realtime?select=symbol,volume,current_price&symbol=not.like.*EGX*&volume=gt.0&order=volume.desc&limit=5",
            "check_field": "volume"
        },
        {
            "name": "💰 الفرص الاستثمارية (النهائي)",
            "query": "stocks_realtime?select=symbol,change_percent,volume&symbol=not.like.*EGX*&volume=gt.0&or=(change_percent.gte.2,change_percent.lte.-2)&order=volume.desc&limit=3",
            "check_field": "volume"
        },
        {
            "name": "🔍 أفضل الأسهم أداءً",
            "query": "stocks_realtime?select=symbol,change_percent&symbol=not.like.*EGX*&change_percent=gt.0&order=change_percent.desc&limit=3",
            "check_field": "change_percent"
        }
    ]
    
    all_passed = True
    
    for test in tests:
        print(f"\n{test['name']}")
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/{test['query']}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data:
                    print(f"   ✅ عدد الأسهم: {len(data)}")
                    
                    # Display results
                    for i, stock in enumerate(data, 1):
                        symbol = stock['symbol']
                        
                        if test['check_field'] == 'volume':
                            volume = format_volume(stock.get('volume', 0))
                            price = stock.get('current_price', 'N/A')
                            print(f"   {i}. {symbol}: {volume} ({price} جنيه)")
                        elif test['check_field'] == 'change_percent':
                            change = stock.get('change_percent', 0)
                            print(f"   {i}. {symbol}: +{change}%")
                    
                    # Verify no suspended stocks
                    if test['check_field'] == 'volume':
                        suspended = [s for s in data if s.get('volume', 0) <= 0]
                        if suspended:
                            print(f"   ❌ يحتوي على أسهم متوقفة: {len(suspended)}")
                            all_passed = False
                        else:
                            print(f"   ✅ جميع الأسهم نشطة")
                    
                else:
                    print("   ⚠️ لا توجد نتائج")
                    
            else:
                print(f"   ❌ خطأ: {response.status_code}")
                print(f"      {response.text[:100]}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ تم استبعاد الأسهم المتوقفة والمؤشرات")
        print("✅ يتم عرض الأسهم النشطة فقط")
        print("🔄 الفرونت اند جاهز للاستخدام")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("🔧 يحتاج لمراجعة إضافية")

def show_market_summary():
    """Show final market summary"""
    print("\n📊 ملخص السوق النهائي:")
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    try:
        # Count total stocks (excluding indices)
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol&symbol=not.like.*EGX*",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        total_stocks = len(response.json()) if response.status_code == 200 else 0
        
        # Count active stocks
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol&symbol=not.like.*EGX*&volume=gt.0",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        active_stocks = len(response.json()) if response.status_code == 200 else 0
        
        # Count indices
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol&symbol=like.*EGX*",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        indices_count = len(response.json()) if response.status_code == 200 else 0
        
        suspended_stocks = total_stocks - active_stocks
        
        print(f"   📊 إجمالي الأسهم (بدون مؤشرات): {total_stocks}")
        print(f"   ✅ أسهم نشطة: {active_stocks}")
        print(f"   🔴 أسهم متوقفة: {suspended_stocks}")
        print(f"   📈 مؤشرات السوق: {indices_count}")
        print(f"   📱 المعروض في الفرونت اند: {active_stocks} سهم نشط فقط")
        
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")

if __name__ == "__main__":
    show_market_summary()
    test_final_stock_lists()
