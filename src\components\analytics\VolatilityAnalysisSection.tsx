 

/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity } from 'lucide-react';
import { useVolatilityAnalysis } from '@/hooks/useVolatilityAnalysis';

const VolatilityAnalysisSection = () => {
  const { data: volatilityAnalysisArr, isLoading, error } = useVolatilityAnalysis();

  const volatilityAnalysisNew: Record<string, any> = {};
  for (const v of volatilityAnalysisArr ?? []) {
    volatilityAnalysisNew[v.symbol] = {
      current: v.current_volatility,
      average: v.avg_volatility,
      trend: v.trend,
    };
  }

  return (
    <Card className="border-2 border-red-200 bg-gradient-to-br from-red-50 to-pink-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-800">
          <Activity className="h-5 w-5" />
          تحليل التقلبات
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center text-red-800">جاري التحميل...</div>
        ) : error ? (
          <div className="text-center text-red-600">حدث خطأ في التحميل</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(volatilityAnalysisNew).map(([symbol, data]: [string, any], index) => (
              <div key={symbol ?? index} className="p-4 bg-white/60 rounded-lg border border-red-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-bold">{symbol}</span>
                  <Badge className={`${
                    data.trend === 'مرتفع' ? 'bg-red-500' : 'bg-green-500'
                  } text-white`}>
                    {data.trend}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">التقلب الحالي</span>
                    <span className="font-medium">{data.current}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">المتوسط</span>
                    <span className="font-medium">{data.average}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VolatilityAnalysisSection;
