#!/usr/bin/env python3
"""
Quick data status check after import
"""

import os
from datetime import datetime
from supabase import create_client
from dotenv import load_dotenv

load_dotenv()

supabase = create_client(
    os.getenv("SUPABASE_URL"), 
    os.getenv("SUPABASE_SERVICE_ROLE_KEY")
)

def check_data_status():
    print("🔍 EGX Stock AI Oracle - Current Data Status")
    print("=" * 50)
    
    # Historical data summary
    try:
        hist_result = supabase.table("stocks_historical").select("symbol", count="exact").execute()
        hist_count = hist_result.count
        
        symbols_result = supabase.table("stocks_historical").select("symbol").execute()
        unique_symbols = len(set(row['symbol'] for row in symbols_result.data))
        
        print(f"📈 Historical Records: {hist_count:,}")
        print(f"📊 Symbols with History: {unique_symbols}")
        
        # Latest dates
        latest_result = supabase.table("stocks_historical").select("date").order("date", desc=True).limit(1).execute()
        if latest_result.data:
            print(f"📅 Latest Date: {latest_result.data[0]['date']}")
            
    except Exception as e:
        print(f"❌ Error checking historical: {e}")
    
    # Real-time data
    try:
        rt_result = supabase.table("stocks_realtime").select("symbol", count="exact").execute()
        print(f"⚡ Real-time Symbols: {rt_result.count}")
    except Exception as e:
        print(f"❌ Error checking real-time: {e}")
    
    # Financial data
    try:
        fin_result = supabase.table("stocks_financials").select("symbol", count="exact").execute()
        print(f"💰 Financial Records: {fin_result.count}")
    except Exception as e:
        print(f"❌ Error checking financial: {e}")
    
    # Master stocks
    try:
        master_result = supabase.table("stocks_master").select("symbol", count="exact").execute()
        print(f"🏢 Total Stocks: {master_result.count}")
    except Exception as e:
        print(f"❌ Error checking master: {e}")
    
    print("\n✅ Data import appears successful!")
    print("🚀 Ready for advanced analytics and AI features")

if __name__ == "__main__":
    check_data_status()
