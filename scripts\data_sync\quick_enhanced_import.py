#!/usr/bin/env python3
"""
Quick Enhanced Data Import Script
Runs all enhanced loaders in sequence for initial setup
"""

import os
import sys
import logging
import subprocess
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quick_enhanced_import.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_script(script_name, args=None):
    """Run a Python script and return success status"""
    try:
        cmd = [sys.executable, script_name]
        if args:
            cmd.extend(args)
        
        logger.info(f"🚀 Running: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {script_name} completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout[-500:]}")  # Last 500 chars
            return True
        else:
            logger.error(f"❌ {script_name} failed with code {result.returncode}")
            if result.stderr:
                logger.error(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error running {script_name}: {e}")
        return False

def check_file_exists(file_path):
    """Check if required data file exists"""
    if os.path.exists(file_path):
        logger.info(f"✅ Found: {file_path}")
        return True
    else:
        logger.warning(f"❌ Missing: {file_path}")
        return False

def main():
    """Main function to run enhanced import sequence"""
    logger.info("🚀 Starting Quick Enhanced Data Import...")
    logger.info("=" * 60)
    
    # Check for required files
    logger.info("📁 Checking data file availability...")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    excel_path = os.getenv("REALTIME_EXCEL_PATH", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx")
    historical_folder = os.getenv("HISTORICAL_DATA_FOLDER", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2")
    financial_csv = os.getenv("FINANCIAL_CSV_PATH", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv")
    
    files_available = {
        'realtime': check_file_exists(excel_path),
        'historical': check_file_exists(historical_folder),
        'financial': check_file_exists(financial_csv)
    }
    
    # Import sequence
    import_sequence = [
        {
            'name': 'Enhanced Real-time Data',
            'script': 'load_realtime_enhanced.py',
            'required': files_available['realtime'],
            'args': None
        },
        {
            'name': 'Enhanced Financial Data',
            'script': 'load_financials_enhanced.py',
            'required': files_available['financial'],
            'args': ['--validate']
        },
        {
            'name': 'Enhanced Historical Data (Sample)',
            'script': 'load_historical_enhanced.py',
            'required': files_available['historical'],
            'args': ['--symbol', 'COMI']  # Test with one symbol first
        }
    ]
    
    successful_imports = 0
    
    for item in import_sequence:
        logger.info(f"\n📊 Starting {item['name']}...")
        
        if not item['required']:
            logger.warning(f"⏭️  Skipping {item['name']} - required file not found")
            continue
        
        if run_script(item['script'], item['args']):
            successful_imports += 1
            logger.info(f"✅ {item['name']} completed successfully")
        else:
            logger.error(f"❌ {item['name']} failed")
    
    # Run system test
    logger.info(f"\n🧪 Running Enhanced System Test...")
    if run_script('test_enhanced_system.py'):
        logger.info("✅ System test completed")
    else:
        logger.error("❌ System test failed")
    
    # Summary
    logger.info("=" * 60)
    logger.info(f"📋 Import Summary:")
    logger.info(f"   Successful imports: {successful_imports}/{len(import_sequence)}")
    
    if successful_imports == len(import_sequence):
        logger.info("🎉 All enhanced imports completed successfully!")
        logger.info("💡 Next steps:")
        logger.info("   1. Check enhanced_system_report.json for detailed metrics")
        logger.info("   2. Run full historical import: python load_historical_enhanced.py")
        logger.info("   3. Set up scheduler for regular updates")
    else:
        logger.warning("⚠️  Some imports failed. Check logs for details.")
    
    logger.info("📄 Logs saved to: quick_enhanced_import.log")

if __name__ == "__main__":
    main()
