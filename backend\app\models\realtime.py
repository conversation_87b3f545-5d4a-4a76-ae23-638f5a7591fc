from sqlalchemy import Column, String, Numeric, BigInteger, DateTime, Boolean, Integer, ForeignKey
from sqlalchemy.sql import func
from ..database import Base


class StockRealtime(Base):
    """Real-time stock market data model"""
    __tablename__ = "stocks_realtime"
    
    symbol = Column(String(15), ForeignKey("stocks_master.symbol"), primary_key=True, index=True)
    
    # Price data
    open_price = Column(Numeric(15, 4))
    high_price = Column(Numeric(15, 4))
    low_price = Column(Numeric(15, 4))
    current_price = Column(Numeric(15, 4))
    previous_close = Column(Numeric(15, 4))
    change_amount = Column(Numeric(15, 4))
    change_percent = Column(Numeric(8, 4))
    
    # Volume data
    volume = Column(BigInteger)
    turnover = Column(Numeric(20, 4))
    trades_count = Column(Integer)
    
    # Market data
    market_cap = Column(Numeric(20, 4))
    pe_ratio = Column(Numeric(12, 4))
    dividend_yield = Column(Numeric(8, 4))
    eps_annual = Column(Numeric(8, 4))
    book_value = Column(Numeric(8, 4))
    
    # Liquidity data
    liquidity_ratio = Column(Numeric(8, 4))
    liquidity_flow = Column(Numeric(8, 4))
    liquidity_inflow = Column(Numeric(20, 4))
    liquidity_outflow = Column(Numeric(20, 4))
    volume_inflow = Column(BigInteger)
    volume_outflow = Column(BigInteger)
    
    # Technical indicators
    ma5 = Column(Numeric(15, 4))
    ma10 = Column(Numeric(15, 4))
    ma20 = Column(Numeric(15, 4))
    ma50 = Column(Numeric(15, 4))
    ma100 = Column(Numeric(15, 4))
    ma200 = Column(Numeric(15, 4))
    
    tk_indicator = Column(Numeric(12, 4))
    kj_indicator = Column(Numeric(12, 4))
    
    # Trading targets and signals
    target_1 = Column(Numeric(15, 4))
    target_2 = Column(Numeric(15, 4))
    target_3 = Column(Numeric(15, 4))
    stop_loss = Column(Numeric(15, 4))
    stock_status = Column(Integer)
    speculation_opportunity = Column(Boolean, default=False)
    
    # Additional metrics
    price_range = Column(Numeric(8, 4))
    avg_net_volume_3d = Column(Numeric(15, 2))
    avg_net_volume_5d = Column(Numeric(15, 2))
    
    # Timestamp
    last_update = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
