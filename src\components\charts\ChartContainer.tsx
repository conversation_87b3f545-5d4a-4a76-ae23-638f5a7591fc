import React, { Suspense } from 'react';

// Lazy load individual chart components for better code splitting
const RSIChart = React.lazy(() => import('./RSIChart'));
const MACDChart = React.lazy(() => import('./MACDChart'));
const BollingerBandsChart = React.lazy(() => import('./BollingerBandsChart'));
const VolumeChart = React.lazy(() => import('./VolumeChart'));

interface StockData {
  date: string;
  price: number;
  volume: number;
  rsi: number;
  macd: number;
  signal: number;
  bb_upper: number;
  bb_middle: number;
  bb_lower: number;
}

interface ChartContainerProps {
  activeIndicator: string;
  data: StockData[];
}

const ChartContainer: React.FC<ChartContainerProps> = React.memo(({ activeIndicator, data }) => {
  const renderChart = React.useCallback(() => {
    switch (activeIndicator) {
      case 'RSI':
        return <RSIChart data={data} />;
      case 'MACD':
        return <MACDChart data={data} />;
      case 'Bollinger Bands':
        return <BollingerBandsChart data={data} />;
      case 'Volume':
        return <VolumeChart data={data} />;
      default:
        return <RSIChart data={data} />;
    }
  }, [activeIndicator, data]);

  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-[300px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      {renderChart()}
    </Suspense>
  );
});

ChartContainer.displayName = 'ChartContainer';

export default ChartContainer;
