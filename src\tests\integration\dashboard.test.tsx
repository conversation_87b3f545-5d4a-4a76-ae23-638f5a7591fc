import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import Index from '../../pages/Index';
import { createTestQueryClient, TestWrapper } from '../test-utils';
import { QueryClient } from '@tanstack/react-query';

// Mock all the hooks used in the dashboard
vi.mock('../../hooks/useMarketOverview', () => ({
  useMarketOverview: () => ({
    data: {
      indices: [
        { symbol: 'EGX30', value: 25000, change: 150, changePercent: 0.6 }
      ],
      topGainers: [
        { symbol: 'ATQA', price: 15.5, change: 2.1, changePercent: 13.5 }
      ],
      topLosers: [
        { symbol: 'EFIH', price: 12.3, change: -1.8, changePercent: -12.8 }
      ]
    },
    isLoading: false,
    error: null
  })
}));

vi.mock('../../hooks/useMlPredictions', () => ({
  useMlPredictions: () => ({
    data: {
      predictions: [
        {
          symbol: 'ATQA',
          currentPrice: 15.5,
          predictedPrice: 17.2,
          confidence: 0.82,
          trend: 'bullish'
        }
      ],
      marketSentiment: {
        overall: 'neutral',
        score: 0.52,
        confidence: 0.78
      }
    },
    isLoading: false,
    error: null
  })
}));

vi.mock('../../hooks/useUserPortfolios', () => ({
  useUserPortfolios: () => ({
    data: [
      {
        id: 1,
        name: 'Test Portfolio',
        totalValue: 100000,
        dailyChange: 1500,
        dailyChangePercent: 1.52,
        holdings: [
          {
            symbol: 'ATQA',
            shares: 100,
            avgPrice: 14.0,
            currentPrice: 15.5,
            value: 1550
          }
        ]
      }
    ],
    isLoading: false,
    error: null
  })
}));

vi.mock('../../hooks/usePriceAlerts', () => ({
  usePriceAlerts: () => ({
    alerts: [
      {
        id: 1,
        symbol: 'ATQA',
        targetPrice: 16.0,
        condition: 'above',
        isActive: true
      }
    ],
    addAlert: vi.fn(),
    removeAlert: vi.fn(),
    isLoading: false
  })
}));

// Mock WebSocket hook
vi.mock('../../hooks/useWebSocket', () => ({
  useWebSocket: () => ({
    isConnected: true,
    lastMessage: null,
    sendMessage: vi.fn()
  })
}));

// Mock authentication
vi.mock('../../hooks/useAuthUser', () => ({
  useAuthUser: () => ({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    },
    isLoading: false
  })
}));

const IntegrationWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      <TestWrapper>
        {children}
      </TestWrapper>
    </BrowserRouter>
  );
};

describe('Dashboard Integration Tests', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  beforeEach(() => {
    queryClient = createTestQueryClient();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllTimers();
    vi.useRealTimers();
  });

  it('renders complete dashboard with all main components', async () => {
    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Wait for initial data load
    await waitFor(() => {
      // Check Market Overview
      expect(screen.getByText('EGX30')).toBeInTheDocument();
      expect(screen.getByText('25,000')).toBeInTheDocument();
      
      // Check AI Insights
      expect(screen.getByText(/AI Predictions/i)).toBeInTheDocument();
      expect(screen.getByText('ATQA')).toBeInTheDocument();
      
      // Check Portfolio Tracker
      expect(screen.getByText('Test Portfolio')).toBeInTheDocument();
      expect(screen.getByText('100,000')).toBeInTheDocument();
    });
  });

  it('handles stock symbol search and selection across components', async () => {
    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Look for a search input
    const searchInput = screen.getByPlaceholderText(/search/i);
    await user.type(searchInput, 'ATQA');
    
    // Verify search results appear
    await waitFor(() => {
      expect(screen.getByText('ATQA')).toBeInTheDocument();
    });

    // Click on a stock symbol
    const stockSymbol = screen.getByText('ATQA');
    await user.click(stockSymbol);
    
    // Verify stock details are displayed
    await waitFor(() => {
      expect(screen.getByText('15.5')).toBeInTheDocument(); // current price
    });
  });

  it('updates data when real-time WebSocket receives updates', async () => {
    vi.useFakeTimers();
    
    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Initial state
    await waitFor(() => {
      expect(screen.getByText('15.5')).toBeInTheDocument();
    });

    // Simulate WebSocket price update
    vi.doMock('../../hooks/useWebSocket', () => ({
      useWebSocket: () => ({
        isConnected: true,
        lastMessage: {
          type: 'price_update',
          data: { symbol: 'ATQA', price: 16.2, change: 0.7 }
        },
        sendMessage: vi.fn()
      })
    }));

    // Fast-forward timers to trigger updates
    vi.advanceTimersByTime(1000);
    
    await waitFor(() => {
      // Updated price should be reflected
      expect(screen.getByText('16.2')).toBeInTheDocument();
    });

    vi.useRealTimers();
  });

  it('maintains user interactions across component updates', async () => {
    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Interact with portfolio component
    const portfolioSection = screen.getByText('Test Portfolio').closest('div');
    expect(portfolioSection).toBeInTheDocument();

    // Add a price alert
    const alertButton = screen.getByRole('button', { name: /add alert/i });
    await user.click(alertButton);

    // Fill alert form
    const priceInput = screen.getByLabelText(/target price/i);
    await user.type(priceInput, '18.00');

    const submitButton = screen.getByRole('button', { name: /create alert/i });
    await user.click(submitButton);

    // Verify alert was added
    await waitFor(() => {
      expect(screen.getByText('18.00')).toBeInTheDocument();
    });
  });

  it('handles error states gracefully across components', async () => {
    // Mock error states
    vi.doMock('../../hooks/useMarketOverview', () => ({
      useMarketOverview: () => ({
        data: null,
        isLoading: false,
        error: new Error('Market data unavailable')
      })
    }));

    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Check error message appears
    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });

    // Verify other components still work
    expect(screen.getByText('Test Portfolio')).toBeInTheDocument();
  });

  it('persforms well with multiple simultaneous data updates', async () => {
    vi.useFakeTimers();
    const startTime = performance.now();

    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Simulate multiple rapid updates
    for (let i = 0; i < 10; i++) {
      vi.advanceTimersByTime(100);
      await waitFor(() => {
        // Just ensure the component is still responsive
        expect(screen.getByText('EGX30')).toBeInTheDocument();
      });
    }

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Ensure rendering stays performant (< 1000ms for 10 updates)
    expect(renderTime).toBeLessThan(1000);

    vi.useRealTimers();
  });

  it('maintains responsive layout across different screen sizes', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });
    
    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Check mobile-specific elements
    const mobileMenu = screen.queryByRole('button', { name: /menu/i });
    if (mobileMenu) {
      expect(mobileMenu).toBeInTheDocument();
    }

    // Simulate desktop viewport
    Object.defineProperty(window, 'innerWidth', {
      value: 1920,
    });
    
    // Trigger resize event
    window.dispatchEvent(new Event('resize'));

    await waitFor(() => {
      // Desktop layout should be visible
      expect(screen.getByText('EGX30')).toBeInTheDocument();
    });
  });

  it('handles authentication state changes', async () => {
    render(
      <IntegrationWrapper>
        <Index />
      </IntegrationWrapper>
    );

    // Initial authenticated state
    expect(screen.getByText('Test Portfolio')).toBeInTheDocument();

    // Simulate logout
    vi.doMock('../../hooks/useAuthUser', () => ({
      useAuthUser: () => ({
        user: null,
        isLoading: false
      })
    }));

    // Trigger re-render
    await waitFor(() => {
      // Should show login or guest view
      const loginButton = screen.queryByText(/sign in/i);
      if (loginButton) {
        expect(loginButton).toBeInTheDocument();
      }
    });
  });
});
