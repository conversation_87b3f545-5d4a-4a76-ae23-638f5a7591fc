
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface VolatilityResult {
  id: string;
  symbol: string;
  current_volatility: number;
  avg_volatility: number;
  trend: string;
  analyzed_at: string;
}

export function useVolatilityAnalysis() {
  return useQuery({
    queryKey: ['analytics_volatility'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('analytics_volatility')
        .select('*')
        .order('analyzed_at', { ascending: false });
      if (error) throw error;
      // Group by latest per symbol
      const latest: Record<string, VolatilityResult> = {};
      for (const v of data ?? []) {
        if (!latest[v.symbol] || v.analyzed_at > latest[v.symbol].analyzed_at) {
          latest[v.symbol] = v;
        }
      }
      return Object.values(latest);
    }
  });
}
