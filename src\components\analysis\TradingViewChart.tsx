import React, { memo } from 'react';
import TradingViewWidget, { Themes } from 'react-tradingview-widget';

interface TradingViewChartProps {
  symbol: string;
  width?: string | number;
  height?: string | number;
  theme?: 'light' | 'dark';
  interval?: string;
  timezone?: string;
  locale?: string;
  range?: string;
  hide_side_toolbar?: boolean;
  allow_symbol_change?: boolean;
  autosize?: boolean;
}

const TradingViewChart: React.FC<TradingViewChartProps> = memo(({
  symbol,
  width = '100%',
  height = 600,
  theme = 'light',
  interval = 'D',
  timezone = 'Africa/Cairo',
  locale = 'ar',
  range = '12M',
  hide_side_toolbar = false,
  allow_symbol_change = false,
  autosize = false
}) => {
  // تحويل رمز السهم المصري إلى تنسيق TradingView
  // جرب تنسيقات مختلفة للأسهم المصرية
  const tradingViewSymbols = [
    `EGX:${symbol}`,
    `CASE:${symbol}`,
    `${symbol}`,
    `TADAWUL:${symbol}` // تجربة البورصة السعودية للمقارنة
  ];

  return (
    <div className="tradingview-widget-container w-full h-full">
      <TradingViewWidget
        symbol={tradingViewSymbols[0]} // استخدم التنسيق الأول كافتراضي
        theme={theme === 'dark' ? Themes.DARK : Themes.LIGHT}
        autosize={autosize}
        width={autosize ? undefined : width}
        height={autosize ? undefined : height}
        interval={interval}
        timezone={timezone}
        locale={locale}
        toolbar_bg={theme === 'dark' ? "#1e1e1e" : "#f1f3f6"}
        enable_publishing={false}
        withdateranges={true}
        range={range}
        hide_side_toolbar={hide_side_toolbar}
        allow_symbol_change={allow_symbol_change}
        save_image={false}
        // إعدادات متقدمة للتحليل الفني
        studies={[
          'RSI@tv-basicstudies',
          'MASimple@tv-basicstudies', 
          'MACD@tv-basicstudies',
          'BB@tv-basicstudies',
          'Volume@tv-basicstudies'
        ]}
        // تفعيل أدوات الرسم
        drawings_access={{
          type: 'black',
          tools: [
            { name: 'Regression Trend' },
            { name: 'Trend Line' },
            { name: 'Horizontal Line' },
            { name: 'Vertical Line' },
            { name: 'Rectangle' },
            { name: 'Fibonacci Retracement' },
            { name: 'Fibonacci Extension' },
            { name: 'Elliott Wave' },
            { name: 'Gann Box' },
            { name: 'Andrew\'s Pitchfork' }
          ]
        }}
        // إعدادات الواجهة المحسنة
        overrides={{
          "paneProperties.background": theme === 'dark' ? "#0d1421" : "#ffffff",
          "paneProperties.vertGridProperties.color": theme === 'dark' ? "#1e293b" : "#e2e8f0",
          "paneProperties.horzGridProperties.color": theme === 'dark' ? "#1e293b" : "#e2e8f0",
          "symbolWatermarkProperties.transparency": 90,
          "scalesProperties.textColor": theme === 'dark' ? "#cbd5e1" : "#1e293b",
          "scalesProperties.lineColor": theme === 'dark' ? "#374151" : "#e2e8f0",
          
          // ألوان الشموع المحسنة للسوق المصري
          "mainSeriesProperties.candleStyle.upColor": "#10b981", // أخضر
          "mainSeriesProperties.candleStyle.downColor": "#ef4444", // أحمر
          "mainSeriesProperties.candleStyle.borderUpColor": "#059669",
          "mainSeriesProperties.candleStyle.borderDownColor": "#dc2626",
          "mainSeriesProperties.candleStyle.wickUpColor": "#059669",
          "mainSeriesProperties.candleStyle.wickDownColor": "#dc2626",
          
          // ألوان الحجم
          "volumePaneSize": "medium",
          "mainSeriesProperties.priceAxisProperties.autoScale": true,
          "mainSeriesProperties.priceAxisProperties.autoScaleDisabled": false,
          "mainSeriesProperties.priceAxisProperties.percentage": false,
          "mainSeriesProperties.priceAxisProperties.percentageDisabled": false,
          "mainSeriesProperties.priceAxisProperties.log": false,
          "mainSeriesProperties.priceAxisProperties.logDisabled": false        }}
      />
      
      {/* معلومات إضافية عن الشارت */}
      <div className="tradingview-widget-copyright mt-2 text-center">
        <div className="flex flex-wrap justify-center items-center gap-4 text-xs text-gray-600">
          <a 
            href={`https://tradingview.com/symbols/EGX-${symbol}/`} 
            rel="noopener noreferrer" 
            target="_blank"
            className="text-blue-600 hover:underline"
          >
            شارت {symbol} من TradingView
          </a>
          <span>•</span>
          <span>البيانات مقدمة من البورصة المصرية</span>
          <span>•</span>
          <span>تحديث لحظي</span>
        </div>
        
        {/* تلميح للمستخدم في حالة عدم ظهور البيانات */}
        <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
          💡 <strong>تلميح:</strong> إذا لم تظهر البيانات، جرب البحث عن السهم داخل الشارت باستخدام "{symbol}" أو "EGX:{symbol}"
        </div>
      </div>
    </div>
  );
});

TradingViewChart.displayName = 'TradingViewChart';

export default TradingViewChart;
