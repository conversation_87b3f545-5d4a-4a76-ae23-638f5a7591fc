#!/usr/bin/env python3
"""
Enhanced Historical Data Loader for EGX Stock AI Oracle
Processes historical TXT files with format: TICKER,PER,DTYYYYMMDD,TIME,OPEN,HIGH,LOW,CLOSE,VOL,OPENINT
"""

import os
import glob
import pandas as pd
from supabase import create_client, Client
from tqdm import tqdm
import logging
from datetime import datetime, timezone
from dotenv import load_dotenv
import sys

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('historical_data_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
DATA_FOLDER = os.getenv("HISTORICAL_DATA_FOLDER", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def validate_and_convert_date(date_str):
    """Convert YYYYMMDD format to YYYY-MM-DD"""
    try:
        if isinstance(date_str, str) and len(date_str) == 8:
            year = date_str[:4]
            month = date_str[4:6]
            day = date_str[6:8]
            return f"{year}-{month}-{day}"
        elif isinstance(date_str, (int, float)):
            date_str = str(int(date_str))
            if len(date_str) == 8:
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
                return f"{year}-{month}-{day}"
    except:
        pass
    return None

def safe_float_convert(value):
    """Safely convert value to float"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A':
            return None
        return float(value)
    except (ValueError, TypeError):
        return None

def safe_int_convert(value):
    """Safely convert value to int"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A':
            return None
        return int(float(value))
    except (ValueError, TypeError):
        return None

def extract_symbol_from_filename(filename):
    """Extract symbol from filename (remove .TXT or D.TXT extension)"""
    basename = os.path.basename(filename)
    if basename.endswith('D.TXT'):
        return basename[:-5]  # Remove 'D.TXT'
    elif basename.endswith('.TXT'):
        return basename[:-4]  # Remove '.TXT'
    return basename

def process_historical_file(file_path):
    """Process a single historical data file"""
    try:
        symbol = extract_symbol_from_filename(file_path)
        logger.info(f"Processing {symbol} from {file_path}")
        
        # Read the TXT file with proper format
        # Expected format: TICKER,PER,DTYYYYMMDD,TIME,OPEN,HIGH,LOW,CLOSE,VOL,OPENINT
        df = pd.read_csv(
            file_path,
            names=['ticker', 'period', 'date', 'time', 'open', 'high', 'low', 'close', 'volume', 'open_interest'],
            skiprows=1  # Skip header row
        )
        
        if df.empty:
            logger.warning(f"No data found in {file_path}")
            return 0
        
        logger.info(f"Loaded {len(df)} historical records for {symbol}")
        
        # Process and clean data
        processed_records = []
        
        for index, row in df.iterrows():
            try:
                # Validate and convert date
                date_formatted = validate_and_convert_date(row['date'])
                if not date_formatted:
                    logger.warning(f"Invalid date format in row {index}: {row['date']}")
                    continue
                
                # Prepare record
                record = {
                    'symbol': symbol,
                    'date': date_formatted,
                    'open': safe_float_convert(row['open']),
                    'high': safe_float_convert(row['high']),
                    'low': safe_float_convert(row['low']),
                    'close': safe_float_convert(row['close']),
                    'volume': safe_int_convert(row['volume']),
                    'open_interest': safe_int_convert(row['open_interest']),
                    'adjusted_close': safe_float_convert(row['close']),  # Use close as adjusted_close
                    'created_at': datetime.now(timezone.utc).isoformat()
                }
                
                # Validate essential fields
                if record['open'] is None or record['high'] is None or record['low'] is None or record['close'] is None:
                    logger.warning(f"Missing essential price data in row {index} for {symbol}")
                    continue
                
                # Basic data validation
                if (record['high'] < record['low'] or 
                    record['close'] > record['high'] or 
                    record['close'] < record['low'] or
                    record['open'] > record['high'] or 
                    record['open'] < record['low']):
                    logger.warning(f"Invalid OHLC data in row {index} for {symbol}")
                    continue
                
                processed_records.append(record)
                
            except Exception as e:
                logger.error(f"Error processing row {index} in {file_path}: {e}")
                continue
        
        if not processed_records:
            logger.warning(f"No valid records found in {file_path}")
            return 0
        
        # Upsert data in batches
        batch_size = 1000
        total_inserted = 0
        
        for i in range(0, len(processed_records), batch_size):
            batch = processed_records[i:i + batch_size]
            
            try:
                response = supabase.table("stocks_historical").upsert(
                    batch,
                    on_conflict="symbol,date",
                    returning="minimal"
                ).execute()
                
                total_inserted += len(batch)
                logger.info(f"Inserted batch of {len(batch)} records for {symbol}")
                
            except Exception as e:
                logger.error(f"Error inserting batch for {symbol}: {e}")
                continue
        
        logger.info(f"Successfully processed {total_inserted} historical records for {symbol}")
        return total_inserted
        
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}")
        return 0

def get_latest_date_for_symbol(symbol):
    """Get the latest date we have data for a specific symbol"""
    try:
        response = supabase.table("stocks_historical").select("date").eq("symbol", symbol).order("date", desc=True).limit(1).execute()
        
        if response.data:
            return response.data[0]['date']
        return None
    except Exception as e:
        logger.error(f"Error getting latest date for {symbol}: {e}")
        return None

def process_incremental_update(file_path, latest_date):
    """Process only new records after the latest date"""
    try:
        symbol = extract_symbol_from_filename(file_path)
        logger.info(f"Processing incremental update for {symbol} after {latest_date}")
        
        df = pd.read_csv(
            file_path,
            names=['ticker', 'period', 'date', 'time', 'open', 'high', 'low', 'close', 'volume', 'open_interest'],
            skiprows=1
        )
        
        if df.empty:
            return 0
        
        # Filter for new records
        new_records = []
        latest_datetime = datetime.strptime(latest_date, '%Y-%m-%d')
        
        for index, row in df.iterrows():
            try:
                date_formatted = validate_and_convert_date(row['date'])
                if not date_formatted:
                    continue
                
                record_datetime = datetime.strptime(date_formatted, '%Y-%m-%d')
                
                # Only process records newer than latest date
                if record_datetime > latest_datetime:
                    record = {
                        'symbol': symbol,
                        'date': date_formatted,
                        'open': safe_float_convert(row['open']),
                        'high': safe_float_convert(row['high']),
                        'low': safe_float_convert(row['low']),
                        'close': safe_float_convert(row['close']),
                        'volume': safe_int_convert(row['volume']),
                        'open_interest': safe_int_convert(row['open_interest']),
                        'adjusted_close': safe_float_convert(row['close']),
                        'created_at': datetime.now(timezone.utc).isoformat()
                    }
                    
                    # Validate essential fields
                    if all(record[key] is not None for key in ['open', 'high', 'low', 'close']):
                        new_records.append(record)
                
            except Exception as e:
                logger.error(f"Error processing incremental row {index}: {e}")
                continue
        
        if new_records:
            try:
                response = supabase.table("stocks_historical").upsert(
                    new_records,
                    on_conflict="symbol,date",
                    returning="minimal"
                ).execute()
                
                logger.info(f"Added {len(new_records)} new records for {symbol}")
                return len(new_records)
            except Exception as e:
                logger.error(f"Error inserting incremental data for {symbol}: {e}")
                return 0
        else:
            logger.info(f"No new records found for {symbol}")
            return 0
            
    except Exception as e:
        logger.error(f"Error in incremental update for {file_path}: {e}")
        return 0

def process_all_historical_data(incremental=True):
    """Process all historical data files"""
    try:
        if not os.path.exists(DATA_FOLDER):
            logger.error(f"Data folder not found: {DATA_FOLDER}")
            return
        
        # Find all TXT files
        txt_files = []
        for pattern in ['*.TXT', '*D.TXT']:
            txt_files.extend(glob.glob(os.path.join(DATA_FOLDER, pattern)))
        
        if not txt_files:
            logger.warning(f"No TXT files found in {DATA_FOLDER}")
            return
        
        logger.info(f"Found {len(txt_files)} historical data files")
        
        total_processed = 0
        successful_files = 0
        
        for file_path in tqdm(txt_files, desc="Processing historical files"):
            try:
                symbol = extract_symbol_from_filename(file_path)
                
                if incremental:
                    # Check for latest date and do incremental update
                    latest_date = get_latest_date_for_symbol(symbol)
                    if latest_date:
                        records_added = process_incremental_update(file_path, latest_date)
                        total_processed += records_added
                        if records_added > 0:
                            successful_files += 1
                    else:
                        # No data exists, do full import
                        records_added = process_historical_file(file_path)
                        total_processed += records_added
                        if records_added > 0:
                            successful_files += 1
                else:
                    # Full import
                    records_added = process_historical_file(file_path)
                    total_processed += records_added
                    if records_added > 0:
                        successful_files += 1
                        
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                continue
        
        logger.info(f"Historical data sync completed.")
        logger.info(f"Successfully processed {successful_files} files")
        logger.info(f"Total records processed: {total_processed}")
        
    except Exception as e:
        logger.error(f"Error in process_all_historical_data: {e}")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Load historical stock data')
    parser.add_argument('--full', action='store_true', help='Do full import instead of incremental')
    parser.add_argument('--symbol', type=str, help='Process specific symbol file')
    
    args = parser.parse_args()
    
    logger.info("Starting enhanced historical data sync...")
    
    try:
        if args.symbol:
            # Process specific symbol
            file_patterns = [
                os.path.join(DATA_FOLDER, f"{args.symbol}.TXT"),
                os.path.join(DATA_FOLDER, f"{args.symbol}D.TXT")
            ]
            
            for file_path in file_patterns:
                if os.path.exists(file_path):
                    records_added = process_historical_file(file_path)
                    logger.info(f"Processed {records_added} records for {args.symbol}")
                    break
            else:
                logger.error(f"No file found for symbol {args.symbol}")
        else:
            # Process all files
            incremental = not args.full
            process_all_historical_data(incremental=incremental)
            
        logger.info("Historical data sync completed successfully")
        
    except Exception as e:
        logger.error(f"Historical data sync failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
