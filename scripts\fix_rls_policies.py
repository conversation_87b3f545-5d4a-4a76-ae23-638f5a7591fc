#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Fix RLS Policies Script
This script will directly connect to PostgreSQL and fix RLS policies
to allow public read access to financial data tables.
"""

import os
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_SERVICE_ROLE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
    print("❌ Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env file")
    exit(1)

def fix_rls_policies():
    """Fix RLS policies using Supabase SQL RPC"""
    print("🔧 Fixing RLS policies for public data access...")
    
    # Initialize Supabase client with service role
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
    
    # SQL commands to fix RLS policies
    sql_commands = [
        # Market Indices
        "DROP POLICY IF EXISTS \"Allow public read access to market_indices\" ON public.market_indices;",
        "CREATE POLICY \"Allow public read access to market_indices\" ON public.market_indices FOR SELECT TO anon USING (true);",
        
        # Stocks Realtime
        "DROP POLICY IF EXISTS \"Allow public read access to stocks_realtime\" ON public.stocks_realtime;", 
        "CREATE POLICY \"Allow public read access to stocks_realtime\" ON public.stocks_realtime FOR SELECT TO anon USING (true);",
        
        # Stocks Master
        "DROP POLICY IF EXISTS \"Allow public read access to stocks_master\" ON public.stocks_master;",
        "CREATE POLICY \"Allow public read access to stocks_master\" ON public.stocks_master FOR SELECT TO anon USING (true);",
        
        # Stocks Historical
        "DROP POLICY IF EXISTS \"Allow public read access to stocks_historical\" ON public.stocks_historical;",
        "CREATE POLICY \"Allow public read access to stocks_historical\" ON public.stocks_historical FOR SELECT TO anon USING (true);",
        
        # Stocks Financials
        "DROP POLICY IF EXISTS \"Allow public read access to stocks_financials\" ON public.stocks_financials;",
        "CREATE POLICY \"Allow public read access to stocks_financials\" ON public.stocks_financials FOR SELECT TO anon USING (true);",
        
        # Technical Indicators
        "DROP POLICY IF EXISTS \"Allow public read access to technical_indicators\" ON public.technical_indicators;",
        "CREATE POLICY \"Allow public read access to technical_indicators\" ON public.technical_indicators FOR SELECT TO anon USING (true);",
        
        # Market News
        "DROP POLICY IF EXISTS \"Allow public read access to market_news\" ON public.market_news;",
        "CREATE POLICY \"Allow public read access to market_news\" ON public.market_news FOR SELECT TO anon USING (true);",
    ]
    
    success_count = 0
    total_count = len(sql_commands)
    
    for i, sql in enumerate(sql_commands, 1):
        try:
            # Execute SQL using Supabase RPC
            result = supabase.rpc('exec_sql', {'sql': sql}).execute()
            
            if not result.data or 'error' not in str(result.data):
                print(f"  ✅ Command {i}/{total_count}: Policy updated successfully")
                success_count += 1
            else:
                print(f"  ⚠️ Command {i}/{total_count}: {result.data}")
                
        except Exception as e:
            print(f"  ❌ Command {i}/{total_count}: Error executing SQL")
            print(f"     SQL: {sql}")
            print(f"     Error: {str(e)}")
    
    print(f"\n📊 Results: {success_count}/{total_count} policies updated successfully")
    return success_count == total_count

def test_access_after_fix():
    """Test data access with anon key after fixing policies"""
    print("\n🧪 Testing data access after RLS fix...")
    
    # Create client with anon key (same as frontend)
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    anon_supabase: Client = create_client(SUPABASE_URL, anon_key)
    
    # Test market indices (what the frontend needs)
    try:
        result = anon_supabase.table('market_indices').select('*').in_('symbol', ['EGX30', 'EGX70']).execute()
        if result.data and len(result.data) > 0:
            print("  ✅ Market indices accessible via anon key!")
            for index in result.data:
                print(f"     📊 {index['symbol']}: {index['current_value']}")
        else:
            print("  ❌ Market indices still not accessible")
            
    except Exception as e:
        print(f"  ❌ Error accessing market indices: {str(e)}")
    
    # Test stocks realtime
    try:
        result = anon_supabase.table('stocks_realtime').select('symbol, current_price').limit(3).execute()
        if result.data and len(result.data) > 0:
            print("  ✅ Stocks realtime accessible via anon key!")
            for stock in result.data:
                print(f"     📈 {stock['symbol']}: {stock['current_price']}")
        else:
            print("  ❌ Stocks realtime still not accessible")
            
    except Exception as e:
        print(f"  ❌ Error accessing stocks realtime: {str(e)}")

if __name__ == "__main__":
    print("🔐 EGX Stock AI Oracle - RLS Policy Fixer")
    print("=" * 60)
    
    # Fix RLS policies
    success = fix_rls_policies()
    
    if success:
        print("\n✅ All RLS policies updated successfully!")
        
        # Test access
        test_access_after_fix()
        
        print("\n" + "=" * 60)
        print("🎉 RLS fix completed!")
        print("🌐 Frontend should now be able to access market data")
        print("🚀 Try refreshing your application!")
        
    else:
        print("\n❌ Some RLS policies failed to update")
        print("🔧 Please check the errors above and try again")
