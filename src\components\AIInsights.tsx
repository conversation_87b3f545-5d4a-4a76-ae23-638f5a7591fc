
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Brain, TrendingUp, TrendingDown, Target, AlertTriangle, Star, Zap, Trophy } from 'lucide-react';

interface AIInsight {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  reason: string;
  targetPrice: number;
  timeframe: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

const AIInsights = () => {
  const [insights, setInsights] = useState<AIInsight[]>([
    {
      id: '1',
      symbol: 'COMI',
      type: 'BUY',
      confidence: 87,
      reason: 'نمط صاعد قوي مع كسر مستوى المقاومة عند 50 جنيه. حجم التداول العالي يؤكد الاتجاه.',
      targetPrice: 58.50,
      timeframe: '2-3 أسابيع',
      riskLevel: 'MEDIUM'
    },
    {
      id: '2',
      symbol: 'TALAAT',
      type: 'BUY',
      confidence: 92,
      reason: 'تحليل فيبوناتشي يشير إلى ارتداد قوي من مستوى 23.6%. مؤشرات فنية إيجابية.',
      targetPrice: 35.20,
      timeframe: '1-2 شهر',
      riskLevel: 'LOW'
    },
    {
      id: '3',
      symbol: 'ETEL',
      type: 'SELL',
      confidence: 78,
      reason: 'نمط رأس وكتفين مكتمل. مؤشر RSI في منطقة الإشباع الشرائي.',
      targetPrice: 16.80,
      timeframe: '1-2 أسابيع',
      riskLevel: 'HIGH'
    }
  ]);

  const [aiAccuracy, setAiAccuracy] = useState(84.7);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    // Simulate accuracy updates
    const interval = setInterval(() => {
      setAiAccuracy(prev => Math.min(99, Math.max(80, prev + (Math.random() - 0.5) * 2)));
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'BUY':
        return <TrendingUp className="h-4 w-4 text-market-green" />;
      case 'SELL':
        return <TrendingDown className="h-4 w-4 text-market-red" />;
      default:
        return <Target className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'BUY':
        return 'bg-market-green hover:bg-market-green/80 shadow-green-500/20';
      case 'SELL':
        return 'bg-market-red hover:bg-market-red/80 shadow-red-500/20';
      default:
        return 'bg-yellow-500 hover:bg-yellow-500/80 shadow-yellow-500/20';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'LOW':
        return 'text-market-green bg-green-50 border-green-200';
      case 'HIGH':
        return 'text-market-red bg-red-50 border-red-200';
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'LOW':
        return '🟢';
      case 'HIGH':
        return '🔴';
      default:
        return '🟡';
    }
  };

  return (
    <div className="space-y-8">
      {/* Enhanced AI Accuracy Header */}
      <Card className={`border-purple-200 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 hover:shadow-elevated transition-all duration-500 card-hover overflow-hidden relative
        ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
        
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 30% 40%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                             radial-gradient(circle at 70% 60%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)`
          }}></div>
        </div>

        <CardHeader className="relative z-10">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-purple-800">توصيات الذكاء الاصطناعي</h3>
              <p className="text-sm text-purple-600 mt-1">مدعوم بتقنيات التعلم الآلي المتقدمة</p>
            </div>
            <div className="ml-auto flex gap-2">
              <Badge className="bg-purple-600 hover:bg-purple-700 shadow-lg">
                <Star className="h-3 w-3 mr-1" />
                دقة {aiAccuracy.toFixed(1)}%
              </Badge>
              <Badge className="bg-green-600 hover:bg-green-700 shadow-lg">
                <Trophy className="h-3 w-3 mr-1" />
                نشط
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex justify-between text-sm font-medium">
                <span className="text-purple-700">دقة التوقعات</span>
                <span className="font-bold text-purple-800">{aiAccuracy.toFixed(1)}%</span>
              </div>
              <Progress value={aiAccuracy} className="h-3 bg-purple-100" />
              <div className="text-xs text-purple-600">آخر تحديث: منذ دقائق</div>
            </div>
            
            <div className="text-center p-4 bg-white/60 rounded-lg backdrop-blur-sm border border-purple-200">
              <div className="text-3xl font-bold text-purple-600 mb-1">156</div>
              <div className="text-sm text-purple-700 font-medium">توصية ناجحة</div>
              <div className="text-xs text-purple-600 mt-1">هذا الشهر</div>
            </div>

            <div className="text-center p-4 bg-white/60 rounded-lg backdrop-blur-sm border border-purple-200">
              <div className="text-3xl font-bold text-green-600 mb-1">+23.8%</div>
              <div className="text-sm text-purple-700 font-medium">متوسط العائد</div>
              <div className="text-xs text-purple-600 mt-1">30 يوم</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced AI Insights Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {insights.map((insight, index) => (
          <Card 
            key={insight.id} 
            className={`border-2 hover:shadow-elevated transition-all duration-500 card-hover overflow-hidden relative group bg-gradient-to-br from-white to-gray-50
              ${insight.type === 'BUY' ? 'border-green-200 hover:border-green-300' : 
                insight.type === 'SELL' ? 'border-red-200 hover:border-red-300' : 
                'border-yellow-200 hover:border-yellow-300'}
              ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            style={{ 
              animationDelay: `${index * 200}ms`,
              animation: isVisible ? 'slide-up 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards' : 'none'
            }}
          >
            {/* Shine Effect */}
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-100%] transition-transform duration-1000"></div>
            </div>

            <CardHeader className="pb-3 relative z-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="text-2xl font-bold text-egx-blue-800 bg-egx-blue-50 px-3 py-1 rounded-lg">
                    {insight.symbol}
                  </div>
                  <Badge className={`${getTypeColor(insight.type)} text-white shadow-lg font-bold px-3 py-1`}>
                    {getTypeIcon(insight.type)}
                    <span className="ml-1">
                      {insight.type === 'BUY' ? 'شراء' : insight.type === 'SELL' ? 'بيع' : 'انتظار'}
                    </span>
                  </Badge>
                </div>
                
                <div className="text-right">
                  <div className="text-xs text-muted-foreground">مستوى الثقة</div>
                  <div className="text-lg font-bold text-egx-blue-700">{insight.confidence}%</div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4 relative z-10">
              {/* AI Analysis */}
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="flex items-start gap-2 mb-2">
                  <Zap className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-xs font-semibold text-blue-700">تحليل الذكاء الاصطناعي</span>
                </div>
                <p className="text-sm text-gray-700 leading-relaxed arabic" dir="rtl">
                  {insight.reason}
                </p>
              </div>
              
              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-3">
                <div className="p-3 bg-gray-50 rounded-lg border">
                  <div className="text-xs text-muted-foreground mb-1">السعر المستهدف</div>
                  <div className="font-bold text-lg text-egx-blue-800">{insight.targetPrice} EGP</div>
                </div>
                
                <div className="p-3 bg-gray-50 rounded-lg border">
                  <div className="text-xs text-muted-foreground mb-1">الإطار الزمني</div>
                  <div className="text-sm font-semibold text-gray-700">{insight.timeframe}</div>
                </div>
              </div>
              
              {/* Risk Assessment */}
              <div className={`p-3 rounded-lg border-2 ${getRiskColor(insight.riskLevel)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="text-sm font-semibold">مستوى المخاطر</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-lg">{getRiskIcon(insight.riskLevel)}</span>
                    <span className="text-sm font-bold">
                      {insight.riskLevel === 'LOW' ? 'منخفض' : insight.riskLevel === 'HIGH' ? 'عالي' : 'متوسط'}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Confidence Meter */}
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span className="text-muted-foreground">مؤشر الثقة</span>
                  <span className="font-bold">{insight.confidence}%</span>
                </div>
                <div className="relative">
                  <Progress value={insight.confidence} className="h-2" />
                  <div 
                    className="absolute top-0 h-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-full transition-all duration-1000"
                    style={{ width: `${insight.confidence}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced VIP Upgrade Prompt */}
      <Card className={`gold-gradient text-white border-none hover:shadow-elevated transition-all duration-500 card-hover overflow-hidden relative
        ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
        
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                             radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%)`
          }}></div>
        </div>

        <CardContent className="p-8 text-center relative z-10">
          <div className="max-w-md mx-auto space-y-6">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
              <Star className="h-8 w-8 text-yellow-200" />
            </div>
            
            <h3 className="text-2xl font-bold mb-2">
              احصل على المزيد من التوصيات المتقدمة
            </h3>
            
            <p className="text-white/90 leading-relaxed">
              انضم إلى العضوية المميزة للحصول على تحليلات أعمق، توصيات مخصصة، 
              وإشعارات فورية عبر تيليجرام
            </p>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-white/80 mb-6">
              <div className="flex items-center gap-1">
                <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                تحليلات متقدمة
              </div>
              <div className="flex items-center gap-1">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                إشعارات فورية
              </div>
              <div className="flex items-center gap-1">
                <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                دعم مباشر
              </div>
            </div>
            
            <Button className="bg-white text-egx-gold-700 hover:bg-white/90 font-bold text-lg px-8 py-3 shadow-lg btn-animate">
              <Trophy className="h-5 w-5 mr-2" />
              ترقية للعضوية المميزة
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIInsights;
