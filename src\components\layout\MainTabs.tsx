import React, { Suspense, lazy } from "react";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import {
  BarChart3, Briefcase, TrendingUp, Filter, Brain, Sparkles, Settings, Smartphone, Newspaper, Calendar, BookOpen, Bell, Target, Activity
} from "lucide-react";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import EnhancedDashboardHeader from "@/components/dashboard/EnhancedDashboardHeader";
import NavigationTabs from "@/components/dashboard/NavigationTabs";
import MarketOverview from "@/components/MarketOverview";
import AdvancedMarketDashboard from "@/components/AdvancedMarketDashboard";
import QuickStatsGrid from "@/components/dashboard/QuickStatsGrid";
import StockTicker from "@/components/StockTicker";
import { useMobileGestures } from "@/hooks/useMobileGestures";

// Lazy load heavy components
const PriceAlertsManager = lazy(() => import("@/components/alerts/PriceAlertsManager"));
const SmartNotifications = lazy(() => import("@/components/notifications/SmartNotifications"));
const TechnicalIndicators = lazy(() => import("@/components/charts/TechnicalIndicators"));
import FinalEGX30Chart from "@/components/charts/FinalEGX30Chart";
const PortfolioTracker = lazy(() => import("@/components/PortfolioTracker"));
const PortfolioAnalyzer = lazy(() => import("@/components/portfolio/PortfolioAnalyzer"));
const PaperTrading = lazy(() => import("@/components/PaperTrading"));
const AdvancedAnalytics = lazy(() => import("@/components/AdvancedAnalytics"));
const EnhancedExperience = lazy(() => import("@/components/EnhancedExperience"));
const RiskManagement = lazy(() => import("@/components/RiskManagement"));
const DashboardCustomizer = lazy(() => import("@/components/DashboardCustomizer"));
const MobileFeatures = lazy(() => import("@/components/MobileFeatures"));
const StockScreener = lazy(() => import("@/components/advanced/StockScreener"));
const SmartAlerts = lazy(() => import("@/components/alerts/SmartAlerts"));
const LiveTradingScreen = lazy(() => import("@/components/trading/LiveTradingScreen"));
const SimpleLiveTradingScreen = lazy(() => import("@/components/trading/SimpleLiveTradingScreen"));
const EnhancedLiveTradingScreen = lazy(() => import("@/components/trading/EnhancedLiveTradingScreen"));
const AdvancedLiveTradingScreen = lazy(() => import("@/components/trading/AdvancedLiveTradingScreen"));
const RealTimeTradingScreen = lazy(() => import("@/components/trading/RealTimeTradingScreen"));
import TopStocksWidget from "@/components/trading/TopStocksWidget";
const NewsIntegration = lazy(() => import("@/components/NewsIntegration"));
const MarketCalendar = lazy(() => import("@/components/MarketCalendar"));
const UserOnboarding = lazy(() => import("@/components/UserOnboarding"));
const AIInsights = lazy(() => import("@/components/AIInsights"));
const TelegramIntegration = lazy(() => import("@/components/TelegramIntegration"));
const MarketDashboard = lazy(() => import("@/components/enhanced/MarketDashboard"));
const ComprehensiveStockAnalysis = lazy(() => import("@/components/analysis/ComprehensiveStockAnalysis"));

interface MainTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
  isLoaded: boolean;
  isConnected: boolean;
  lastMessage: string | null;
  isDarkMode: boolean;
  toggleTheme: () => void;
  navItems: { id: string; label: string; icon: string; description: string }[];
  gestureRef: React.RefObject<HTMLDivElement> | ((el: HTMLDivElement) => void);
}

// Icon mapping
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  BarChart3,
  Activity,
  Briefcase,
  TrendingUp,
  Filter,
  Brain,
  Sparkles,
  Settings,
  Smartphone,
  Newspaper,
  Calendar,
  BookOpen,
  Bell,
  Target
};

// Loading component for lazy-loaded components
const ComponentLoader = () => (
  <Card>
    <CardContent className="p-8 text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <div className="text-gray-600">جاري التحميل...</div>
    </CardContent>
  </Card>
);

const MainTabs: React.FC<MainTabsProps> = ({
  activeTab,
  setActiveTab,
  mobileMenuOpen,
  setMobileMenuOpen,
  isLoaded,
  isConnected,
  lastMessage,
  isDarkMode,
  toggleTheme,
  navItems,
  gestureRef,
}) => {
  return (
    <div ref={gestureRef} className={`min-h-screen transition-all duration-500 ${isLoaded ? "opacity-100" : "opacity-0"} bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900`}>
      <div className="relative">
        <StockTicker />
        {isConnected && lastMessage && (
          <div className="absolute top-2 right-4 flex items-center gap-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            بيانات مباشرة
          </div>
        )}
      </div>

      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          {/* الهيدر المحسن مع التنقل */}
          <EnhancedDashboardHeader
            title="نظرة عامة على السوق"
            description="تحليل شامل وبيانات فورية للبورصة المصرية"
            isDarkMode={isDarkMode}
            toggleTheme={toggleTheme}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            mobileMenuOpen={mobileMenuOpen}
            setMobileMenuOpen={setMobileMenuOpen}
          />

          <TabsContent value="overview" className="space-y-8">
            {/* نظرة عامة مبسطة على السوق */}
            <MarketOverview />

            {/* أهم الأسهم النشطة */}
            <TopStocksWidget 
              onViewAll={() => setActiveTab('live-trading')}
              maxItems={5}
            />

            <FinalEGX30Chart />
          </TabsContent>          {/* جميع التبويبات الآن تستخدم الهيدر المحسن في الأعلى */}
          <TabsContent value="portfolio" className="space-y-8">
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <Suspense fallback={<ComponentLoader />}>
                <PortfolioTracker />
              </Suspense>
              <Suspense fallback={<ComponentLoader />}>
                <PortfolioAnalyzer />
              </Suspense>
            </div>
          </TabsContent>

          <TabsContent value="paper-trading">
            <Suspense fallback={<ComponentLoader />}>
              <PaperTrading />
            </Suspense>
          </TabsContent>

          <TabsContent value="live-trading">
            <Suspense fallback={<ComponentLoader />}>
              <RealTimeTradingScreen />
            </Suspense>
          </TabsContent>

          <TabsContent value="analytics">
            <Suspense fallback={<ComponentLoader />}>
              <TechnicalIndicators />
            </Suspense>
            <Suspense fallback={<ComponentLoader />}>
              <AdvancedAnalytics />
            </Suspense>
          </TabsContent>

          <TabsContent value="enhanced-experience">
            <Suspense fallback={<ComponentLoader />}>
              <EnhancedExperience />
            </Suspense>
          </TabsContent>

          <TabsContent value="risk-management">
            <Suspense fallback={<ComponentLoader />}>
              <RiskManagement />
            </Suspense>
          </TabsContent>

          <TabsContent value="dashboard-customizer">
            <Suspense fallback={<ComponentLoader />}>
              <DashboardCustomizer />
            </Suspense>
          </TabsContent>

          <TabsContent value="mobile-features">
            <Suspense fallback={<ComponentLoader />}>
              <MobileFeatures />
            </Suspense>
          </TabsContent>

          <TabsContent value="screener">
            <Suspense fallback={<ComponentLoader />}>
              <StockScreener />
            </Suspense>
          </TabsContent>

          <TabsContent value="alerts" className="space-y-8">
            {/* مركز التنبيهات الشامل */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <Suspense fallback={<ComponentLoader />}>
                <PriceAlertsManager />
              </Suspense>
              <Suspense fallback={<ComponentLoader />}>
                <SmartNotifications />
              </Suspense>
            </div>
            
            <Suspense fallback={<ComponentLoader />}>
              <SmartAlerts />
            </Suspense>
          </TabsContent>

          <TabsContent value="news">
            <Suspense fallback={<ComponentLoader />}>
              <NewsIntegration />
            </Suspense>
          </TabsContent>

          <TabsContent value="calendar">
            <Suspense fallback={<ComponentLoader />}>
              <MarketCalendar />
            </Suspense>
          </TabsContent>

          <TabsContent value="learning">
            <Suspense fallback={<ComponentLoader />}>
              <UserOnboarding />
            </Suspense>
          </TabsContent>

          <TabsContent value="ai-insights">
            <Suspense fallback={<ComponentLoader />}>
              <AIInsights />
            </Suspense>
          </TabsContent>

          <TabsContent value="telegram">
            <Suspense fallback={<ComponentLoader />}>
              <TelegramIntegration />
            </Suspense>
          </TabsContent>

          <TabsContent value="settings">
            <Card className="hover:shadow-elevated transition-all duration-300 card-hover">
              <CardContent className="p-12">
                <div className="text-center space-y-4">
                  <Settings className="h-16 w-16 text-muted-foreground mx-auto opacity-50" />
                  <h3 className="text-2xl font-bold text-muted-foreground">قريباً...</h3>
                  <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
                    إعدادات النظام والتفضيلات الشخصية، إدارة الحساب، والمزيد من الميزات المتقدمة
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced-market" className="space-y-8">
            {/* لوحة التحكم الرئيسية للتحليل المتقدم */}
            <div className="grid grid-cols-1 gap-8">
              <AdvancedMarketDashboard />
            </div>
          </TabsContent>

          <TabsContent value="comprehensive-analysis" className="space-y-8">
            <Suspense fallback={<ComponentLoader />}>
              <ComprehensiveStockAnalysis />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MainTabs;
