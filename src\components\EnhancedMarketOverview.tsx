import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  BarChart3, 
  Target, 
  AlertTriangle, 
  Sparkles,
  DollarSign,
  Users,
  PieChart
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface MarketStats {
  totalStocks: number;
  activeStocks: number;
  totalVolume: number;
  totalTurnover: number;
  gainers: number;
  losers: number;
  unchanged: number;
  topGainers: Array<{
    symbol: string;
    change_percent: number;
    current_price: number;
  }>;
  topLosers: Array<{
    symbol: string;
    change_percent: number;
    current_price: number;
  }>;
  mostActive: Array<{
    symbol: string;
    volume: number;
    turnover: number;
  }>;
  sectors: Array<{
    name: string;
    count: number;
    avgPerformance: number;
  }>;
}

const useEnhancedMarketData = () => {
  return useQuery<MarketStats>({
    queryKey: ['enhanced_market_overview'],
    queryFn: async () => {
      try {
        // جلب جميع البيانات من stocks_realtime
        const { data: stocks, error } = await supabase
          .from('stocks_realtime')
          .select(`
            symbol,
            current_price,
            change_percent,
            volume,
            turnover,
            sector
          `)
          .not('symbol', 'like', '%EGX%')
          .not('symbol', 'like', '%INDEX%')
          .gt('current_price', 0);

        if (error) throw error;

        if (!stocks || stocks.length === 0) {
          throw new Error('لا توجد بيانات متاحة');
        }

        // حساب الإحصائيات
        const activeStocks = stocks.filter(s => (s.volume || 0) > 0);
        const totalVolume = stocks.reduce((sum, s) => sum + (s.volume || 0), 0);
        const totalTurnover = stocks.reduce((sum, s) => sum + (s.turnover || 0), 0);

        const gainers = stocks.filter(s => (s.change_percent || 0) > 0);
        const losers = stocks.filter(s => (s.change_percent || 0) < 0);
        const unchanged = stocks.filter(s => (s.change_percent || 0) === 0);

        // أفضل وأسوأ أداء
        const topGainers = [...gainers]
          .sort((a, b) => (b.change_percent || 0) - (a.change_percent || 0))
          .slice(0, 5)
          .map(s => ({
            symbol: s.symbol,
            change_percent: s.change_percent || 0,
            current_price: s.current_price || 0
          }));

        const topLosers = [...losers]
          .sort((a, b) => (a.change_percent || 0) - (b.change_percent || 0))
          .slice(0, 5)
          .map(s => ({
            symbol: s.symbol,
            change_percent: s.change_percent || 0,
            current_price: s.current_price || 0
          }));

        // الأكثر نشاطاً
        const mostActive = [...activeStocks]
          .sort((a, b) => (b.turnover || 0) - (a.turnover || 0))
          .slice(0, 5)
          .map(s => ({
            symbol: s.symbol,
            volume: s.volume || 0,
            turnover: s.turnover || 0
          }));

        // إحصائيات القطاعات
        const sectorMap = new Map<string, { count: number; totalPerf: number }>();
        stocks.forEach(stock => {
          const sector = stock.sector || 'غير محدد';
          const perf = stock.change_percent || 0;
          
          if (sectorMap.has(sector)) {
            const existing = sectorMap.get(sector)!;
            sectorMap.set(sector, {
              count: existing.count + 1,
              totalPerf: existing.totalPerf + perf
            });
          } else {
            sectorMap.set(sector, { count: 1, totalPerf: perf });
          }
        });

        const sectors = Array.from(sectorMap.entries())
          .map(([name, data]) => ({
            name,
            count: data.count,
            avgPerformance: data.totalPerf / data.count
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 6);

        return {
          totalStocks: stocks.length,
          activeStocks: activeStocks.length,
          totalVolume,
          totalTurnover,
          gainers: gainers.length,
          losers: losers.length,
          unchanged: unchanged.length,
          topGainers,
          topLosers,
          mostActive,
          sectors
        };
      } catch (error) {
        console.error('Error fetching enhanced market data:', error);
        throw error;
      }
    },
    refetchInterval: 30000, // تحديث كل 30 ثانية
    staleTime: 25000,
    retry: 3
  });
};

const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(1)}B`;
  } else if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toLocaleString();
};

const formatPercent = (num: number): string => {
  const sign = num >= 0 ? '+' : '';
  return `${sign}${num.toFixed(2)}%`;
};

const formatPrice = (price: number): string => {
  return price.toFixed(2);
};

const EnhancedMarketOverview: React.FC = () => {
  const { data: marketStats, isLoading, error } = useEnhancedMarketData();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-pulse">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-bold text-red-700 mb-2">خطأ في تحميل البيانات</h3>
          <p className="text-red-600">تعذر جلب بيانات السوق المحسنة</p>
        </CardContent>
      </Card>
    );
  }

  if (!marketStats) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold">{marketStats.totalStocks}</div>
                <div className="text-sm text-gray-600">إجمالي الأسهم</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold">{marketStats.activeStocks}</div>
                <div className="text-sm text-gray-600">الأسهم النشطة</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <div>
                <div className="text-lg font-bold">{formatNumber(marketStats.totalTurnover)}</div>
                <div className="text-sm text-gray-600">قيمة التداول</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-600" />
              <div>
                <div className="text-lg font-bold">{formatNumber(marketStats.totalVolume)}</div>
                <div className="text-sm text-gray-600">حجم التداول</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* توزيع الأداء */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            توزيع أداء السوق
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{marketStats.gainers}</div>
              <div className="text-sm text-green-700">أسهم صاعدة</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{marketStats.losers}</div>
              <div className="text-sm text-red-700">أسهم هابطة</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{marketStats.unchanged}</div>
              <div className="text-sm text-gray-700">بدون تغيير</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* أفضل أداء */}
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-700">
              <TrendingUp className="h-5 w-5" />
              أفضل أداء
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {marketStats.topGainers.map((stock, index) => (
              <div key={stock.symbol} className="flex justify-between items-center">
                <div>
                  <div className="font-bold">{stock.symbol}</div>
                  <div className="text-sm text-gray-600">#{index + 1}</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-green-600">
                    {formatPercent(stock.change_percent)}
                  </div>
                  <div className="text-sm">{formatPrice(stock.current_price)}</div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* أسوأ أداء */}
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <TrendingDown className="h-5 w-5" />
              أسوأ أداء
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {marketStats.topLosers.map((stock, index) => (
              <div key={stock.symbol} className="flex justify-between items-center">
                <div>
                  <div className="font-bold">{stock.symbol}</div>
                  <div className="text-sm text-gray-600">#{index + 1}</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-red-600">
                    {formatPercent(stock.change_percent)}
                  </div>
                  <div className="text-sm">{formatPrice(stock.current_price)}</div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* الأكثر نشاطاً */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Target className="h-5 w-5" />
              الأكثر نشاطاً
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {marketStats.mostActive.map((stock, index) => (
              <div key={stock.symbol} className="flex justify-between items-center">
                <div>
                  <div className="font-bold">{stock.symbol}</div>
                  <div className="text-sm text-gray-600">#{index + 1}</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-blue-600">
                    {formatNumber(stock.turnover)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {formatNumber(stock.volume)} سهم
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* أداء القطاعات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            أداء القطاعات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {marketStats.sectors.map((sector) => (
              <div key={sector.name} className="p-4 border rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-bold text-sm">{sector.name}</div>
                  <Badge variant="outline" className="text-xs">
                    {sector.count} سهم
                  </Badge>
                </div>
                <div className={`font-bold text-lg ${
                  sector.avgPerformance >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formatPercent(sector.avgPerformance)}
                </div>
                <div className="text-xs text-gray-600">متوسط الأداء</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedMarketOverview;
