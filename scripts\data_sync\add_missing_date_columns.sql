-- Add missing date/time columns to stocks_realtime table
-- Execute this in Supabase SQL editor if the Python script fails

ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS last_trade_date DATE;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS last_trade_time TIME;

-- Verify columns were added
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'stocks_realtime' 
AND column_name IN ('last_trade_date', 'last_trade_time')
ORDER BY column_name;
