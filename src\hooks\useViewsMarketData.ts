import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// أنواع البيانات للـ Views الجديدة
export interface ViewsMarketData {
  marketSummary: {
    total_stocks: number;
    active_stocks: number;
    high_volume_stocks: number;
    gainers: number;
    losers: number;
    unchanged: number;
    avg_change: number;
    avg_volume: number;
    total_volume: number;
    total_turnover: number;
    dividend_stocks: number;
    speculation_opportunities: number;
    last_updated: string;
  };
  technicalAnalysis: {
    bullish_stocks: number;
    bearish_stocks: number;
    strong_uptrend: number;
    strong_downtrend: number;
    above_ma20: number;
    bullish_crossover: number;
    bearish_crossover: number;
    near_targets: number;
    near_stop_loss: number;
    liquidity_inflow: number;
    liquidity_outflow: number;
    high_momentum: number;
    last_updated: string;
  };
  advancedMetrics: {
    average_pe: number;
    average_dividend_yield: number;
    market_breadth: number;
    quality_score: number;
    speculation_index: number;
    volatility_index: number;
    momentum_score: number;
    last_updated: string;
  };
  topPerformers: Array<{
    category: 'gainers' | 'losers' | 'most_active';
    symbol: string;
    name: string;
    current_price: number;
    change_percent: number;
    volume: number;
    rank: number;
  }>;
  sectorPerformance: Array<{
    sector: string;
    total_stocks: number;
    gainers: number;
    losers: number;
    avg_change: number;
    total_volume: number;
    total_turnover: number;
    avg_pe: number;
    avg_dividend_yield: number;
    last_updated: string;
  }>;
  marketLiquidity: {
    high_liquidity: number;
    medium_liquidity: number;
    low_liquidity: number;
    total_inflow: number;
    total_outflow: number;
    total_net_liquidity: number;
    total_volume_inflow: number;
    total_volume_outflow: number;
    avg_liquidity_ratio: number;
    avg_liquidity_flow: number;
    last_updated: string;
  };
}

export function useViewsMarketData() {
  return useQuery<ViewsMarketData>({
    queryKey: ['views-market-data'],
    queryFn: async () => {
      console.log('🔍 جاري جلب البيانات من Views المحسوبة...');

      // جلب جميع البيانات من Views بشكل متوازي
      const [
        marketSummaryResult,
        technicalAnalysisResult,
        advancedMetricsResult,
        topPerformersResult,
        sectorPerformanceResult,
        marketLiquidityResult
      ] = await Promise.all([
        supabase.from('market_summary_view').select('*').single(),
        supabase.from('technical_analysis_view').select('*').single(),
        supabase.from('advanced_metrics_view').select('*').single(),
        supabase.from('top_performers_view').select('*').order('rank'),
        supabase.from('sector_performance_view').select('*').order('avg_change', { ascending: false }),
        supabase.from('market_liquidity_view').select('*').single()
      ]);

      // فحص الأخطاء
      if (marketSummaryResult.error) {
        console.error('❌ خطأ في market_summary_view:', marketSummaryResult.error);
        throw marketSummaryResult.error;
      }
      if (technicalAnalysisResult.error) {
        console.error('❌ خطأ في technical_analysis_view:', technicalAnalysisResult.error);
        throw technicalAnalysisResult.error;
      }
      if (advancedMetricsResult.error) {
        console.error('❌ خطأ في advanced_metrics_view:', advancedMetricsResult.error);
        throw advancedMetricsResult.error;
      }
      if (topPerformersResult.error) {
        console.error('❌ خطأ في top_performers_view:', topPerformersResult.error);
        throw topPerformersResult.error;
      }
      if (sectorPerformanceResult.error) {
        console.error('❌ خطأ في sector_performance_view:', sectorPerformanceResult.error);
        throw sectorPerformanceResult.error;
      }
      if (marketLiquidityResult.error) {
        console.error('❌ خطأ في market_liquidity_view:', marketLiquidityResult.error);
        throw marketLiquidityResult.error;
      }

      console.log('✅ تم جلب جميع البيانات من Views بنجاح');
      console.log(`📊 إحصائيات: ${marketSummaryResult.data?.total_stocks} سهم، ${topPerformersResult.data?.length} أداء مُتقدم`);

      const result: ViewsMarketData = {
        marketSummary: marketSummaryResult.data || {} as any,
        technicalAnalysis: technicalAnalysisResult.data || {} as any,
        advancedMetrics: advancedMetricsResult.data || {} as any,
        topPerformers: topPerformersResult.data || [],
        sectorPerformance: sectorPerformanceResult.data || [],
        marketLiquidity: marketLiquidityResult.data || {} as any
      };

      return result;
    },
    staleTime: 3 * 60 * 1000, // 3 minutes - البيانات تُعتبر حديثة لمدة 3 دقائق
    refetchInterval: 5 * 60 * 1000, // 5 minutes - تحديث كل 5 دقائق
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Hook مُبسط للحصول على ملخص سريع فقط
export function useQuickMarketSummary() {
  return useQuery({
    queryKey: ['quick-market-summary'],
    queryFn: async () => {
      console.log('⚡ جاري جلب الملخص السريع...');
      
      const { data, error } = await supabase
        .from('market_summary_view')
        .select('*')
        .single();

      if (error) {
        console.error('❌ خطأ في الملخص السريع:', error);
        throw error;
      }

      console.log('✅ تم جلب الملخص السريع');
      return data;
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // 2 minutes
    retry: 2
  });
}

// Hook للحصول على أفضل الأسهم فقط
export function useTopStocksOnly() {
  return useQuery({
    queryKey: ['top-stocks-only'],
    queryFn: async () => {
      console.log('🏆 جاري جلب أفضل الأسهم...');
      
      const { data, error } = await supabase
        .from('top_performers_view')
        .select('*')
        .order('rank')
        .limit(15); // أفضل 15 فقط

      if (error) {
        console.error('❌ خطأ في أفضل الأسهم:', error);
        throw error;
      }

      // تجميع حسب الفئة
      const grouped = {
        gainers: data.filter(s => s.category === 'gainers').slice(0, 5),
        losers: data.filter(s => s.category === 'losers').slice(0, 5),
        mostActive: data.filter(s => s.category === 'most_active').slice(0, 5)
      };

      console.log(`✅ تم جلب أفضل الأسهم: ${grouped.gainers.length} رابح، ${grouped.losers.length} خاسر، ${grouped.mostActive.length} نشط`);
      return grouped;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 3 * 60 * 1000, // 3 minutes
    retry: 2
  });
}
