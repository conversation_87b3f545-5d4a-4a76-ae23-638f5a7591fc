# 🇪🇬 EGX Stock AI Oracle - مرشد الأسهم المصرية الذكي

> **آخر تحديث:** 16 يونيو، 2025 ✨  
> **الحالة:** مكتمل ومجهز للإنتاج 🚀  
> **الهيكل:** تم تنظيمه بالكامل 📁

**Live Demo:** [https://lovable.dev/projects/49078af7-79bf-486a-854a-16daaf6c8a6c](https://lovable.dev/projects/49078af7-79bf-486a-854a-16daaf6c8a6c)

## 🚀 نظرة عامة

منصة تحليل الأسهم المصرية الأكثر تقدماً وشمولية، تجمع بين الذكاء الاصطناعي وأحدث تقنيات التحليل الفني لتوفير رؤية شاملة للسوق المصري. تدعم 174 سهم عبر 19 قطاع مختلف مع تحليل فني ومالي متقدم.

---

## ✨ الميزات الرئيسية

### 📊 **التحليل الشامل للأسهم** 🆕
- **شارت TradingView احترافي**: أكثر من 100 مؤشر فني وأدوات رسم متقدمة
- **جميع مدارس التحليل الفني**: كلاسيكي، هارمونيك، Elliott Wave، Wyckoff، ICT، SMC
- **كشف الأنماط التلقائي**: Head & Shoulders، المثلثات، Double Top/Bottom
- **تحليل Smart Money**: Order Blocks، Fair Value Gaps، Market Structure
- **Volume Profile**: تحليل توزيع الحجم عبر مستويات الأسعار
- **وصول سريع**: من أي مكان في التطبيق بنقرة واحدة

### 🎯 **مرشح الأسهم المتقدم**
- **البحث الذكي**: بالرمز أو اسم الشركة عبر 174 سهم
- **تصفية القطاعات**: 19 قطاع مختلف مع تحليل الأداء
- **المؤشرات الفنية**: RSI، MACD، Moving Averages، Bollinger Bands
- **الإشارات الفنية**: اتجاه صاعد، هابط، اختراق، تشبع بيعي/شرائي
- **النطاقات المرنة**: السعر، التغيير، P/E، عائد التوزيعات
- **توصيات ذكية**: مبنية على التحليل الفني والأساسي

- **Market Overview**: Real-time indices (EGX30, EGX70), market stats, gainers/losers/unchanged stocks.
- **Quick Stats**: Top gainers, most active stocks, investment opportunities.
- **Charts & Technicals**: Interactive charts with technical analysis tools (RSI, MACD, Bollinger Bands, etc.).
- **Stock Screener**: Filter stocks by technical/financial criteria.
- **Analytics**: AI-driven sentiment, volatility, technical pattern recognition, backtesting, and much more.
- **Portfolio Management**: Manage, track, and simulate investment portfolios and paper trading.
- **News & Calendar**: Financial news aggregation, event calendars synced with the Egyptian stock market.
- **Mobile & PWA Ready**: Modern mobile experience and installable app.
- **Alerting**: Custom price alerts and smart notifications.
- **Personalization**: Dark/light themes, dashboard customizer, and tailored onboarding.
- **Supabase Integration**: Scalable backend, authentication, and real-time updates for the Egyptian market.
- **Advanced Analytics**: ML predictions, multi-timeframe analysis, correlation, and more.
- **Integration & Export**: Telegram bot notifications and data export features.

---

## 🔥 آخر التحديثات (يونيو 2025)

### ✅ شارت مؤشر EGX30 في النظرة العامة
- **استبدال شارت COMI**: تم استبدال شارت سهم COMI بشارت مؤشر EGX30 في تاب "النظرة العامة"
- **نظرة شاملة للسوق**: الآن يمكن للمستخدمين رؤية أداء السوق المصري ككل من خلال مؤشر EGX30
- **معلومات تفصيلية**: يتضمن الشارت معلومات عن:
  - أداء مؤشر EGX30 بالوقت الفعلي
  - حركة السوق العامة والاتجاهات
  - أدوات التحليل الفني لمؤشر السوق
  - مقارنة الأداء عبر فترات زمنية مختلفة

### ✅ تحليل شامل للأسهم مع شارت TradingView
- **شارت TradingView احترافي**: أكثر من 100 مؤشر فني وأدوات رسم متقدمة
- **جميع مدارس التحليل الفني**: كلاسيكي، هارمونيك، Elliott Wave، Wyckoff، ICT، SMC
- **كشف الأنماط التلقائي**: Head & Shoulders، المثلثات، Double Top/Bottom
- **تحليل Smart Money**: Order Blocks، Fair Value Gaps، Market Structure
- **Volume Profile**: تحليل توزيع الحجم عبر مستويات الأسعار
- **وصول سريع**: من أي مكان في التطبيق بنقرة واحدة

---

## Stack

- **Frontend**: React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL, real-time, Edge Functions)
- **State Management / Data Fetching**: Tanstack React Query
- **Charts**: recharts
- **Icons**: lucide-react
- **Mobile**: PWA-ready, gesture support

---

## Project Structure

```
src/
│
├── components/                # All UI components (dashboard, analytics, trading, etc.)
├── hooks/                     # React hooks for data fetching, state management, etc.
├── integrations/supabase/     # Supabase client and types
├── pages/                     # Top-level pages/routes
├── lib/                       # Utilities and shared helpers
├── App.tsx                    # Main app entry
└── main.tsx                   # Vite entry point
```

## Data & API

- Real-time and historical data is retrieved directly from [Supabase tables](https://supabase.com/).
- Supabase Edge Functions are used for algorithmic/backtesting triggers and secure operations.

**ETL Scripts for Data Sync (Python):**
- See `scripts/data_sync/` for:
  - `load_historical.py`: Loads EOD historical prices to Supabase.
  - `load_realtime.py`: Updates live prices and volumes.
  - `load_financials.py`: Syncs fundamental/financials data.

**Frontend Mock Data Replacement:**
- See `mock_data_components.txt` for a comprehensive list of components/pages currently using mock data—that must be switched to Supabase APIs for real deployment.

---

## Getting Started

1. **Clone the Repo**

    ```
    git clone <YOUR_GIT_URL>
    cd <YOUR_PROJECT_NAME>
    ```

2. **Install Dependencies**

    ```
    npm install
    ```

3. **Setup Supabase**

    - [Create a Supabase project](https://app.supabase.com/).
    - Add the required tables as per the backend scripts.
    - Get your Supabase URL & public/service role keys.
    - Set up your environment variables if needed.

4. **Run the App**

    ```
    npm run dev
    ```

5. **Data Sync (Optional, for your backend)**

    - Run the Python scripts in `scripts/data_sync/` to load/sync your market data.  
    - **Note:** Replace `"YOUR_SUPABASE_SERVICE_ROLE_KEY"` in each script with your actual key.

---

## Deployment

- Deploy directly from the Lovable editor using "Share → Publish", or
- Use Vercel/Netlify or your preferred React hosting platform.  
- For custom domains, see Project Settings → Domains in Lovable.

---

## Customization & Extensions

- Add or replace icons via [lucide-react](https://lucide.dev/).
- Modify dashboard widgets, themes, and sections to tailor to your needs.
- Use the shadcn/ui system for additional modern UI components.

---

## Notes

- See `mock_data_components.txt` to track and migrate all "mock → API" changes.
- PWA and mobile features are enabled out-of-the-box.
- Telegram integration and advanced analytics require additional Supabase configuration.

---

## Resources

- [Lovable Documentation](https://docs.lovable.dev)
- [Supabase Docs](https://supabase.com/docs)
- [shadcn/ui](https://ui.shadcn.com/)
- [Tanstack React Query](https://tanstack.com/query/v5)
- [Lucide Icons](https://lucide.dev/)
- [Recharts](https://recharts.org/)

---

**For any questions, suggestions, or custom builds, please contact the project owner or open an issue!**
