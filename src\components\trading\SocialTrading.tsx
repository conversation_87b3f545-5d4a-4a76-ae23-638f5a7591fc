
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Users, Star, TrendingUp, MessageCircle, Copy, Eye } from 'lucide-react';

interface Trader {
  id: string;
  name: string;
  avatar: string;
  followers: number;
  winRate: number;
  totalReturn: number;
  risk: 'low' | 'medium' | 'high';
  trades: number;
  rating: number;
  strategy: string;
}

interface Trade {
  id: string;
  trader: string;
  symbol: string;
  action: 'buy' | 'sell';
  price: number;
  quantity: number;
  timestamp: string;
  profitLoss?: number;
}

const SocialTrading = () => {
  const [topTraders] = useState<Trader[]>([
    {
      id: '1',
      name: 'أحمد المصري',
      avatar: 'AM',
      followers: 1250,
      winRate: 78,
      totalReturn: 34.5,
      risk: 'medium',
      trades: 89,
      rating: 4.7,
      strategy: 'التحليل الفني'
    },
    {
      id: '2',
      name: 'سارة أحمد',
      avatar: 'SA',
      followers: 950,
      winRate: 82,
      totalReturn: 28.3,
      risk: 'low',
      trades: 67,
      rating: 4.9,
      strategy: 'الاستثمار طويل المدى'
    },
    {
      id: '3',
      name: 'محمد علي',
      avatar: 'MA',
      followers: 2100,
      winRate: 71,
      totalReturn: 45.8,
      risk: 'high',
      trades: 156,
      rating: 4.5,
      strategy: 'التداول اليومي'
    }
  ]);

  const [recentTrades] = useState<Trade[]>([
    {
      id: '1',
      trader: 'أحمد المصري',
      symbol: 'COMI',
      action: 'buy',
      price: 52.30,
      quantity: 200,
      timestamp: '2024-01-15 14:30',
      profitLoss: 850
    },
    {
      id: '2',
      trader: 'سارة أحمد',
      symbol: 'ETEL',
      action: 'sell',
      price: 18.75,
      quantity: 500,
      timestamp: '2024-01-15 13:45',
      profitLoss: -320
    }
  ]);

  const [followedTraders, setFollowedTraders] = useState<string[]>([]);

  const followTrader = (traderId: string) => {
    if (!followedTraders.includes(traderId)) {
      setFollowedTraders([...followedTraders, traderId]);
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'low': return 'منخفض';
      case 'medium': return 'متوسط';
      case 'high': return 'مرتفع';
      default: return 'غير محدد';
    }
  };

  return (
    <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-800">
          <Users className="h-5 w-5" />
          التداول الاجتماعي
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Top Traders Leaderboard */}
        <div>
          <h3 className="text-lg font-semibold mb-4">أفضل المتداولين</h3>
          <div className="space-y-4">
            {topTraders.map((trader, index) => (
              <div key={trader.id} className="p-4 bg-white/80 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Badge className="bg-blue-100 text-blue-800 text-xs">
                        #{index + 1}
                      </Badge>
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-blue-100 text-blue-800">
                          {trader.avatar}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    
                    <div>
                      <div className="font-medium">{trader.name}</div>
                      <div className="text-sm text-gray-600">{trader.strategy}</div>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                          <span className="text-xs">{trader.rating}</span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Users className="h-3 w-3" />
                          {trader.followers}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-xs text-gray-500">العائد الإجمالي</div>
                      <div className="font-bold text-green-600">+{trader.totalReturn}%</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">نسبة النجاح</div>
                      <div className="font-bold">{trader.winRate}%</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">المخاطر</div>
                      <Badge className={getRiskColor(trader.risk)}>
                        {getRiskLabel(trader.risk)}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant={followedTraders.includes(trader.id) ? "default" : "outline"}
                      onClick={() => followTrader(trader.id)}
                    >
                      {followedTraders.includes(trader.id) ? 'متابع' : 'متابعة'}
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-1" />
                      عرض
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Trades Feed */}
        <div>
          <h3 className="text-lg font-semibold mb-4">آخر الصفقات</h3>
          <div className="space-y-3">
            {recentTrades.map(trade => (
              <div key={trade.id} className="p-3 bg-white/80 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge className={trade.action === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {trade.action === 'buy' ? 'شراء' : 'بيع'}
                    </Badge>
                    <div>
                      <div className="font-medium">{trade.trader}</div>
                      <div className="text-sm text-gray-600">
                        {trade.symbol} - {trade.quantity} سهم @ {trade.price} ج.م
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-xs text-gray-500">{trade.timestamp}</div>
                    {trade.profitLoss && (
                      <div className={`font-medium ${trade.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {trade.profitLoss >= 0 ? '+' : ''}{trade.profitLoss} ج.م
                      </div>
                    )}
                  </div>

                  <Button size="sm" variant="outline">
                    <Copy className="h-4 w-4 mr-1" />
                    نسخ
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Social Trading Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-blue-50">
            <CardContent className="p-4 text-center">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h4 className="font-medium">النسخ التلقائي</h4>
              <p className="text-sm text-gray-600">انسخ صفقات أفضل المتداولين تلقائياً</p>
            </CardContent>
          </Card>

          <Card className="bg-green-50">
            <CardContent className="p-4 text-center">
              <MessageCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h4 className="font-medium">مناقشات السوق</h4>
              <p className="text-sm text-gray-600">شارك في المناقشات والتحليلات</p>
            </CardContent>
          </Card>

          <Card className="bg-purple-50">
            <CardContent className="p-4 text-center">
              <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h4 className="font-medium">إشارات التداول</h4>
              <p className="text-sm text-gray-600">احصل على إشارات من خبراء السوق</p>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
};

export default SocialTrading;
