import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Filter, Save, TrendingUp, TrendingDown, Volume, Eye } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  pe: number;
  rsi: number;
}

interface FilterCriteria {
  minPrice: string;
  maxPrice: string;
  minVolume: string;
  maxPE: string;
  minRSI: string;
  maxRSI: string;
  changeDirection: string;
}

const StockScreener = () => {
  const [filteredStocks, setFilteredStocks] = useState<StockData[]>([]);
  const [filters, setFilters] = useState<FilterCriteria>({
    minPrice: '',
    maxPrice: '',
    minVolume: '',
    maxPE: '',
    minRSI: '',
    maxRSI: '',
    changeDirection: 'all'
  });
  const [savedFilters, setSavedFilters] = useState<{ name: string; filters: FilterCriteria }[]>([]);
  const [filterName, setFilterName] = useState('');

  // Fetch real stock data from Supabase
  const { data: stocksData, isLoading, error } = useQuery({
    queryKey: ['stock_screener_data'],
    queryFn: async () => {      // Get real-time price data
      const { data: realtimeData, error: realtimeError } = await supabase
        .from('stocks_realtime')
        .select('symbol, current_price, change_amount, change_percent, volume, market_cap')        .not('symbol', 'like', '*EGX*') // Exclude market indices
        .gt('volume', 0); // Exclude stocks with zero or null volume (delisted/suspended)

      if (realtimeError) throw realtimeError;

      // Get technical indicators (RSI)
      const { data: technicalData, error: technicalError } = await supabase
        .from('technical_indicators')
        .select('symbol, rsi_14')
        .order('date', { ascending: false });

      if (technicalError) throw technicalError;      // Get stock master data for names
      const { data: masterData, error: masterError } = await supabase
        .from('stocks_master')
        .select('symbol, name');

      if (masterError) throw masterError;

      // Get financial data for PE ratios
      const { data: financialData, error: financialError } = await supabase
        .from('stocks_financials')
        .select('symbol, pe_ratio');

      if (financialError) throw financialError;      // Combine all data
      const combinedData: StockData[] = (realtimeData || []).map(stock => {
        const technical = technicalData?.find(t => t.symbol === stock.symbol);
        const master = masterData?.find(m => m.symbol === stock.symbol);
        const financial = financialData?.find(f => f.symbol === stock.symbol);

        return {
          symbol: stock.symbol,
          name: master?.name || stock.symbol,
          price: stock.current_price || 0,
          change: stock.change_amount || 0,
          changePercent: stock.change_percent || 0,
          volume: stock.volume || 0,
          marketCap: stock.market_cap || 0,
          pe: financial?.pe_ratio || 0,
          rsi: technical?.rsi_14 || 50
        };
      });

      return combinedData;
    },
    refetchInterval: 300000, // Refetch every 5 minutes
  });  // Transform Supabase data to StockData format
  const stocks: StockData[] = useMemo(() => stocksData || [], [stocksData]);

  // Initialize component and load saved filters
  useEffect(() => {
    // Load saved filters
    const saved = localStorage.getItem('savedFilters');
    if (saved) {
      setSavedFilters(JSON.parse(saved));
    }
  }, []);

  // Define applyFilters before using it in the dependency array
  const applyFilters = useCallback(() => {
    const filtered = stocks.filter(stock => {
      const matchesPrice = (!filters.minPrice || stock.price >= parseFloat(filters.minPrice)) &&
                          (!filters.maxPrice || stock.price <= parseFloat(filters.maxPrice));
      
      const matchesVolume = !filters.minVolume || stock.volume >= parseFloat(filters.minVolume);
      
      const matchesPE = !filters.maxPE || stock.pe <= parseFloat(filters.maxPE);
      
      const matchesRSI = (!filters.minRSI || stock.rsi >= parseFloat(filters.minRSI)) &&
                        (!filters.maxRSI || stock.rsi <= parseFloat(filters.maxRSI));
      
      const matchesDirection = filters.changeDirection === 'all' ||
                              (filters.changeDirection === 'up' && stock.change > 0) ||
                              (filters.changeDirection === 'down' && stock.change < 0);
      
      return matchesPrice && matchesVolume && matchesPE && matchesRSI && matchesDirection;
    });
      setFilteredStocks(filtered);
  }, [stocks, filters]);
  
  // Apply filters when stocks data or filters change
  useEffect(() => {
    if (stocks.length > 0) {
      applyFilters();
    }
  }, [filters, stocks, applyFilters]);

  const resetFilters = () => {
    setFilters({
      minPrice: '',
      maxPrice: '',
      minVolume: '',
      maxPE: '',
      minRSI: '',
      maxRSI: '',
      changeDirection: 'all'
    });
    setFilteredStocks(stocks);
  };

  const saveFilter = () => {
    if (!filterName) return;
    
    const newSavedFilter = { name: filterName, filters };
    const updated = [...savedFilters, newSavedFilter];
    setSavedFilters(updated);
    localStorage.setItem('savedFilters', JSON.stringify(updated));
    setFilterName('');
  };

  const loadFilter = (savedFilter: { name: string; filters: FilterCriteria }) => {
    setFilters(savedFilter.filters);
  };

  const presetFilters = {
    trendingUp: () => setFilters({...filters, changeDirection: 'up', minRSI: '60'}),
    oversold: () => setFilters({...filters, maxRSI: '30'}),
    highVolume: () => setFilters({...filters, minVolume: '1000000'}),
    lowPE: () => setFilters({...filters, maxPE: '15'})
  };

  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 1000000000) {
      return `${(marketCap / 1000000000).toFixed(1)}B`;
    }
    return `${(marketCap / 1000000).toFixed(0)}M`;
  };
  return (
    <div className="space-y-6">
      {/* Loading and Error States */}
      {isLoading && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-blue-600">جاري تحميل بيانات الأسهم...</div>
          </CardContent>
        </Card>
      )}
      
      {error && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-red-600">خطأ في تحميل بيانات الأسهم</div>
          </CardContent>
        </Card>
      )}
      
      {/* Filter Controls */}
      {!isLoading && !error && (
        <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800 arabic" dir="rtl">
            <Filter className="h-5 w-5" />
            مرشح الأسهم المتقدم
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Preset Filters */}
          <div>
            <label className="text-sm font-medium mb-2 block arabic" dir="rtl">مرشحات جاهزة</label>
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm" onClick={presetFilters.trendingUp}>
                اتجاه صاعد
              </Button>
              <Button variant="outline" size="sm" onClick={presetFilters.oversold}>
                مبالغ في البيع
              </Button>
              <Button variant="outline" size="sm" onClick={presetFilters.highVolume}>
                حجم تداول عالي
              </Button>
              <Button variant="outline" size="sm" onClick={presetFilters.lowPE}>
                نسبة سعر/ربح منخفضة
              </Button>
            </div>
          </div>

          {/* Custom Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div>
              <label className="text-sm font-medium mb-1 block arabic" dir="rtl">أدنى سعر</label>
              <Input
                type="number"
                placeholder="0"
                value={filters.minPrice}
                onChange={(e) => setFilters({...filters, minPrice: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block arabic" dir="rtl">أعلى سعر</label>
              <Input
                type="number"
                placeholder="∞"
                value={filters.maxPrice}
                onChange={(e) => setFilters({...filters, maxPrice: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block arabic" dir="rtl">حجم التداول</label>
              <Input
                type="number"
                placeholder="0"
                value={filters.minVolume}
                onChange={(e) => setFilters({...filters, minVolume: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block arabic" dir="rtl">أقصى نسبة س/ر</label>
              <Input
                type="number"
                placeholder="∞"
                value={filters.maxPE}
                onChange={(e) => setFilters({...filters, maxPE: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block arabic" dir="rtl">مؤشر القوة النسبية</label>
              <div className="flex gap-1">
                <Input
                  type="number"
                  placeholder="0"
                  value={filters.minRSI}
                  onChange={(e) => setFilters({...filters, minRSI: e.target.value})}
                  className="text-right text-xs"
                  dir="rtl"
                />
                <Input
                  type="number"
                  placeholder="100"
                  value={filters.maxRSI}
                  onChange={(e) => setFilters({...filters, maxRSI: e.target.value})}
                  className="text-right text-xs"
                  dir="rtl"
                />
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block arabic" dir="rtl">اتجاه السعر</label>
              <Select value={filters.changeDirection} onValueChange={(value) => setFilters({...filters, changeDirection: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">الكل</SelectItem>
                  <SelectItem value="up">صاعد</SelectItem>
                  <SelectItem value="down">هابط</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={resetFilters} variant="outline">
              إعادة تعيين
            </Button>
            <div className="flex gap-2">
              <Input
                placeholder="اسم المرشح"
                value={filterName}
                onChange={(e) => setFilterName(e.target.value)}
                className="w-32 text-right"
                dir="rtl"
              />
              <Button onClick={saveFilter} variant="outline" size="sm">
                <Save className="h-4 w-4" />
              </Button>
            </div>
          </div>          {/* Saved Filters */}
          {savedFilters.length > 0 && (
            <div>
              <label className="text-sm font-medium mb-2 block arabic" dir="rtl">المرشحات المحفوظة</label>
              <div className="flex flex-wrap gap-2">
                {savedFilters.map((saved, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => loadFilter(saved)}
                  >
                    {saved.name}
                  </Button>
                ))}
              </div>            </div>
          )}        </CardContent>
        </Card>
      )}

      {/* Results */}
      {!isLoading && !error && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="arabic" dir="rtl">نتائج المرشح ({filteredStocks.length} سهم)</span>
              <Eye className="h-5 w-5" />
            </CardTitle>
          </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredStocks.map(stock => (
              <div key={stock.symbol} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center gap-4">
                  <div>
                    <div className="font-bold text-lg">{stock.symbol}</div>
                    <div className="text-sm text-muted-foreground">{stock.name}</div>
                  </div>
                  <Badge variant={stock.change >= 0 ? 'default' : 'destructive'}>
                    {stock.change >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                    {stock.changePercent.toFixed(2)}%
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center text-sm">
                  <div>
                    <div className="text-xs text-muted-foreground">السعر</div>
                    <div className="font-semibold">{stock.price.toFixed(2)} ج.م</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">الحجم</div>
                    <div className="font-semibold">{(stock.volume / 1000000).toFixed(1)}M</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">القيمة السوقية</div>
                    <div className="font-semibold">{formatMarketCap(stock.marketCap)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">نسبة س/ر</div>
                    <div className="font-semibold">{stock.pe.toFixed(1)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">RSI</div>
                    <div className={`font-semibold ${stock.rsi > 70 ? 'text-red-600' : stock.rsi < 30 ? 'text-green-600' : ''}`}>
                      {stock.rsi.toFixed(0)}                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>        </CardContent>
        </Card>
      )}
    </div>
  );
};

export default StockScreener;