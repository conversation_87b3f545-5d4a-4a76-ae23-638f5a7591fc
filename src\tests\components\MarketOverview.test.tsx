import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import MarketOverview from '../../components/MarketOverview';
import { createTestQueryClient, TestWrapper } from '../test-utils';
import { QueryClient } from '@tanstack/react-query';

// Mock the custom hooks
vi.mock('../../hooks/useMarketOverview', () => ({
  useMarketOverview: () => ({
    data: {
      indices: [
        { symbol: 'EGX30', value: 25000, change: 150, changePercent: 0.6 },
        { symbol: 'EGX70', value: 3500, change: -25, changePercent: -0.7 }
      ],
      topGainers: [
        { symbol: 'ATQA', price: 15.5, change: 2.1, changePercent: 13.5 },
        { symbol: 'CLHO', price: 8.2, change: 1.2, changePercent: 14.6 }
      ],
      topLosers: [
        { symbol: 'EFIH', price: 12.3, change: -1.8, changePercent: -12.8 },
        { symbol: 'OCDI', price: 9.7, change: -1.1, changePercent: -10.2 }
      ]
    },
    isLoading: false,
    error: null
  })
}));

describe('MarketOverview', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = createTestQueryClient();
    vi.clearAllMocks();
  });

  it('renders market overview with indices and movers', async () => {
    render(
      <TestWrapper>
        <MarketOverview />
      </TestWrapper>
    );

    // Check if indices are displayed
    expect(screen.getByText('EGX30')).toBeInTheDocument();
    expect(screen.getByText('25,000')).toBeInTheDocument();
    expect(screen.getByText('+150')).toBeInTheDocument();

    // Check if top gainers are displayed
    expect(screen.getByText('ATQA')).toBeInTheDocument();
    expect(screen.getByText('15.50')).toBeInTheDocument();
    expect(screen.getByText('+13.5%')).toBeInTheDocument();

    // Check if top losers are displayed
    expect(screen.getByText('EFIH')).toBeInTheDocument();
    expect(screen.getByText('12.30')).toBeInTheDocument();
    expect(screen.getByText('-12.8%')).toBeInTheDocument();
  });

  it('handles loading state correctly', () => {
    vi.doMock('../../hooks/useMarketOverview', () => ({
      useMarketOverview: () => ({
        data: null,
        isLoading: true,
        error: null
      })
    }));

    render(
      <TestWrapper>
        <MarketOverview />
      </TestWrapper>
    );

    expect(screen.getByText(/جاري التحميل/)).toBeInTheDocument();
  });

  it('handles error state correctly', () => {
    vi.doMock('../../hooks/useMarketOverview', () => ({
      useMarketOverview: () => ({
        data: null,
        isLoading: false,
        error: new Error('Failed to fetch market data')
      })
    }));

    render(
      <TestWrapper>
        <MarketOverview />
      </TestWrapper>
    );

    expect(screen.getByText(/حدث خطأ أثناء تحميل بيانات السوق/)).toBeInTheDocument();
  });
});
