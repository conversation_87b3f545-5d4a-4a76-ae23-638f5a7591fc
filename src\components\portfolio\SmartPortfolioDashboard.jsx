import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Shield, 
  Copy, 
  BarChart3, 
  PieChart, 
  Activity,
  Zap,
  Award,
  AlertTriangle
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const SmartPortfolioDashboard = () => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('overview');
  const [timeframe, setTimeframe] = useState('1M');
  const [realTimeData, setRealTimeData] = useState({});

  useEffect(() => {
    fetchPortfolioData();
    
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(fetchPortfolioData, 30000);
    
    // الاتصال بـ WebSocket للتحديثات الفورية
    connectWebSocket();
    
    return () => {
      clearInterval(interval);
      // إغلاق WebSocket
    };
  }, [timeframe]);

  const fetchPortfolioData = async () => {
    try {
      const response = await fetch(`/api/v1/smart-portfolio/dashboard?timeframe=${timeframe}`);
      const data = await response.json();
      setPortfolioData(data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching portfolio data:', error);
      setLoading(false);
    }
  };

  const connectWebSocket = () => {
    // سيتم تنفيذ اتصال WebSocket للتحديثات الفورية
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <Brain className="w-16 h-16 text-blue-500 mx-auto mb-4 animate-pulse" />
          <h2 className="text-2xl font-bold text-gray-700 mb-2">المحفظة الذكية</h2>
          <p className="text-gray-500">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="smart-portfolio-dashboard bg-gray-50 min-h-screen" dir="rtl">
      {/* Header */}
      <PortfolioHeader 
        portfolioData={portfolioData}
        timeframe={timeframe}
        onTimeframeChange={setTimeframe}
      />

      {/* Navigation */}
      <PortfolioNavigation 
        selectedTab={selectedTab}
        onTabChange={setSelectedTab}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {selectedTab === 'overview' && (
          <OverviewTab portfolioData={portfolioData} />
        )}
        
        {selectedTab === 'positions' && (
          <PositionsTab portfolioData={portfolioData} />
        )}
        
        {selectedTab === 'performance' && (
          <PerformanceTab portfolioData={portfolioData} timeframe={timeframe} />
        )}
        
        {selectedTab === 'analytics' && (
          <AnalyticsTab portfolioData={portfolioData} />
        )}
        
        {selectedTab === 'copy-trading' && (
          <CopyTradingTab portfolioData={portfolioData} />
        )}
      </div>
    </div>
  );
};

// Header Component
const PortfolioHeader = ({ portfolioData, timeframe, onTimeframeChange }) => {
  const summary = portfolioData?.portfolio_summary || {};
  const metrics = portfolioData?.performance_metrics || {};

  return (
    <div className="bg-gradient-to-l from-blue-600 to-purple-600 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Title and Controls */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Brain className="w-10 h-10 ml-3" />
            <div>
              <h1 className="text-3xl font-bold">المحفظة الذكية</h1>
              <p className="text-blue-100">نظام إدارة المحافظ بالذكاء الاصطناعي</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 space-x-reverse">
            <TimeframeSelector 
              selected={timeframe}
              onChange={onTimeframeChange}
            />
            <div className="text-sm">
              <span className="block text-blue-100">آخر تحديث</span>
              <span className="font-semibold">{new Date().toLocaleTimeString('ar-EG')}</span>
            </div>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <MetricCard
            title="القيمة الإجمالية"
            value={`${summary.current_portfolio_value?.toLocaleString()} جنيه`}
            change={metrics.total_return_percentage}
            icon={<DollarSign className="w-6 h-6" />}
            trend={metrics.total_return_percentage >= 0 ? 'up' : 'down'}
          />
          
          <MetricCard
            title="العائد الإجمالي"
            value={`${metrics.total_return_percentage?.toFixed(2)}%`}
            change={metrics.daily_return}
            icon={<TrendingUp className="w-6 h-6" />}
            trend={metrics.total_return_percentage >= 0 ? 'up' : 'down'}
          />
          
          <MetricCard
            title="نسبة شارب"
            value={metrics.sharpe_ratio?.toFixed(2)}
            benchmark="1.5"
            icon={<Award className="w-6 h-6" />}
            trend={metrics.sharpe_ratio >= 1.5 ? 'up' : 'down'}
          />
          
          <MetricCard
            title="معدل الربح"
            value={`${metrics.win_rate?.toFixed(1)}%`}
            change={metrics.win_rate - 70}
            icon={<Target className="w-6 h-6" />}
            trend={metrics.win_rate >= 70 ? 'up' : 'down'}
          />
        </div>
      </div>
    </div>
  );
};

// Metric Card Component
const MetricCard = ({ title, value, change, icon, trend, benchmark }) => {
  const trendColor = trend === 'up' ? 'text-green-400' : 'text-red-400';
  const bgColor = trend === 'up' ? 'bg-green-500/20' : 'bg-red-500/20';

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
      <div className="flex items-center justify-between mb-2">
        <div className={`p-2 rounded-lg ${bgColor}`}>
          {icon}
        </div>
        {change !== undefined && (
          <div className={`flex items-center text-sm ${trendColor}`}>
            {trend === 'up' ? <TrendingUp className="w-4 h-4 ml-1" /> : <TrendingDown className="w-4 h-4 ml-1" />}
            {Math.abs(change).toFixed(2)}%
          </div>
        )}
      </div>
      
      <div>
        <p className="text-blue-100 text-sm">{title}</p>
        <p className="text-2xl font-bold">{value}</p>
        {benchmark && (
          <p className="text-blue-200 text-xs">المستهدف: {benchmark}</p>
        )}
      </div>
    </div>
  );
};

// Navigation Component
const PortfolioNavigation = ({ selectedTab, onTabChange }) => {
  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: BarChart3 },
    { id: 'positions', name: 'المراكز النشطة', icon: Activity },
    { id: 'performance', name: 'الأداء', icon: TrendingUp },
    { id: 'analytics', name: 'التحليلات', icon: PieChart },
    { id: 'copy-trading', name: 'نسخ التداول', icon: Copy }
  ];

  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4">
        <nav className="flex space-x-8 space-x-reverse">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center px-4 py-4 text-sm font-medium border-b-2 transition-colors ${
                selectedTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-5 h-5 ml-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

// Timeframe Selector Component
const TimeframeSelector = ({ selected, onChange }) => {
  const timeframes = [
    { value: '1D', label: 'يوم' },
    { value: '1W', label: 'أسبوع' },
    { value: '1M', label: 'شهر' },
    { value: '3M', label: '3 أشهر' },
    { value: '6M', label: '6 أشهر' },
    { value: '1Y', label: 'سنة' }
  ];

  return (
    <div className="flex bg-white/10 rounded-lg p-1">
      {timeframes.map((tf) => (
        <button
          key={tf.value}
          onClick={() => onChange(tf.value)}
          className={`px-3 py-1 text-sm rounded transition-colors ${
            selected === tf.value
              ? 'bg-white text-blue-600 font-semibold'
              : 'text-blue-100 hover:text-white'
          }`}
        >
          {tf.label}
        </button>
      ))}
    </div>
  );
};

// Overview Tab Component
const OverviewTab = ({ portfolioData }) => {
  const positions = portfolioData?.active_positions || [];
  const metrics = portfolioData?.performance_metrics || {};
  const recentSignals = portfolioData?.recent_signal_processing || [];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Portfolio Performance Chart */}
      <div className="lg:col-span-2">
        <PortfolioPerformanceChart data={portfolioData} />
      </div>
      
      {/* Quick Stats */}
      <div className="space-y-6">
        <QuickStatsCard metrics={metrics} />
        <RiskMetricsCard metrics={metrics} />
      </div>
      
      {/* Active Positions Summary */}
      <div className="lg:col-span-2">
        <ActivePositionsSummary positions={positions.slice(0, 5)} />
      </div>
      
      {/* Recent Signal Processing */}
      <div>
        <RecentSignalsCard signals={recentSignals.slice(0, 10)} />
      </div>
    </div>
  );
};

// Portfolio Performance Chart Component
const PortfolioPerformanceChart = ({ data }) => {
  // بيانات وهمية للرسم البياني
  const chartData = [
    { date: '2024-01', portfolio: 1000000, benchmark: 1000000 },
    { date: '2024-02', portfolio: 1025000, benchmark: 1015000 },
    { date: '2024-03', portfolio: 1055000, benchmark: 1030000 },
    { date: '2024-04', portfolio: 1080000, benchmark: 1045000 },
    { date: '2024-05', portfolio: 1095000, benchmark: 1055000 },
    { date: '2024-06', portfolio: 1125000, benchmark: 1070000 }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">أداء المحفظة</h3>
        <div className="flex items-center space-x-4 space-x-reverse text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
            <span>المحفظة الذكية</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gray-400 rounded mr-2"></div>
            <span>مؤشر EGX30</span>
          </div>
        </div>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip 
              formatter={(value) => [`${value.toLocaleString()} جنيه`, '']}
              labelStyle={{ textAlign: 'right' }}
            />
            <Line 
              type="monotone" 
              dataKey="portfolio" 
              stroke="#3B82F6" 
              strokeWidth={3}
              dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="benchmark" 
              stroke="#9CA3AF" 
              strokeWidth={2}
              strokeDasharray="5 5"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      
      <div className="mt-4 grid grid-cols-3 gap-4 text-center">
        <div>
          <p className="text-sm text-gray-600">العائد الإجمالي</p>
          <p className="text-lg font-semibold text-green-600">+12.5%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">مقارنة بالمؤشر</p>
          <p className="text-lg font-semibold text-blue-600">+5.5%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">نسبة شارب</p>
          <p className="text-lg font-semibold text-purple-600">1.85</p>
        </div>
      </div>
    </div>
  );
};

// Quick Stats Card Component
const QuickStatsCard = ({ metrics }) => {
  const stats = [
    {
      label: 'إجمالي الصفقات',
      value: metrics.total_trades || 0,
      icon: Activity,
      color: 'blue'
    },
    {
      label: 'معدل الربح',
      value: `${(metrics.win_rate || 0).toFixed(1)}%`,
      icon: Target,
      color: 'green'
    },
    {
      label: 'أقصى انخفاض',
      value: `${(metrics.max_drawdown || 0).toFixed(1)}%`,
      icon: TrendingDown,
      color: 'red'
    },
    {
      label: 'التقلبات',
      value: `${(metrics.volatility || 0).toFixed(1)}%`,
      icon: Activity,
      color: 'yellow'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">إحصائيات سريعة</h3>
      
      <div className="space-y-4">
        {stats.map((stat, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg bg-${stat.color}-100`}>
                <stat.icon className={`w-4 h-4 text-${stat.color}-600`} />
              </div>
              <span className="mr-3 text-sm text-gray-600">{stat.label}</span>
            </div>
            <span className="font-semibold text-gray-900">{stat.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

// Risk Metrics Card Component
const RiskMetricsCard = ({ metrics }) => {
  const riskLevel = metrics.var_95 > 5 ? 'عالي' : metrics.var_95 > 3 ? 'متوسط' : 'منخفض';
  const riskColor = metrics.var_95 > 5 ? 'red' : metrics.var_95 > 3 ? 'yellow' : 'green';

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">مقاييس المخاطر</h3>
        <div className={`px-3 py-1 rounded-full text-xs font-medium bg-${riskColor}-100 text-${riskColor}-800`}>
          {riskLevel}
        </div>
      </div>
      
      <div className="space-y-4">
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600">VaR (95%)</span>
            <span className="font-semibold">{(metrics.var_95 || 0).toFixed(2)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`bg-${riskColor}-500 h-2 rounded-full`}
              style={{ width: `${Math.min((metrics.var_95 || 0) * 10, 100)}%` }}
            ></div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <p className="text-xs text-gray-600">Expected Shortfall</p>
            <p className="font-semibold">{(metrics.expected_shortfall || 0).toFixed(2)}%</p>
          </div>
          <div>
            <p className="text-xs text-gray-600">Beta</p>
            <p className="font-semibold">{(1.0).toFixed(2)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Active Positions Summary Component
const ActivePositionsSummary = ({ positions }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">المراكز النشطة</h3>
        <span className="text-sm text-gray-500">{positions.length} مراكز</span>
      </div>
      
      <div className="space-y-4">
        {positions.map((position, index) => (
          <PositionSummaryRow key={index} position={position} />
        ))}
      </div>
      
      {positions.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Activity className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>لا توجد مراكز نشطة حالياً</p>
        </div>
      )}
    </div>
  );
};

// Position Summary Row Component
const PositionSummaryRow = ({ position }) => {
  const pnl = position.unrealized_pnl || 0;
  const pnlPercentage = position.entry_price ? ((position.current_price - position.entry_price) / position.entry_price) * 100 : 0;
  const isProfit = pnl >= 0;

  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center">
        <div className={`w-3 h-3 rounded-full mr-3 ${isProfit ? 'bg-green-500' : 'bg-red-500'}`}></div>
        <div>
          <p className="font-medium text-gray-900">{position.stock_name_ar || position.stock_code}</p>
          <p className="text-sm text-gray-500">{position.remaining_shares?.toLocaleString()} سهم</p>
        </div>
      </div>
      
      <div className="text-left">
        <p className={`font-semibold ${isProfit ? 'text-green-600' : 'text-red-600'}`}>
          {isProfit ? '+' : ''}{pnl.toFixed(0)} جنيه
        </p>
        <p className={`text-sm ${isProfit ? 'text-green-500' : 'text-red-500'}`}>
          ({isProfit ? '+' : ''}{pnlPercentage.toFixed(1)}%)
        </p>
      </div>
    </div>
  );
};

// Recent Signals Card Component
const RecentSignalsCard = ({ signals }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">الإشارات الأخيرة</h3>
        <Zap className="w-5 h-5 text-yellow-500" />
      </div>
      
      <div className="space-y-3">
        {signals.map((signal, index) => (
          <SignalRow key={index} signal={signal} />
        ))}
      </div>
      
      {signals.length === 0 && (
        <div className="text-center py-6 text-gray-500">
          <Zap className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">لا توجد إشارات جديدة</p>
        </div>
      )}
    </div>
  );
};

// Signal Row Component
const SignalRow = ({ signal }) => {
  const resultColors = {
    'EXECUTED': 'green',
    'REJECTED': 'red',
    'ERROR': 'gray'
  };
  
  const resultColor = resultColors[signal.processing_result] || 'gray';

  return (
    <div className="flex items-center justify-between p-2 border-r-2 border-gray-200">
      <div className="flex items-center">
        <div className={`w-2 h-2 rounded-full bg-${resultColor}-500 mr-3`}></div>
        <div>
          <p className="text-sm font-medium">{signal.stock_code}</p>
          <p className="text-xs text-gray-500">{signal.signal_type}</p>
        </div>
      </div>
      
      <div className="text-left">
        <p className={`text-xs font-medium text-${resultColor}-600`}>
          {signal.processing_result}
        </p>
        <p className="text-xs text-gray-500">
          {new Date(signal.processed_at).toLocaleTimeString('ar-EG')}
        </p>
      </div>
    </div>
  );
};

export default SmartPortfolioDashboard;
