
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Target, TrendingUp, TrendingDown, Calendar, DollarSign } from 'lucide-react';

interface Option {
  id: string;
  symbol: string;
  type: 'call' | 'put';
  strike: number;
  expiry: string;
  premium: number;
  currentPrice: number;
  profitLoss: number;
  status: 'open' | 'closed';
}

const OptionsSimulator = () => {
  const [options, setOptions] = useState<Option[]>([
    {
      id: '1',
      symbol: 'COMI',
      type: 'call',
      strike: 50,
      expiry: '2024-07-15',
      premium: 2.5,
      currentPrice: 52.3,
      profitLoss: 450,
      status: 'open'
    }
  ]);

  const [newOption, setNewOption] = useState({
    symbol: '',
    type: 'call',
    strike: '',
    expiry: '',
    premium: '',
    quantity: '1'
  });

  const calculateProfitLoss = (option: Option) => {
    const intrinsicValue = option.type === 'call' 
      ? Math.max(0, option.currentPrice - option.strike)
      : Math.max(0, option.strike - option.currentPrice);
    return (intrinsicValue - option.premium) * 100; // Assuming 100 shares per contract
  };

  const addOption = () => {
    if (newOption.symbol && newOption.strike && newOption.premium) {
      const option: Option = {
        id: Date.now().toString(),
        symbol: newOption.symbol.toUpperCase(),
        type: newOption.type as 'call' | 'put',
        strike: parseFloat(newOption.strike),
        expiry: newOption.expiry,
        premium: parseFloat(newOption.premium),
        currentPrice: 52.3, // Mock current price
        profitLoss: 0,
        status: 'open'
      };
      option.profitLoss = calculateProfitLoss(option);
      setOptions([...options, option]);
      setNewOption({
        symbol: '',
        type: 'call',
        strike: '',
        expiry: '',
        premium: '',
        quantity: '1'
      });
    }
  };

  return (
    <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-violet-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-purple-800">
          <Target className="h-5 w-5" />
          محاكي تداول الخيارات
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add New Option */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 bg-white/60 rounded-lg">
          <Input
            placeholder="رمز السهم"
            value={newOption.symbol}
            onChange={(e) => setNewOption({...newOption, symbol: e.target.value})}
          />
          <Select value={newOption.type} onValueChange={(value) => setNewOption({...newOption, type: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="call">Call</SelectItem>
              <SelectItem value="put">Put</SelectItem>
            </SelectContent>
          </Select>
          <Input
            type="number"
            placeholder="سعر التنفيذ"
            value={newOption.strike}
            onChange={(e) => setNewOption({...newOption, strike: e.target.value})}
          />
          <Input
            type="date"
            placeholder="تاريخ الانتهاء"
            value={newOption.expiry}
            onChange={(e) => setNewOption({...newOption, expiry: e.target.value})}
          />
          <Input
            type="number"
            step="0.01"
            placeholder="القسط"
            value={newOption.premium}
            onChange={(e) => setNewOption({...newOption, premium: e.target.value})}
          />
          <Button onClick={addOption}>
            <TrendingUp className="h-4 w-4 mr-2" />
            إضافة
          </Button>
        </div>

        {/* Options Portfolio */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">محفظة الخيارات</h3>
          {options.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              لا توجد خيارات في المحفظة
            </div>
          ) : (
            <div className="space-y-3">
              {options.map(option => (
                <div key={option.id} className="p-4 bg-white/80 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
                    <div>
                      <div className="font-bold">{option.symbol}</div>
                      <Badge className={option.type === 'call' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {option.type.toUpperCase()}
                      </Badge>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500">سعر التنفيذ</div>
                      <div className="font-medium">{option.strike} ج.م</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500">السعر الحالي</div>
                      <div className="font-medium">{option.currentPrice} ج.م</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500">القسط المدفوع</div>
                      <div className="font-medium">{option.premium} ج.م</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500">تاريخ الانتهاء</div>
                      <div className="font-medium flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {option.expiry}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500">الربح/الخسارة</div>
                      <div className={`font-bold flex items-center gap-1 ${option.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        <DollarSign className="h-3 w-3" />
                        {option.profitLoss >= 0 ? '+' : ''}{option.profitLoss.toFixed(0)} ج.م
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Options Strategy Guide */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium mb-3">استراتيجيات الخيارات الشائعة:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="font-medium text-green-700">استراتيجيات صاعدة:</div>
              <ul className="text-sm space-y-1">
                <li>• شراء Call</li>
                <li>• بيع Put</li>
                <li>• Bull Call Spread</li>
              </ul>
            </div>
            <div className="space-y-2">
              <div className="font-medium text-red-700">استراتيجيات هابطة:</div>
              <ul className="text-sm space-y-1">
                <li>• شراء Put</li>
                <li>• بيع Call</li>
                <li>• Bear Put Spread</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OptionsSimulator;
