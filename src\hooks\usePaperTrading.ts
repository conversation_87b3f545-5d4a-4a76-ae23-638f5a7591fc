import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuthUser } from "./useAuthUser";

export type PaperTradingAccount = {
  id: string;
  account_name: string;
  current_balance: number;
  initial_balance?: number;
  is_active?: boolean;
  user_id: string;
  total_profit_loss?: number;
  total_trades?: number;
};

export type PaperTrade = {
  id: string;
  account_id: string;
  symbol: string;
  quantity: number;
  entry_price: number;
  exit_price?: number | null;
  trade_type: string;
  entry_time?: string;
  exit_time?: string;
  notes?: string | null;
};

export function usePaperTrading() {
  const { user } = useAuthUser();
  const queryClient = useQueryClient();
  const userId = user?.id ?? "";

  // Accounts query
  const accountsQuery = useQuery({
    queryKey: ["paper_accounts", userId],
    queryFn: async () => {
      if (!userId) return [];
      const { data, error } = await supabase
        .from("paper_trading_accounts")
        .select("*")
        .eq("user_id", userId);
      if (error) throw new Error(error.message);
      return data as PaperTradingAccount[];
    },
    enabled: !!userId,
  });

  // Trades for the selected account
  function useTrades(accountId: string | null) {
    return useQuery({
      queryKey: ["paper_trades", accountId],
      queryFn: async () => {
        if (!accountId) return [];
        const { data, error } = await supabase
          .from("paper_trades")
          .select("*")
          .eq("account_id", accountId)
          .order("entry_time", { ascending: false });
        if (error) throw new Error(error.message);
        return data as PaperTrade[];
      },
      enabled: !!accountId,
    });
  }

  // Mutation: create account
  const addAccount = useMutation({
    mutationFn: async (payload: { account_name: string; initial_balance?: number }) => {
      if (!userId) throw new Error("User not logged in");
      const { data, error } = await supabase
        .from("paper_trading_accounts")
        .insert([
          {
            account_name: payload.account_name,
            initial_balance: payload.initial_balance ?? 100000,
            current_balance: payload.initial_balance ?? 100000,
            user_id: userId,
          },
        ])
        .select("*")
        .single();
      if (error) throw new Error(error.message);
      return data as PaperTradingAccount;
    },
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["paper_accounts", userId] }),
  });

  // Mutation: delete account
  const deleteAccount = useMutation({
    mutationFn: async (accountId: string) => {
      await supabase.from("paper_trading_accounts").delete().eq("id", accountId);
    },
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["paper_accounts", userId] }),
  });

  // Mutation: add trade
  const addTrade = useMutation({
    mutationFn: async (payload: {
      account_id: string;
      symbol: string;
      quantity: number;
      entry_price: number;
      trade_type: string;
    }) => {
      const { data, error } = await supabase
        .from("paper_trades")
        .insert([payload])
        .select("*")
        .single();
      if (error) throw new Error(error.message);
      return data as PaperTrade;
    },
    onSuccess: (_, v) =>
      queryClient.invalidateQueries({ queryKey: ["paper_trades", v.account_id] }),
  });

  // Mutation: delete trade
  const deleteTrade = useMutation({
    mutationFn: async ({ tradeId, accountId }: { tradeId: string; accountId: string }) => {
      await supabase.from("paper_trades").delete().eq("id", tradeId);
    },
    onSuccess: (_, { accountId }) =>
      queryClient.invalidateQueries({ queryKey: ["paper_trades", accountId] }),
  });

  return {
    accountsQuery,
    addAccount,
    deleteAccount,
    addTrade,
    deleteTrade,
    useTrades,
  };
}
