-- إنشاء Materialized Views للإحصائيات المحسوبة
-- Creating Materialized Views for Pre-computed Statistics
-- هذا سيقلل من الحسابات المحلية في JavaScript

-- 1. ملخص السوق العام
-- Market Summary View
CREATE MATERIALIZED VIEW IF NOT EXISTS market_summary_view AS
SELECT 
    -- إحصائيات أساسية
    COUNT(*) as total_stocks,
    COUNT(CASE WHEN volume > 0 THEN 1 END) as active_stocks,
    COUNT(CASE WHEN volume > 100000 THEN 1 END) as high_volume_stocks,
    
    -- توزيع التغييرات
    COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
    COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
    COUNT(CASE WHEN change_percent = 0 THEN 1 END) as unchanged,
    
    -- متوسطات
    ROUND(AVG(change_percent), 2) as avg_change,
    ROUND(AVG(volume), 0) as avg_volume,
    
    -- إجماليات
    SUM(volume) as total_volume,
    SUM(turnover) as total_turnover,
    COUNT(CASE WHEN dividend_yield > 0 THEN 1 END) as dividend_stocks,
    COUNT(CASE WHEN speculation_opportunity = true THEN 1 END) as speculation_opportunities,
    
    -- آخر تحديث
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL;

-- 2. التحليل الفني للسوق
-- Technical Analysis View
CREATE MATERIALIZED VIEW IF NOT EXISTS technical_analysis_view AS
SELECT 
    -- اتجاهات المتوسطات المتحركة
    COUNT(CASE WHEN ma5 > ma20 THEN 1 END) as bullish_stocks,
    COUNT(CASE WHEN ma5 < ma20 THEN 1 END) as bearish_stocks,
    COUNT(CASE WHEN ma5 > ma20 AND ma20 > ma50 THEN 1 END) as strong_uptrend,
    COUNT(CASE WHEN ma5 < ma20 AND ma20 < ma50 THEN 1 END) as strong_downtrend,
    
    -- كسر المستويات
    COUNT(CASE WHEN current_price > ma20 AND previous_close <= ma20 THEN 1 END) as breakouts_above_ma20,
    COUNT(CASE WHEN ma5 > ma20 AND (ma5 - ma20) / ma20 > 0.02 THEN 1 END) as bullish_crossover,
    COUNT(CASE WHEN ma5 < ma20 AND (ma20 - ma5) / ma20 > 0.02 THEN 1 END) as bearish_crossover,
    
    -- الأهداف ووقف الخسارة
    COUNT(CASE WHEN target_1 IS NOT NULL AND current_price >= target_1 * 0.95 THEN 1 END) as near_targets,
    COUNT(CASE WHEN stop_loss IS NOT NULL AND current_price <= stop_loss * 1.05 THEN 1 END) as near_stop_loss,
    
    -- السيولة والزخم
    COUNT(CASE WHEN liquidity_ratio > 1 THEN 1 END) as liquidity_inflow,
    COUNT(CASE WHEN liquidity_ratio < 1 THEN 1 END) as liquidity_outflow,
    COUNT(CASE WHEN change_percent > 5 AND volume > 1000000 THEN 1 END) as high_momentum,
    
    -- آخر تحديث
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL;

-- 3. المقاييس المتقدمة
-- Advanced Metrics View
CREATE MATERIALIZED VIEW IF NOT EXISTS advanced_metrics_view AS
SELECT 
    -- متوسطات مالية
    ROUND(AVG(CASE WHEN pe_ratio > 0 AND pe_ratio < 100 THEN pe_ratio END), 2) as average_pe,
    ROUND(AVG(CASE WHEN dividend_yield > 0 THEN dividend_yield END), 2) as average_dividend_yield,
    
    -- مؤشرات السوق
    ROUND(
        COUNT(CASE WHEN ma20 IS NOT NULL AND current_price > ma20 THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN ma20 IS NOT NULL THEN 1 END), 0), 2
    ) as market_breadth,
    
    -- مؤشر الجودة (بيانات كاملة)
    ROUND(
        COUNT(CASE 
            WHEN pe_ratio IS NOT NULL 
            AND dividend_yield IS NOT NULL 
            AND ma20 IS NOT NULL 
            THEN 1 END) * 100.0 / COUNT(*), 2
    ) as quality_score,
    
    -- مؤشر المضاربة
    ROUND(
        COUNT(CASE WHEN speculation_opportunity = true OR change_percent > 10 THEN 1 END) * 100.0 / 
        COUNT(*), 2
    ) as speculation_index,
    
    -- مؤشر التقلبات (متوسط التغيير المطلق)
    ROUND(AVG(ABS(change_percent)), 2) as volatility_index,
    
    -- نقاط الزخم
    ROUND(
        (COUNT(CASE WHEN ma5 > ma20 THEN 1 END) - 
         COUNT(CASE WHEN ma5 < ma20 THEN 1 END)) * 100.0 / COUNT(*), 1
    ) as momentum_score,
    
    -- إجمالي تدفق السيولة
    SUM(CASE 
        WHEN liquidity_flow IS NOT NULL THEN liquidity_flow 
        ELSE volume * (CASE WHEN change_percent > 0 THEN 1 ELSE -1 END)
    END) as total_liquidity_flow,
    
    -- آخر تحديث
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL;

-- 4. أفضل الأداء (Top Performers)
-- Top Performers View
CREATE MATERIALIZED VIEW IF NOT EXISTS top_performers_view AS
WITH ranked_stocks AS (
    SELECT 
        symbol, current_price, change_percent, volume, turnover, 
        dividend_yield, liquidity_ratio, pe_ratio, sector,
        ROW_NUMBER() OVER (ORDER BY change_percent DESC) as gain_rank,
        ROW_NUMBER() OVER (ORDER BY change_percent ASC) as loss_rank,
        ROW_NUMBER() OVER (ORDER BY volume DESC) as volume_rank,
        ROW_NUMBER() OVER (ORDER BY turnover DESC) as turnover_rank,
        ROW_NUMBER() OVER (ORDER BY dividend_yield DESC) as dividend_rank,
        ROW_NUMBER() OVER (ORDER BY liquidity_ratio DESC) as liquidity_rank,
        ROW_NUMBER() OVER (ORDER BY pe_ratio ASC) as pe_rank,
        ROW_NUMBER() OVER (
            ORDER BY CASE WHEN ma5 > ma20 AND ma20 > ma50 AND change_percent > 0 THEN change_percent ELSE 0 END DESC
        ) as uptrend_rank
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND symbol NOT LIKE '%ETF%'
      AND name IS NOT NULL
      AND change_percent IS NOT NULL
)
SELECT 
    -- أفضل رابح
    MAX(CASE WHEN gain_rank = 1 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'change_percent', change_percent,
            'volume', volume,
            'sector', sector
        ) END) as top_gainer,
    
    -- أكبر خاسر
    MAX(CASE WHEN loss_rank = 1 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'change_percent', change_percent,
            'volume', volume,
            'sector', sector
        ) END) as top_loser,
    
    -- الأكثر نشاطاً
    MAX(CASE WHEN volume_rank = 1 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'change_percent', change_percent,
            'volume', volume,
            'sector', sector
        ) END) as most_active,
    
    -- أعلى قيمة تداول
    MAX(CASE WHEN turnover_rank = 1 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'change_percent', change_percent,
            'turnover', turnover,
            'sector', sector
        ) END) as highest_turnover,
    
    -- أفضل عائد أرباح
    MAX(CASE WHEN dividend_rank = 1 AND dividend_yield > 0 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'dividend_yield', dividend_yield,
            'pe_ratio', pe_ratio,
            'sector', sector
        ) END) as best_dividend,
    
    -- أفضل سيولة
    MAX(CASE WHEN liquidity_rank = 1 AND liquidity_ratio > 0 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'liquidity_ratio', liquidity_ratio,
            'volume', volume,
            'sector', sector
        ) END) as best_liquidity,
    
    -- أفضل P/E
    MAX(CASE WHEN pe_rank = 1 AND pe_ratio > 0 AND pe_ratio < 50 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'pe_ratio', pe_ratio,
            'dividend_yield', dividend_yield,
            'sector', sector
        ) END) as best_pe,
    
    -- أقوى اتجاه صاعد
    MAX(CASE WHEN uptrend_rank = 1 THEN 
        json_build_object(
            'symbol', symbol,
            'current_price', current_price,
            'change_percent', change_percent,
            'volume', volume,
            'sector', sector
        ) END) as strongest_uptrend,
    
    -- آخر تحديث
    MAX(updated_at) as last_updated
FROM ranked_stocks;

-- 5. توزيع القطاعات
-- Sector Analysis View
CREATE MATERIALIZED VIEW IF NOT EXISTS sector_analysis_view AS
SELECT 
    COALESCE(sector, 'غير محدد') as sector_name,
    COUNT(*) as total_stocks,
    COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
    COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
    ROUND(AVG(change_percent), 2) as avg_change,
    SUM(volume) as total_volume,
    SUM(turnover) as total_turnover,
    ROUND(AVG(CASE WHEN pe_ratio > 0 AND pe_ratio < 100 THEN pe_ratio END), 2) as avg_pe,
    ROUND(AVG(CASE WHEN dividend_yield > 0 THEN dividend_yield END), 2) as avg_dividend,
    COUNT(CASE WHEN ma5 > ma20 THEN 1 END) as bullish_stocks,
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL
GROUP BY COALESCE(sector, 'غير محدد')
HAVING COUNT(*) > 0
ORDER BY total_volume DESC;

-- 6. إنشاء function لتحديث جميع المناظر
-- Function to refresh all materialized views
CREATE OR REPLACE FUNCTION refresh_all_market_views()
RETURNS text AS $$
BEGIN
    REFRESH MATERIALIZED VIEW market_summary_view;
    REFRESH MATERIALIZED VIEW technical_analysis_view;
    REFRESH MATERIALIZED VIEW advanced_metrics_view;
    REFRESH MATERIALIZED VIEW top_performers_view;
    REFRESH MATERIALIZED VIEW sector_analysis_view;
    
    RETURN 'جميع المناظر المحسوبة تم تحديثها في: ' || NOW()::text;
END;
$$ LANGUAGE plpgsql;

-- 7. إنشاء indexes للأداء
-- Create indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stocks_realtime_change_percent ON stocks_realtime(change_percent DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stocks_realtime_volume ON stocks_realtime(volume DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stocks_realtime_sector ON stocks_realtime(sector);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stocks_realtime_ma_comparison ON stocks_realtime((ma5 > ma20));
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stocks_realtime_updated_at ON stocks_realtime(updated_at DESC);

-- 8. تشغيل التحديث الأولي
-- Initial refresh
SELECT refresh_all_market_views();
