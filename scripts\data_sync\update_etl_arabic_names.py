#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 محدث ETL مُحسن للأسماء العربية
Enhanced ETL Arabic Names Updater

📅 Created: 2025-06-16
🎯 Purpose: تحديث جميع ETL scripts لاستخدام الأسماء العربية الحقيقية
"""

import os
import logging
import sys
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('etl_arabic_names_update.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ETLArabicNamesUpdater:
    """محدث أسماء ETL scripts للأسماء العربية"""
    
    def __init__(self):
        self.scripts_dir = Path(__file__).parent
        self.etl_scripts = [
            'realtime_data_importer.py',
            'enhanced_realtime_financial_importer.py',
            'financial_data_importer.py',
            'historical_data_importer.py',
            'master_data_importer.py'
        ]
    
    def create_arabic_name_helper_function(self) -> str:
        """إنشاء دالة مساعدة لاستخراج الأسماء العربية"""
        return '''
def get_arabic_name_from_excel(symbol: str, df: pd.DataFrame = None) -> str:
    """الحصول على الاسم العربي الحقيقي من Excel أو إرجاع اسم مؤقت"""
    if df is not None and 'الاسم' in df.columns and 'symbol' in df.columns:
        # البحث عن الرمز في DataFrame
        for _, row in df.iterrows():
            row_symbol = str(row.get('symbol', '')).strip().upper()
            arabic_name = str(row.get('الاسم', '')).strip()
            
            if row_symbol == symbol and arabic_name and not arabic_name.startswith('مؤشر'):
                return arabic_name
    
    # إذا لم يتم العثور على اسم، استخدم اسم مؤقت
    return f"سهم {symbol}"
'''
    
    def create_enhanced_ensure_stocks_function(self) -> str:
        """إنشاء دالة محسنة للتأكد من وجود الأسهم"""
        return '''
def ensure_stocks_exist_with_arabic_names(symbols: list, df: pd.DataFrame = None, cursor=None) -> bool:
    """التأكد من وجود الأسهم مع الأسماء العربية الصحيحة"""
    try:
        if not cursor:
            return False
            
        # الحصول على الرموز الموجودة
        cursor.execute("SELECT symbol FROM stocks_master WHERE symbol = ANY(%s)", (symbols,))
        existing_symbols = {row[0] for row in cursor.fetchall()}
        
        # إضافة الرموز المفقودة
        new_symbols = set(symbols) - existing_symbols
        
        if new_symbols:
            logger.info(f"➕ إضافة {len(new_symbols)} رمز جديد إلى stocks_master")
            
            # إنشاء خريطة الرموز إلى الأسماء العربية
            symbol_to_arabic_name = {}
            if df is not None and 'الاسم' in df.columns and 'symbol' in df.columns:
                for _, row in df.iterrows():
                    symbol = str(row.get('symbol', '')).strip().upper()
                    arabic_name = str(row.get('الاسم', '')).strip()
                    if symbol and arabic_name and not arabic_name.startswith('مؤشر'):
                        symbol_to_arabic_name[symbol] = arabic_name
            
            for symbol in new_symbols:
                # استخدام الاسم العربي الحقيقي إذا كان متاحاً
                arabic_name = symbol_to_arabic_name.get(symbol, f"سهم {symbol}")
                
                cursor.execute("""
                    INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (symbol) DO NOTHING
                """, (symbol, arabic_name, symbol, True))
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في التأكد من وجود الأسهم: {e}")
        return False
'''
    
    def update_import_section(self, content: str) -> str:
        """تحديث قسم الاستيراد"""
        # إضافة import pandas إذا لم يكن موجوداً
        if 'import pandas as pd' not in content:
            lines = content.split('\n')
            import_section_end = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    import_section_end = i
            
            lines.insert(import_section_end + 1, 'import pandas as pd')
            content = '\n'.join(lines)
        
        return content
    
    def add_helper_functions(self, content: str) -> str:
        """إضافة الدوال المساعدة"""
        # البحث عن مكان مناسب لإدراج الدوال
        lines = content.split('\n')
        class_start_line = -1
        
        for i, line in enumerate(lines):
            if line.strip().startswith('class ') and 'class EnhancedEGXDataImporter' in line:
                class_start_line = i
                break
        
        if class_start_line == -1:
            # إذا لم توجد class، أضف الدوال قبل main
            for i, line in enumerate(lines):
                if line.strip() == 'def main():' or line.strip().startswith('if __name__'):
                    class_start_line = i
                    break
        
        if class_start_line != -1:
            # إدراج الدوال المساعدة
            helper_functions = [
                '',
                '# ===== دوال مساعدة للأسماء العربية =====',
                self.get_arabic_name_from_excel(),
                '',
                self.create_enhanced_ensure_stocks_function(),
                ''
            ]
            
            for j, func in enumerate(helper_functions):
                lines.insert(class_start_line + j, func)
        
        return '\n'.join(lines)
    
    def update_ensure_symbol_calls(self, content: str) -> str:
        """تحديث استدعاءات ensure_symbol_exists"""
        # استبدال الاستدعاءات القديمة
        replacements = [
            ('ensure_symbol_exists(symbol)', 'ensure_symbol_exists(symbol, get_arabic_name_from_excel(symbol, df))'),
            ('self.ensure_symbol_exists(symbol)', 'self.ensure_symbol_exists(symbol, get_arabic_name_from_excel(symbol, df))'),
            ('ensure_stocks_exist(symbols)', 'ensure_stocks_exist_with_arabic_names(symbols, df, cursor)'),
            ('self.ensure_stocks_exist(symbols)', 'self.ensure_stocks_exist_with_arabic_names(symbols, df, cursor)'),
            ("f'سهم {symbol}'", "get_arabic_name_from_excel(symbol, df)"),
            ("f\"سهم {symbol}\"", "get_arabic_name_from_excel(symbol, df)")
        ]
        
        updated_content = content
        for old, new in replacements:
            updated_content = updated_content.replace(old, new)
        
        return updated_content
    
    def backup_script(self, script_path: Path) -> Path:
        """إنشاء نسخة احتياطية من السكريبت"""
        backup_path = script_path.with_suffix('.py.backup')
        backup_path.write_text(script_path.read_text(encoding='utf-8'), encoding='utf-8')
        return backup_path
    
    def update_script(self, script_name: str) -> bool:
        """تحديث سكريبت واحد"""
        try:
            script_path = self.scripts_dir / script_name
            
            if not script_path.exists():
                logger.warning(f"⚠️ السكريبت غير موجود: {script_name}")
                return False
            
            logger.info(f"🔄 تحديث السكريبت: {script_name}")
            
            # إنشاء نسخة احتياطية
            backup_path = self.backup_script(script_path)
            logger.info(f"💾 تم إنشاء نسخة احتياطية: {backup_path.name}")
            
            # قراءة المحتوى
            content = script_path.read_text(encoding='utf-8')
            
            # تطبيق التحديثات
            updated_content = self.update_import_section(content)
            updated_content = self.add_helper_functions(updated_content)
            updated_content = self.update_ensure_symbol_calls(updated_content)
            
            # كتابة المحتوى المحدث
            script_path.write_text(updated_content, encoding='utf-8')
            
            logger.info(f"✅ تم تحديث السكريبت: {script_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث السكريبت {script_name}: {e}")
            return False
    
    def update_all_scripts(self):
        """تحديث جميع ETL scripts"""
        logger.info("🏁 بدء تحديث جميع ETL scripts...")
        
        updated_count = 0
        failed_count = 0
        
        for script_name in self.etl_scripts:
            if self.update_script(script_name):
                updated_count += 1
            else:
                failed_count += 1
        
        logger.info("=" * 60)
        logger.info("🎉 اكتمل تحديث ETL Scripts!")
        logger.info(f"✅ تم التحديث: {updated_count}")
        logger.info(f"❌ فشل: {failed_count}")
        logger.info("=" * 60)
        
        if failed_count == 0:
            logger.info("🚀 جميع السكريبتات جاهزة الآن لاستخدام الأسماء العربية الحقيقية!")
        else:
            logger.warning("⚠️ بعض السكريبتات تحتاج إلى مراجعة يدوية")

def main():
    """الدالة الرئيسية"""
    try:
        updater = ETLArabicNamesUpdater()
        updater.update_all_scripts()
        
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
