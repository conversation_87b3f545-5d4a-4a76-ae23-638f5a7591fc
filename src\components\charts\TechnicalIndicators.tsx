
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { TrendingUp, TrendingDown, Activity, BarChart3, AlertTriangle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Lazy load the entire chart component to reduce initial bundle
const ChartContainer = React.lazy(() => import('./ChartContainer'));

interface StockData {
  date: string;
  price: number;
  volume: number;
  rsi: number;
  macd: number;
  signal: number;
  bb_upper: number;
  bb_middle: number;
  bb_lower: number;
}

const TechnicalIndicators = ({ symbol = 'COMI' }: { symbol?: string }) => {
  const [activeIndicator, setActiveIndicator] = useState<string>('RSI');

  // Fetch technical indicators data from Supabase
  const { data: technicalData, isLoading, error } = useQuery({
    queryKey: ['technical_indicators', symbol],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('technical_indicators')
        .select('date, rsi_14, macd, macd_signal, bb_upper, bb_middle, bb_lower')
        .eq('symbol', symbol)
        .order('date', { ascending: true })
        .limit(30);

      if (error) throw error;
      return data || [];
    },
    refetchInterval: 300000, // Refetch every 5 minutes
  });

  // Fetch current price data
  const { data: priceData } = useQuery({
    queryKey: ['stock_prices', symbol],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stocks_historical')
        .select('date, close, volume')
        .eq('symbol', symbol)
        .order('date', { ascending: true })
        .limit(30);

      if (error) throw error;
      return data || [];
    },
    refetchInterval: 300000,
  });

  // Combine technical and price data
  const data: StockData[] = React.useMemo(() => {
    if (!technicalData || !priceData) return [];

    return technicalData.map(tech => {
      const priceRecord = priceData.find(p => p.date === tech.date);
      return {
        date: new Date(tech.date).toLocaleDateString('ar-EG'),
        price: priceRecord?.close || 0,
        volume: priceRecord?.volume || 0,
        rsi: tech.rsi_14 || 50,
        macd: tech.macd || 0,
        signal: tech.macd_signal || 0,
        bb_upper: tech.bb_upper || 0,
        bb_middle: tech.bb_middle || 0,
        bb_lower: tech.bb_lower || 0
      };
    });
  }, [technicalData, priceData]);

  // Calculate signals from latest data
  const signals = React.useMemo(() => {
    if (data.length === 0) {
      return { rsi: 'neutral' as const, macd: 'neutral' as const, bb: 'neutral' as const };
    }

    const latestData = data[data.length - 1];
    return {
      rsi: latestData.rsi > 70 ? 'sell' as const : latestData.rsi < 30 ? 'buy' as const : 'neutral' as const,
      macd: latestData.macd > latestData.signal ? 'buy' as const : 'sell' as const,
      bb: latestData.price > latestData.bb_upper ? 'sell' as const : 
           latestData.price < latestData.bb_lower ? 'buy' as const : 'neutral' as const
    };
  }, [data]);

  const indicators = [
    {
      id: 'RSI',
      name: 'مؤشر القوة النسبية',
      icon: TrendingUp,
      description: 'يقيس قوة الاتجاه',
      signal: signals.rsi
    },
    {
      id: 'MACD',
      name: 'مؤشر الماكد',
      icon: Activity,
      description: 'يحدد تغيير الاتجاه',
      signal: signals.macd
    },    {
      id: 'Bollinger Bands',
      name: 'نطاقات بولينجر',
      icon: BarChart3,
      description: 'يقيس التذبذب',
      signal: signals.bb
    },
    {
      id: 'Volume',
      name: 'حجم التداول',
      icon: BarChart3,
      description: 'يظهر النشاط',
      signal: 'neutral' as const
    }
  ];

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'buy': return 'bg-green-500';
      case 'sell': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getSignalText = (signal: string) => {
    switch (signal) {
      case 'buy': return 'شراء';
      case 'sell': return 'بيع';
      default: return 'محايد';
    }
  };  const renderChart = () => {
    if (isLoading) {
      return (
        <div className="h-[300px] flex items-center justify-center">
          <div className="text-center text-purple-600">جاري تحميل المؤشرات الفنية...</div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="h-[300px] flex items-center justify-center">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <div className="text-red-600">خطأ في تحميل البيانات</div>
            <div className="text-sm text-gray-500 mt-1">
              {error instanceof Error ? error.message : 'خطأ غير محدد'}
            </div>
          </div>
        </div>
      );
    }

    if (data.length === 0) {
      return (
        <div className="h-[300px] flex items-center justify-center">
          <div className="text-center text-gray-600">لا توجد بيانات متاحة للرمز {symbol}</div>
        </div>
      );
    }

    return (
      <ErrorBoundary
        fallback={
          <div className="h-[300px] flex items-center justify-center">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <div className="text-red-600">خطأ في عرض الرسم البياني</div>
              <div className="text-sm text-gray-500 mt-1">يرجى المحاولة مرة أخرى</div>
            </div>
          </div>
        }
        onError={(error, errorInfo) => {
          console.error('Chart rendering error:', error, errorInfo);
        }}
      >
        <React.Suspense fallback={
          <div className="h-[300px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        }>
          <ChartContainer activeIndicator={activeIndicator} data={data} />
        </React.Suspense>
      </ErrorBoundary>
    );
  };

  return (
    <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-violet-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-purple-800">
          <BarChart3 className="h-5 w-5" />
          المؤشرات الفنية - {symbol}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Indicator Selection */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {indicators.map(indicator => (
            <Button
              key={indicator.id}
              variant={activeIndicator === indicator.id ? "default" : "outline"}
              onClick={() => setActiveIndicator(indicator.id)}
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <div className="flex items-center gap-2">
                <indicator.icon className="h-5 w-5" />
                <span className="font-bold">{indicator.name}</span>
              </div>
              <div className="text-xs text-muted-foreground text-center">
                {indicator.description}
              </div>
              <Badge className={getSignalColor(indicator.signal)}>
                {getSignalText(indicator.signal)}
              </Badge>
            </Button>
          ))}
        </div>

        {/* Chart */}
        <div className="bg-white/80 rounded-lg p-4">
          {renderChart()}
        </div>

        {/* Signals Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {indicators.map(indicator => (
            <div key={indicator.id} className="p-4 bg-white/60 rounded-lg text-center">
              <div className="font-medium mb-2">{indicator.name}</div>
              <Badge className={`${getSignalColor(indicator.signal)} text-white px-4 py-2`}>
                إشارة: {getSignalText(indicator.signal)}
              </Badge>
            </div>
          ))}
        </div>

        {/* Overall Signal */}
        <div className="p-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg text-center">
          <div className="text-lg font-bold mb-2">التوصية العامة</div>
          <div className="text-sm text-muted-foreground mb-3">
            بناءً على تحليل المؤشرات الفنية
          </div>
          {(() => {
            const buySignals = Object.values(signals).filter(s => s === 'buy').length;
            const sellSignals = Object.values(signals).filter(s => s === 'sell').length;
            
            if (buySignals > sellSignals) {
              return <Badge className="bg-green-500 text-white px-6 py-3 text-lg">توصية شراء قوية</Badge>;
            } else if (sellSignals > buySignals) {
              return <Badge className="bg-red-500 text-white px-6 py-3 text-lg">توصية بيع قوية</Badge>;
            } else {
              return <Badge className="bg-gray-500 text-white px-6 py-3 text-lg">موقف محايد</Badge>;
            }
          })()}
        </div>
      </CardContent>
    </Card>
  );
};

export default TechnicalIndicators;
