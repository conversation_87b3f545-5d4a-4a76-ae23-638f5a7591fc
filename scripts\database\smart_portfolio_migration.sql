-- Smart Portfolio Database Migration
-- إنشاء جداول المحفظة الذكية ونسخ التداول
-- تاريخ الإنشاء: 17 يونيو 2025

-- ===========================
-- جدول مراكز المحفظة الذكية
-- ===========================
CREATE TABLE IF NOT EXISTS smart_portfolio_positions (
    id SERIAL PRIMARY KEY,
    position_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- معلومات السهم
    stock_code VARCHAR(10) NOT NULL,
    stock_name_ar VARCHAR(100),
    
    -- معلومات الدخول
    entry_price DECIMAL(10,2) NOT NULL,
    entry_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    initial_shares INTEGER NOT NULL,
    remaining_shares INTEGER NOT NULL,
    
    -- إدارة المخاطر والأهداف
    stop_loss_price DECIMAL(10,2) NOT NULL,
    trailing_stop_distance DECIMAL(10,2),
    highest_price_reached DECIMAL(10,2) DEFAULT 0.0,
    protected_profit DECIMAL(10,2) DEFAULT 0.0,
    
    -- أهداف الربح وحالة التنفيذ (JSON)
    profit_targets JSON,
    targets_achieved JSON DEFAULT '[]',
    
    -- مقاييس الأداء
    unrealized_pnl DECIMAL(15,2) DEFAULT 0.0,
    realized_pnl DECIMAL(15,2) DEFAULT 0.0,
    total_fees DECIMAL(10,2) DEFAULT 0.0,
    
    -- معلومات الإشارة والمعالجة
    signal_id VARCHAR(50),
    signal_confidence DECIMAL(5,3),
    position_size_reasoning TEXT,
    
    -- تفاصيل حساب حجم المركز
    kelly_fraction DECIMAL(5,3),
    risk_percentage DECIMAL(5,2),
    volatility_adjustment DECIMAL(5,3),
    liquidity_adjustment DECIMAL(5,3),
    
    -- حالة المركز
    status VARCHAR(20) DEFAULT 'ACTIVE',
    close_reason VARCHAR(50),
    
    -- التوقيتات
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP
);

-- فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_smart_positions_stock_status ON smart_portfolio_positions(stock_code, status);
CREATE INDEX IF NOT EXISTS idx_smart_positions_entry_date ON smart_portfolio_positions(entry_date);
CREATE INDEX IF NOT EXISTS idx_smart_positions_status ON smart_portfolio_positions(status);

-- ===========================
-- جدول معاملات المحفظة الذكية
-- ===========================
CREATE TABLE IF NOT EXISTS smart_portfolio_transactions (
    id SERIAL PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    position_id VARCHAR(50) NOT NULL,
    
    -- تفاصيل المعاملة
    transaction_type VARCHAR(20) NOT NULL,
    shares INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total_value DECIMAL(15,2) NOT NULL,
    fees DECIMAL(10,2) DEFAULT 0.0,
    
    -- سبب المعاملة
    trigger_reason VARCHAR(50),
    target_number INTEGER,
    
    -- الربح/الخسارة
    pnl DECIMAL(15,2) DEFAULT 0.0,
    pnl_percentage DECIMAL(8,3) DEFAULT 0.0,
    
    -- معلومات إضافية
    notes TEXT,
    execution_quality VARCHAR(20),
    
    -- التوقيت
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفتاح الخارجي
    FOREIGN KEY (position_id) REFERENCES smart_portfolio_positions(position_id) ON DELETE CASCADE
);

-- فهارس للمعاملات
CREATE INDEX IF NOT EXISTS idx_smart_transactions_position_type ON smart_portfolio_transactions(position_id, transaction_type);
CREATE INDEX IF NOT EXISTS idx_smart_transactions_timestamp ON smart_portfolio_transactions(timestamp);
CREATE INDEX IF NOT EXISTS idx_smart_transactions_type ON smart_portfolio_transactions(transaction_type);

-- ===========================
-- جدول مقاييس أداء المحفظة الذكية
-- ===========================
CREATE TABLE IF NOT EXISTS smart_portfolio_metrics (
    id SERIAL PRIMARY KEY,
    date DATE UNIQUE NOT NULL DEFAULT CURRENT_DATE,
    
    -- قيم المحفظة
    total_portfolio_value DECIMAL(15,2) NOT NULL,
    cash_balance DECIMAL(15,2) DEFAULT 0.0,
    invested_value DECIMAL(15,2) DEFAULT 0.0,
    unrealized_pnl DECIMAL(15,2) DEFAULT 0.0,
    realized_pnl DECIMAL(15,2) DEFAULT 0.0,
    
    -- مقاييس العائد
    daily_return DECIMAL(8,4) DEFAULT 0.0,
    total_return DECIMAL(15,2) DEFAULT 0.0,
    total_return_percentage DECIMAL(8,3) DEFAULT 0.0,
    annualized_return DECIMAL(8,3) DEFAULT 0.0,
    
    -- مقاييس المخاطر
    sharpe_ratio DECIMAL(8,3) DEFAULT 0.0,
    max_drawdown DECIMAL(8,3) DEFAULT 0.0,
    current_drawdown DECIMAL(8,3) DEFAULT 0.0,
    volatility DECIMAL(8,3) DEFAULT 0.0,
    
    -- مقاييس مخاطر متقدمة
    var_95 DECIMAL(8,3) DEFAULT 0.0,
    var_99 DECIMAL(8,3) DEFAULT 0.0,
    expected_shortfall DECIMAL(8,3) DEFAULT 0.0,
    beta DECIMAL(6,3) DEFAULT 1.0,
    
    -- إحصائيات التداول
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0.0,
    profit_factor DECIMAL(8,3) DEFAULT 0.0,
    
    -- متوسط الربح والخسارة
    average_win DECIMAL(10,2) DEFAULT 0.0,
    average_loss DECIMAL(10,2) DEFAULT 0.0,
    largest_win DECIMAL(10,2) DEFAULT 0.0,
    largest_loss DECIMAL(10,2) DEFAULT 0.0,
    
    -- إحصائيات الإشارات
    signals_processed_today INTEGER DEFAULT 0,
    signals_executed_today INTEGER DEFAULT 0,
    signals_rejected_today INTEGER DEFAULT 0,
    signal_acceptance_rate DECIMAL(5,2) DEFAULT 0.0,
    
    -- توزيع المحفظة
    active_positions_count INTEGER DEFAULT 0,
    sector_distribution JSON,
    position_sizes JSON,
    
    -- معلومات إضافية
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس لتاريخ المقاييس
CREATE INDEX IF NOT EXISTS idx_smart_metrics_date ON smart_portfolio_metrics(date);

-- ===========================
-- جدول اشتراكات نسخ التداول
-- ===========================
CREATE TABLE IF NOT EXISTS copy_trading_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subscription_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- حالة الاشتراك
    is_active BOOLEAN DEFAULT false,
    subscription_type VARCHAR(20) DEFAULT 'VIP',
    
    -- إعدادات النسخ
    copy_percentage DECIMAL(5,2) DEFAULT 1.0,
    max_position_size DECIMAL(15,2),
    max_positions_count INTEGER DEFAULT 10,
    
    -- مرشحات التحكم
    min_signal_confidence DECIMAL(5,3) DEFAULT 0.7,
    max_risk_per_trade DECIMAL(5,3) DEFAULT 0.02,
    excluded_stocks JSON DEFAULT '[]',
    included_sectors JSON DEFAULT '[]',
    
    -- حدود مالية
    available_balance DECIMAL(15,2) DEFAULT 0.0,
    reserved_balance DECIMAL(15,2) DEFAULT 0.0,
    
    -- إحصائيات الأداء
    total_copied_trades INTEGER DEFAULT 0,
    successful_copies INTEGER DEFAULT 0,
    total_pnl DECIMAL(15,2) DEFAULT 0.0,
    total_fees_paid DECIMAL(10,2) DEFAULT 0.0,
    
    -- إعدادات الإشعارات
    notifications_enabled BOOLEAN DEFAULT true,
    telegram_chat_id VARCHAR(50),
    email_notifications BOOLEAN DEFAULT true,
    
    -- التوقيتات
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP
);

-- فهارس لاشتراكات نسخ التداول
CREATE INDEX IF NOT EXISTS idx_copy_subscriptions_user_id ON copy_trading_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_copy_subscriptions_active ON copy_trading_subscriptions(is_active);

-- ===========================
-- جدول الصفقات المنسوخة
-- ===========================
CREATE TABLE IF NOT EXISTS copied_trades (
    id SERIAL PRIMARY KEY,
    copy_trade_id VARCHAR(50) UNIQUE NOT NULL,
    subscription_id VARCHAR(50) NOT NULL,
    original_position_id VARCHAR(50) NOT NULL,
    
    -- معلومات السهم
    stock_code VARCHAR(10) NOT NULL,
    stock_name_ar VARCHAR(100),
    
    -- تفاصيل النسخ
    original_shares INTEGER NOT NULL,
    copied_shares INTEGER NOT NULL,
    copy_ratio DECIMAL(5,2) NOT NULL,
    
    -- معلومات الدخول
    entry_price DECIMAL(10,2) NOT NULL,
    entry_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_investment DECIMAL(15,2) NOT NULL,
    fees_paid DECIMAL(10,2) DEFAULT 0.0,
    
    -- حالة الصفقة
    status VARCHAR(20) DEFAULT 'ACTIVE',
    remaining_shares INTEGER,
    
    -- الأداء
    unrealized_pnl DECIMAL(15,2) DEFAULT 0.0,
    realized_pnl DECIMAL(15,2) DEFAULT 0.0,
    total_pnl DECIMAL(15,2) DEFAULT 0.0,
    
    -- معلومات الإغلاق
    close_reason VARCHAR(50),
    close_date TIMESTAMP,
    
    -- التوقيتات
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفتاح الخارجي
    FOREIGN KEY (subscription_id) REFERENCES copy_trading_subscriptions(subscription_id) ON DELETE CASCADE
);

-- فهارس للصفقات المنسوخة
CREATE INDEX IF NOT EXISTS idx_copied_trades_subscription_stock ON copied_trades(subscription_id, stock_code);
CREATE INDEX IF NOT EXISTS idx_copied_trades_status_date ON copied_trades(status, entry_date);
CREATE INDEX IF NOT EXISTS idx_copied_trades_original_position ON copied_trades(original_position_id);

-- ===========================
-- جدول سجل معالجة الإشارات
-- ===========================
CREATE TABLE IF NOT EXISTS smart_portfolio_signal_processing (
    id SERIAL PRIMARY KEY,
    processing_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- معلومات الإشارة
    signal_id VARCHAR(50),
    stock_code VARCHAR(10) NOT NULL,
    signal_type VARCHAR(20),
    
    -- نتيجة المعالجة
    processing_result VARCHAR(20),
    decision_reason TEXT,
    
    -- تفاصيل التحليل
    signal_confidence DECIMAL(5,3),
    risk_reward_ratio DECIMAL(8,3),
    position_size_calculated DECIMAL(15,2),
    
    -- معايير القبول/الرفض (JSON)
    criteria_checks JSON,
    stock_analysis JSON,
    
    -- معلومات التنفيذ (إذا تم)
    position_id VARCHAR(50),
    execution_price DECIMAL(10,2),
    executed_shares INTEGER,
    
    -- التوقيت
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_duration DECIMAL(8,3)
);

-- فهارس لمعالجة الإشارات
CREATE INDEX IF NOT EXISTS idx_signal_processing_stock_result ON smart_portfolio_signal_processing(stock_code, processing_result);
CREATE INDEX IF NOT EXISTS idx_signal_processing_processed_at ON smart_portfolio_signal_processing(processed_at);

-- ===========================
-- جدول لقطات أداء المحفظة
-- ===========================
CREATE TABLE IF NOT EXISTS portfolio_performance_snapshots (
    id SERIAL PRIMARY KEY,
    snapshot_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- معلومات اللقطة
    snapshot_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    snapshot_type VARCHAR(20),
    
    -- قيم المحفظة
    portfolio_value DECIMAL(15,2) NOT NULL,
    cash_value DECIMAL(15,2) DEFAULT 0.0,
    positions_value DECIMAL(15,2) DEFAULT 0.0,
    
    -- العوائد
    period_return DECIMAL(8,3) DEFAULT 0.0,
    cumulative_return DECIMAL(8,3) DEFAULT 0.0,
    
    -- مقارنة بالمؤشر
    egx30_value DECIMAL(15,2),
    egx30_return DECIMAL(8,3),
    alpha DECIMAL(8,3),
    beta DECIMAL(6,3),
    
    -- إحصائيات المراكز
    positions_count INTEGER DEFAULT 0,
    winning_positions INTEGER DEFAULT 0,
    losing_positions INTEGER DEFAULT 0,
    
    -- مقاييس المخاطر
    portfolio_volatility DECIMAL(8,3) DEFAULT 0.0,
    sharpe_ratio DECIMAL(8,3) DEFAULT 0.0,
    max_drawdown DECIMAL(8,3) DEFAULT 0.0,
    
    -- بيانات تفصيلية (JSON)
    positions_snapshot JSON,
    sector_allocation JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للقطات الأداء
CREATE INDEX IF NOT EXISTS idx_performance_snapshots_date ON portfolio_performance_snapshots(snapshot_date);
CREATE INDEX IF NOT EXISTS idx_performance_snapshots_type ON portfolio_performance_snapshots(snapshot_type);

-- ===========================
-- إنشاء دوال مساعدة للتحديث التلقائي
-- ===========================

-- دالة تحديث timestamp عند التعديل
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق الدالة على الجداول المناسبة
DROP TRIGGER IF EXISTS update_smart_positions_updated_at ON smart_portfolio_positions;
CREATE TRIGGER update_smart_positions_updated_at
    BEFORE UPDATE ON smart_portfolio_positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_copy_subscriptions_updated_at ON copy_trading_subscriptions;
CREATE TRIGGER update_copy_subscriptions_updated_at
    BEFORE UPDATE ON copy_trading_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_copied_trades_updated_at ON copied_trades;
CREATE TRIGGER update_copied_trades_updated_at
    BEFORE UPDATE ON copied_trades
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===========================
-- إنشاء Views للاستعلامات المعقدة
-- ===========================

-- عرض ملخص المحفظة الحالي
CREATE OR REPLACE VIEW smart_portfolio_current_summary AS
SELECT 
    COUNT(*) as total_positions,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_positions,
    COUNT(CASE WHEN status = 'CLOSED' THEN 1 END) as closed_positions,
    SUM(CASE WHEN status = 'ACTIVE' THEN remaining_shares * entry_price ELSE 0 END) as current_investment,
    SUM(realized_pnl) as total_realized_pnl,
    SUM(CASE WHEN status = 'ACTIVE' THEN unrealized_pnl ELSE 0 END) as total_unrealized_pnl,
    AVG(signal_confidence) as avg_signal_confidence,
    COUNT(CASE WHEN realized_pnl > 0 THEN 1 END) as winning_trades,
    COUNT(CASE WHEN realized_pnl < 0 THEN 1 END) as losing_trades,
    CASE 
        WHEN COUNT(CASE WHEN status = 'CLOSED' THEN 1 END) > 0 
        THEN COUNT(CASE WHEN realized_pnl > 0 THEN 1 END) * 100.0 / COUNT(CASE WHEN status = 'CLOSED' THEN 1 END)
        ELSE 0 
    END as win_rate
FROM smart_portfolio_positions
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days';

-- عرض أداء نسخ التداول
CREATE OR REPLACE VIEW copy_trading_performance_summary AS
SELECT 
    s.subscription_id,
    s.user_id,
    s.is_active,
    s.copy_percentage,
    s.available_balance,
    s.reserved_balance,
    COUNT(t.copy_trade_id) as total_copied_trades,
    COUNT(CASE WHEN t.total_pnl > 0 THEN 1 END) as successful_trades,
    SUM(t.total_pnl) as total_pnl,
    SUM(t.fees_paid) as total_fees_paid,
    CASE 
        WHEN COUNT(t.copy_trade_id) > 0 
        THEN COUNT(CASE WHEN t.total_pnl > 0 THEN 1 END) * 100.0 / COUNT(t.copy_trade_id)
        ELSE 0 
    END as success_rate
FROM copy_trading_subscriptions s
LEFT JOIN copied_trades t ON s.subscription_id = t.subscription_id
GROUP BY s.subscription_id, s.user_id, s.is_active, s.copy_percentage, s.available_balance, s.reserved_balance;

-- عرض أداء الأسهم في المحفظة الذكية
CREATE OR REPLACE VIEW stock_performance_summary AS
SELECT 
    stock_code,
    stock_name_ar,
    COUNT(*) as total_positions,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_positions,
    COUNT(CASE WHEN status = 'CLOSED' THEN 1 END) as closed_positions,
    SUM(realized_pnl) as total_realized_pnl,
    SUM(CASE WHEN status = 'ACTIVE' THEN unrealized_pnl ELSE 0 END) as total_unrealized_pnl,
    AVG(signal_confidence) as avg_signal_confidence,
    COUNT(CASE WHEN realized_pnl > 0 THEN 1 END) as winning_positions,
    COUNT(CASE WHEN realized_pnl < 0 THEN 1 END) as losing_positions,
    CASE 
        WHEN COUNT(CASE WHEN status = 'CLOSED' THEN 1 END) > 0 
        THEN COUNT(CASE WHEN realized_pnl > 0 THEN 1 END) * 100.0 / COUNT(CASE WHEN status = 'CLOSED' THEN 1 END)
        ELSE 0 
    END as success_rate
FROM smart_portfolio_positions
GROUP BY stock_code, stock_name_ar
ORDER BY total_realized_pnl DESC;

-- ===========================
-- إدراج بيانات أولية للاختبار
-- ===========================

-- إدراج مقاييس أولية للمحفظة
INSERT INTO smart_portfolio_metrics (
    total_portfolio_value, 
    cash_balance, 
    invested_value,
    daily_return,
    total_return,
    total_return_percentage
) VALUES (
    1000000.00,  -- رأس المال الأولي
    1000000.00,  -- نقد متاح
    0.00,        -- استثمارات
    0.00,        -- عائد يومي
    0.00,        -- إجمالي العائد
    0.00         -- نسبة العائد
) ON CONFLICT (date) DO NOTHING;

-- ===========================
-- إنشاء فهارس إضافية للأداء
-- ===========================

-- فهارس مركبة لتحسين استعلامات التقارير
CREATE INDEX IF NOT EXISTS idx_smart_positions_status_created ON smart_portfolio_positions(status, created_at);
CREATE INDEX IF NOT EXISTS idx_smart_transactions_pnl ON smart_portfolio_transactions(pnl) WHERE pnl != 0;
CREATE INDEX IF NOT EXISTS idx_copied_trades_pnl ON copied_trades(total_pnl) WHERE total_pnl != 0;

-- فهارس للاستعلامات التحليلية
CREATE INDEX IF NOT EXISTS idx_smart_positions_confidence ON smart_portfolio_positions(signal_confidence) WHERE signal_confidence IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_signal_processing_confidence ON smart_portfolio_signal_processing(signal_confidence) WHERE signal_confidence IS NOT NULL;

-- ===========================
-- إعطاء صلاحيات للمستخدم
-- ===========================

-- منح صلاحيات للمستخدم (تأكد من استبدال 'your_user' باسم المستخدم الصحيح)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- إعادة تعيين التسلسل للجداول الموجودة
-- SELECT setval('smart_portfolio_positions_id_seq', 1, false);
-- SELECT setval('smart_portfolio_transactions_id_seq', 1, false);
-- SELECT setval('smart_portfolio_metrics_id_seq', 1, false);
-- SELECT setval('copy_trading_subscriptions_id_seq', 1, false);
-- SELECT setval('copied_trades_id_seq', 1, false);

COMMIT;

-- رسالة النجاح
SELECT 'Smart Portfolio database migration completed successfully!' as status;
