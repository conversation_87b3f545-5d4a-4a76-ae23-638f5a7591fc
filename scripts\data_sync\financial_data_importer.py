#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💰 مستورد البيانات المالية - نظام الأسهم المصرية
Financial Data Importer - Egyptian Stock Market System

📅 Created: 2025-06-16
🎯 Purpose: استيراد البيانات المالية من financial_data.csv (130+ عمود)
📁 Source: /mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv
"""

import pandas as pd
import psycopg2
from pathlib import Path
import logging
from datetime import datetime
import sys
from typing import Dict, List, Tuple
import numpy as np

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_data_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EGXFinancialDataImporter:
    """مستورد البيانات المالية للأسهم المصرية"""
    
    def __init__(self, db_config: Dict[str, str]):
        """تهيئة المستورد مع إعدادات قاعدة البيانات"""
        self.db_config = db_config
        self.connection = None
        self.csv_file_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv")
        
        # إحصائيات العملية
        self.stats = {
            'total_records': 0,
            'inserted_records': 0,
            'updated_records': 0,
            'failed_records': 0,
            'start_time': None,
            'end_time': None
        }
        
        # خريطة تطابق أعمدة CSV مع قاعدة البيانات
        self.column_mapping = {
            # معرفات أساسية
            'Symbol': 'symbol',
            'الرمز': 'symbol',
            'Fiscal Year': 'fiscal_year',
            'السنة المالية': 'fiscal_year',
            'Quarter': 'fiscal_quarter',
            'الربع': 'fiscal_quarter',
            
            # تقييم السوق
            'Market Cap': 'market_cap',
            'القيمة السوقية': 'market_cap',
            'Enterprise Value': 'enterprise_value',
            'Shares Outstanding': 'shares_outstanding',
            
            # نسب التقييم
            'P/E Ratio': 'pe_ratio',
            'PE': 'pe_ratio',
            'مضاعف السعر للربح': 'pe_ratio',
            'P/B Ratio': 'pb_ratio',
            'PB': 'pb_ratio',
            'P/S Ratio': 'ps_ratio',
            'PS': 'ps_ratio',
            'P/CF Ratio': 'pcf_ratio',
            'PEG Ratio': 'peg_ratio',
            
            # الربحية لكل سهم
            'EPS Basic': 'eps_basic',
            'EPS': 'eps_basic',
            'ربحية السهم': 'eps_basic',
            'EPS Diluted': 'eps_diluted',
            'EPS Growth YoY': 'eps_growth_yoy',
            'EPS Growth QoQ': 'eps_growth_qoq',
            
            # الأرباح والتوزيعات
            'Dividend Yield': 'dividend_yield',
            'عائد التوزيع': 'dividend_yield',
            'Dividend Payout Ratio': 'dividend_payout_ratio',
            'Dividends Per Share': 'dividends_per_share',
            
            # قائمة الدخل
            'Total Revenue': 'total_revenue',
            'الإيرادات': 'total_revenue',
            'Gross Profit': 'gross_profit',
            'إجمالي الربح': 'gross_profit',
            'Operating Income': 'operating_income',
            'الدخل التشغيلي': 'operating_income',
            'Net Income': 'net_income',
            'صافي الدخل': 'net_income',
            'EBITDA': 'ebitda',
            'EBIT': 'ebit',
            
            # هوامش الربح
            'Gross Margin': 'gross_margin',
            'هامش إجمالي الربح': 'gross_margin',
            'Operating Margin': 'operating_margin',
            'هامش التشغيل': 'operating_margin',
            'Net Margin': 'net_margin',
            'هامش صافي الربح': 'net_margin',
            'EBITDA Margin': 'ebitda_margin',
            
            # الميزانية العمومية
            'Total Assets': 'total_assets',
            'إجمالي الأصول': 'total_assets',
            'Current Assets': 'current_assets',
            'الأصول المتداولة': 'current_assets',
            'Non-Current Assets': 'non_current_assets',
            'Total Liabilities': 'total_liabilities',
            'إجمالي الخصوم': 'total_liabilities',
            'Current Liabilities': 'current_liabilities',
            'الخصوم المتداولة': 'current_liabilities',
            'Long-term Debt': 'long_term_debt',
            'الديون طويلة الأجل': 'long_term_debt',
            'Total Equity': 'total_equity',
            'إجمالي حقوق الملكية': 'total_equity',
            'Retained Earnings': 'retained_earnings',
            'الأرباح المحتجزة': 'retained_earnings',
            'Cash and Equivalents': 'cash_and_equivalents',
            'النقد وما يعادله': 'cash_and_equivalents',
            'Inventory': 'inventory',
            'المخزون': 'inventory',
            'Accounts Receivable': 'accounts_receivable',
            
            # التدفقات النقدية
            'Operating Cash Flow': 'operating_cash_flow',
            'التدفق النقدي التشغيلي': 'operating_cash_flow',
            'Investing Cash Flow': 'investing_cash_flow',
            'Financing Cash Flow': 'financing_cash_flow',
            'Free Cash Flow': 'free_cash_flow',
            'التدفق النقدي الحر': 'free_cash_flow',
            'Capital Expenditures': 'capital_expenditures',
            'النفقات الرأسمالية': 'capital_expenditures',
            
            # نسب السيولة
            'Current Ratio': 'current_ratio',
            'نسبة التداول': 'current_ratio',
            'Quick Ratio': 'quick_ratio',
            'نسبة السيولة السريعة': 'quick_ratio',
            'Cash Ratio': 'cash_ratio',
            'Operating Cash Flow Ratio': 'operating_cash_flow_ratio',
            
            # نسب الرافعة المالية
            'Debt to Equity': 'debt_to_equity',
            'نسبة الدين لحقوق الملكية': 'debt_to_equity',
            'Debt to Assets': 'debt_to_assets',
            'Debt to EBITDA': 'debt_to_ebitda',
            'Equity Ratio': 'equity_ratio',
            
            # نسب الربحية
            'ROE': 'roe',
            'العائد على حقوق الملكية': 'roe',
            'ROA': 'roa',
            'العائد على الأصول': 'roa',
            'ROIC': 'roic',
            'العائد على رأس المال المستثمر': 'roic',
            
            # نسب النشاط
            'Asset Turnover': 'asset_turnover',
            'معدل دوران الأصول': 'asset_turnover',
            'Inventory Turnover': 'inventory_turnover',
            'معدل دوران المخزون': 'inventory_turnover',
            'Receivables Turnover': 'receivables_turnover',
            'Payables Turnover': 'payables_turnover',
            
            # معلومات متقدمة
            'Beta': 'beta',
            'بيتا': 'beta',
            'Analyst Rating': 'analyst_rating',
            'Target Price': 'target_price',
            'السعر المستهدف': 'target_price',
            'Target Performance 1Y': 'target_performance_1y',
            
            # تواريخ مهمة
            'Recent Earnings Date': 'recent_earnings_date',
            'Upcoming Earnings Date': 'upcoming_earnings_date',
            'Dividend Ex Date': 'dividend_ex_date',
            'Dividend Pay Date': 'dividend_pay_date',
            
            # نقاط إضافية
            'Industry Comparison Score': 'industry_comparison_score',
            'Financial Strength Score': 'financial_strength_score',
            'Growth Score': 'growth_score'
        }
    
    def connect_db(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.connection.autocommit = False
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close_db(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            logger.info("🔒 تم إغلاق الاتصال بقاعدة البيانات")
    
    def validate_csv_file(self) -> Tuple[bool, str]:
        """التحقق من صحة ملف CSV"""
        try:
            if not self.csv_file_path.exists():
                return False, "ملف CSV غير موجود"
            
            if self.csv_file_path.stat().st_size == 0:
                return False, "ملف CSV فارغ"
            
            # محاولة قراءة أول سطر
            df_test = pd.read_csv(self.csv_file_path, nrows=1)
            if df_test.empty:
                return False, "ملف CSV فارغ من البيانات"
            
            return True, "صحيح"
            
        except Exception as e:
            return False, f"خطأ في التحقق: {str(e)}"
    
    def load_csv_data(self) -> pd.DataFrame:
        """تحميل البيانات من ملف CSV"""
        try:
            logger.info(f"📖 قراءة ملف CSV: {self.csv_file_path.name}")
            
            # قراءة الملف مع معالجة الترميز
            try:
                df = pd.read_csv(self.csv_file_path, encoding='utf-8')
            except:
                df = pd.read_csv(self.csv_file_path, encoding='latin-1')
            
            logger.info(f"📊 تم تحميل {len(df)} سجل من ملف CSV")
            logger.info(f"📋 الأعمدة المتاحة ({len(df.columns)}): {list(df.columns)[:10]}...")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ خطأ في قراءة ملف CSV: {e}")
            return pd.DataFrame()
    
    def clean_and_transform_financial_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """تنظيف وتحويل البيانات المالية"""
        if df.empty:
            return df
        
        try:
            logger.info("🧹 بدء تنظيف وتحويل البيانات المالية")
            
            # نسخ البيانات
            clean_df = df.copy()
            
            # إعادة تسمية الأعمدة حسب التطابق
            rename_dict = {}
            for csv_col in clean_df.columns:
                if csv_col in self.column_mapping:
                    rename_dict[csv_col] = self.column_mapping[csv_col]
            
            clean_df = clean_df.rename(columns=rename_dict)
            
            # التأكد من وجود عمود الرمز
            if 'symbol' not in clean_df.columns:
                # البحث عن عمود يحتوي على رموز الأسهم
                possible_symbol_cols = ['Symbol', 'SYMBOL', 'الرمز', 'ticker', 'TICKER']
                for col in possible_symbol_cols:
                    if col in clean_df.columns:
                        clean_df['symbol'] = clean_df[col]
                        break
            
            if 'symbol' not in clean_df.columns:
                logger.error("❌ لم يتم العثور على عمود الرمز")
                return pd.DataFrame()
            
            # تنظيف رموز الأسهم
            clean_df['symbol'] = clean_df['symbol'].astype(str).str.strip().str.upper()
            clean_df = clean_df[clean_df['symbol'] != '']
            clean_df = clean_df[clean_df['symbol'] != 'NAN']
            clean_df = clean_df.dropna(subset=['symbol'])
            
            # تعيين قيم افتراضية للسنة المالية والربع
            if 'fiscal_year' not in clean_df.columns:
                clean_df['fiscal_year'] = datetime.now().year
            else:
                clean_df['fiscal_year'] = pd.to_numeric(clean_df['fiscal_year'], errors='coerce').fillna(datetime.now().year).astype(int)
            
            if 'fiscal_quarter' not in clean_df.columns:
                clean_df['fiscal_quarter'] = 4  # آخر ربع افتراضي
            else:
                clean_df['fiscal_quarter'] = pd.to_numeric(clean_df['fiscal_quarter'], errors='coerce').fillna(4).astype(int)
                clean_df['fiscal_quarter'] = clean_df['fiscal_quarter'].clip(1, 4)
            
            # تحويل جميع الأعمدة الرقمية
            numeric_columns = [
                'market_cap', 'enterprise_value', 'shares_outstanding',
                'pe_ratio', 'pb_ratio', 'ps_ratio', 'pcf_ratio', 'peg_ratio',
                'eps_basic', 'eps_diluted', 'eps_growth_yoy', 'eps_growth_qoq',
                'dividend_yield', 'dividend_payout_ratio', 'dividends_per_share',
                'total_revenue', 'gross_profit', 'operating_income', 'net_income',
                'ebitda', 'ebit', 'gross_margin', 'operating_margin', 'net_margin',
                'ebitda_margin', 'total_assets', 'current_assets', 'non_current_assets',
                'total_liabilities', 'current_liabilities', 'long_term_debt',
                'total_equity', 'retained_earnings', 'cash_and_equivalents',
                'inventory', 'accounts_receivable', 'operating_cash_flow',
                'investing_cash_flow', 'financing_cash_flow', 'free_cash_flow',
                'capital_expenditures', 'current_ratio', 'quick_ratio', 'cash_ratio',
                'operating_cash_flow_ratio', 'debt_to_equity', 'debt_to_assets',
                'debt_to_ebitda', 'equity_ratio', 'roe', 'roa', 'roic',
                'asset_turnover', 'inventory_turnover', 'receivables_turnover',
                'payables_turnover', 'beta', 'target_price', 'target_performance_1y',
                'industry_comparison_score', 'financial_strength_score', 'growth_score'
            ]
            
            for col in numeric_columns:
                if col in clean_df.columns:
                    clean_df[col] = pd.to_numeric(clean_df[col], errors='coerce')
            
            # تحويل أعمدة التاريخ
            date_columns = ['recent_earnings_date', 'upcoming_earnings_date', 
                           'dividend_ex_date', 'dividend_pay_date']
            
            for col in date_columns:
                if col in clean_df.columns:
                    clean_df[col] = pd.to_datetime(clean_df[col], errors='coerce')
            
            # إضافة تاريخ التقرير إذا لم يكن موجوداً
            if 'reporting_date' not in clean_df.columns:
                clean_df['reporting_date'] = datetime.now().date()
            
            # إضافة أوقات التحديث
            clean_df['created_at'] = datetime.now()
            clean_df['updated_at'] = datetime.now()
            
            # إزالة الصفوف المكررة بناءً على الرمز والسنة والربع
            clean_df = clean_df.drop_duplicates(subset=['symbol', 'fiscal_year', 'fiscal_quarter'], keep='last')
            
            logger.info(f"✅ تم تنظيف البيانات المالية - المتبقي: {len(clean_df)} سجل")
            
            return clean_df
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف البيانات المالية: {e}")
            return pd.DataFrame()
    
    def ensure_stocks_exist(self, symbols: List[str]) -> bool:
        """التأكد من وجود الأسهم في جدول stocks_master"""
        try:
            cursor = self.connection.cursor()
            
            # الحصول على الرموز الموجودة
            cursor.execute("SELECT symbol FROM stocks_master WHERE symbol = ANY(%s)", (symbols,))
            existing_symbols = {row[0] for row in cursor.fetchall()}
            
            # إضافة الرموز المفقودة
            new_symbols = set(symbols) - existing_symbols
            
            if new_symbols:
                logger.info(f"➕ إضافة {len(new_symbols)} رمز جديد إلى stocks_master")
                
                for symbol in new_symbols:
                    cursor.execute("""
                        INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT (symbol) DO NOTHING
                    """, (symbol, f"سهم {symbol}", symbol, True))
            
            cursor.close()
            self.connection.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في التأكد من وجود الأسهم: {e}")
            return False
    
    def upsert_financial_data(self, df: pd.DataFrame) -> Tuple[int, int, int]:
        """إدراج أو تحديث البيانات المالية"""
        if df.empty:
            return 0, 0, 0
        
        try:
            cursor = self.connection.cursor()
            
            inserted_count = 0
            updated_count = 0
            failed_count = 0
            
            # إعداد قائمة الأعمدة للإدراج
            db_columns = [
                'symbol', 'fiscal_year', 'fiscal_quarter', 'reporting_date',
                'market_cap', 'enterprise_value', 'shares_outstanding',
                'pe_ratio', 'pb_ratio', 'ps_ratio', 'pcf_ratio', 'peg_ratio',
                'eps_basic', 'eps_diluted', 'eps_growth_yoy', 'eps_growth_qoq',
                'dividend_yield', 'dividend_payout_ratio', 'dividends_per_share',
                'total_revenue', 'gross_profit', 'operating_income', 'net_income',
                'ebitda', 'ebit', 'gross_margin', 'operating_margin', 'net_margin',
                'ebitda_margin', 'total_assets', 'current_assets', 'non_current_assets',
                'total_liabilities', 'current_liabilities', 'long_term_debt',
                'total_equity', 'retained_earnings', 'cash_and_equivalents',
                'inventory', 'accounts_receivable', 'operating_cash_flow',
                'investing_cash_flow', 'financing_cash_flow', 'free_cash_flow',
                'capital_expenditures', 'current_ratio', 'quick_ratio', 'cash_ratio',
                'operating_cash_flow_ratio', 'debt_to_equity', 'debt_to_assets',
                'debt_to_ebitda', 'equity_ratio', 'roe', 'roa', 'roic',
                'asset_turnover', 'inventory_turnover', 'receivables_turnover',
                'payables_turnover', 'beta', 'analyst_rating', 'target_price',
                'target_performance_1y', 'recent_earnings_date', 'upcoming_earnings_date',
                'dividend_ex_date', 'dividend_pay_date', 'industry_comparison_score',
                'financial_strength_score', 'growth_score', 'created_at', 'updated_at'
            ]
            
            # إعداد استعلام UPSERT
            columns_str = ', '.join(db_columns)
            values_str = ', '.join(['%s'] * len(db_columns))
            update_str = ', '.join([f"{col} = EXCLUDED.{col}" for col in db_columns 
                                   if col not in ['symbol', 'fiscal_year', 'fiscal_quarter', 'created_at']])
            
            upsert_query = f"""
                INSERT INTO stocks_financials ({columns_str})
                VALUES ({values_str})
                ON CONFLICT (symbol, fiscal_year, fiscal_quarter) DO UPDATE SET
                {update_str}
            """
            
            # معالجة كل سجل
            for _, row in df.iterrows():
                try:
                    # تحضير البيانات للإدراج
                    values = []
                    for col in db_columns:
                        value = row.get(col)
                        if pd.isna(value):
                            values.append(None)
                        else:
                            values.append(value)
                    
                    # تنفيذ الاستعلام
                    cursor.execute(upsert_query, values)
                    
                    # تحديد نوع العملية (إدراج أم تحديث)
                    if cursor.rowcount == 1:
                        inserted_count += 1
                    else:
                        updated_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    logger.warning(f"⚠️ فشل في معالجة السهم {row.get('symbol', 'غير معروف')}: {e}")
            
            cursor.close()
            
            logger.info(f"💾 إدراج: {inserted_count}, تحديث: {updated_count}, فشل: {failed_count}")
            
            return inserted_count, updated_count, failed_count
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في إدراج البيانات المالية: {e}")
            return 0, 0, len(df)
    
    def log_sync_operation(self, records_processed: int, records_inserted: int, 
                          records_updated: int, records_failed: int, 
                          duration: int, status: str, error_msg: str = None):
        """تسجيل عملية المزامنة"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO data_sync_log (
                    sync_type, source_file, records_processed, records_inserted,
                    records_updated, records_failed, sync_status, error_message, 
                    sync_duration_seconds, started_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                'financial', self.csv_file_path.name, records_processed, 
                records_inserted, records_updated, records_failed, status, 
                error_msg, duration, datetime.now()
            ))
            cursor.close()
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل عملية المزامنة: {e}")
    
    def import_financial_data(self) -> Dict:
        """استيراد البيانات المالية الرئيسية"""
        self.stats['start_time'] = datetime.now()
        logger.info("🚀 بدء استيراد البيانات المالية للأسهم المصرية")
        
        if not self.connect_db():
            return self.stats
        
        try:
            # التحقق من صحة الملف
            is_valid, validation_msg = self.validate_csv_file()
            if not is_valid:
                logger.error(f"❌ ملف CSV غير صحيح: {validation_msg}")
                return self.stats
            
            # تحميل البيانات
            df = self.load_csv_data()
            if df.empty:
                logger.error("❌ لا توجد بيانات في ملف CSV")
                return self.stats
            
            self.stats['total_records'] = len(df)
            
            # تنظيف البيانات
            clean_df = self.clean_and_transform_financial_data(df)
            if clean_df.empty:
                logger.error("❌ لا توجد بيانات صحيحة بعد التنظيف")
                return self.stats
            
            # التأكد من وجود الأسهم في stocks_master
            symbols = clean_df['symbol'].unique().tolist()
            if not self.ensure_stocks_exist(symbols):
                logger.error("❌ فشل في التأكد من وجود الأسهم")
                return self.stats
            
            # إدراج البيانات المالية
            inserted, updated, failed = self.upsert_financial_data(clean_df)
            
            self.stats['inserted_records'] = inserted
            self.stats['updated_records'] = updated
            self.stats['failed_records'] = failed
            
            if inserted + updated > 0:
                self.connection.commit()
                status = 'SUCCESS'
                logger.info("✅ تم حفظ البيانات المالية بنجاح")
            else:
                self.connection.rollback()
                status = 'FAILED'
                logger.error("❌ فشل في حفظ البيانات المالية")
            
            self.stats['end_time'] = datetime.now()
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            # تسجيل العملية
            self.log_sync_operation(
                len(clean_df), inserted, updated, failed, 
                int(duration), status
            )
            
            # تقرير نهائي
            logger.info("=" * 80)
            logger.info("📋 تقرير استيراد البيانات المالية")
            logger.info("=" * 80)
            logger.info(f"📊 إجمالي السجلات: {self.stats['total_records']}")
            logger.info(f"➕ سجلات جديدة: {self.stats['inserted_records']}")
            logger.info(f"🔄 سجلات محدثة: {self.stats['updated_records']}")
            logger.info(f"❌ سجلات فاشلة: {self.stats['failed_records']}")
            logger.info(f"⏱️ المدة: {duration:.1f} ثانية")
            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في عملية الاستيراد: {e}")
            self.connection.rollback()
        
        finally:
            self.close_db()
        
        return self.stats

def main():
    """الدالة الرئيسية"""    # إعدادات قاعدة البيانات
    db_config = {
        'host': 'localhost',
        'database': 'egx_stock_oracle',
        'user': 'postgres',
        'password': '',
        'port': 5432
    }
    
    # إنشاء المستورد
    importer = EGXFinancialDataImporter(db_config)
    
    # بدء عملية الاستيراد
    stats = importer.import_financial_data()
    
    print("\n🎉 اكتملت عملية استيراد البيانات المالية!")
    print(f"📊 تم معالجة {stats['inserted_records'] + stats['updated_records']} سجل مالي")

if __name__ == "__main__":
    main()
