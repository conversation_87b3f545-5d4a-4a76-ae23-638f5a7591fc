
-- Enable RLS on all user-related tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_holdings ENABLE ROW LEVEL SECURITY;
ALTER TABLE price_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE paper_trading_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE paper_trades ENABLE ROW LEVEL SECURITY;

-- Users can SELECT/UPDATE their own record
CREATE POLICY "Users can view own info" ON users
FOR SELECT USING (id = auth.uid());
CREATE POLICY "Users can update own info" ON users
FOR UPDATE USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- Each user sees and manages only their own portfolios
CREATE POLICY "User can view own portfolios" ON user_portfolios
FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "User can insert portfolio" ON user_portfolios
FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can update own portfolio" ON user_portfolios
FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can delete own portfolio" ON user_portfolios
FOR DELETE USING (user_id = auth.uid());

-- Users can see/update only their own holdings
CREATE POLICY "User can view holdings" ON portfolio_holdings
FOR SELECT USING (
  portfolio_id IN (SELECT id FROM user_portfolios WHERE user_id = auth.uid())
);
CREATE POLICY "User can insert holding" ON portfolio_holdings
FOR INSERT WITH CHECK (
  portfolio_id IN (SELECT id FROM user_portfolios WHERE user_id = auth.uid())
);
CREATE POLICY "User can update holding" ON portfolio_holdings
FOR UPDATE USING (
  portfolio_id IN (SELECT id FROM user_portfolios WHERE user_id = auth.uid())
) WITH CHECK (
  portfolio_id IN (SELECT id FROM user_portfolios WHERE user_id = auth.uid())
);
CREATE POLICY "User can delete holding" ON portfolio_holdings
FOR DELETE USING (
  portfolio_id IN (SELECT id FROM user_portfolios WHERE user_id = auth.uid())
);

-- Each user manages only their own alerts
CREATE POLICY "User can view alerts" ON price_alerts
FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "User can insert alert" ON price_alerts
FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can update alert" ON price_alerts
FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can delete alert" ON price_alerts
FOR DELETE USING (user_id = auth.uid());

-- Each user gets only their own notifications
CREATE POLICY "User can view notifications" ON user_notifications
FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "User can insert notification" ON user_notifications
FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can update notification" ON user_notifications
FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can delete notification" ON user_notifications
FOR DELETE USING (user_id = auth.uid());

-- Each user can manage their paper trading account(s)
CREATE POLICY "User can view paper accounts" ON paper_trading_accounts
FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "User can insert paper account" ON paper_trading_accounts
FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can update paper account" ON paper_trading_accounts
FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
CREATE POLICY "User can delete paper account" ON paper_trading_accounts
FOR DELETE USING (user_id = auth.uid());

-- Each user can manage their own paper trades (through their account)
CREATE POLICY "User can view paper trades" ON paper_trades
FOR SELECT USING (
  account_id IN (SELECT id FROM paper_trading_accounts WHERE user_id = auth.uid())
);
CREATE POLICY "User can insert paper trade" ON paper_trades
FOR INSERT WITH CHECK (
  account_id IN (SELECT id FROM paper_trading_accounts WHERE user_id = auth.uid())
);
CREATE POLICY "User can update paper trade" ON paper_trades
FOR UPDATE USING (
  account_id IN (SELECT id FROM paper_trading_accounts WHERE user_id = auth.uid())
) WITH CHECK (
  account_id IN (SELECT id FROM paper_trading_accounts WHERE user_id = auth.uid())
);
CREATE POLICY "User can delete paper trade" ON paper_trades
FOR DELETE USING (
  account_id IN (SELECT id FROM paper_trading_accounts WHERE user_id = auth.uid())
);
