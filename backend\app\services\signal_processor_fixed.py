# Enhanced Signal Processing Service with Advanced PnL Logic
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from datetime import datetime
import logging

from ..database import get_db
from ..models.trading_signals import TradingSignal, LiveRecommendations, SignalPerformance
from .telegram_service import TelegramService
from .notification_service import NotificationService

logger = logging.getLogger(__name__)

class SignalProcessor:
    """
    Enhanced signal processor with advanced PnL logic for trailing stops
    and protected profit calculations
    """
    
    def __init__(self):
        self.db_session = None
    
    async def process_buy_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """
        Process buy signal with strict duplicate prevention
        ONLY accept buy signals if there's NO active recommendation for the stock
        """
        try:
            # Get database session
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            buy_price = signal_data.get('buy_price')
            tp1 = signal_data.get('tp1')
            tp2 = signal_data.get('tp2')
            tp3 = signal_data.get('tp3')
            stop_loss = signal_data.get('sl')
            
            # CRITICAL: Check if there's already an active recommendation for this stock
            existing_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if existing_rec:
                # REJECT duplicate/open buy signal
                logger.warning(f"REJECTED duplicate buy signal for {stock_code} - Active recommendation already exists (ID: {existing_rec.id})")
                
                # Update signal status to rejected
                signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
                if signal_record:
                    signal_record.status = 'rejected'
                    signal_record.reject_reason = 'duplicate_active_buy'
                
                db.commit()
                
                # Send warning notification
                await self._send_duplicate_signal_warning(signal_data, existing_rec)
                return
            
            # Calculate risk-reward ratio
            risk_reward_ratio = None
            if buy_price and stop_loss and tp1:
                risk = buy_price - stop_loss
                reward = tp1 - buy_price
                risk_reward_ratio = reward / risk if risk > 0 else None
            
            # Create new live recommendation (only if no active one exists)
            live_rec = LiveRecommendations(
                stock_code=stock_code,
                action='buy',
                entry_price=buy_price,
                tp1=tp1,
                tp2=tp2,
                tp3=tp3,
                stop_loss=stop_loss,
                risk_reward_ratio=risk_reward_ratio,
                status='active',
                highest_price=buy_price  # Initialize highest price
            )
            
            db.add(live_rec)
            
            # Update signal status to processed
            signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
            if signal_record:
                signal_record.status = 'processed'
            
            db.commit()
            
            logger.info(f"✅ ACCEPTED new buy signal for {stock_code} at {buy_price}")
            
            # Send notifications
            await self._send_buy_notification(signal_data)
            
        except Exception as e:
            logger.error(f"Error processing buy signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def process_sell_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """
        Process sell signal ONLY if there's an active recommendation
        IGNORE orphan sell signals
        """
        try:
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            sell_price = signal_data.get('sell_price')
            
            # Find active recommendation
            active_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if not active_rec:
                # IGNORE orphan sell signal - no active buy recommendation exists
                logger.warning(f"🚫 IGNORED orphan sell signal for {stock_code} - No active buy recommendation found")
                
                # Update signal status to ignored
                signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
                if signal_record:
                    signal_record.status = 'ignored'
                    signal_record.reject_reason = 'orphan_sell_no_active_buy'
                
                db.commit()
                
                # Send warning notification about orphan signal
                await self._send_orphan_signal_warning(signal_data, 'sell')
                return
            
            # Process sell signal with advanced PnL logic
            if sell_price and active_rec.entry_price:
                final_pnl = active_rec.calculate_pnl(sell_price)
                
                # Update highest price if this sell price is higher
                if not active_rec.highest_price or sell_price > active_rec.highest_price:
                    active_rec.highest_price = sell_price
                
                # Determine if this is a protected profit scenario
                targets_hit = sum([active_rec.tp1_hit or False, active_rec.tp2_hit or False, active_rec.tp3_hit or False])
                is_protected_profit = False
                
                # Check if we have trailing stop scenario with achieved targets
                if targets_hit > 0 and active_rec.highest_price:
                    # Calculate max profit that was achieved
                    max_profit_pnl = active_rec.calculate_pnl(active_rec.highest_price)
                    
                    # If current sell price gives positive return but less than max achieved
                    if final_pnl > 0 and final_pnl < max_profit_pnl:
                        # This is a trailing stop scenario - we're protecting profit
                        is_protected_profit = True
                        active_rec.protected_profit = final_pnl
                
                # Update recommendation
                active_rec.current_price = sell_price
                active_rec.current_pnl = final_pnl
                active_rec.status = 'closed'
                active_rec.close_reason = 'protected_profit' if is_protected_profit else 'sell_signal'
                active_rec.closed_at = datetime.utcnow()
                
                # Create performance record with enhanced logic
                outcome = 'profit' if final_pnl > 0 else 'loss' if final_pnl < 0 else 'breakeven'
                if is_protected_profit:
                    outcome = 'protected_profit'
                
                performance = SignalPerformance(
                    signal_id=signal_id,
                    stock_code=stock_code,
                    total_return=final_pnl,
                    max_profit=active_rec.max_pnl or final_pnl,
                    outcome=outcome,
                    targets_hit=targets_hit
                )
                
                db.add(performance)
                
                # Update signal status to processed
                signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
                if signal_record:
                    signal_record.status = 'processed'
                
                db.commit()
                
                logger.info(f"✅ PROCESSED sell signal for {stock_code} with {final_pnl:.2f}% P&L (Protected: {is_protected_profit})")
                
                # Send notifications with enhanced info
                await self._send_sell_notification(signal_data, active_rec.current_pnl, 
                                                 targets_hit, active_rec.close_reason == 'protected_profit')
                
        except Exception as e:
            logger.error(f"Error processing sell signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def process_tp_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """
        Process take profit signal ONLY if there's an active recommendation
        IGNORE orphan TP signals
        """
        try:
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            signal_type = signal_data['report']
            new_sl = signal_data.get('sl')
            
            # Find active recommendation
            active_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if not active_rec:
                # IGNORE orphan TP signal - no active buy recommendation exists
                logger.warning(f"🚫 IGNORED orphan TP signal ({signal_type}) for {stock_code} - No active buy recommendation found")
                
                # Update signal status to ignored
                signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
                if signal_record:
                    signal_record.status = 'ignored'
                    signal_record.reject_reason = f'orphan_{signal_type}_no_active_buy'
                
                db.commit()
                
                # Send warning notification about orphan signal
                await self._send_orphan_signal_warning(signal_data, signal_type)
                return
            
            # Process TP signal
            tp_price = None
            tp_number = None
            
            # Update TP achievement and stop loss
            if signal_type == 'tp1done':
                active_rec.tp1_hit = True
                active_rec.tp1_hit_at = datetime.utcnow()
                tp_price = active_rec.tp1
                tp_number = '1'
            elif signal_type == 'tp2done':
                active_rec.tp2_hit = True
                active_rec.tp2_hit_at = datetime.utcnow()
                tp_price = active_rec.tp2
                tp_number = '2'
            elif signal_type == 'tp3done':
                active_rec.tp3_hit = True
                active_rec.tp3_hit_at = datetime.utcnow()
                tp_price = active_rec.tp3
                tp_number = '3'
            
            # Update stop loss to breakeven or profit zone
            if new_sl:
                active_rec.stop_loss = new_sl
            
            # Calculate current P&L and update highest price
            if tp_price and active_rec.entry_price:
                current_pnl = active_rec.calculate_pnl(tp_price)
                active_rec.current_pnl = current_pnl
                active_rec.current_price = tp_price
                
                # Update highest price if this TP price is higher
                if not active_rec.highest_price or tp_price > active_rec.highest_price:
                    active_rec.highest_price = tp_price
                    
                    # Update max profit
                    if not active_rec.max_pnl or current_pnl > active_rec.max_pnl:
                        active_rec.max_pnl = current_pnl
                
                active_rec.updated_at = datetime.utcnow()
                
                # Update signal status to processed
                signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
                if signal_record:
                    signal_record.status = 'processed'
                
                db.commit()
                
                logger.info(f"✅ PROCESSED TP{tp_number} for {stock_code}, new SL: {new_sl}, highest price: {active_rec.highest_price}")
                
                # Send notifications
                await self._send_tp_notification(signal_data, tp_number, tp_price)
                
        except Exception as e:
            logger.error(f"Error processing TP signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def process_stop_loss_signal(self, signal_data: Dict[str, Any], signal_id: int):
        """
        Process stop loss signal ONLY if there's an active recommendation
        IGNORE orphan SL signals
        """
        try:
            db = next(get_db())
            
            stock_code = signal_data['stock_code'].upper()
            sl_price = signal_data.get('sl')
            
            # Find active recommendation
            active_rec = db.query(LiveRecommendations).filter(
                LiveRecommendations.stock_code == stock_code,
                LiveRecommendations.status == 'active'
            ).first()
            
            if not active_rec:
                # IGNORE orphan SL signal - no active buy recommendation exists
                logger.warning(f"🚫 IGNORED orphan SL signal for {stock_code} - No active buy recommendation found")
                
                # Update signal status to ignored
                signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
                if signal_record:
                    signal_record.status = 'ignored'
                    signal_record.reject_reason = 'orphan_sl_no_active_buy'
                
                db.commit()
                
                # Send warning notification about orphan signal
                await self._send_orphan_signal_warning(signal_data, 'tsl')
                return
            
            # Process SL signal - close the recommendation with loss
            if sl_price and active_rec.entry_price:
                final_pnl = active_rec.calculate_pnl(sl_price)
                
                # Update recommendation
                active_rec.current_price = sl_price
                active_rec.current_pnl = final_pnl
                active_rec.status = 'closed'
                active_rec.close_reason = 'stop_loss'
                active_rec.closed_at = datetime.utcnow()
                
                # Count targets hit
                targets_hit = sum([active_rec.tp1_hit or False, active_rec.tp2_hit or False, active_rec.tp3_hit or False])
                
                # Create performance record
                performance = SignalPerformance(
                    signal_id=signal_id,
                    stock_code=stock_code,
                    total_return=final_pnl,
                    max_profit=active_rec.max_pnl or final_pnl,
                    outcome='loss',
                    targets_hit=targets_hit
                )
                
                db.add(performance)
                
                # Update signal status to processed
                signal_record = db.query(TradingSignal).filter(TradingSignal.id == signal_id).first()
                if signal_record:
                    signal_record.status = 'processed'
                
                db.commit()
                
                logger.info(f"✅ PROCESSED SL for {stock_code} with {final_pnl:.2f}% loss")
                
                # Send notifications
                await self._send_sl_notification(signal_data, final_pnl, targets_hit)
                
        except Exception as e:
            logger.error(f"Error processing stop loss signal: {str(e)}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    # Notification methods
    async def _send_buy_notification(self, signal_data: Dict[str, Any]):
        """Send buy signal notification with Arabic support"""
        try:
            stock_code = signal_data['stock_code']
            buy_price = signal_data.get('buy_price', 0)
            tp1 = signal_data.get('tp1', 0)
            tp2 = signal_data.get('tp2', 0)
            tp3 = signal_data.get('tp3', 0)
            sl = signal_data.get('sl', 0)
            
            # Calculate risk-reward ratio
            risk = buy_price - sl if buy_price and sl else 0
            reward = tp1 - buy_price if tp1 and buy_price else 0
            rr_ratio = reward / risk if risk > 0 else 0
            
            # Telegram message
            telegram_message = f"""
🟢 إشارة شراء جديدة

📈 السهم: {stock_code}
💰 سعر الشراء: {buy_price:.2f} جنيه
🎯 الأهداف:
   الأول: {tp1:.2f} جنيه
   الثاني: {tp2:.2f} جنيه  
   الثالث: {tp3:.2f} جنيه
⛔ وقف الخسارة: {sl:.2f} جنيه
📊 نسبة المخاطرة/العائد: 1:{rr_ratio:.2f}

⏰ {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # Send via Telegram
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
            # Send web notifications
            notification_service = NotificationService()
            await notification_service.send_signal_notification({
                'type': 'buy_signal',
                'stock_code': stock_code,
                'message': f'إشارة شراء جديدة - {stock_code}',
                'data': signal_data
            })
            
        except Exception as e:
            logger.error(f"Error sending buy notification: {str(e)}")
    
    async def _send_sell_notification(self, signal_data: Dict[str, Any], pnl: Optional[float] = None, 
                                    targets_hit: int = 0, is_protected: bool = False):
        """Send sell signal notification with enhanced info"""
        try:
            stock_code = signal_data['stock_code']
            sell_price = signal_data.get('sell_price', 0)
            
            pnl_text = f"\n💰 الربح/الخسارة: {pnl:.2f}%" if pnl else ""
            pnl_emoji = "📈" if pnl and pnl > 0 else "📉" if pnl and pnl < 0 else "🔄"
            
            protection_text = ""
            if is_protected:
                protection_text = "\n🛡️ تم تأمين الأرباح (حماية الأرباح)"
            
            targets_text = ""
            if targets_hit > 0:
                targets_text = f"\n✅ الأهداف المحققة: {targets_hit}"
            
            telegram_message = f"""
{pnl_emoji} إشارة بيع

📈 السهم: {stock_code}
💰 سعر البيع: {sell_price:.2f} جنيه{pnl_text}{targets_text}{protection_text}

⏰ {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
        except Exception as e:
            logger.error(f"Error sending sell notification: {str(e)}")
    
    async def _send_duplicate_signal_warning(self, signal_data: Dict[str, Any], existing_rec):
        """Send warning about duplicate buy signal"""
        try:
            stock_code = signal_data['stock_code']
            
            telegram_message = f"""
⚠️ تحذير: إشارة شراء مكررة

📈 السهم: {stock_code}
❌ تم رفض الإشارة - يوجد توصية نشطة بالفعل
📅 التوصية الأصلية: {existing_rec.created_at.strftime('%Y-%m-%d %H:%M')}
💰 سعر الدخول الأصلي: {existing_rec.entry_price:.2f} جنيه

⏰ {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_warning_message(telegram_message)
            
            logger.info(f"Sent duplicate signal warning for {stock_code}")
            
        except Exception as e:
            logger.error(f"Error sending duplicate signal warning: {str(e)}")
    
    async def _send_orphan_signal_warning(self, signal_data: Dict[str, Any], signal_type: str):
        """Send warning about orphan signal (TP/SL/Sell without active buy)"""
        try:
            stock_code = signal_data['stock_code']
            
            signal_types = {
                'sell': 'بيع',
                'tp1done': 'الهدف الأول',
                'tp2done': 'الهدف الثاني', 
                'tp3done': 'الهدف الثالث',
                'tsl': 'وقف الخسارة المتحرك'
            }
            
            signal_name = signal_types.get(signal_type, signal_type)
            
            telegram_message = f"""
⚠️ تحذير: إشارة يتيمة

📈 السهم: {stock_code}
🔸 نوع الإشارة: {signal_name}
❌ تم تجاهل الإشارة - لا توجد توصية شراء نشطة

💡 ملاحظة: يجب وجود إشارة شراء نشطة أولاً لمعالجة إشارات البيع/الأهداف

⏰ {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_warning_message(telegram_message)
            
            logger.info(f"Sent orphan signal warning for {stock_code} - {signal_type}")
            
        except Exception as e:
            logger.error(f"Error sending orphan signal warning: {str(e)}")
    
    async def _send_tp_notification(self, signal_data: Dict[str, Any], tp_number: str, tp_price: float):
        """Send take profit notification"""
        try:
            stock_code = signal_data['stock_code']
            new_sl = signal_data.get('sl', 0)
            
            telegram_message = f"""
🎯 تحقق الهدف!

📈 السهم: {stock_code}
✅ الهدف {tp_number}: {tp_price:.2f} جنيه
⛔ وقف الخسارة الجديد: {new_sl:.2f} جنيه

🔒 تم تأمين الأرباح بنقل وقف الخسارة

⏰ {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
            logger.info(f"Sent TP{tp_number} notification for {stock_code}")
            
        except Exception as e:
            logger.error(f"Error sending TP notification: {str(e)}")
    
    async def _send_sl_notification(self, signal_data: Dict[str, Any], pnl: float, targets_hit: int):
        """Send stop loss notification"""
        try:
            stock_code = signal_data['stock_code']
            sl_price = signal_data.get('sl', 0)
            
            targets_text = ""
            if targets_hit > 0:
                targets_text = f"\n✅ عدد الأهداف المحققة: {targets_hit}"
            
            telegram_message = f"""
🔴 تفعيل وقف الخسارة

📈 السهم: {stock_code}
💔 سعر الإغلاق: {sl_price:.2f} جنيه
📉 الخسارة: {pnl:.2f}%{targets_text}

⏰ {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            telegram_service = TelegramService()
            await telegram_service.send_signal_message(telegram_message)
            
            logger.info(f"Sent SL notification for {stock_code}")
            
        except Exception as e:
            logger.error(f"Error sending SL notification: {str(e)}")
