import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

type StockData = Database['public']['Tables']['stocks_realtime']['Row'];

export interface FinalMarketOverview {
  marketSummary: {
    totalStocks: number;
    activeStocks: number;
    gainersCount: number;
    losersCount: number;
    unchangedCount: number;
    avgChange: number;
    totalVolume: number;
    totalValue: number;
    marketTrend: 'bullish' | 'bearish' | 'neutral';
  };
  topStocks: {
    gainers: Array<{
      symbol: string;
      name: string | null;
      change_percent: number;
      current_price: number;
      volume: number;
    }>;
    losers: Array<{
      symbol: string;
      name: string | null;
      change_percent: number;
      current_price: number;
      volume: number;
    }>;
    mostActive: Array<{
      symbol: string;
      name: string | null;
      volume: number;
      current_price: number;
      change_percent: number;
    }>;
  };
  technicalSummary: {
    bullishStocks: number;
    bearishStocks: number;
    strongUptrend: number;
    stocksWithTargets: number;
    avgPE: number;
    avgDividendYield: number;
  };
  sectorSummary: {
    bestSector: string | null;
    worstSector: string | null;
    sectorCount: number;
  };
}

export function useFinalMarketOverview() {
  return useQuery<FinalMarketOverview>({
    queryKey: ['final-market-overview'],
    queryFn: async () => {
      console.log('🔍 جاري جلب النظرة العامة النهائية...');

      // استعلام واحد محسن لجلب جميع البيانات المطلوبة
      const { data: marketData, error } = await supabase
        .from('stocks_realtime')
        .select(`
          symbol,
          name,
          current_price,
          change_percent,
          volume,
          turnover,
          ma5,
          ma20,
          ma50,
          target_1,
          pe_ratio,
          dividend_yield,
          sector
        `)
        .not('symbol', 'like', '%EGX%')
        .not('symbol', 'like', '%ETF%')
        .not('name', 'is', null)
        .not('current_price', 'is', null)
        .order('volume', { ascending: false });

      if (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw error;
      }

      if (!marketData || marketData.length === 0) {
        throw new Error('لا توجد بيانات متاحة');
      }

      console.log(`✅ تم جلب ${marketData.length} سهم للتحليل النهائي`);

      // تنظيف وتصفية البيانات
      const validStocks = marketData.filter(s => 
        s.change_percent !== null && 
        s.current_price !== null &&
        s.volume !== null
      ) as Array<StockData>;

      // === الحسابات الأساسية ===
      const gainers = validStocks.filter(s => (s.change_percent || 0) > 0);
      const losers = validStocks.filter(s => (s.change_percent || 0) < 0);
      const unchanged = validStocks.filter(s => (s.change_percent || 0) === 0);
      const activeStocks = validStocks.filter(s => (s.volume || 0) > 0);
      
      const totalVolume = validStocks.reduce((sum, s) => sum + (s.volume || 0), 0);
      const totalValue = validStocks.reduce((sum, s) => sum + (s.turnover || 0), 0);
      const avgChange = validStocks.length > 0 
        ? validStocks.reduce((sum, s) => sum + (s.change_percent || 0), 0) / validStocks.length
        : 0;

      // تحديد اتجاه السوق
      let marketTrend: 'bullish' | 'bearish' | 'neutral' = 'neutral';
      if (gainers.length > losers.length * 1.3) {
        marketTrend = 'bullish';
      } else if (losers.length > gainers.length * 1.3) {
        marketTrend = 'bearish';
      }

      // === أفضل الأسهم ===
      const topGainers = gainers
        .sort((a, b) => (b.change_percent || 0) - (a.change_percent || 0))
        .slice(0, 5)
        .map(s => ({
          symbol: s.symbol,
          name: s.name,
          change_percent: s.change_percent || 0,
          current_price: s.current_price || 0,
          volume: s.volume || 0
        }));

      const topLosers = losers
        .sort((a, b) => (a.change_percent || 0) - (b.change_percent || 0))
        .slice(0, 5)
        .map(s => ({
          symbol: s.symbol,
          name: s.name,
          change_percent: s.change_percent || 0,
          current_price: s.current_price || 0,
          volume: s.volume || 0
        }));

      const mostActive = activeStocks
        .sort((a, b) => (b.volume || 0) - (a.volume || 0))
        .slice(0, 5)
        .map(s => ({
          symbol: s.symbol,
          name: s.name,
          volume: s.volume || 0,
          current_price: s.current_price || 0,
          change_percent: s.change_percent || 0
        }));

      // === التحليل الفني ===
      const bullishStocks = validStocks.filter(s => 
        (s.ma5 || 0) > (s.ma20 || 0) && s.ma5 && s.ma20
      ).length;

      const bearishStocks = validStocks.filter(s => 
        (s.ma5 || 0) < (s.ma20 || 0) && s.ma5 && s.ma20
      ).length;

      const strongUptrend = validStocks.filter(s => 
        (s.ma5 || 0) > (s.ma20 || 0) &&
        (s.ma20 || 0) > (s.ma50 || 0) &&
        s.ma5 && s.ma20 && s.ma50
      ).length;

      const stocksWithTargets = validStocks.filter(s => 
        s.target_1 && s.target_1 > 0
      ).length;

      // متوسط P/E و Dividend Yield
      const validPE = validStocks.filter(s => s.pe_ratio && s.pe_ratio > 0 && s.pe_ratio < 100);
      const avgPE = validPE.length > 0 
        ? validPE.reduce((sum, s) => sum + (s.pe_ratio || 0), 0) / validPE.length
        : 0;

      const validDividend = validStocks.filter(s => s.dividend_yield && s.dividend_yield > 0);
      const avgDividendYield = validDividend.length > 0 
        ? validDividend.reduce((sum, s) => sum + (s.dividend_yield || 0), 0) / validDividend.length
        : 0;

      // === تحليل القطاعات ===
      const sectorMap = new Map<string, { count: number; avgChange: number; totalChange: number }>();
      validStocks.forEach(s => {
        if (s.sector) {
          const existing = sectorMap.get(s.sector) || { count: 0, avgChange: 0, totalChange: 0 };
          existing.count += 1;
          existing.totalChange += s.change_percent || 0;
          existing.avgChange = existing.totalChange / existing.count;
          sectorMap.set(s.sector, existing);
        }
      });

      const sectors = Array.from(sectorMap.entries())
        .map(([sector, data]) => ({ sector, ...data }))
        .sort((a, b) => b.avgChange - a.avgChange);

      const bestSector = sectors.length > 0 ? sectors[0].sector : null;
      const worstSector = sectors.length > 0 ? sectors[sectors.length - 1].sector : null;

      const result: FinalMarketOverview = {
        marketSummary: {
          totalStocks: validStocks.length,
          activeStocks: activeStocks.length,
          gainersCount: gainers.length,
          losersCount: losers.length,
          unchangedCount: unchanged.length,
          avgChange: Math.round(avgChange * 100) / 100,
          totalVolume: Math.round(totalVolume),
          totalValue: Math.round(totalValue),
          marketTrend
        },
        topStocks: {
          gainers: topGainers,
          losers: topLosers,
          mostActive: mostActive
        },
        technicalSummary: {
          bullishStocks,
          bearishStocks,
          strongUptrend,
          stocksWithTargets,
          avgPE: Math.round(avgPE * 100) / 100,
          avgDividendYield: Math.round(avgDividendYield * 100) / 100
        },
        sectorSummary: {
          bestSector,
          worstSector,
          sectorCount: sectors.length
        }
      };

      console.log('✅ تم حساب النظرة العامة النهائية بنجاح');
      console.log(`📊 الإحصائيات: ${gainers.length} رابح، ${losers.length} خاسر، اتجاه: ${marketTrend}`);
      console.log(`🎯 أفضل قطاع: ${bestSector}، أسوأ قطاع: ${worstSector}`);
      
      return result;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Hook مُبسط للحصول على ملخص سريع فقط
export function useFastMarketSummary() {
  return useQuery({
    queryKey: ['fast-market-summary'],
    queryFn: async () => {
      console.log('⚡ جاري جلب الملخص السريع...');
      
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select('change_percent, volume, turnover')
        .not('symbol', 'like', '%EGX%')
        .not('name', 'is', null)
        .not('change_percent', 'is', null);

      if (error) {
        console.error('❌ خطأ في الملخص السريع:', error);
        throw error;
      }

      const gainers = data.filter(s => (s.change_percent || 0) > 0).length;
      const losers = data.filter(s => (s.change_percent || 0) < 0).length;
      const totalVolume = data.reduce((sum, s) => sum + (s.volume || 0), 0);
      const avgChange = data.length > 0 
        ? data.reduce((sum, s) => sum + (s.change_percent || 0), 0) / data.length
        : 0;

      console.log('✅ تم جلب الملخص السريع');
      return {
        totalStocks: data.length,
        gainers,
        losers,
        avgChange: Math.round(avgChange * 100) / 100,
        totalVolume: Math.round(totalVolume)
      };
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // 2 minutes
    retry: 2
  });
}
