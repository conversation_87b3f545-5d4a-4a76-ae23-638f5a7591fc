#!/usr/bin/env python3
"""
EGX Stock AI Oracle - Data Validation & Monitoring
Validates data integrity and provides monitoring metrics
"""

import os
import sys
import logging
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
from supabase import create_client, Client
import pandas as pd

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_validation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

class DataValidator:
    def __init__(self):
        self.validation_results = {}
        self.metrics = {}
    
    def validate_stocks_master(self):
        """Validate stocks master data"""
        logger.info("Validating stocks_master table...")
        
        try:
            # Get all stocks
            result = supabase.table("stocks_master").select("*").execute()
            stocks = result.data
            
            if not stocks:
                self.validation_results["stocks_master"] = "FAILED: No stocks found"
                return
            
            total_stocks = len(stocks)
            active_stocks = len([s for s in stocks if s.get('is_active', True)])
            stocks_with_names = len([s for s in stocks if s.get('name')])
            stocks_with_sectors = len([s for s in stocks if s.get('sector')])
            
            # Check for duplicates
            symbols = [s['symbol'] for s in stocks]
            duplicates = len(symbols) - len(set(symbols))
            
            self.metrics["stocks_master"] = {
                "total_stocks": total_stocks,
                "active_stocks": active_stocks,
                "stocks_with_names": stocks_with_names,
                "stocks_with_sectors": stocks_with_sectors,
                "duplicate_symbols": duplicates
            }
            
            if duplicates > 0:
                self.validation_results["stocks_master"] = f"WARNING: {duplicates} duplicate symbols found"
            else:
                self.validation_results["stocks_master"] = "PASSED"
                
            logger.info(f"Stocks master validation: {total_stocks} total, {active_stocks} active")
            
        except Exception as e:
            self.validation_results["stocks_master"] = f"ERROR: {e}"
            logger.error(f"Error validating stocks_master: {e}")
    
    def validate_historical_data(self):
        """Validate historical data completeness and quality"""
        logger.info("Validating stocks_historical table...")
        
        try:
            # Get data summary
            result = supabase.table("stocks_historical").select("symbol, date, close, volume").execute()
            historical_data = result.data
            
            if not historical_data:
                self.validation_results["stocks_historical"] = "FAILED: No historical data found"
                return
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame(historical_data)
            
            # Basic metrics
            total_records = len(df)
            unique_symbols = df['symbol'].nunique()
            date_range = (pd.to_datetime(df['date']).max() - pd.to_datetime(df['date']).min()).days
            
            # Data quality checks
            missing_prices = df['close'].isna().sum()
            zero_prices = (df['close'] == 0).sum()
            negative_prices = (df['close'] < 0).sum()
            missing_volumes = df['volume'].isna().sum()
            
            # Recent data check (last 7 days)
            recent_date = datetime.now() - timedelta(days=7)
            recent_data = df[pd.to_datetime(df['date']) >= recent_date]
            symbols_with_recent_data = recent_data['symbol'].nunique()
            
            self.metrics["stocks_historical"] = {
                "total_records": total_records,
                "unique_symbols": unique_symbols,
                "date_range_days": date_range,
                "missing_prices": missing_prices,
                "zero_prices": zero_prices,
                "negative_prices": negative_prices,
                "missing_volumes": missing_volumes,
                "symbols_with_recent_data": symbols_with_recent_data
            }
            
            # Validation logic
            issues = []
            if missing_prices > total_records * 0.01:  # More than 1% missing
                issues.append(f"{missing_prices} missing prices")
            if zero_prices > 0:
                issues.append(f"{zero_prices} zero prices")
            if negative_prices > 0:
                issues.append(f"{negative_prices} negative prices")
            if symbols_with_recent_data < unique_symbols * 0.8:  # Less than 80% have recent data
                issues.append(f"Only {symbols_with_recent_data}/{unique_symbols} symbols have recent data")
            
            if issues:
                self.validation_results["stocks_historical"] = f"WARNING: {'; '.join(issues)}"
            else:
                self.validation_results["stocks_historical"] = "PASSED"
                
            logger.info(f"Historical data validation: {total_records} records, {unique_symbols} symbols")
            
        except Exception as e:
            self.validation_results["stocks_historical"] = f"ERROR: {e}"
            logger.error(f"Error validating historical data: {e}")
    
    def validate_realtime_data(self):
        """Validate real-time data freshness and quality"""
        logger.info("Validating stocks_realtime table...")
        
        try:
            result = supabase.table("stocks_realtime").select("*").execute()
            realtime_data = result.data
            
            if not realtime_data:
                self.validation_results["stocks_realtime"] = "FAILED: No real-time data found"
                return
            
            total_symbols = len(realtime_data)
            symbols_with_prices = len([s for s in realtime_data if s.get('current_price')])
            symbols_with_volume = len([s for s in realtime_data if s.get('volume')])
            
            # Check data freshness (updated within last hour)
            one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
            fresh_data_count = 0
            
            for symbol_data in realtime_data:
                if symbol_data.get('updated_at'):
                    try:
                        updated_at = pd.to_datetime(symbol_data['updated_at'])
                        if updated_at >= one_hour_ago:
                            fresh_data_count += 1
                    except:
                        pass
            
            # Price validation
            valid_prices = 0
            for symbol_data in realtime_data:
                price = symbol_data.get('current_price')
                if price and price > 0:
                    valid_prices += 1
            
            self.metrics["stocks_realtime"] = {
                "total_symbols": total_symbols,
                "symbols_with_prices": symbols_with_prices,
                "symbols_with_volume": symbols_with_volume,
                "fresh_data_count": fresh_data_count,
                "valid_prices": valid_prices
            }
            
            # Validation logic
            issues = []
            if symbols_with_prices < total_symbols * 0.9:  # Less than 90% have prices
                issues.append(f"Only {symbols_with_prices}/{total_symbols} symbols have prices")
            if fresh_data_count < total_symbols * 0.5:  # Less than 50% updated in last hour
                issues.append(f"Only {fresh_data_count}/{total_symbols} symbols updated recently")
            if valid_prices < symbols_with_prices * 0.95:  # Less than 95% have valid prices
                issues.append(f"Only {valid_prices}/{symbols_with_prices} symbols have valid prices")
            
            if issues:
                self.validation_results["stocks_realtime"] = f"WARNING: {'; '.join(issues)}"
            else:
                self.validation_results["stocks_realtime"] = "PASSED"
                
            logger.info(f"Real-time data validation: {total_symbols} symbols, {fresh_data_count} fresh")
            
        except Exception as e:
            self.validation_results["stocks_realtime"] = f"ERROR: {e}"
            logger.error(f"Error validating real-time data: {e}")
    
    def validate_financial_data(self):
        """Validate financial data completeness"""
        logger.info("Validating stocks_financials table...")
        
        try:
            result = supabase.table("stocks_financials").select("*").execute()
            financial_data = result.data
            
            if not financial_data:
                self.validation_results["stocks_financials"] = "WARNING: No financial data found"
                return
            
            total_records = len(financial_data)
            unique_symbols = len(set(f['symbol'] for f in financial_data))
            
            # Check for key financial metrics
            records_with_pe = len([f for f in financial_data if f.get('pe_ratio')])
            records_with_market_cap = len([f for f in financial_data if f.get('market_cap')])
            records_with_eps = len([f for f in financial_data if f.get('eps_ttm')])
            
            self.metrics["stocks_financials"] = {
                "total_records": total_records,
                "unique_symbols": unique_symbols,
                "records_with_pe": records_with_pe,
                "records_with_market_cap": records_with_market_cap,
                "records_with_eps": records_with_eps
            }
            
            # Basic validation
            if total_records > 0:
                self.validation_results["stocks_financials"] = "PASSED"
            else:
                self.validation_results["stocks_financials"] = "WARNING: No financial data"
                
            logger.info(f"Financial data validation: {total_records} records, {unique_symbols} symbols")
            
        except Exception as e:
            self.validation_results["stocks_financials"] = f"ERROR: {e}"
            logger.error(f"Error validating financial data: {e}")
    
    def generate_report(self):
        """Generate comprehensive validation report"""
        logger.info("Generating validation report...")
        
        report = []
        report.append("=" * 60)
        report.append("EGX STOCK AI ORACLE - DATA VALIDATION REPORT")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)
        
        # Validation Results
        report.append("\nVALIDATION RESULTS:")
        report.append("-" * 40)
        for table, result in self.validation_results.items():
            status = "✓" if result == "PASSED" else "⚠" if "WARNING" in result else "✗"
            report.append(f"{status} {table}: {result}")
        
        # Metrics Summary
        report.append("\nMETRICS SUMMARY:")
        report.append("-" * 40)
        for table, metrics in self.metrics.items():
            report.append(f"\n{table.upper()}:")
            for metric, value in metrics.items():
                report.append(f"  {metric}: {value}")
        
        # Recommendations
        report.append("\nRECOMMENDATIONS:")
        report.append("-" * 40)
        
        failed_validations = [table for table, result in self.validation_results.items() 
                            if "ERROR" in result or "FAILED" in result]
        warning_validations = [table for table, result in self.validation_results.items() 
                             if "WARNING" in result]
        
        if failed_validations:
            report.append("CRITICAL ISSUES:")
            for table in failed_validations:
                report.append(f"  - Fix {table}: {self.validation_results[table]}")
        
        if warning_validations:
            report.append("RECOMMENDED ACTIONS:")
            for table in warning_validations:
                report.append(f"  - Review {table}: {self.validation_results[table]}")
        
        if not failed_validations and not warning_validations:
            report.append("✓ All validations passed successfully!")
            report.append("✓ Data quality looks good.")
            report.append("✓ Continue with regular monitoring.")
        
        report.append("\n" + "=" * 60)
        
        report_text = "\n".join(report)
        
        # Save report to file
        report_filename = f"data_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        logger.info(f"Validation report saved to: {report_filename}")
        print(report_text)
        
        return report_text
    
    def run_all_validations(self):
        """Run all validation checks"""
        logger.info("Starting comprehensive data validation...")
        
        validations = [
            self.validate_stocks_master,
            self.validate_historical_data,
            self.validate_realtime_data,
            self.validate_financial_data
        ]
        
        for validation in validations:
            try:
                validation()
            except Exception as e:
                logger.error(f"Error in validation {validation.__name__}: {e}")
        
        return self.generate_report()

def main():
    logger.info("EGX Stock AI Oracle - Data Validation Started")
    
    validator = DataValidator()
    report = validator.run_all_validations()
    
    # Return exit code based on validation results
    failed_count = sum(1 for result in validator.validation_results.values() 
                      if "ERROR" in result or "FAILED" in result)
    
    if failed_count > 0:
        logger.error(f"Validation completed with {failed_count} critical issues")
        sys.exit(1)
    else:
        logger.info("Validation completed successfully")
        sys.exit(0)

if __name__ == "__main__":
    main()
