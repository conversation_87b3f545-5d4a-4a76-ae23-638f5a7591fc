
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Bot, Target, Users, Link, Zap } from 'lucide-react';
import AlgorithmicStrategyBuilder from './AlgorithmicStrategyBuilder';
import OptionsSimulator from './OptionsSimulator';
import SocialTrading from './SocialTrading';
import BrokerIntegration from './BrokerIntegration';

const TradingHub = () => {
  const [activeModule, setActiveModule] = useState('strategies');

  const modules = [
    {
      id: 'strategies',
      label: 'الاستراتيجيات الخوارزمية',
      icon: Bot,
      description: 'بناء واختبار استراتيجيات التداول الآلي',
      component: AlgorithmicStrategyBuilder
    },
    {
      id: 'options',
      label: 'محاكي الخيارات',
      icon: Target,
      description: 'تداول الخيارات بشكل تجريبي',
      component: OptionsSimulator
    },
    {
      id: 'social',
      label: 'التداول الاجتماعي',
      icon: Users,
      description: 'تابع أفضل المتداولين وانسخ صفقاتهم',
      component: SocialTrading
    },
    {
      id: 'brokers',
      label: 'ربط الوسطاء',
      icon: Link,
      description: 'اربط حسابك مع الوسطاء المختلفين',
      component: BrokerIntegration
    }
  ];

  const ActiveComponent = modules.find(m => m.id === activeModule)?.component || AlgorithmicStrategyBuilder;

  return (
    <div className="space-y-6">
      {/* Module Selector */}
      <Card className="border-2 border-indigo-200 bg-gradient-to-br from-indigo-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-indigo-800">
            <Zap className="h-5 w-5" />
            مركز التداول المتقدم - المرحلة الثالثة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {modules.map(module => {
              const IconComponent = module.icon;
              return (
                <Button
                  key={module.id}
                  variant={activeModule === module.id ? "default" : "outline"}
                  className={`h-auto p-4 flex flex-col items-center gap-2 ${
                    activeModule === module.id 
                      ? 'bg-indigo-600 hover:bg-indigo-700' 
                      : 'hover:bg-indigo-50'
                  }`}
                  onClick={() => setActiveModule(module.id)}
                >
                  <IconComponent className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">{module.label}</div>
                    <div className="text-xs opacity-80 mt-1">{module.description}</div>
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Active Module */}
      <div className="animate-fade-in">
        <ActiveComponent />
      </div>
    </div>
  );
};

export default TradingHub;
