#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏗️ إعداد قاعدة البيانات المخصصة - الأسهم المصرية
Database Setup Script - Egyptian Stock Market Custom Database

📅 Created: 2025-06-16
🎯 Purpose: إعداد قاعدة البيانات والجداول والفهارس المحسنة
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import logging
import sys
from pathlib import Path
from typing import Dict, List, Tuple
import time

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_setup.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EGXDatabaseSetup:
    """إعداد قاعدة البيانات للأسهم المصرية"""
    
    def __init__(self, admin_db_config: Dict[str, str], target_db_config: Dict[str, str]):
        """تهيئة الإعداد"""
        self.admin_db_config = admin_db_config  # للاتصال بـ postgres database
        self.target_db_config = target_db_config  # قاعدة البيانات المستهدفة
        self.sql_file_path = Path(__file__).parent.parent / "database" / "create_custom_database.sql"
        
    def create_database_if_not_exists(self) -> bool:
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            logger.info("🏗️ فحص وإنشاء قاعدة البيانات...")
            
            # الاتصال بقاعدة postgres للتحكم الإداري
            conn = psycopg2.connect(**self.admin_db_config)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # فحص وجود قاعدة البيانات
            cursor.execute(
                "SELECT 1 FROM pg_database WHERE datname = %s",
                (self.target_db_config['database'],)
            )
            
            if cursor.fetchone():
                logger.info(f"✅ قاعدة البيانات {self.target_db_config['database']} موجودة بالفعل")
            else:
                logger.info(f"🔨 إنشاء قاعدة البيانات {self.target_db_config['database']}...")
                
                cursor.execute(f"""
                    CREATE DATABASE {self.target_db_config['database']}
                    WITH ENCODING 'UTF8'
                    LC_COLLATE = 'en_US.UTF-8'
                    LC_CTYPE = 'en_US.UTF-8'
                    TEMPLATE template0
                """)
                
                logger.info("✅ تم إنشاء قاعدة البيانات بنجاح")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    def create_users_and_permissions(self) -> bool:
        """إنشاء المستخدمين والصلاحيات"""
        try:
            logger.info("👥 إنشاء المستخدمين والصلاحيات...")
            
            conn = psycopg2.connect(**self.admin_db_config)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            users_config = [
                {
                    'username': 'egx_api_user',
                    'password': 'secure_api_password_2025',
                    'role': 'readonly',
                    'description': 'مستخدم API للقراءة فقط'
                },
                {
                    'username': 'egx_etl_user', 
                    'password': 'secure_etl_password_2025',
                    'role': 'readwrite',
                    'description': 'مستخدم ETL للقراءة والكتابة'
                }
            ]
            
            for user_config in users_config:
                try:
                    # فحص وجود المستخدم
                    cursor.execute(
                        "SELECT 1 FROM pg_user WHERE usename = %s",
                        (user_config['username'],)
                    )
                    
                    if cursor.fetchone():
                        logger.info(f"✅ المستخدم {user_config['username']} موجود بالفعل")
                    else:
                        cursor.execute(f"""
                            CREATE USER {user_config['username']} 
                            WITH PASSWORD '{user_config['password']}'
                        """)
                        logger.info(f"➕ تم إنشاء المستخدم {user_config['username']}")
                        
                except Exception as e:
                    logger.warning(f"⚠️ تحذير في إنشاء المستخدم {user_config['username']}: {e}")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المستخدمين: {e}")
            return False
    
    def execute_sql_file(self) -> bool:
        """تنفيذ ملف SQL لإنشاء الجداول"""
        try:
            logger.info("📋 تنفيذ ملف SQL لإنشاء الجداول...")
            
            if not self.sql_file_path.exists():
                logger.error(f"❌ ملف SQL غير موجود: {self.sql_file_path}")
                return False
            
            # قراءة ملف SQL
            with open(self.sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # الاتصال بقاعدة البيانات المستهدفة
            conn = psycopg2.connect(**self.target_db_config)
            cursor = conn.cursor()
            
            # تقسيم وتنفيذ الاستعلامات
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            successful_statements = 0
            failed_statements = 0
            
            for i, statement in enumerate(sql_statements):
                if not statement or statement.startswith('--') or statement.startswith('/*'):
                    continue
                
                try:
                    cursor.execute(statement)
                    successful_statements += 1
                    
                    # تقرير التقدم كل 10 استعلامات
                    if (i + 1) % 10 == 0:
                        logger.info(f"📊 تم تنفيذ {i + 1}/{len(sql_statements)} استعلام")
                        
                except Exception as e:
                    failed_statements += 1
                    logger.warning(f"⚠️ فشل في تنفيذ الاستعلام {i + 1}: {e}")
                    # المتابعة مع الاستعلامات الأخرى
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"✅ تم تنفيذ {successful_statements} استعلام بنجاح")
            if failed_statements > 0:
                logger.warning(f"⚠️ فشل في {failed_statements} استعلام")
            
            return successful_statements > 0
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ ملف SQL: {e}")
            return False
    
    def verify_database_structure(self) -> bool:
        """التحقق من هيكل قاعدة البيانات"""
        try:
            logger.info("🔍 فحص هيكل قاعدة البيانات...")
            
            conn = psycopg2.connect(**self.target_db_config)
            cursor = conn.cursor()
            
            # قائمة الجداول المتوقعة
            expected_tables = [
                'stocks_master',
                'stocks_historical', 
                'stocks_realtime',
                'stocks_financials',
                'technical_indicators',
                'market_statistics',
                'data_sync_log'
            ]
            
            # فحص وجود الجداول
            existing_tables = []
            missing_tables = []
            
            for table in expected_tables:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, (table,))
                
                if cursor.fetchone()[0]:
                    existing_tables.append(table)
                else:
                    missing_tables.append(table)
            
            # تقرير النتائج
            logger.info(f"✅ الجداول الموجودة ({len(existing_tables)}):")
            for table in existing_tables:
                # عد الأعمدة لكل جدول
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_name = %s
                """, (table,))
                columns_count = cursor.fetchone()[0]
                logger.info(f"  📋 {table}: {columns_count} عمود")
            
            if missing_tables:
                logger.warning(f"⚠️ الجداول المفقودة ({len(missing_tables)}):")
                for table in missing_tables:
                    logger.warning(f"  ❌ {table}")
            
            # فحص الفهارس
            cursor.execute("""
                SELECT schemaname, tablename, indexname 
                FROM pg_indexes 
                WHERE schemaname = 'public'
                ORDER BY tablename, indexname
            """)
            
            indexes = cursor.fetchall()
            logger.info(f"🔍 الفهارس المتاحة: {len(indexes)}")
            
            cursor.close()
            conn.close()
            
            return len(missing_tables) == 0
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص هيكل قاعدة البيانات: {e}")
            return False
    
    def setup_database(self) -> bool:
        """إعداد قاعدة البيانات الكامل"""
        logger.info("🚀 بدء إعداد قاعدة البيانات للأسهم المصرية")
        logger.info("=" * 80)
        
        steps = [
            ("إنشاء قاعدة البيانات", self.create_database_if_not_exists),
            ("إنشاء المستخدمين", self.create_users_and_permissions),
            ("إنشاء الجداول والفهارس", self.execute_sql_file),
            ("فحص الهيكل النهائي", self.verify_database_structure)
        ]
        
        all_successful = True
        
        for step_name, step_function in steps:
            logger.info(f"▶️ {step_name}...")
            start_time = time.time()
            
            try:
                success = step_function()
                duration = time.time() - start_time
                
                if success:
                    logger.info(f"✅ {step_name} اكتمل بنجاح ({duration:.1f}s)")
                else:
                    logger.error(f"❌ فشل في {step_name}")
                    all_successful = False
                    
            except Exception as e:
                logger.error(f"❌ خطأ في {step_name}: {e}")
                all_successful = False
            
            # فترة راحة قصيرة
            time.sleep(1)
        
        logger.info("=" * 80)
        
        if all_successful:
            logger.info("🎉 تم إعداد قاعدة البيانات بنجاح!")
            logger.info("💎 قاعدة البيانات جاهزة لاستقبال كنز الأسهم المصرية")
        else:
            logger.error("⚠️ اكتمل الإعداد مع بعض المشاكل")
        
        return all_successful

def main():
    """الدالة الرئيسية"""
    print("🏗️ إعداد قاعدة البيانات المخصصة - الأسهم المصرية")
    print("=" * 80)
      # إعدادات قاعدة البيانات الإدارية (postgres)
    admin_db_config = {
        'host': 'localhost',
        'database': 'postgres',  # قاعدة البيانات الافتراضية للإدارة
        'user': 'postgres',
        'password': '',  # PostgreSQL بدون كلمة مرور في WSL
        'port': 5432
    }
    
    # إعدادات قاعدة البيانات المستهدفة
    target_db_config = {
        'host': 'localhost',
        'database': 'egx_stock_oracle',
        'user': 'postgres',
        'password': '',  # PostgreSQL بدون كلمة مرور في WSL
        'port': 5432
    }
    
    # إنشاء مدير الإعداد
    setup_manager = EGXDatabaseSetup(admin_db_config, target_db_config)
    
    # تشغيل الإعداد الكامل
    success = setup_manager.setup_database()
    
    if success:
        print("\n🎉 اكتمل إعداد قاعدة البيانات بنجاح!")
        print("🔄 يمكنك الآن تشغيل master_data_importer.py لاستيراد البيانات")
    else:
        print("\n⚠️ اكتمل الإعداد مع بعض المشاكل")
        print("📋 راجع ملف السجل للتفاصيل")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
