#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_active_stocks_only():
    """Test that suspended/delisted stocks are excluded"""
    print("🧪 اختبار استبعاد الأسهم المتوقفة والمشطوبة")
    print("=" * 60)
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}"
    }
    
    # First, let's see the current suspended stocks
    print("🔍 البحث عن الأسهم المتوقفة (حجم تداول = null أو 0)...")
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol,volume,current_price&symbol=not.like.*EGX*&or=(volume.is.null,volume.eq.0)&limit=10",
            headers=headers
        )
        
        if response.status_code == 200:
            suspended_stocks = response.json()
            print(f"   📊 عدد الأسهم المتوقفة: {len(suspended_stocks)}")
            if suspended_stocks:
                print("   🔴 الأسهم المتوقفة:")
                for stock in suspended_stocks[:5]:
                    volume = stock['volume'] if stock['volume'] is not None else 'null'
                    print(f"      {stock['symbol']}: حجم = {volume}")
        else:
            print(f"   ❌ خطأ: {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
    
    print("\n" + "-" * 60)
    
    # Test updated queries that should exclude suspended stocks
    tests = [
        {
            "name": "الأكثر تداولاً (محدث)",
            "query": "stocks_realtime?select=symbol,volume&symbol=not.like.*EGX*&not=volume.is.null&volume=gt.0&order=volume.desc&limit=5"
        },
        {
            "name": "شريط الأسعار (محدث)",
            "query": "stocks_realtime?select=symbol,volume,current_price&symbol=not.like.*EGX*&not=volume.is.null&volume=gt.0&order=volume.desc&limit=5"
        },
        {
            "name": "الفرص الاستثمارية (محدث)",
            "query": "stocks_realtime?select=symbol,change_percent,volume&symbol=not.like.*EGX*&not=volume.is.null&volume=gt.0&or=(change_percent.gte.2,change_percent.lte.-2)&order=volume.desc&limit=5"
        }
    ]
    
    for test in tests:
        print(f"\n🔍 اختبار: {test['name']}")
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/{test['query']}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data:
                    print(f"   ✅ عدد الأسهم النشطة: {len(data)}")
                    
                    # Check if any stock has null or 0 volume
                    problem_stocks = [
                        stock for stock in data 
                        if stock.get('volume') is None or stock.get('volume') == 0
                    ]
                    
                    if problem_stocks:
                        print(f"   ⚠️ أسهم بحجم تداول مشكوك فيه: {len(problem_stocks)}")
                        for stock in problem_stocks:
                            print(f"      {stock['symbol']}: {stock.get('volume')}")
                    else:
                        print("   ✅ جميع الأسهم لها حجم تداول صحيح")
                    
                    # Show sample of active stocks
                    print(f"   📈 عينة من الأسهم النشطة:")
                    for stock in data[:3]:
                        volume = format_volume(stock.get('volume', 0))
                        print(f"      {stock['symbol']}: {volume}")
                else:
                    print("   ⚠️ لا توجد أسهم نشطة")
                    
            else:
                print(f"   ❌ خطأ في الاستعلام: {response.status_code}")
                print(f"      {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✅ اكتمل الاختبار!")
    print("🔄 قم بتحديث الفرونت اند لرؤية الأسهم النشطة فقط")

def format_volume(volume):
    """Format volume for display"""
    if volume is None or volume == 0:
        return "0"
    elif volume >= 1000000:
        return f"{volume/1000000:.1f}M"
    elif volume >= 1000:
        return f"{volume/1000:.0f}K"
    else:
        return str(volume)

def show_active_vs_suspended():
    """Show comparison between active and suspended stocks"""
    print("\n📊 مقارنة بين الأسهم النشطة والمتوقفة:")
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    try:
        # Count total stocks (excluding indices)
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol&symbol=not.like.*EGX*",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        total_stocks = len(response.json()) if response.status_code == 200 else 0
        
        # Count active stocks
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol&symbol=not.like.*EGX*&not=volume.is.null&volume=gt.0",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        active_stocks = len(response.json()) if response.status_code == 200 else 0
        
        suspended_stocks = total_stocks - active_stocks
        
        print(f"   📊 إجمالي الأسهم: {total_stocks}")
        print(f"   ✅ أسهم نشطة: {active_stocks} ({active_stocks/total_stocks*100:.1f}%)")
        print(f"   🔴 أسهم متوقفة: {suspended_stocks} ({suspended_stocks/total_stocks*100:.1f}%)")
        
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")

if __name__ == "__main__":
    show_active_vs_suspended()
    test_active_stocks_only()
