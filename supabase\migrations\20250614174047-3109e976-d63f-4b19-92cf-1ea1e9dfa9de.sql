
DO $$
BEGIN
  -- Add chk_positive_entry_price constraint if it does not exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname = 'chk_positive_entry_price'
  ) THEN
    ALTER TABLE public.paper_trades
      ADD CONSTRAINT chk_positive_entry_price CHECK (entry_price > 0);
  END IF;

  -- Add chk_positive_quantity constraint if it does not exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname = 'chk_positive_quantity'
  ) THEN
    ALTER TABLE public.paper_trades
      ADD CONSTRAINT chk_positive_quantity CHECK (quantity > 0);
  END IF;
END $$;

-- Set appropriate defaults for calculation/aggregation columns
ALTER TABLE public.paper_trading_accounts
  ALTER COLUMN total_profit_loss SET DEFAULT 0,
  ALTER COLUMN total_trades SET DEFAULT 0,
  ALTER COLUMN winning_trades SET DEFAULT 0,
  ALTER COLUMN losing_trades SET DEFAULT 0,
  ALTER COLUMN max_drawdown SET DEFAULT 0,
  ALTER COLUMN sharpe_ratio SET DEFAULT 0,
  ALTER COLUMN win_rate SET DEFAULT 0;

ALTER TABLE public.paper_trades 
  ALTER COLUMN profit_loss SET DEFAULT 0;
