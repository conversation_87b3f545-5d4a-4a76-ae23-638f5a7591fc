import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  BarChart3, 
  Users, 
  Zap,
  Target,
  Brain,
  Filter,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Building,
  DollarSign
} from 'lucide-react';
import { useEnhancedMarketData } from '@/hooks/useEnhancedMarketData';

// Enhanced data interfaces
interface MarketMetrics {
  totalVolume: number;
  totalTurnover: number;
  avgVolume: number;
  topGainer: { symbol: string; change: number; price: number };
  topLoser: { symbol: string; change: number; price: number };
  mostActive: { symbol: string; volume: number; turnover: number };
  marketCap: number;
}

interface SectorData {
  sector: string;
  count: number;
  avgChange: number;
  totalVolume: number;
  topStock: string;
}

interface TechnicalIndicators {
  breakouts: number;
  oversold: number;
  overbought: number;
  bullishCrossover: number;
  bearishCrossover: number;
}

const MarketDashboard = () => {
  const [activeView, setActiveView] = useState('overview');

  // Fetch comprehensive market data
  const { data: marketData, isLoading } = useEnhancedMarketData();

  const formatNumber = (num: number) => {
    if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const formatCurrency = (num: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(num);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!marketData) return null;

  const { summary, performers, sectors, technical } = marketData;

  return (
    <div className="space-y-6">
      {/* Enhanced Market Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">        {/* Total Volume */}
        <Card className="gradient-card bg-gradient-to-br from-blue-600 to-blue-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm font-medium">إجمالي الحجم</p>
                <p className="text-2xl font-bold">{formatNumber(summary.totalVolume)}</p>
                <p className="text-blue-200 text-xs">سهم</p>
              </div>
              <Activity className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        {/* Total Turnover */}
        <Card className="gradient-card bg-gradient-to-br from-green-600 to-green-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">إجمالي القيمة</p>
                <p className="text-2xl font-bold">{formatNumber(summary.totalTurnover)}</p>
                <p className="text-green-200 text-xs">جنيه مصري</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        {/* Top Gainer */}
        <Card className="gradient-card bg-gradient-to-br from-emerald-500 to-emerald-700 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-emerald-100 text-sm font-medium">أكبر رابح</p>
                <p className="text-lg font-bold">{performers.topGainer?.symbol || '--'}</p>
                <div className="flex items-center">
                  <ArrowUpRight className="h-4 w-4 mr-1" />
                  <span className="text-sm">{(performers.topGainer?.change_percent || 0).toFixed(2)}%</span>
                </div>
              </div>
              <TrendingUp className="h-8 w-8 text-emerald-200" />
            </div>
          </CardContent>
        </Card>

        {/* Top Loser */}
        <Card className="gradient-card bg-gradient-to-br from-red-500 to-red-700 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-100 text-sm font-medium">أكبر خاسر</p>
                <p className="text-lg font-bold">{performers.topLoser?.symbol || '--'}</p>
                <div className="flex items-center">
                  <ArrowDownRight className="h-4 w-4 mr-1" />
                  <span className="text-sm">{Math.abs(performers.topLoser?.change_percent || 0).toFixed(2)}%</span>
                </div>
              </div>
              <TrendingDown className="h-8 w-8 text-red-200" />
            </div>
          </CardContent>
        </Card>

        {/* Most Active */}
        <Card className="gradient-card bg-gradient-to-br from-purple-600 to-purple-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">الأكثر نشاطاً</p>
                <p className="text-lg font-bold">{performers.mostActive?.symbol || '--'}</p>
                <p className="text-purple-200 text-xs">{formatNumber(performers.mostActive?.volume || 0)} سهم</p>
              </div>
              <Zap className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        {/* Market Cap */}
        <Card className="gradient-card bg-gradient-to-br from-orange-600 to-orange-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">القيمة السوقية</p>
                <p className="text-2xl font-bold">{formatNumber(summary.totalMarketCap)}</p>
                <p className="text-orange-200 text-xs">جنيه مصري</p>
              </div>
              <Building className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        {/* Technical Breakouts */}
        <Card className="gradient-card bg-gradient-to-br from-indigo-600 to-indigo-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-indigo-100 text-sm font-medium">اختراقات فنية</p>
                <p className="text-2xl font-bold">{technical.breakoutsAboveMA20}</p>
                <p className="text-indigo-200 text-xs">سهم فوق المتوسط</p>
              </div>
              <Target className="h-8 w-8 text-indigo-200" />
            </div>
          </CardContent>
        </Card>

        {/* Total Stocks */}
        <Card className="gradient-card bg-gradient-to-br from-gray-600 to-gray-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-100 text-sm font-medium">إجمالي الأسهم</p>
                <p className="text-2xl font-bold">{summary.totalStocks}</p>
                <p className="text-gray-200 text-xs">شركة مدرجة</p>
              </div>
              <Users className="h-8 w-8 text-gray-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs value={activeView} onValueChange={setActiveView}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="sectors">القطاعات</TabsTrigger>
          <TabsTrigger value="technical">التحليل الفني</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  أداء السوق اليوم
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-green-800 font-medium">الأسهم الصاعدة</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {technical.bullishCrossover}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                  <span className="text-red-800 font-medium">الأسهم الهابطة</span>
                  <Badge variant="secondary" className="bg-red-100 text-red-800">
                    {technical.bearishCrossover}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-800 font-medium">متوسط الحجم</span>
                  <span className="font-bold">{formatNumber(summary.avgVolume)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Brain className="h-5 w-5 mr-2" />
                  إشارات فنية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-blue-800 font-medium">فرص شراء</span>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {technical.oversoldStocks}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                  <span className="text-orange-800 font-medium">تحذير بيع</span>                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                    {technical.overboughtStocks}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                    <span className="font-medium">مناطق التشبع البيعي</span>
                  </div>
                  <Badge className="bg-purple-600">{technical.oversoldStocks}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sectors" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sectors.map((sector, index) => (
              <Card key={sector.sector} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center justify-between">
                    <span>{sector.sector}</span>
                    <Badge variant={sector.avgChange >= 0 ? "default" : "destructive"}>
                      {sector.avgChange.toFixed(2)}%
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">عدد الشركات:</span>
                      <span className="font-medium">{sector.count}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">إجمالي الحجم:</span>
                      <span className="font-medium">{formatNumber(sector.totalVolume)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">أفضل سهم:</span>
                      <span className="font-medium text-blue-600">{sector.topStock}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="technical" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>المؤشرات الفنية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <span className="font-medium">تقاطع صاعد (MA5 &gt; MA20)</span>
                    </div>
                    <Badge className="bg-green-600">{technical.bullishCrossover}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                      <span className="font-medium">تقاطع هابط (MA5 &lt; MA20)</span>
                    </div>
                    <Badge className="bg-red-600">{technical.bearishCrossover}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                      <span className="font-medium">اختراق المقاومة</span>
                    </div>
                    <Badge className="bg-blue-600">{technical.breakoutsAboveMA20}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                      <span className="font-medium">مناطق التشبع الشرائي</span>
                    </div>
                    <Badge className="bg-orange-600">{technical.overbought}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                      <span className="font-medium">مناطق التشبع البيعي</span>
                    </div>
                    <Badge className="bg-purple-600">{technical.oversoldStocks}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>ملخص تنفيذي للسوق</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg">
                    <h4 className="font-bold mb-2">اتجاه السوق العام</h4>
                    <p className="text-sm opacity-90">
                      {technical.bullishCrossover > technical.bearishCrossover ? 
                        'السوق يظهر اتجاهاً إيجابياً مع تفوق الإشارات الصاعدة' :
                        'السوق يظهر ضعفاً مع تفوق الإشارات الهابطة'
                      }
                    </p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg">
                    <h4 className="font-bold mb-2">فرص الاستثمار</h4>
                    <p className="text-sm opacity-90">
                      يوجد {technical.oversold} سهم في منطقة التشبع البيعي قد يكون مناسباً للشراء
                    </p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-r from-orange-600 to-orange-700 text-white rounded-lg">
                    <h4 className="font-bold mb-2">تحذيرات المخاطر</h4>
                    <p className="text-sm opacity-90">
                      {technical.overbought} سهم في منطقة التشبع الشرائي - توخي الحذر
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MarketDashboard;
