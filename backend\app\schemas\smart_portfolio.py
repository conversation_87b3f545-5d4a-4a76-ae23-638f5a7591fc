"""
Smart Portfolio Pydantic Schemas - مخططات بيانات المحفظة الذكية
تحتوي على جميع المخططات للواجهة البرمجية والتحقق من البيانات
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Optional, Any
from datetime import datetime
from enum import Enum

# Enums للحالات المختلفة
class PositionStatus(str, Enum):
    ACTIVE = "ACTIVE"
    CLOSED = "CLOSED"
    PARTIAL_CLOSED = "PARTIAL_CLOSED"

class TransactionType(str, Enum):
    BUY = "BUY"
    SELL_PARTIAL = "SELL_PARTIAL"
    SELL_FULL = "SELL_FULL"
    STOP_LOSS = "STOP_LOSS"
    TRAILING_STOP = "TRAILING_STOP"

class ProcessingResult(str, Enum):
    EXECUTED = "EXECUTED"
    REJECTED = "REJECTED"
    ERROR = "ERROR"

class SubscriptionType(str, Enum):
    VIP = "VIP"
    PREMIUM = "PREMIUM"

# Schemas أساسية
class ProfitTarget(BaseModel):
    """هدف ربح"""
    target_number: int = Field(..., ge=1, le=5)
    price: float = Field(..., gt=0)
    sell_percentage: float = Field(..., ge=0.1, le=1.0)
    achieved: bool = False
    achieved_at: Optional[datetime] = None

class PositionSizing(BaseModel):
    """تفاصيل حساب حجم المركز"""
    shares_count: int = Field(..., ge=1)
    position_value: float = Field(..., gt=0)
    entry_price: float = Field(..., gt=0)
    stop_loss: float = Field(..., gt=0)
    kelly_fraction: float = Field(..., ge=0.01, le=1.0)
    final_multiplier: float = Field(..., ge=0.1, le=2.0)
    risk_amount: float = Field(..., ge=0)
    risk_percentage: float = Field(..., ge=0, le=10)
    
    adjustments: Dict[str, float] = Field(default_factory=dict)

class SignalQuality(BaseModel):
    """جودة الإشارة"""
    confidence_score: float = Field(..., ge=0, le=1)
    expected_return: float = Field(..., ge=0, le=50)
    expected_risk: float = Field(..., ge=0, le=20)
    risk_reward_ratio: float = Field(..., ge=0)
    success_probability: float = Field(..., ge=0, le=1)
    factors: Dict[str, float] = Field(default_factory=dict)

class StockAnalysis(BaseModel):
    """تحليل السهم"""
    technical: Dict[str, Any] = Field(default_factory=dict)
    trend: Dict[str, Any] = Field(default_factory=dict)
    liquidity: Dict[str, Any] = Field(default_factory=dict)
    historical_success: Dict[str, Any] = Field(default_factory=dict)
    market_sentiment: Dict[str, Any] = Field(default_factory=dict)

# Schemas للطلبات (Requests)
class SignalProcessingRequest(BaseModel):
    """طلب معالجة إشارة"""
    stock_code: str = Field(..., min_length=2, max_length=10)
    stock_name_ar: Optional[str] = None
    signal_type: str = Field(..., pattern="^(buy|sell|tp|sl)$")
    buy_price: Optional[float] = Field(None, gt=0)
    target_1: Optional[float] = Field(None, gt=0)
    target_2: Optional[float] = Field(None, gt=0)
    target_3: Optional[float] = Field(None, gt=0)
    stop_loss: Optional[float] = Field(None, gt=0)
    signal_id: Optional[str] = None
    
    @validator('target_1', 'target_2', 'target_3')
    def targets_greater_than_buy_price(cls, v, values):
        if v is not None and 'buy_price' in values and values['buy_price']:
            if v <= values['buy_price']:
                raise ValueError('Target price must be greater than buy price')
        return v
    
    @validator('stop_loss')
    def stop_loss_less_than_buy_price(cls, v, values):
        if v is not None and 'buy_price' in values and values['buy_price']:
            if v >= values['buy_price']:
                raise ValueError('Stop loss must be less than buy price')
        return v

class CopyTradingRequest(BaseModel):
    """طلب تفعيل نسخ التداول"""
    user_id: int = Field(..., gt=0)
    copy_percentage: float = Field(1.0, ge=0.1, le=2.0)
    max_position_size: Optional[float] = Field(None, gt=0)
    max_positions_count: int = Field(10, ge=1, le=20)
    min_signal_confidence: float = Field(0.7, ge=0.5, le=1.0)
    max_risk_per_trade: float = Field(0.02, ge=0.005, le=0.05)
    excluded_stocks: List[str] = Field(default_factory=list)
    included_sectors: List[str] = Field(default_factory=list)
    available_balance: float = Field(..., gt=0)
    notifications_enabled: bool = True
    telegram_chat_id: Optional[str] = None

class PositionManagementRequest(BaseModel):
    """طلب إدارة المركز"""
    position_id: str = Field(..., min_length=5)
    action: str = Field(..., pattern="^(close_full|close_partial|update_stop_loss|update_targets)$")
    shares_to_close: Optional[int] = Field(None, gt=0)
    new_stop_loss: Optional[float] = Field(None, gt=0)
    new_targets: Optional[List[ProfitTarget]] = None
    reason: Optional[str] = None

# Schemas للاستجابات (Responses)
class SmartPortfolioPosition(BaseModel):
    """مركز في المحفظة الذكية"""
    position_id: str
    stock_code: str
    stock_name_ar: Optional[str]
    
    # معلومات الدخول
    entry_price: float
    entry_date: datetime
    initial_shares: int
    remaining_shares: int
    
    # إدارة المخاطر
    stop_loss_price: float
    trailing_stop_distance: Optional[float]
    highest_price_reached: float
    protected_profit: float
    
    # الأهداف والأداء
    profit_targets: List[ProfitTarget]
    targets_achieved: List[int]
    unrealized_pnl: float
    realized_pnl: float
    total_fees: float
    
    # معلومات الإشارة
    signal_id: Optional[str]
    signal_confidence: Optional[float]
    
    # حالة المركز
    status: PositionStatus
    close_reason: Optional[str]
    
    # التوقيتات
    created_at: datetime
    updated_at: datetime
    closed_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class SmartPortfolioTransaction(BaseModel):
    """معاملة المحفظة الذكية"""
    transaction_id: str
    position_id: str
    transaction_type: TransactionType
    shares: int
    price: float
    total_value: float
    fees: float
    trigger_reason: Optional[str]
    target_number: Optional[int]
    pnl: float
    pnl_percentage: float
    timestamp: datetime
    
    class Config:
        from_attributes = True

class PortfolioPerformanceMetrics(BaseModel):
    """مقاييس أداء المحفظة"""
    # قيم المحفظة
    total_portfolio_value: float
    cash_balance: float
    invested_value: float
    unrealized_pnl: float
    realized_pnl: float
    
    # مقاييس العائد
    daily_return: float
    total_return: float
    total_return_percentage: float
    annualized_return: float
    
    # مقاييس المخاطر
    sharpe_ratio: float
    max_drawdown: float
    current_drawdown: float
    volatility: float
    var_95: float
    expected_shortfall: float
    
    # إحصائيات التداول
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    profit_factor: float
    average_win: float
    average_loss: float
    
    # إحصائيات الإشارات
    signals_processed_today: int
    signals_executed_today: int
    signals_rejected_today: int
    signal_acceptance_rate: float
    
    # توزيع المحفظة
    active_positions_count: int
    sector_distribution: Dict[str, float]
    
    class Config:
        from_attributes = True

class SignalProcessingResult(BaseModel):
    """نتيجة معالجة الإشارة"""
    action: ProcessingResult
    reason: str
    
    # تفاصيل المعالجة (إذا تم التنفيذ)
    position_id: Optional[str] = None
    stock_code: Optional[str] = None
    entry_price: Optional[float] = None
    shares: Optional[int] = None
    position_value: Optional[float] = None
    stop_loss: Optional[float] = None
    profit_targets: Optional[List[ProfitTarget]] = None
    
    # تحليل الإشارة
    signal_quality: Optional[SignalQuality] = None
    stock_analysis: Optional[StockAnalysis] = None
    position_sizing: Optional[PositionSizing] = None
    
    # معايير القبول/الرفض
    criteria_checks: Optional[Dict[str, Any]] = None
    
    # التوقيت
    timestamp: datetime = Field(default_factory=datetime.now)

class CopyTradingSubscription(BaseModel):
    """اشتراك نسخ التداول"""
    subscription_id: str
    user_id: int
    is_active: bool
    subscription_type: SubscriptionType
    
    # إعدادات النسخ
    copy_percentage: float
    max_position_size: Optional[float]
    max_positions_count: int
    min_signal_confidence: float
    max_risk_per_trade: float
    excluded_stocks: List[str]
    included_sectors: List[str]
    
    # الأرصدة
    available_balance: float
    reserved_balance: float
    
    # الإحصائيات
    total_copied_trades: int
    successful_copies: int
    total_pnl: float
    
    # التوقيتات
    created_at: datetime
    updated_at: datetime
    last_activity: Optional[datetime]
    
    class Config:
        from_attributes = True

class CopiedTrade(BaseModel):
    """صفقة منسوخة"""
    copy_trade_id: str
    subscription_id: str
    original_position_id: str
    
    # معلومات السهم
    stock_code: str
    stock_name_ar: Optional[str]
    
    # تفاصيل النسخ
    original_shares: int
    copied_shares: int
    copy_ratio: float
    
    # معلومات الدخول
    entry_price: float
    entry_date: datetime
    total_investment: float
    fees_paid: float
    
    # الحالة والأداء
    status: PositionStatus
    remaining_shares: Optional[int]
    unrealized_pnl: float
    realized_pnl: float
    total_pnl: float
    
    # معلومات الإغلاق
    close_reason: Optional[str]
    close_date: Optional[datetime]
    
    class Config:
        from_attributes = True

class SmartPortfolioDashboard(BaseModel):
    """لوحة تحكم المحفظة الذكية"""
    # ملخص المحفظة
    portfolio_summary: Dict[str, Any]
    
    # المراكز النشطة
    active_positions: List[SmartPortfolioPosition]
    
    # مقاييس الأداء
    performance_metrics: PortfolioPerformanceMetrics
    
    # معالجة الإشارات الأخيرة
    recent_signal_processing: List[Dict[str, Any]]
    
    # إحصائيات التداول
    trading_statistics: Dict[str, Any]
    
    # مقارنة بالمؤشر
    benchmark_comparison: Dict[str, Any]
    
    # اشتراكات نسخ التداول
    copy_trading_stats: Dict[str, Any]
    
    # تحديث آخر
    last_updated: datetime = Field(default_factory=datetime.now)

class PerformanceComparison(BaseModel):
    """مقارنة الأداء"""
    timeframe: str  # 1W, 1M, 3M, 6M, 1Y
    
    # أداء المحفظة الذكية
    smart_portfolio_return: float
    smart_portfolio_volatility: float
    smart_portfolio_sharpe: float
    
    # أداء المؤشر
    benchmark_return: float
    benchmark_volatility: float
    benchmark_sharpe: float
    
    # المقارنة
    alpha: float  # العائد الإضافي
    beta: float   # الحساسية للسوق
    correlation: float
    
    # بيانات الرسم البياني
    performance_history: List[Dict[str, Any]]

class RiskMetrics(BaseModel):
    """مقاييس المخاطر المتقدمة"""
    # Value at Risk
    var_1d_95: float  # VaR يوم واحد 95%
    var_1d_99: float  # VaR يوم واحد 99%
    var_1w_95: float  # VaR أسبوع 95%
    
    # Expected Shortfall
    es_1d_95: float
    es_1d_99: float
    
    # مقاييس أخرى
    maximum_drawdown: float
    current_drawdown: float
    volatility_annualized: float
    downside_deviation: float
    sortino_ratio: float
    
    # توزيع المخاطر
    concentration_risk: float
    sector_risk: Dict[str, float]
    correlation_risk: float

class PortfolioOptimizationSuggestions(BaseModel):
    """اقتراحات تحسين المحفظة"""
    # اقتراحات عامة
    optimization_score: float  # نقاط من 100
    suggestions: List[str]
    
    # توصيات إعادة التوازن
    rebalancing_needed: bool
    suggested_changes: List[Dict[str, Any]]
    
    # إدارة المخاطر
    risk_adjustments: List[str]
    position_size_adjustments: Dict[str, float]
    
    # اقتراحات الإعدادات
    parameter_optimizations: Dict[str, Any]

# Response Models للواجهة البرمجية
class APIResponse(BaseModel):
    """استجابة عامة للواجهة البرمجية"""
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class PaginatedResponse(BaseModel):
    """استجابة مع تقسيم الصفحات"""
    items: List[Any]
    total: int
    page: int
    per_page: int
    pages: int
    has_next: bool
    has_prev: bool

# Validation Helpers
def validate_stock_code(stock_code: str) -> str:
    """التحقق من رمز السهم"""
    if not stock_code or len(stock_code) < 2:
        raise ValueError("Stock code must be at least 2 characters")
    return stock_code.upper().strip()

def validate_price(price: float, field_name: str) -> float:
    """التحقق من السعر"""
    if price <= 0:
        raise ValueError(f"{field_name} must be greater than 0")
    if price > 1000000:  # حد أقصى مليون جنيه للسهم
        raise ValueError(f"{field_name} seems too high")
    return round(price, 2)

def validate_percentage(percentage: float, field_name: str) -> float:
    """التحقق من النسبة المئوية"""
    if percentage < 0 or percentage > 100:
        raise ValueError(f"{field_name} must be between 0 and 100")
    return round(percentage, 2)
