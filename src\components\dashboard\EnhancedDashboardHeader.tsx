import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  <PERSON>, 
  Moon, 
  Sparkles, 
  Zap, 
  Menu, 
  X,
  BarChart3,
  Activity,
  TrendingUp,
  Filter,
  Brain,
  Settings,
  Smartphone,
  Newspaper,
  Calendar,
  BookOpen,
  Bell,
  Target,
  Briefcase,
  ChevronDown,
  Search,
  User
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  category?: 'main' | 'analysis' | 'tools' | 'features';
}

interface EnhancedDashboardHeaderProps {
  title: string;
  description: string;
  isDarkMode: boolean;
  toggleTheme: () => void;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
}

const EnhancedDashboardHeader = ({ 
  title, 
  description, 
  isDarkMode, 
  toggleTheme,
  activeTab,
  setActiveTab,
  mobileMenuOpen,
  setMobileMenuOpen
}: EnhancedDashboardHeaderProps) => {
  const [searchQuery, setSearchQuery] = useState('');

  // تقسيم العناصر حسب الفئات
  const navigationItems: NavigationItem[] = [
    // الرئيسية
    { id: 'overview', label: 'ملخص السوق والمؤشرات العامة', icon: BarChart3, description: 'نظرة شاملة على المؤشرات وأهم الأسهم النشطة', category: 'main' },
    { id: 'live-trading', label: 'الشاشة اللحظية', icon: Activity, description: 'رصد مباشر لجميع أسهم البورصة مع تحديثات لحظية', category: 'main' },
    
    // التحليل
    { id: 'advanced-market', label: 'التحليل المتقدم للسوق', icon: TrendingUp, description: 'أدوات احترافية للتحليل المتعمق وإدارة المخاطر', category: 'analysis' },
    { id: 'comprehensive-analysis', label: 'التحليل الشامل للأسهم', icon: Brain, description: 'تحليل متقدم يشمل جميع مدارس التحليل الفني', category: 'analysis' },
    { id: 'ai-insights', label: 'توصيات الذكاء الاصطناعي', icon: Sparkles, description: 'تحليلات وتوصيات مدعومة بالذكاء الاصطناعي', category: 'analysis' },
    
    // الأدوات
    { id: 'portfolio', label: 'محفظتي', icon: Briefcase, description: 'إدارة وتتبع محفظتك الاستثمارية', category: 'tools' },
    { id: 'screener', label: 'مرشح الأسهم', icon: Filter, description: 'البحث والتصفية المتقدمة للأسهم', category: 'tools' },
    { id: 'alerts', label: 'التنبيهات الذكية', icon: Bell, description: 'مركز شامل لإدارة التنبيهات والإشعارات الذكية', category: 'tools' },
    { id: 'paper-trading', label: 'التداول التجريبي', icon: Target, description: 'محاكي التداول للتدريب', category: 'tools' },
    
    // الميزات
    { id: 'news', label: 'الأخبار', icon: Newspaper, description: 'آخر الأخبار المالية والتحليلات', category: 'features' },
    { id: 'calendar', label: 'التقويم المالي', icon: Calendar, description: 'الأحداث المهمة والمواعيد القادمة', category: 'features' },
    { id: 'learning', label: 'التعلم', icon: BookOpen, description: 'دروس ونصائح تعليمية', category: 'features' },
    { id: 'mobile', label: 'الميزات المحمولة', icon: Smartphone, description: 'إعدادات التطبيق المحمول', category: 'features' },
    { id: 'settings', label: 'الإعدادات', icon: Settings, description: 'تخصيص التفضيلات والحساب', category: 'features' }
  ];

  const getItemsByCategory = (category: string) => {
    return navigationItems.filter(item => item.category === category);
  };

  const mainItems = getItemsByCategory('main');
  const analysisItems = getItemsByCategory('analysis');
  const toolsItems = getItemsByCategory('tools');
  const featuresItems = getItemsByCategory('features');

  const getCurrentItem = () => {
    return navigationItems.find(item => item.id === activeTab);
  };

  return (
    <div className="bg-gradient-to-r from-white via-gray-50 to-white border-b-2 border-egx-gold-200 shadow-lg">
      {/* الهيدر الرئيسي */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          {/* العنوان والوصف */}
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-2 h-8 bg-gradient-to-b from-egx-gold-500 to-egx-gold-600 rounded-full"></div>
              <h1 className="text-3xl lg:text-4xl font-bold text-gradient-primary arabic" dir="rtl">
                {title}
              </h1>
              <Sparkles className="h-6 w-6 text-egx-gold-400 animate-pulse" />
            </div>
            <p className="text-gray-600 arabic text-lg leading-relaxed mr-5" dir="rtl">
              {description}
            </p>
          </div>

          {/* أدوات الهيدر */}
          <div className="flex items-center gap-3">
            {/* شريط البحث السريع */}
            <div className="hidden md:block relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="البحث السريع..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10 pl-4 py-2 border-2 border-gray-200 rounded-lg focus:border-egx-gold-300 focus:outline-none w-64"
                dir="rtl"
              />
            </div>

            {/* تبديل النمط */}
            <Button
              variant="outline"
              size="sm"
              onClick={toggleTheme}
              className="glass-effect border-2 hover:shadow-lg relative overflow-hidden"
            >
              <div className="flex items-center gap-2">
                {isDarkMode ? 
                  <Sun className="h-4 w-4 animate-pulse" /> : 
                  <Moon className="h-4 w-4 animate-pulse" />
                }
              </div>
            </Button>

            {/* حالة التحديث */}
            <Badge className="bg-egx-gold-500 text-egx-gold-900 hover:bg-egx-gold-400 px-4 py-2 shadow-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>متصل</span>
              </div>
            </Badge>

            {/* قائمة المستخدم */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="glass-effect">
                  <User className="h-4 w-4" />
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>حسابي</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>الملف الشخصي</DropdownMenuItem>
                <DropdownMenuItem>الاشتراك</DropdownMenuItem>
                <DropdownMenuItem>الدعم</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>تسجيل الخروج</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* شريط التنقل الرئيسي */}
      <div className="bg-white border-t border-gray-200">
        <div className="container mx-auto px-4">
          {/* شريط التنقل للشاشات الكبيرة */}
          <div className="hidden lg:flex items-center justify-between py-3">
            {/* القوائم الرئيسية */}
            <div className="flex items-center gap-1">
              {/* الرئيسية */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant={mainItems.some(item => item.id === activeTab) ? "default" : "ghost"}
                    className="flex items-center gap-2 px-4 py-2"
                  >
                    <BarChart3 className="h-4 w-4" />
                    الرئيسية
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-80">
                  <DropdownMenuLabel>الصفحات الرئيسية</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {mainItems.map((item) => (
                    <DropdownMenuItem 
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`cursor-pointer p-3 ${activeTab === item.id ? 'bg-egx-gold-50' : ''}`}
                    >
                      <div className="flex items-start gap-3">
                        <item.icon className="h-5 w-5 text-egx-gold-600 mt-0.5" />
                        <div>
                          <div className="font-medium arabic">{item.label}</div>
                          <div className="text-sm text-gray-500 arabic">{item.description}</div>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* التحليل */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant={analysisItems.some(item => item.id === activeTab) ? "default" : "ghost"}
                    className="flex items-center gap-2 px-4 py-2"
                  >
                    <Brain className="h-4 w-4" />
                    التحليل
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-80">
                  <DropdownMenuLabel>أدوات التحليل</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {analysisItems.map((item) => (
                    <DropdownMenuItem 
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`cursor-pointer p-3 ${activeTab === item.id ? 'bg-egx-gold-50' : ''}`}
                    >
                      <div className="flex items-start gap-3">
                        <item.icon className="h-5 w-5 text-egx-gold-600 mt-0.5" />
                        <div>
                          <div className="font-medium arabic">{item.label}</div>
                          <div className="text-sm text-gray-500 arabic">{item.description}</div>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* الأدوات */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant={toolsItems.some(item => item.id === activeTab) ? "default" : "ghost"}
                    className="flex items-center gap-2 px-4 py-2"
                  >
                    <Target className="h-4 w-4" />
                    الأدوات
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-80">
                  <DropdownMenuLabel>أدوات التداول</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {toolsItems.map((item) => (
                    <DropdownMenuItem 
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`cursor-pointer p-3 ${activeTab === item.id ? 'bg-egx-gold-50' : ''}`}
                    >
                      <div className="flex items-start gap-3">
                        <item.icon className="h-5 w-5 text-egx-gold-600 mt-0.5" />
                        <div>
                          <div className="font-medium arabic">{item.label}</div>
                          <div className="text-sm text-gray-500 arabic">{item.description}</div>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* الميزات */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant={featuresItems.some(item => item.id === activeTab) ? "default" : "ghost"}
                    className="flex items-center gap-2 px-4 py-2"
                  >
                    <Sparkles className="h-4 w-4" />
                    الميزات
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-80">
                  <DropdownMenuLabel>الميزات الإضافية</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {featuresItems.map((item) => (
                    <DropdownMenuItem 
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`cursor-pointer p-3 ${activeTab === item.id ? 'bg-egx-gold-50' : ''}`}
                    >
                      <div className="flex items-start gap-3">
                        <item.icon className="h-5 w-5 text-egx-gold-600 mt-0.5" />
                        <div>
                          <div className="font-medium arabic">{item.label}</div>
                          <div className="text-sm text-gray-500 arabic">{item.description}</div>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* مؤشر الصفحة الحالية */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>الصفحة الحالية:</span>
              <Badge variant="secondary" className="arabic">
                {getCurrentItem()?.label || 'غير محدد'}
              </Badge>
            </div>
          </div>

          {/* شريط التنقل للموبايل */}
          <div className="lg:hidden py-3">
            <Button
              variant="outline"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="w-full justify-between h-12 text-lg font-bold arabic border-2 border-egx-gold-300 hover:bg-egx-gold-100"
            >
              <div className="flex items-center gap-3">
                <Menu className="h-5 w-5 text-egx-gold-600" />
                <span>القائمة الرئيسية</span>
              </div>
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
            </Button>

            {/* القائمة المنسدلة للموبايل */}
            {mobileMenuOpen && (
              <Card className="mt-3 shadow-lg border-2 border-egx-gold-200">
                <CardContent className="p-0">
                  <div className="space-y-1">
                    {navigationItems.map((item) => (
                      <button
                        key={item.id}
                        onClick={() => {
                          setActiveTab(item.id);
                          setMobileMenuOpen(false);
                        }}
                        className={`w-full p-4 text-right hover:bg-gray-50 transition-colors ${
                          activeTab === item.id ? 'bg-egx-gold-50 border-r-4 border-egx-gold-500' : ''
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <item.icon className="h-5 w-5 text-egx-gold-600" />
                          <div className="text-right">
                            <div className="font-medium arabic">{item.label}</div>
                            <div className="text-sm text-gray-500 arabic">{item.description}</div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedDashboardHeader;
