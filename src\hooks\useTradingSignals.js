// Trading Signals API Service
import { useState, useEffect, useCallback } from 'react';

const API_BASE_URL = 'http://localhost:8003/api/v1';

// API service for trading signals
export class TradingSignalsAPI {
  static async getRecentSignals(limit = 20, signalType = null, stockCode = null) {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (signalType) params.append('signal_type', signalType);
    if (stockCode) params.append('stock_code', stockCode);
    
    const response = await fetch(`${API_BASE_URL}/webhook/signals/recent?${params}`);
    if (!response.ok) throw new Error('Failed to fetch recent signals');
    return response.json();
  }
  
  static async getLiveRecommendations(status = 'active', limit = 50) {
    const params = new URLSearchParams({ status, limit: limit.toString() });
    
    const response = await fetch(`${API_BASE_URL}/webhook/recommendations/live?${params}`);
    if (!response.ok) throw new Error('Failed to fetch live recommendations');
    return response.json();
  }
  
  static async getSignalPerformance(days = 30) {
    const response = await fetch(`${API_BASE_URL}/webhook/signals/performance?days=${days}`);
    if (!response.ok) throw new Error('Failed to fetch signal performance');
    return response.json();
  }
  
  static async testSignal(signalData) {
    const response = await fetch(`${API_BASE_URL}/webhook/test-signal`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(signalData)
    });
    if (!response.ok) throw new Error('Failed to test signal');
    return response.json();
  }
}

// Custom hook for trading signals
export const useTradingSignals = () => {
  const [signals, setSignals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const fetchSignals = useCallback(async (filters = {}) => {
    try {
      setLoading(true);
      const data = await TradingSignalsAPI.getRecentSignals(
        filters.limit || 20,
        filters.signalType,
        filters.stockCode
      );
      setSignals(data.signals || []);
      setError(null);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching trading signals:', err);
    } finally {
      setLoading(false);
    }
  }, []);
  
  useEffect(() => {
    fetchSignals();
  }, [fetchSignals]);
  
  return { signals, loading, error, refetch: fetchSignals };
};

// Custom hook for live recommendations
export const useLiveRecommendations = () => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const fetchRecommendations = useCallback(async (status = 'active') => {
    try {
      setLoading(true);
      const data = await TradingSignalsAPI.getLiveRecommendations(status);
      setRecommendations(data.recommendations || []);
      setError(null);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching live recommendations:', err);
    } finally {
      setLoading(false);
    }
  }, []);
  
  useEffect(() => {
    fetchRecommendations();
  }, [fetchRecommendations]);
  
  return { recommendations, loading, error, refetch: fetchRecommendations };
};

// Custom hook for signal performance
export const useSignalPerformance = (days = 30) => {
  const [performance, setPerformance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchPerformance = async () => {
      try {
        setLoading(true);
        const data = await TradingSignalsAPI.getSignalPerformance(days);
        setPerformance(data.metrics || null);
        setError(null);
      } catch (err) {
        setError(err.message);
        console.error('Error fetching signal performance:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPerformance();
  }, [days]);
  
  return { performance, loading, error };
};

// Signal type mappings and utilities
export const SIGNAL_TYPES = {
  buy: { 
    label: 'شراء', 
    labelEn: 'Buy',
    color: 'green', 
    icon: '🟢',
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    borderColor: 'border-green-300'
  },
  sell: { 
    label: 'بيع', 
    labelEn: 'Sell',
    color: 'red', 
    icon: '🔴',
    bgColor: 'bg-red-100',
    textColor: 'text-red-800',
    borderColor: 'border-red-300'
  },
  tp1done: { 
    label: 'تحقق الهدف الأول', 
    labelEn: 'TP1 Hit',
    color: 'blue', 
    icon: '🎯',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-300'
  },
  tp2done: { 
    label: 'تحقق الهدف الثاني', 
    labelEn: 'TP2 Hit',
    color: 'blue', 
    icon: '🎯',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-300'
  },
  tp3done: { 
    label: 'تحقق الهدف الثالث', 
    labelEn: 'TP3 Hit',
    color: 'blue', 
    icon: '🎯',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-300'
  },
  tsl: { 
    label: 'وقف خسارة متحرك', 
    labelEn: 'Trailing Stop',
    color: 'orange', 
    icon: '⚠️',
    bgColor: 'bg-orange-100',
    textColor: 'text-orange-800',
    borderColor: 'border-orange-300'
  }
};

// Utility functions
export const formatSignalPrice = (price) => {
  if (!price) return 'غير محدد';
  return `${parseFloat(price).toFixed(2)} جنيه`;
};

export const formatSignalTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleString('ar-EG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const calculatePnL = (entryPrice, currentPrice, isLong = true) => {
  if (!entryPrice || !currentPrice) return null;
  
  const pnl = isLong ? 
    ((currentPrice - entryPrice) / entryPrice) * 100 :
    ((entryPrice - currentPrice) / entryPrice) * 100;
    
  return pnl;
};

export const formatPnL = (pnl) => {
  if (pnl === null || pnl === undefined) return 'غير محدد';
  
  const sign = pnl >= 0 ? '+' : '';
  const color = pnl >= 0 ? 'text-green-600' : 'text-red-600';
  
  return {
    text: `${sign}${pnl.toFixed(2)}%`,
    color
  };
};

export default TradingSignalsAPI;
