
import pandas as pd
from supabase import create_client, Client
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
import sys
import argparse

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('realtime_data_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Parse command line arguments
parser = argparse.ArgumentParser(description='Load realtime stock data')
parser.add_argument('--file', help='Path to Excel file to process')
args = parser.parse_args()

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
EXCEL_PATH = args.file if args.file else os.getenv("REALTIME_EXCEL_PATH", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def calculate_change_metrics(current_price, previous_close):
    """Calculate change amount and percentage"""
    try:
        if previous_close and previous_close > 0:
            change_amount = current_price - previous_close
            change_percent = (change_amount / previous_close) * 100
            return change_amount, change_percent
        return 0, 0
    except:
        return 0, 0

def process_realtime_data(excel_path=None):
    """Process and sync real-time data from Excel file"""
    try:
        file_path = excel_path or EXCEL_PATH
        if not os.path.exists(file_path):
            logger.error(f"Excel file not found: {file_path}")
            return
        
        logger.info(f"Reading Excel file: {file_path}")
        
        # Try different sheet names and handle Arabic column names
        try:
            df = pd.read_excel(file_path, sheet_name=0)  # First sheet
        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            return
        
        if df.empty:
            logger.warning("Excel file is empty")
            return
        
        logger.info(f"Loaded {len(df)} rows from Excel file")
        
        # Print column names for debugging
        logger.info(f"Available columns: {list(df.columns)}")
        
        processed_count = 0
        batch_payload = []
        
        for index, row in df.iterrows():
            try:
                # Handle different possible column names (Arabic and English)
                symbol_cols = ["الرمز", "Symbol", "symbol", "رمز", "الكود", "Code"]
                symbol = None
                for col in symbol_cols:
                    if col in row.index and pd.notna(row[col]):
                        symbol = str(row[col]).strip()
                        break
                
                if not symbol:
                    logger.warning(f"No symbol found in row {index}")
                    continue
                
                # Get price data with fallback column names
                current_price = None
                for col in ["الاغلاق", "Close", "close", "آخر سعر", "Last Price", "Current Price"]:
                    if col in row.index and pd.notna(row[col]):
                        current_price = float(row[col])
                        break
                
                if current_price is None:
                    logger.warning(f"No current price found for {symbol}")
                    continue
                
                # Get other price data
                open_price = None
                for col in ["إفتتاح", "Open", "open", "افتتاح"]:
                    if col in row.index and pd.notna(row[col]):
                        open_price = float(row[col])
                        break
                
                high_price = None
                for col in ["أعلى", "High", "high", "اعلى"]:
                    if col in row.index and pd.notna(row[col]):
                        high_price = float(row[col])
                        break
                
                low_price = None
                for col in ["أدنى", "Low", "low", "ادنى"]:
                    if col in row.index and pd.notna(row[col]):
                        low_price = float(row[col])
                        break
                
                # Get volume
                volume = None
                for col in ["الحجم", "Volume", "volume", "حجم", "Quantity", "الكمية"]:
                    if col in row.index and pd.notna(row[col]):
                        try:
                            volume = int(float(row[col]))
                        except:
                            volume = 0
                        break
                
                # Get change percent directly from data (if available)
                change_percent = None
                for col in ["التغير %", "Change %", "change_percent", "التغيير %", "نسبة التغيير"]:
                    if col in row.index and pd.notna(row[col]):
                        try:
                            change_percent = float(row[col])
                            break
                        except:
                            continue
                
                # Get previous close (if available)
                previous_close = None
                for col in ["الاغلاق السابق", "Previous Close", "prev_close"]:
                    if col in row.index and pd.notna(row[col]):
                        previous_close = float(row[col])
                        break
                
                # If no previous close, calculate it from current price and change percent
                if previous_close is None and change_percent is not None:
                    # Calculate previous close from current price and change percent
                    previous_close = current_price / (1 + (change_percent / 100))
                elif previous_close is None:
                    previous_close = current_price
                
                # Calculate change metrics if not already available
                if change_percent is None:
                    change_amount, change_percent = calculate_change_metrics(current_price, previous_close)
                else:
                    # Calculate change amount from change percent
                    change_amount = current_price - previous_close
                
                # Get turnover/value
                turnover = None
                for col in ["القيمة", "Value", "Turnover", "turnover", "قيمة"]:
                    if col in row.index and pd.notna(row[col]):
                        try:
                            turnover = float(row[col])
                        except:
                            turnover = None
                        break
                
                # Get number of transactions
                transactions = None
                for col in ["الصفقات", "Transactions", "transactions", "عدد الصفقات", "Trades"]:
                    if col in row.index and pd.notna(row[col]):
                        try:
                            transactions = int(float(row[col]))
                        except:
                            transactions = None
                        break
                
                # Build payload
                payload = {
                    "symbol": symbol,
                    "current_price": current_price,
                    "open_price": open_price,
                    "high_price": high_price,
                    "low_price": low_price,
                    "previous_close": previous_close,
                    "change_amount": change_amount,
                    "change_percent": change_percent,
                    "volume": volume,
                    "turnover": turnover,
                    "transactions": transactions,
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                batch_payload.append(payload)
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Error processing row {index} for symbol {symbol}: {e}")
                continue
        
        # Batch insert/upsert
        if batch_payload:
            try:
                batch_size = 50
                for i in range(0, len(batch_payload), batch_size):
                    batch = batch_payload[i:i+batch_size]
                    result = supabase.table("stocks_realtime").upsert(batch).execute()
                    logger.info(f"Upserted batch of {len(batch)} real-time records")
                
                logger.info(f"Successfully processed {processed_count} real-time stock records")
                
            except Exception as e:
                logger.error(f"Error upserting real-time data: {e}")
        else:
            logger.warning("No valid data to process")
            
    except Exception as e:
        logger.error(f"Error in process_realtime_data: {e}")

def update_market_indices():
    """Update market indices like EGX30, EGX70 if data is available"""
    try:
        # This would need specific market index data
        # For now, we'll calculate some basic market stats
        
        # Get current market data
        result = supabase.table("stocks_realtime").select("*").execute()
        
        if result.data:
            total_volume = sum(row.get('volume', 0) for row in result.data if row.get('volume'))
            total_turnover = sum(row.get('turnover', 0) for row in result.data if row.get('turnover'))
            active_stocks = len([row for row in result.data if row.get('current_price')])
            
            # Update or insert market summary
            market_summary = {
                "symbol": "EGX_MARKET",
                "name": "Egyptian Exchange Market Summary",
                "name_ar": "ملخص البورصة المصرية",
                "volume": total_volume,
                "turnover": total_turnover,
                "constituents_count": active_stocks,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            supabase.table("market_indices").upsert(market_summary).execute()
            logger.info("Updated market summary")
            
    except Exception as e:
        logger.error(f"Error updating market indices: {e}")

def main():
    logger.info("Starting real-time data sync")
    
    # Process real-time stock data
    process_realtime_data(args.file)
    
    # Update market indices
    update_market_indices()
    
    logger.info("Real-time data sync completed")

if __name__ == "__main__":
    main()

