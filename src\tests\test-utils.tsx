 
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';

// Test utilities for React Query
export const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// Wrapper component for tests that need React Query
export const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Custom render function with providers
export const renderWithProviders = (ui: React.ReactElement, options = {}) => {
  return render(ui, {
    wrapper: TestWrapper,
    ...options,
  });
};

// Mock data generators
export const mockStockData = {
  basic: {
    symbol: 'COMI',
    name: 'Commercial International Bank',
    price: 45.50,
    change: 2.30,
    changePercent: 5.33,
    volume: 1250000,
  },
  
  realtime: [
    {
      symbol: 'COMI',
      current_price: 45.50,
      change_amount: 2.30,
      change_percent: 5.33,
      volume: 1250000,
    },
    {
      symbol: 'ETEL',
      current_price: 18.75,
      change_amount: -0.85,
      change_percent: -4.34,
      volume: 890000,
    },
  ],
  
  historical: [
    {
      date: '2025-06-15',
      symbol: 'COMI',
      open: 43.20,
      high: 46.00,
      low: 42.50,
      close: 45.50,
      volume: 1250000,
    },
    {
      date: '2025-06-14',
      symbol: 'COMI',
      open: 42.80,
      high: 44.20,
      low: 42.00,
      close: 43.20,
      volume: 980000,
    },
  ],
  
  technicalIndicators: [
    {
      date: '2025-06-15',
      symbol: 'COMI',
      rsi_14: 65.5,
      macd: 0.75,
      macd_signal: 0.60,
      bb_upper: 47.20,
      bb_middle: 45.50,
      bb_lower: 43.80,
    },
  ],
};

// Mock Supabase client
export const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn(() => ({
            data: mockStockData.realtime,
            error: null,
          })),
        })),
      })),
    })),
  })),
  
  channel: vi.fn(() => ({
    on: vi.fn(() => ({
      subscribe: vi.fn(),
    })),
    unsubscribe: vi.fn(),
  })),
  
  auth: {
    getUser: vi.fn(() => Promise.resolve({
      data: { user: { id: 'test-user-id', email: '<EMAIL>' } },
      error: null,
    })),
    signIn: vi.fn(),
    signOut: vi.fn(),
  },
};

// Mock ResizeObserver for chart tests
export const mockResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver for virtual scrolling tests
export const mockIntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Performance mock for performance monitoring tests
export const mockPerformance = {
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByName: vi.fn(() => [{ duration: 100 }]),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024,
    totalJSHeapSize: 100 * 1024 * 1024,
    jsHeapSizeLimit: 200 * 1024 * 1024,
  },
};

// Test data validation helpers
export const testDataValidation = {
  isValidStockSymbol: (symbol: string) => /^[A-Z]{2,10}$/.test(symbol),
  isValidPrice: (price: number) => price > 0 && price < 1000000,
  isValidPercentage: (percent: number) => percent >= -100 && percent <= 1000,
  isValidVolume: (volume: number) => volume >= 0 && volume < 1000000000,
};

// Common test assertions
export const testAssertions = {
  expectStockDataStructure: (data: any) => {
    expect(data).toHaveProperty('symbol');
    expect(data).toHaveProperty('price');
    expect(data).toHaveProperty('change');
    expect(data).toHaveProperty('changePercent');
    expect(data).toHaveProperty('volume');
  },
  
  expectTechnicalIndicatorStructure: (data: any) => {
    expect(data).toHaveProperty('date');
    expect(data).toHaveProperty('rsi');
    expect(data).toHaveProperty('macd');
    expect(data).toHaveProperty('signal');
  },
  
  expectPortfolioStructure: (data: any) => {
    expect(data).toHaveProperty('id');
    expect(data).toHaveProperty('name');
    expect(data).toHaveProperty('holdings');
    expect(Array.isArray(data.holdings)).toBe(true);
  },
};

// Setup and teardown helpers
export const testSetup = {
  beforeEach: () => {
    vi.clearAllMocks();
    
    // Setup global mocks
    global.ResizeObserver = mockResizeObserver;
    global.IntersectionObserver = mockIntersectionObserver;
    global.performance = mockPerformance as any;
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    };
    global.localStorage = localStorageMock as any;
    
    // Mock fetch
    global.fetch = vi.fn();
  },
  
  afterEach: () => {
    vi.resetAllMocks();
  },
};

// Async test helpers
export const asyncTestHelpers = {
  waitForElement: async (testId: string) => {
    return await waitFor(() => screen.getByTestId(testId));
  },
  
  waitForText: async (text: string) => {
    return await waitFor(() => screen.getByText(text));
  },
  
  waitForQuery: async (queryKey: string[]) => {
    return await waitFor(() => {
      // This would need to be implemented based on your query client
    });
  },
};

// Component interaction helpers
export const interactionHelpers = {
  clickButton: (buttonText: string) => {
    const button = screen.getByRole('button', { name: buttonText });
    fireEvent.click(button);
    return button;
  },
  
  typeInInput: (placeholder: string, value: string) => {
    const input = screen.getByPlaceholderText(placeholder);
    fireEvent.change(input, { target: { value } });
    return input;
  },
  
  selectOption: (selectElement: HTMLElement, optionText: string) => {
    fireEvent.change(selectElement, { target: { value: optionText } });
  },
};

// Error testing helpers
export const errorTestHelpers = {
  mockConsoleError: () => {
    const originalError = console.error;
    console.error = vi.fn();
    return () => {
      console.error = originalError;
    };
  },
  
  expectErrorBoundary: () => {
    expect(screen.getByText(/حدث خطأ غير متوقع/)).toBeInTheDocument();
  },
  
  expectLoadingState: () => {
    expect(screen.getByText(/جاري تحميل/)).toBeInTheDocument();
  },
};

// Export all test utilities
export {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  afterEach,
  render,
  screen,
  fireEvent,
  waitFor,
};
