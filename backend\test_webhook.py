#!/usr/bin/env python3
"""
Test Trading Signals Webhook System
This script tests the webhook endpoint for trading signals
"""

import requests
import json
import time
import sys

# Test webhook endpoint
WEBHOOK_URL = "http://localhost:8003/api/v1/webhooks/trading-signals"

# Sample trading signal payloads
test_signals = [
    {
        "stock_code": "ABUK",
        "report": "buy",
        "buy_price": "65.50",
        "tp1": "68.00",
        "tp2": "70.50", 
        "tp3": "73.00",
        "sl": "62.00"
    },
    {
        "stock_code": "ABUK",
        "report": "tp1done",
        "tp1": "68.00",
        "sl": "65.50"
    },
    {
        "stock_code": "COMI",
        "report": "buy",
        "buy_price": "45.25",
        "tp1": "47.00",
        "tp2": "49.50",
        "tp3": "52.00",
        "sl": "43.00"
    },
    {
        "stock_code": "SWDY",
        "report": "sell",
        "sell_price": "69.00"
    },
    {
        "stock_code": "FWRY",
        "report": "tsl",
        "sl": "61.00"
    }
]

def test_webhook():
    """Test the webhook endpoint with sample signals"""
    
    print("🎯 Testing Trading Signals Webhook System")
    print("=" * 50)
    
    # Check if server is running
    try:
        health_response = requests.get("http://localhost:8003/health")
        print(f"✅ Server is running: {health_response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Please start the backend server first.")
        print("Run: python run_server.py")
        return False
    
    # Test each signal
    for i, signal in enumerate(test_signals, 1):
        print(f"\n🔄 Testing Signal {i}: {signal['report']} for {signal['stock_code']}")
        
        try:
            response = requests.post(
                WEBHOOK_URL,
                json=signal,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"✅ Signal processed successfully")
                result = response.json()
                print(f"📊 Response: {result}")
            else:
                print(f"❌ Signal failed: {response.status_code}")
                print(f"📝 Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        
        # Wait between signals
        time.sleep(1)
    
    print("\n" + "=" * 50)
    print("🏁 Webhook testing completed!")
    return True

def test_websocket_connection():
    """Test WebSocket connection for real-time signals"""
    
    try:
        import websocket
        
        def on_message(ws, message):
            print(f"📡 Received WebSocket message: {message}")
        
        def on_error(ws, error):
            print(f"❌ WebSocket error: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print("🔌 WebSocket connection closed")
        
        def on_open(ws):
            print("✅ WebSocket connection opened")
        
        print("\n🔗 Testing WebSocket connection...")
        ws = websocket.WebSocketApp(
            "ws://localhost:8003/ws/signals",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        # Run for 5 seconds to test connection
        ws.run_forever()
        
    except ImportError:
        print("⚠️  websocket-client not installed. Install with: pip install websocket-client")
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")

if __name__ == "__main__":
    # Test webhook
    if test_webhook():
        print("\n🔗 Testing WebSocket connection (optional)...")
        # Uncomment to test WebSocket
        # test_websocket_connection()
    
    print("\n💡 Next steps:")
    print("1. Check the server logs for signal processing")
    print("2. Verify signals are stored in the database:")
    print("   PGPASSWORD=egx123 psql -h localhost -U postgres -d egx_stock_oracle -c 'SELECT * FROM trading_signals ORDER BY created_at DESC LIMIT 5;'")
    print("3. Test the frontend 'التوصيات اللحظية' tab")
