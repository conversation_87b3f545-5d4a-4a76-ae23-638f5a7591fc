
import React from 'react';
import { cn } from '@/lib/utils';

interface AnimateFadeInProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

const AnimateFadeIn: React.FC<AnimateFadeInProps> = ({ 
  children, 
  className, 
  delay = 0,
  direction = 'up' 
}) => {
  const directionClasses = {
    up: 'animate-fade-in',
    down: 'animate-slide-down',
    left: 'animate-slide-in-left',
    right: 'animate-slide-in-right'
  };

  return (
    <div 
      className={cn(directionClasses[direction], className)}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
};

export default AnimateFadeIn;
