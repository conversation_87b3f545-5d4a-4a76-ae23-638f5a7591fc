import React, { useState, useEffect } from 'react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown, Target, Shield, Brain, Copy, Eye, AlertTriangle, DollarSign } from 'lucide-react';

const SmartPortfolioDashboard = () => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('overview');
  const [timeframe, setTimeframe] = useState('1M');
  const [copyTradeMode, setCopyTradeMode] = useState(false);
  const [userSubscription, setUserSubscription] = useState('premium'); // free, premium, vip

  useEffect(() => {
    fetchPortfolioData();
    
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(fetchPortfolioData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchPortfolioData = async () => {
    try {
      const response = await fetch('/api/v1/smart-portfolio/dashboard');
      const data = await response.json();
      setPortfolioData(data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching portfolio data:', error);
      setLoading(false);
    }
  };

  const handleCopyTrade = async (tradeId) => {
    if (userSubscription !== 'vip') {
      alert('ميزة نسخ التداول متاحة فقط لأعضاء VIP');
      return;
    }

    try {
      const response = await fetch('/api/v1/smart-portfolio/copy-trade', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ trade_id: tradeId, copy_mode: 'full' })
      });
      
      if (response.ok) {
        alert('تم نسخ الصفقة بنجاح إلى محفظتك');
      }
    } catch (error) {
      console.error('Error copying trade:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">
          <Brain className="w-12 h-12 text-blue-500 mb-4" />
          <p className="text-lg font-semibold">جاري تحميل المحفظة الذكية...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="smart-portfolio-dashboard bg-gray-50 min-h-screen" dir="rtl">
      {/* Header */}
      <PortfolioHeader 
        portfolioData={portfolioData}
        userSubscription={userSubscription}
        onTimeframeChange={setTimeframe}
        selectedTimeframe={timeframe}
      />

      {/* Navigation Tabs */}
      <PortfolioNavigation 
        selectedTab={selectedTab}
        onTabChange={setSelectedTab}
      />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        {selectedTab === 'overview' && (
          <OverviewTab portfolioData={portfolioData} timeframe={timeframe} />
        )}
        
        {selectedTab === 'positions' && (
          <PositionsTab 
            portfolioData={portfolioData}
            onCopyTrade={handleCopyTrade}
            userSubscription={userSubscription}
          />
        )}
        
        {selectedTab === 'performance' && (
          <PerformanceTab portfolioData={portfolioData} timeframe={timeframe} />
        )}
        
        {selectedTab === 'strategy' && (
          <StrategyTab portfolioData={portfolioData} />
        )}
        
        {selectedTab === 'comparison' && (
          <ComparisonTab portfolioData={portfolioData} />
        )}
      </div>
    </div>
  );
};

// Portfolio Header Component
const PortfolioHeader = ({ portfolioData, userSubscription, onTimeframeChange, selectedTimeframe }) => {
  const summary = portfolioData?.portfolio_summary || {};
  
  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Brain className="w-8 h-8 ml-3" />
            <div>
              <h1 className="text-3xl font-bold">المحفظة الذكية</h1>
              <p className="text-blue-100">نظام إدارة المحافظ بالذكاء الاصطناعي</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <SubscriptionBadge subscription={userSubscription} />
            <TimeframeSelector 
              selected={selectedTimeframe}
              onChange={onTimeframeChange}
            />
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <MetricCard
            title="القيمة الإجمالية"
            value={`${summary.current_portfolio_value?.toLocaleString()} جنيه`}
            change={summary.total_return_percentage}
            icon={<DollarSign className="w-6 h-6" />}
          />
          
          <MetricCard
            title="العائد الإجمالي"
            value={`${summary.total_return_percentage?.toFixed(2)}%`}
            change={summary.total_return_percentage}
            icon={<TrendingUp className="w-6 h-6" />}
          />
          
          <MetricCard
            title="معدل النجاح"
            value={`${(summary.win_rate * 100)?.toFixed(1)}%`}
            change={summary.win_rate > 0.6 ? 1 : -1}
            icon={<Target className="w-6 h-6" />}
          />
          
          <MetricCard
            title="عامل الربح"
            value={summary.profit_factor?.toFixed(2)}
            change={summary.profit_factor > 1 ? 1 : -1}
            icon={<Shield className="w-6 h-6" />}
          />
        </div>
      </div>
    </div>
  );
};

// Metric Card Component
const MetricCard = ({ title, value, change, icon }) => {
  const isPositive = change > 0;
  
  return (
    <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-blue-100">{title}</span>
        {icon}
      </div>
      <div className="text-2xl font-bold mb-1">{value}</div>
      <div className={`flex items-center text-sm ${isPositive ? 'text-green-300' : 'text-red-300'}`}>
        {isPositive ? <TrendingUp className="w-4 h-4 ml-1" /> : <TrendingDown className="w-4 h-4 ml-1" />}
        {Math.abs(change)?.toFixed(2)}%
      </div>
    </div>
  );
};

// Positions Tab Component
const PositionsTab = ({ portfolioData, onCopyTrade, userSubscription }) => {
  const openPositions = portfolioData?.open_positions || [];
  const recentClosed = portfolioData?.recent_closed_positions || [];
  
  return (
    <div className="space-y-6">
      {/* Open Positions */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h3 className="text-xl font-semibold flex items-center">
            <Eye className="w-5 h-5 ml-2" />
            المراكز المفتوحة ({openPositions.length})
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  السهم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  سعر الدخول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  السعر الحالي
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الكمية
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الربح/الخسارة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الأهداف المحققة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {openPositions.map((position, index) => (
                <PositionRow 
                  key={index}
                  position={position}
                  onCopyTrade={onCopyTrade}
                  userSubscription={userSubscription}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Closed Positions */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h3 className="text-xl font-semibold">آخر الصفقات المغلقة</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  السهم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الدخول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الخروج
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الربح/الخسارة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  النسبة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentClosed.map((position, index) => (
                <ClosedPositionRow key={index} position={position} />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Position Row Component
const PositionRow = ({ position, onCopyTrade, userSubscription }) => {
  const pnlColor = position.unrealized_pnl >= 0 ? 'text-green-600' : 'text-red-600';
  const pnlIcon = position.unrealized_pnl >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />;
  
  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="font-medium text-gray-900">{position.symbol}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {position.entry_price?.toFixed(2)} جنيه
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {position.current_price?.toFixed(2)} جنيه
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {position.shares?.toLocaleString()} سهم
      </td>
      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${pnlColor}`}>
        <div className="flex items-center">
          {pnlIcon}
          <span className="mr-1">
            {position.unrealized_pnl?.toLocaleString()} جنيه
          </span>
          <span className="text-xs">
            ({position.unrealized_pnl_percentage?.toFixed(2)}%)
          </span>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <div className="flex space-x-1">
          {position.targets_achieved?.map((target, i) => (
            <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {target}
            </span>
          ))}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div className="flex space-x-2">
          <button
            onClick={() => onCopyTrade(position.id)}
            disabled={userSubscription !== 'vip'}
            className={`inline-flex items-center px-3 py-1 rounded-md text-sm font-medium ${
              userSubscription === 'vip' 
                ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' 
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            <Copy className="w-4 h-4 ml-1" />
            نسخ
          </button>
        </div>
      </td>
    </tr>
  );
};

// Performance Tab Component
const PerformanceTab = ({ portfolioData, timeframe }) => {
  const performanceData = portfolioData?.performance_metrics || {};
  const riskData = portfolioData?.risk_analysis || {};
  
  // Sample data for charts (would come from API)
  const equityData = [
    { date: '2025-01-01', value: 1000000 },
    { date: '2025-01-15', value: 1050000 },
    { date: '2025-02-01', value: 1080000 },
    { date: '2025-02-15', value: 1120000 },
    { date: '2025-03-01', value: 1150000 },
    { date: '2025-03-15', value: 1200000 },
  ];
  
  const monthlyReturns = [
    { month: 'يناير', return: 5.2 },
    { month: 'فبراير', return: 3.8 },
    { month: 'مارس', return: 4.1 },
    { month: 'أبريل', return: 2.9 },
    { month: 'مايو', return: 6.3 },
    { month: 'يونيو', return: 4.7 },
  ];
  
  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h4 className="text-lg font-semibold mb-4">إحصائيات الأداء</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">إجمالي الصفقات</span>
              <span className="font-semibold">{performanceData.total_trades}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">الصفقات الرابحة</span>
              <span className="font-semibold text-green-600">{performanceData.winning_trades}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">الصفقات الخاسرة</span>
              <span className="font-semibold text-red-600">{performanceData.losing_trades}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">معدل النجاح</span>
              <span className="font-semibold">{(performanceData.win_rate * 100)?.toFixed(1)}%</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h4 className="text-lg font-semibold mb-4">مقاييس المخاطر</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">التعرض الحالي</span>
              <span className="font-semibold">{riskData.current_exposure?.toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">أكبر مركز</span>
              <span className="font-semibold">{riskData.largest_position?.toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">أقصى انخفاض</span>
              <span className="font-semibold text-red-600">{performanceData.max_drawdown?.toFixed(2)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">نسبة شارب</span>
              <span className="font-semibold">{performanceData.sharpe_ratio?.toFixed(2)}</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h4 className="text-lg font-semibold mb-4">تحليل الربحية</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">عامل الربح</span>
              <span className="font-semibold">{performanceData.profit_factor?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">متوسط الربح</span>
              <span className="font-semibold text-green-600">+4.8%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">متوسط الخسارة</span>
              <span className="font-semibold text-red-600">-2.1%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">العائد السنوي</span>
              <span className="font-semibold">24.5%</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Equity Curve Chart */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h4 className="text-lg font-semibold mb-4">منحنى رأس المال</h4>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={equityData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip 
              formatter={(value) => [`${value.toLocaleString()} جنيه`, 'القيمة']}
            />
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke="#3B82F6" 
              fill="#3B82F6" 
              fillOpacity={0.3}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
      
      {/* Monthly Returns */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h4 className="text-lg font-semibold mb-4">العوائد الشهرية</h4>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={monthlyReturns}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip 
              formatter={(value) => [`${value}%`, 'العائد']}
            />
            <Bar 
              dataKey="return" 
              fill={(entry) => entry.return >= 0 ? '#10B981' : '#EF4444'}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default SmartPortfolioDashboard;
