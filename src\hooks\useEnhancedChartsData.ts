import { useState, useEffect, useRef, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface EnhancedStockData {
  symbol: string;
  name?: string;
  current_price: number;
  previous_close?: number;
  change_amount?: number;
  change_percent: number;
  volume: number;
  turnover: number;
  high_price?: number;
  low_price?: number;
  updated_at: string;
  
  // بيانات محسنة للرسوم البيانية
  price_history?: number[];
  volume_history?: number[];
  price_trend?: 'up' | 'down' | 'neutral';
  volume_trend?: 'up' | 'down' | 'neutral';
  
  // حالات التغيير للتأثيرات البصرية
  priceDirection?: 'up' | 'down' | 'neutral';
  volumeChange?: 'up' | 'down' | 'neutral';
  isNewUpdate?: boolean;
  flashColor?: 'green' | 'red' | 'none';
  isHighlighted?: boolean;
  lastUpdateTime?: number;
}

export interface MarketStats {
  totalStocks: number;
  gainers: number;
  losers: number;
  unchanged: number;
  totalVolume: number;
  totalTurnover: number;
  mostActiveStock?: EnhancedStockData;
  topGainer?: EnhancedStockData;
  topLoser?: EnhancedStockData;
  lastUpdate: string;
  updatesCount: number;
}

export const useEnhancedChartsData = () => {
  const [stocks, setStocks] = useState<EnhancedStockData[]>([]);
  const [marketStats, setMarketStats] = useState<MarketStats | null>(null);
  const [previousData, setPreviousData] = useState<Map<string, EnhancedStockData>>(new Map());
  const [updatesCount, setUpdatesCount] = useState(0);
  
  const flashTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const updateCounterRef = useRef(0);

  // جلب البيانات الأولية
  const {
    data: initialData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['enhanced_charts_data'],
    queryFn: async () => {
      try {
        console.log('🔄 Fetching enhanced charts data...');
        
        const { data: stocks, error: stocksError } = await supabase
          .from('stocks_realtime')
          .select(`
            symbol,
            current_price,
            previous_close,
            change_amount,
            change_percent,
            volume,
            turnover,
            high_price,
            low_price,
            updated_at
          `)
          .not('symbol', 'like', '*EGX*')
          .not('symbol', 'like', '*INDEX*')
          .gt('current_price', 0)
          .order('turnover', { ascending: false })
          .limit(100);

        if (stocksError) throw stocksError;

        // جلب أسماء الأسهم
        const symbols = stocks?.map(s => s.symbol) || [];
        const { data: masterData } = await supabase
          .from('stocks_master')
          .select('symbol, name, name_ar')
          .in('symbol', symbols);

        // دمج البيانات مع إنشاء تاريخ محسن
        const enrichedStocks = stocks?.map(stock => {
          const master = masterData?.find(m => m.symbol === stock.symbol);
          
          // إنشاء تاريخ أسعار متنوع ومنطقي
          const priceHistory = generateRealisticPriceHistory(
            stock.current_price, 
            stock.change_percent,
            stock.high_price,
            stock.low_price
          );
          
          // إنشاء تاريخ حجم متنوع
          const volumeHistory = generateRealisticVolumeHistory(
            stock.volume,
            stock.turnover
          );

          return {
            ...stock,
            name: master?.name_ar || master?.name || stock.symbol,
            change_amount: stock.change_amount || (stock.current_price - (stock.previous_close || stock.current_price)),
            previous_close: stock.previous_close || stock.current_price,
            price_history: priceHistory,
            volume_history: volumeHistory,
            price_trend: calculateTrend(priceHistory),
            volume_trend: calculateTrend(volumeHistory),
            lastUpdateTime: Date.now(),
          };
        }) || [];

        console.log(`✅ Generated enhanced charts data for ${enrichedStocks.length} stocks`);
        return enrichedStocks;
      } catch (error) {
        console.error('❌ Error fetching enhanced charts data:', error);
        throw error;
      }
    },
    refetchInterval: 10000, // تحديث كل 10 ثوان
    retry: 2,
    retryDelay: 2000,
  });

  // دالة لإنشاء تاريخ أسعار واقعي ومتنوع
  const generateRealisticPriceHistory = (
    currentPrice: number,
    changePercent: number,
    highPrice?: number,
    lowPrice?: number
  ): number[] => {
    const points = 15; // 15 نقطة
    const history: number[] = [];
    
    // حساب السعر الابتدائي
    const startPrice = currentPrice / (1 + changePercent / 100);
    
    // تحديد النطاق
    const high = highPrice || currentPrice * 1.02;
    const low = lowPrice || currentPrice * 0.98;
    const range = high - low;
    
    for (let i = 0; i < points; i++) {
      const progress = i / (points - 1);
      
      if (i === 0) {
        history.push(startPrice);
      } else if (i === points - 1) {
        history.push(currentPrice);
      } else {
        // إنشاء حركة طبيعية مع تقلبات
        const baseProgress = startPrice + (currentPrice - startPrice) * progress;
        
        // إضافة تقلبات عشوائية واقعية
        const volatility = range * 0.3;
        const randomFactor = (Math.random() - 0.5) * volatility;
        const smoothFactor = Math.sin(progress * Math.PI * 2) * volatility * 0.3;
        
        let price = baseProgress + randomFactor + smoothFactor;
        
        // التأكد من البقاء ضمن النطاق المنطقي
        price = Math.max(low, Math.min(high, price));
        
        history.push(price);
      }
    }
    
    return history;
  };

  // دالة لإنشاء تاريخ حجم واقعي ومتنوع
  const generateRealisticVolumeHistory = (
    currentVolume: number,
    turnover?: number
  ): number[] => {
    const points = 15;
    const history: number[] = [];
    
    // حساب متوسط الحجم
    const avgVolume = currentVolume || 1000;
    const baseVolume = avgVolume * 0.7; // 70% من الحجم الحالي كقاعدة
    
    for (let i = 0; i < points; i++) {
      const progress = i / (points - 1);
      
      if (i === points - 1) {
        history.push(currentVolume);
      } else {
        // إنشاء تقلبات حجم طبيعية
        const cycleFactor = Math.sin(progress * Math.PI * 3) * 0.3; // دورات تداول
        const randomFactor = (Math.random() - 0.5) * 0.4; // عشوائية
        const trendFactor = Math.pow(progress, 1.5) * 0.3; // اتجاه نحو الحجم الحالي
        
        const volumeMultiplier = 1 + cycleFactor + randomFactor + trendFactor;
        let volume = baseVolume * Math.max(0.3, volumeMultiplier);
        
        // التأكد من القيم المنطقية
        volume = Math.max(avgVolume * 0.1, Math.min(avgVolume * 2, volume));
        
        history.push(Math.round(volume));
      }
    }
    
    return history;
  };

  // حساب الاتجاه العام للبيانات
  const calculateTrend = (data: number[]): 'up' | 'down' | 'neutral' => {
    if (!data || data.length < 2) return 'neutral';
    
    const first = data[0];
    const last = data[data.length - 1];
    const middle = data[Math.floor(data.length / 2)];
    
    // حساب متوسط النصف الأول والثاني
    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    const threshold = Math.abs(firstAvg * 0.002); // 0.2% threshold
    
    if (secondAvg > firstAvg + threshold) return 'up';
    if (secondAvg < firstAvg - threshold) return 'down';
    return 'neutral';
  };
  // حساب إحصائيات السوق
  const calculateMarketStats = useCallback((stockData: EnhancedStockData[]) => {
    if (!stockData || stockData.length === 0) return;

    const gainers = stockData.filter(s => s.change_percent > 0);
    const losers = stockData.filter(s => s.change_percent < 0);
    const unchanged = stockData.filter(s => s.change_percent === 0);
    
    const totalVolume = stockData.reduce((sum, s) => sum + (s.volume || 0), 0);
    const totalTurnover = stockData.reduce((sum, s) => sum + (s.turnover || 0), 0);

    const mostActiveStock = stockData.reduce((max, stock) => 
      (stock.turnover || 0) > (max.turnover || 0) ? stock : max
    );

    const topGainer = gainers.length > 0 ? gainers.reduce((max, stock) => 
      stock.change_percent > max.change_percent ? stock : max
    ) : undefined;

    const topLoser = losers.length > 0 ? losers.reduce((min, stock) => 
      stock.change_percent < min.change_percent ? stock : min
    ) : undefined;

    const stats: MarketStats = {
      totalStocks: stockData.length,
      gainers: gainers.length,
      losers: losers.length,
      unchanged: unchanged.length,
      totalVolume,
      totalTurnover,
      mostActiveStock,
      topGainer,
      topLoser,
      lastUpdate: new Date().toLocaleTimeString('ar-EG'),
      updatesCount: updateCounterRef.current,
    };

    setMarketStats(stats);
  }, []);

  // تحديث البيانات مع التأثيرات البصرية
  const updateStockData = useCallback((newData: EnhancedStockData[]) => {
    const now = Date.now();
    
    const processedStocks = newData.map(stock => {
      const previous = previousData.get(stock.symbol);
      let priceDirection: 'up' | 'down' | 'neutral' = 'neutral';
      let volumeChange: 'up' | 'down' | 'neutral' = 'neutral';
      let flashColor: 'green' | 'red' | 'none' = 'none';
      let isNewUpdate = false;
      let isHighlighted = false;

      if (previous) {
        // مقارنة الأسعار
        if (stock.current_price > previous.current_price) {
          priceDirection = 'up';
          flashColor = 'green';
          isNewUpdate = true;
          isHighlighted = true;
          
          // تحديث تاريخ السعر
          stock.price_history = [...(previous.price_history || []), stock.current_price].slice(-15);
        } else if (stock.current_price < previous.current_price) {
          priceDirection = 'down';
          flashColor = 'red';
          isNewUpdate = true;
          isHighlighted = true;
          
          // تحديث تاريخ السعر
          stock.price_history = [...(previous.price_history || []), stock.current_price].slice(-15);
        } else {
          // نفس السعر - الاحتفاظ بالتاريخ
          stock.price_history = previous.price_history;
        }

        // مقارنة الحجم
        if (stock.volume > previous.volume) {
          volumeChange = 'up';
        } else if (stock.volume < previous.volume) {
          volumeChange = 'down';
        }

        // تحديث تاريخ الحجم
        stock.volume_history = [...(previous.volume_history || []), stock.volume].slice(-15);

        // إدارة تأثير الوميض
        if (isNewUpdate) {
          const existingTimer = flashTimers.current.get(stock.symbol);
          if (existingTimer) clearTimeout(existingTimer);

          const timer = setTimeout(() => {
            flashTimers.current.delete(stock.symbol);
          }, 3000);
          
          flashTimers.current.set(stock.symbol, timer);
        }
      }

      return {
        ...stock,
        priceDirection,
        volumeChange,
        isNewUpdate,
        flashColor,
        isHighlighted,
        lastUpdateTime: now,
        price_trend: calculateTrend(stock.price_history || []),
        volume_trend: calculateTrend(stock.volume_history || []),
      };
    });

    setStocks(processedStocks);
    
    // تحديث البيانات السابقة
    const newPreviousData = new Map<string, EnhancedStockData>();
    processedStocks.forEach(stock => {
      newPreviousData.set(stock.symbol, { ...stock });
    });
    setPreviousData(newPreviousData);

    // حساب الإحصائيات
    calculateMarketStats(processedStocks);
    
    // تحديث عداد التحديثات
    updateCounterRef.current += 1;
    setUpdatesCount(updateCounterRef.current);
    
  }, [previousData, calculateMarketStats]);

  // تهيئة البيانات الأولية
  useEffect(() => {
    if (initialData && initialData.length > 0) {
      updateStockData(initialData);
    }
  }, [initialData, updateStockData]);

  // تنظيف الموارد
  useEffect(() => {
    const timers = flashTimers.current;
    return () => {
      timers.forEach(timer => clearTimeout(timer));
      timers.clear();
    };
  }, []);

  return {
    stocks,
    marketStats,
    isLoading,
    error,
    refetch,
    updatesCount,
  };
};
