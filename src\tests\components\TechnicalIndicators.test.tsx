import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import TechnicalIndicators from '../../components/charts/TechnicalIndicators';

// Mock the chart components
vi.mock('../../components/charts/ChartContainer', () => ({
  default: ({ activeIndicator, data }: { activeIndicator: string; data: unknown[] }) => (
    <div data-testid="chart-container">
      <div data-testid="active-indicator">{activeIndicator}</div>
      <div data-testid="data-length">{data.length}</div>
    </div>
  ),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            limit: vi.fn(() => Promise.resolve({
              data: [
                {
                  date: '2025-06-15',
                  rsi_14: 65.5,
                  macd: 0.75,
                  macd_signal: 0.60,
                  bb_upper: 47.20,
                  bb_middle: 45.50,
                  bb_lower: 43.80,
                }
              ],
              error: null,
            })),
          })),
        })),
      })),
    })),
  },
}));

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
    },
  });
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('TechnicalIndicators Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default symbol COMI', () => {
    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    expect(screen.getByText('المؤشرات الفنية - COMI')).toBeInTheDocument();
  });

  it('renders with custom symbol', () => {
    render(
      <TestWrapper>
        <TechnicalIndicators symbol="ETEL" />
      </TestWrapper>
    );

    expect(screen.getByText('المؤشرات الفنية - ETEL')).toBeInTheDocument();
  });

  it('displays all indicator buttons', () => {
    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    expect(screen.getByText('مؤشر القوة النسبية')).toBeInTheDocument();
    expect(screen.getByText('مؤشر الماكد')).toBeInTheDocument();
    expect(screen.getByText('نطاقات بولينجر')).toBeInTheDocument();
    expect(screen.getByText('حجم التداول')).toBeInTheDocument();
  });

  it('changes active indicator when button is clicked', async () => {
    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    // Initially RSI should be active
    await waitFor(() => {
      expect(screen.getByTestId('active-indicator')).toHaveTextContent('RSI');
    });

    // Click MACD button
    const macdButton = screen.getByText('مؤشر الماكد');
    fireEvent.click(macdButton);

    // Should change to MACD
    await waitFor(() => {
      expect(screen.getByTestId('active-indicator')).toHaveTextContent('MACD');
    });
  });

  it('displays loading state initially', () => {
    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    expect(screen.getByText('جاري تحميل المؤشرات الفنية...')).toBeInTheDocument();
  });

  it('calculates signals correctly', async () => {
    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    // Wait for data to load and signals to be calculated
    await waitFor(() => {
      // RSI is 65.5, which is neutral (between 30-70)
      expect(screen.getByText('محايد')).toBeInTheDocument();
    });
  });

  it('displays overall recommendation', async () => {
    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('التوصية العامة')).toBeInTheDocument();
      expect(screen.getByText('بناءً على تحليل المؤشرات الفنية')).toBeInTheDocument();
    });
  });

  it('handles error state gracefully', async () => {
    // Mock error response
    vi.doMock('@/integrations/supabase/client', () => ({
      supabase: {
        from: vi.fn(() => ({
          select: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                limit: vi.fn(() => Promise.resolve({
                  data: null,
                  error: new Error('Database error'),
                })),
              })),
            })),
          })),
        })),
      },
    }));

    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('خطأ في تحميل البيانات')).toBeInTheDocument();
    });
  });

  it('displays empty state when no data available', async () => {
    // Mock empty response
    vi.doMock('@/integrations/supabase/client', () => ({
      supabase: {
        from: vi.fn(() => ({
          select: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                limit: vi.fn(() => Promise.resolve({
                  data: [],
                  error: null,
                })),
              })),
            })),
          })),
        })),
      },
    }));

    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('لا توجد بيانات متاحة للرمز COMI')).toBeInTheDocument();
    });
  });

  it('has accessible button interactions', () => {
    render(
      <TestWrapper>
        <TechnicalIndicators />
      </TestWrapper>
    );

    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toBeEnabled();
    });
  });
});
