
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Sparkles, Zap } from 'lucide-react';

interface AddStockFormProps {
  newStock: {
    symbol: string;
    quantity: string;
    buyPrice: string;
  };
  setNewStock: React.Dispatch<React.SetStateAction<{
    symbol: string;
    quantity: string;
    buyPrice: string;
  }>>;
  onAddStock: () => void;
}

const AddStockForm = ({ newStock, setNewStock, onAddStock }: AddStockFormProps) => {
  return (
    <Card className="border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-amber-50 card-hover interactive-card relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-100/30 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
      <CardHeader className="relative z-10">
        <CardTitle className="flex items-center gap-2 text-yellow-800">
          <Plus className="h-5 w-5 animate-pulse" />
          <Sparkles className="h-4 w-4 text-yellow-600" />
          إضافة سهم جديد
        </CardTitle>
      </CardHeader>
      <CardContent className="relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            placeholder="رمز السهم (مثل: COMI)"
            value={newStock.symbol}
            onChange={(e) => setNewStock({...newStock, symbol: e.target.value})}
            className="text-right border-2 border-yellow-200 focus:border-yellow-400 transition-all duration-300 hover:shadow-soft"
            dir="rtl"
          />
          <Input
            type="number"
            placeholder="الكمية"
            value={newStock.quantity}
            onChange={(e) => setNewStock({...newStock, quantity: e.target.value})}
            className="text-right border-2 border-yellow-200 focus:border-yellow-400 transition-all duration-300 hover:shadow-soft"
            dir="rtl"
          />
          <Input
            type="number"
            step="0.01"
            placeholder="سعر الشراء"
            value={newStock.buyPrice}
            onChange={(e) => setNewStock({...newStock, buyPrice: e.target.value})}
            className="text-right border-2 border-yellow-200 focus:border-yellow-400 transition-all duration-300 hover:shadow-soft"
            dir="rtl"
          />
          <Button 
            onClick={onAddStock} 
            className="w-full btn-animate bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600 shadow-magical relative overflow-hidden"
          >
            <Zap className="h-4 w-4 mr-2 animate-pulse" />
            إضافة
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AddStockForm;
