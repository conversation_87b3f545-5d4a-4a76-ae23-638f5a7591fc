import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useEnhancedLiveData } from '@/hooks/useEnhancedLiveData';
import { Badge } from '@/components/ui/badge';
import { 
  Activity, 
  RefreshCw, 
  Loader2, 
  Zap, 
  TrendingUp, 
  TrendingDown,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

const LiveDataTester: React.FC = () => {
  const { stocks, marketStats, isLoading, error, refetch, lastUpdate } = useEnhancedLiveData();

  const testResults = {
    dataFetch: stocks && stocks.length > 0,
    visualEffects: stocks?.some(s => s.isNewUpdate || s.flashColor !== 'none'),
    marketStats: marketStats !== null,
    priceTracking: stocks?.some(s => s.priceDirection !== 'neutral'),
    volumeTracking: stocks?.some(s => s.volumeChange !== 'neutral'),
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-blue-600" />
            اختبار البيانات اللحظية المتقدمة
            {lastUpdate && (
              <Badge variant="outline" className="ml-2">
                آخر تحديث: {lastUpdate}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* حالة التحميل */}
          <div className="flex items-center gap-2">
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                <span>جاري تحميل البيانات...</span>
              </>
            ) : error ? (
              <>
                <AlertCircle className="w-4 h-4 text-red-600" />
                <span className="text-red-600">خطأ: {error.message}</span>
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-green-600">تم تحميل البيانات بنجاح</span>
              </>
            )}
          </div>

          {/* نتائج الاختبار */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              {testResults.dataFetch ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <AlertCircle className="w-4 h-4 text-red-600" />
              )}
              <span>جلب البيانات: {stocks?.length || 0} سهم</span>
            </div>

            <div className="flex items-center gap-2">
              {testResults.marketStats ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <AlertCircle className="w-4 h-4 text-red-600" />
              )}
              <span>إحصائيات السوق</span>
            </div>

            <div className="flex items-center gap-2">
              {testResults.visualEffects ? (
                <Zap className="w-4 h-4 text-yellow-500" />
              ) : (
                <AlertCircle className="w-4 h-4 text-gray-400" />
              )}
              <span>التأثيرات البصرية</span>
            </div>

            <div className="flex items-center gap-2">
              {testResults.priceTracking ? (
                <TrendingUp className="w-4 h-4 text-green-600" />
              ) : (
                <AlertCircle className="w-4 h-4 text-gray-400" />
              )}
              <span>تتبع تغيير الأسعار</span>
            </div>

            <div className="flex items-center gap-2">
              {testResults.volumeTracking ? (
                <TrendingDown className="w-4 h-4 text-blue-600" />
              ) : (
                <AlertCircle className="w-4 h-4 text-gray-400" />
              )}
              <span>تتبع تغيير الحجم</span>
            </div>
          </div>

          {/* معلومات مفصلة */}
          {marketStats && (
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{marketStats.totalStocks}</div>
                <div className="text-sm text-gray-500">إجمالي الأسهم</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{marketStats.gainers}</div>
                <div className="text-sm text-gray-500">مرتفعة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{marketStats.losers}</div>
                <div className="text-sm text-gray-500">منخفضة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{marketStats.unchanged}</div>
                <div className="text-sm text-gray-500">ثابتة</div>
              </div>
            </div>
          )}

          {/* أزرار التحكم */}
          <div className="flex gap-2">
            <Button onClick={() => refetch()} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              تحديث البيانات
            </Button>
          </div>

          {/* عينة من البيانات */}
          {stocks && stocks.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">عينة من البيانات:</h4>
              <div className="space-y-2">
                {stocks.slice(0, 5).map(stock => (
                  <div key={stock.symbol} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{stock.symbol}</span>
                      {stock.isNewUpdate && (
                        <Zap className="w-3 h-3 text-yellow-500" />
                      )}
                      {stock.priceDirection === 'up' && (
                        <TrendingUp className="w-3 h-3 text-green-600" />
                      )}
                      {stock.priceDirection === 'down' && (
                        <TrendingDown className="w-3 h-3 text-red-600" />
                      )}
                    </div>
                    <div className="flex items-center gap-4">
                      <span>{stock.current_price.toFixed(2)} ج.م</span>
                      <span className={`text-sm ${
                        stock.change_percent > 0 ? 'text-green-600' :
                        stock.change_percent < 0 ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {stock.change_percent.toFixed(2)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default LiveDataTester;
