#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def calculate_market_distribution():
    """Calculate real market distribution from stocks_realtime"""
    print("📊 حساب توزيع السوق الحقيقي...")
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    try:
        # Get all stocks with change_percent, excluding indices
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol,change_percent&limit=1000",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        
        if response.status_code != 200:
            print(f"❌ خطأ في الحصول على البيانات: {response.status_code}")
            return None
            
        data = response.json()
        
        # Filter out market indices (they contain "EGX" in symbol)
        stocks = [stock for stock in data if 'EGX' not in stock['symbol']]
        
        # Calculate distribution
        gainers = 0  # صاعد
        losers = 0   # هابط  
        unchanged = 0  # ثابت
        
        for stock in stocks:
            change_percent = stock.get('change_percent', 0)
            if change_percent is None:
                change_percent = 0
                
            if change_percent > 0:
                gainers += 1
            elif change_percent < 0:
                losers += 1
            else:
                unchanged += 1
        
        total_stocks = len(stocks)
        
        print(f"📈 إجمالي الأسهم المتداولة: {total_stocks}")
        print(f"🟢 صاعد: {gainers} ({gainers/total_stocks*100:.1f}%)")
        print(f"🔴 هابط: {losers} ({losers/total_stocks*100:.1f}%)")
        print(f"⚪ ثابت: {unchanged} ({unchanged/total_stocks*100:.1f}%)")
        
        return {
            'total_stocks': total_stocks,
            'gainers': gainers,
            'losers': losers,
            'unchanged': unchanged
        }
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return None

def update_market_summary_in_db(distribution):
    """Update the EGX_MARKET index with real distribution data"""
    print("\n🔄 تحديث ملخص السوق في قاعدة البيانات...")
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not service_key:
        print("❌ مفتاح الخدمة غير متوفر")
        return False
    
    try:
        # Update the EGX_MARKET record with real data
        update_data = {
            'constituents_count': distribution['total_stocks'],
            'gainers_count': distribution['gainers'],
            'losers_count': distribution['losers'],
            'unchanged_count': distribution['unchanged'],
            'updated_at': 'now()'
        }
        
        response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/market_indices?symbol=eq.EGX_MARKET",
            headers={
                "apikey": service_key,
                "Authorization": f"Bearer {service_key}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        if response.status_code in [200, 204]:
            print("✅ تم تحديث ملخص السوق بنجاح")
            return True
        else:
            print(f"❌ فشل التحديث: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التحديث: {str(e)}")
        return False

def test_updated_frontend_data():
    """Test the updated market summary data"""
    print("\n🧪 اختبار البيانات المحدثة للفرونت اند...")
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/market_indices?select=*&symbol=eq.EGX_MARKET",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            if data:
                market_summary = data[0]
                print("✅ بيانات ملخص السوق المحدثة:")
                print(f"   📊 إجمالي الأسهم: {market_summary.get('constituents_count', 'غير محدد')}")
                print(f"   🟢 صاعد: {market_summary.get('gainers_count', 'غير محدد')}")
                print(f"   🔴 هابط: {market_summary.get('losers_count', 'غير محدد')}")
                print(f"   ⚪ ثابت: {market_summary.get('unchanged_count', 'غير محدد')}")
                return True
            else:
                print("❌ لا توجد بيانات لملخص السوق")
                return False
        else:
            print(f"❌ خطأ في الحصول على البيانات: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎯 EGX Stock AI Oracle - تحديث توزيع السوق")
    print("=" * 60)
    
    # Calculate real distribution
    distribution = calculate_market_distribution()
    
    if distribution:
        # Update database
        if update_market_summary_in_db(distribution):
            # Test the updated data
            test_updated_frontend_data()
            
            print("\n" + "=" * 60)
            print("✅ تم تحديث توزيع السوق بنجاح!")
            print("🔄 يجب تحديث الفرونت اند ليعرض البيانات الجديدة")
        else:
            print("\n❌ فشل في تحديث قاعدة البيانات")
    else:
        print("\n❌ فشل في حساب توزيع السوق")
