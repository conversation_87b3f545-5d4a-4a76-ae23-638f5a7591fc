import React, { memo } from 'react';
import TradingViewWidget from 'react-tradingview-widget';

interface TradingViewChartProps {
  symbol: string;
  width?: string | number;
  height?: string | number;
  theme?: 'light' | 'dark';
  interval?: string;
  timezone?: string;
  locale?: string;
  range?: string;
  hide_side_toolbar?: boolean;
  allow_symbol_change?: boolean;
}

const TradingViewChart: React.FC<TradingViewChartProps> = memo(({
  symbol,
  width = '100%',
  height = 600,
  theme = 'light',
  interval = 'D',
  timezone = 'Africa/Cairo',
  locale = 'ar',
  range = '12M',
  hide_side_toolbar = false,
  allow_symbol_change = false
}) => {
  // تحويل رمز السهم المصري إلى تنسيق TradingView
  // الأسهم المصرية متاحة بصيغة EGX:SYMBOL أو CASE:SYMBOL
  const tradingViewSymbol = `EGX:${symbol}`;

  return (
    <div className="tradingview-widget-container w-full h-full">
      <TradingViewWidget
        symbol={tradingViewSymbol}
        theme={theme}
        autosize={false}
        width={width}
        height={height}
        interval={interval}
        timezone={timezone}
        locale={locale}
        toolbar_bg="#f1f3f6"
        enable_publishing={false}
        withdateranges={true}
        range={range}
        hide_side_toolbar={hide_side_toolbar}
        allow_symbol_change={allow_symbol_change}
        save_image={false}
        // إعدادات متقدمة للتحليل الفني
        studies={[
          'RSI@tv-basicstudies',
          'MASimple@tv-basicstudies', 
          'MACD@tv-basicstudies',
          'BB@tv-basicstudies',
          'Volume@tv-basicstudies'
        ]}
        // إعدادات الواجهة
        overrides={{
          "paneProperties.background": theme === 'dark' ? "#1e1e1e" : "#ffffff",
          "paneProperties.vertGridProperties.color": theme === 'dark' ? "#2d2d2d" : "#e6e6e6",
          "paneProperties.horzGridProperties.color": theme === 'dark' ? "#2d2d2d" : "#e6e6e6",
          "symbolWatermarkProperties.transparency": 90,
          "scalesProperties.textColor": theme === 'dark' ? "#b8b8b8" : "#131722",
          "mainSeriesProperties.candleStyle.upColor": "#4caf50",
          "mainSeriesProperties.candleStyle.downColor": "#f44336",
          "mainSeriesProperties.candleStyle.borderUpColor": "#4caf50",
          "mainSeriesProperties.candleStyle.borderDownColor": "#f44336",
          "mainSeriesProperties.candleStyle.wickUpColor": "#4caf50",
          "mainSeriesProperties.candleStyle.wickDownColor": "#f44336"
        }}
      />
      <div className="tradingview-widget-copyright mt-2">
        <a 
          href={`https://tradingview.com/symbols/EGX-${symbol}/`} 
          rel="noopener noreferrer" 
          target="_blank"
          className="text-xs text-blue-600 hover:underline"
        >
          شارت {symbol} من TradingView
        </a>
      </div>
    </div>
  );
});

TradingViewChart.displayName = 'TradingViewChart';

export default TradingViewChart;
