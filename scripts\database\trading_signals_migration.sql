-- Migration for Trading Signals System
-- This migration adds tables for webhook trading signals, live recommendations, and subscriptions

-- Create trading_signals table
CREATE TABLE IF NOT EXISTS trading_signals (
    id SERIAL PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL,
    signal_type VARCHAR(20) NOT NULL, -- buy, sell, tp1done, tp2done, tp3done, tsl
    
    -- Buy signal fields
    buy_price DECIMAL(10,2),
    tp1 DECIMAL(10,2),
    tp2 DECIMAL(10,2),
    tp3 DECIMAL(10,2),
    stop_loss DECIMAL(10,2),
    
    -- Sell signal fields
    sell_price DECIMAL(10,2),
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    confidence DECIMAL(5,2),
    raw_data JSONB, -- Store original webhook payload
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for trading_signals
CREATE INDEX IF NOT EXISTS idx_trading_signals_stock_code ON trading_signals(stock_code);
CREATE INDEX IF NOT EXISTS idx_trading_signals_signal_type ON trading_signals(signal_type);
CREATE INDEX IF NOT EXISTS idx_trading_signals_created_at ON trading_signals(created_at);
CREATE INDEX IF NOT EXISTS idx_trading_signals_status ON trading_signals(status);

-- Create live_recommendations table (التوصيات اللحظية)
CREATE TABLE IF NOT EXISTS live_recommendations (
    id SERIAL PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL,
    action VARCHAR(10) NOT NULL, -- buy, sell
    
    -- Price information
    entry_price DECIMAL(10,2) NOT NULL,
    current_price DECIMAL(10,2),
    
    -- Target levels
    tp1 DECIMAL(10,2),
    tp2 DECIMAL(10,2),
    tp3 DECIMAL(10,2),
    
    -- Target achievement tracking
    tp1_hit BOOLEAN DEFAULT FALSE,
    tp1_hit_at TIMESTAMP WITH TIME ZONE,
    tp2_hit BOOLEAN DEFAULT FALSE,
    tp2_hit_at TIMESTAMP WITH TIME ZONE,
    tp3_hit BOOLEAN DEFAULT FALSE,
    tp3_hit_at TIMESTAMP WITH TIME ZONE,
    
    -- Stop loss
    stop_loss DECIMAL(10,2),
    stop_loss_hit BOOLEAN DEFAULT FALSE,
    stop_loss_hit_at TIMESTAMP WITH TIME ZONE,
    
    -- Performance tracking
    current_pnl DECIMAL(8,4), -- Current profit/loss percentage
    max_pnl DECIMAL(8,4),     -- Maximum profit achieved
    min_pnl DECIMAL(8,4),     -- Maximum loss (drawdown)
    
    -- Status
    status VARCHAR(20) DEFAULT 'active', -- active, closed, cancelled, stopped
    close_reason VARCHAR(50),             -- tp1, tp2, tp3, stop_loss, manual, sell_signal
    
    -- Risk management
    risk_reward_ratio DECIMAL(6,2),
    position_size DECIMAL(10,2),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for live_recommendations
CREATE INDEX IF NOT EXISTS idx_live_recommendations_stock_code ON live_recommendations(stock_code);
CREATE INDEX IF NOT EXISTS idx_live_recommendations_status ON live_recommendations(status);
CREATE INDEX IF NOT EXISTS idx_live_recommendations_created_at ON live_recommendations(created_at);
CREATE INDEX IF NOT EXISTS idx_live_recommendations_action ON live_recommendations(action);
CREATE INDEX IF NOT EXISTS idx_live_recommendations_active ON live_recommendations(stock_code, status) WHERE status = 'active';

-- Create signal_performance table
CREATE TABLE IF NOT EXISTS signal_performance (
    id SERIAL PRIMARY KEY,
    signal_id INTEGER NOT NULL,
    stock_code VARCHAR(10) NOT NULL,
    
    -- Performance metrics
    total_return DECIMAL(8,4),
    max_profit DECIMAL(8,4),
    max_loss DECIMAL(8,4),
    duration_hours DECIMAL(10,2),
    
    -- Classification
    outcome VARCHAR(20), -- profit, loss, breakeven
    targets_hit INTEGER DEFAULT 0, -- Number of targets achieved (0-3)
    
    -- Analysis
    market_condition VARCHAR(20), -- bullish, bearish, sideways
    volatility DECIMAL(8,4),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for signal_performance
CREATE INDEX IF NOT EXISTS idx_signal_performance_signal_id ON signal_performance(signal_id);
CREATE INDEX IF NOT EXISTS idx_signal_performance_stock_code ON signal_performance(stock_code);
CREATE INDEX IF NOT EXISTS idx_signal_performance_outcome ON signal_performance(outcome);
CREATE INDEX IF NOT EXISTS idx_signal_performance_created_at ON signal_performance(created_at);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    telegram_id VARCHAR(20),
    
    -- Subscription details
    plan VARCHAR(20) NOT NULL, -- free, premium, professional, enterprise
    status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired, suspended
    
    -- Features access
    live_signals BOOLEAN DEFAULT FALSE,
    ai_alerts BOOLEAN DEFAULT FALSE,
    backtesting BOOLEAN DEFAULT FALSE,
    advanced_analytics BOOLEAN DEFAULT FALSE,
    telegram_alerts BOOLEAN DEFAULT FALSE,
    
    -- Limits
    alerts_limit INTEGER DEFAULT 5,      -- Monthly alerts limit
    alerts_used INTEGER DEFAULT 0,       -- Alerts used this month
    
    -- Subscription period
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    
    -- Payment tracking
    payment_method VARCHAR(20),
    last_payment_at TIMESTAMP WITH TIME ZONE,
    next_billing_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for user_subscriptions
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_telegram_id ON user_subscriptions(telegram_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_plan ON user_subscriptions(plan);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_active ON user_subscriptions(user_id, status) WHERE status = 'active';

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_trading_signals_updated_at 
    BEFORE UPDATE ON trading_signals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_live_recommendations_updated_at 
    BEFORE UPDATE ON live_recommendations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create view for active recommendations (التوصيات النشطة)
CREATE OR REPLACE VIEW active_recommendations AS
SELECT 
    lr.*,
    sm.name_ar,
    sm.name_en,
    sm.sector,
    rt.current_price as live_price,
    rt.change_percent as daily_change,
    rt.volume as current_volume,
    -- Calculate live P&L if we have current price
    CASE 
        WHEN lr.action = 'buy' AND rt.current_price IS NOT NULL THEN
            ROUND(((rt.current_price - lr.entry_price) / lr.entry_price * 100)::numeric, 2)
        WHEN lr.action = 'sell' AND rt.current_price IS NOT NULL THEN
            ROUND(((lr.entry_price - rt.current_price) / lr.entry_price * 100)::numeric, 2)
        ELSE lr.current_pnl
    END as live_pnl
FROM live_recommendations lr
LEFT JOIN stocks_master sm ON lr.stock_code = sm.symbol
LEFT JOIN stocks_realtime rt ON lr.stock_code = rt.symbol
WHERE lr.status = 'active'
ORDER BY lr.created_at DESC;

-- Create view for signal performance summary
CREATE OR REPLACE VIEW signal_performance_summary AS
SELECT 
    DATE(sp.created_at) as trade_date,
    COUNT(*) as total_signals,
    COUNT(CASE WHEN sp.outcome = 'profit' THEN 1 END) as profitable_signals,
    COUNT(CASE WHEN sp.outcome = 'loss' THEN 1 END) as loss_signals,
    ROUND(
        (COUNT(CASE WHEN sp.outcome = 'profit' THEN 1 END)::decimal / COUNT(*) * 100)::numeric, 
        2
    ) as win_rate,
    ROUND(AVG(sp.total_return)::numeric, 2) as avg_return,
    ROUND(SUM(sp.total_return)::numeric, 2) as total_return,
    ROUND(AVG(sp.targets_hit)::numeric, 1) as avg_targets_hit
FROM signal_performance sp
GROUP BY DATE(sp.created_at)
ORDER BY trade_date DESC;

-- Create function to calculate recommendation metrics
CREATE OR REPLACE FUNCTION calculate_recommendation_metrics(
    stock_symbol VARCHAR(10),
    current_market_price DECIMAL(10,2)
)
RETURNS TABLE (
    stock_code VARCHAR(10),
    entry_price DECIMAL(10,2),
    current_pnl DECIMAL(8,4),
    distance_to_tp1 DECIMAL(8,4),
    distance_to_sl DECIMAL(8,4),
    risk_reward_current DECIMAL(6,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lr.stock_code,
        lr.entry_price,
        CASE 
            WHEN lr.action = 'buy' THEN
                ROUND(((current_market_price - lr.entry_price) / lr.entry_price * 100)::numeric, 4)
            ELSE
                ROUND(((lr.entry_price - current_market_price) / lr.entry_price * 100)::numeric, 4)
        END as current_pnl,
        
        CASE 
            WHEN lr.action = 'buy' AND lr.tp1 IS NOT NULL THEN
                ROUND(((lr.tp1 - current_market_price) / current_market_price * 100)::numeric, 4)
            WHEN lr.action = 'sell' AND lr.tp1 IS NOT NULL THEN
                ROUND(((current_market_price - lr.tp1) / current_market_price * 100)::numeric, 4)
            ELSE NULL
        END as distance_to_tp1,
        
        CASE 
            WHEN lr.action = 'buy' AND lr.stop_loss IS NOT NULL THEN
                ROUND(((current_market_price - lr.stop_loss) / current_market_price * 100)::numeric, 4)
            WHEN lr.action = 'sell' AND lr.stop_loss IS NOT NULL THEN
                ROUND(((lr.stop_loss - current_market_price) / current_market_price * 100)::numeric, 4)
            ELSE NULL
        END as distance_to_sl,
        
        lr.risk_reward_ratio
        
    FROM live_recommendations lr
    WHERE lr.stock_code = stock_symbol 
    AND lr.status = 'active';
END;
$$ LANGUAGE plpgsql;

-- Insert sample subscription plans (for reference)
INSERT INTO user_subscriptions (user_id, plan, live_signals, ai_alerts, backtesting, advanced_analytics, telegram_alerts, alerts_limit) 
VALUES 
    ('demo_user_free', 'free', FALSE, FALSE, FALSE, FALSE, FALSE, 5),
    ('demo_user_premium', 'premium', TRUE, TRUE, TRUE, FALSE, TRUE, 50),
    ('demo_user_pro', 'professional', TRUE, TRUE, TRUE, TRUE, TRUE, 200),
    ('demo_user_enterprise', 'enterprise', TRUE, TRUE, TRUE, TRUE, TRUE, 1000)
ON CONFLICT DO NOTHING;

-- Grant permissions (adjust according to your user setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_api_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_api_user;

COMMENT ON TABLE trading_signals IS 'Raw trading signals received from webhook';
COMMENT ON TABLE live_recommendations IS 'Active trading recommendations displayed to users (التوصيات اللحظية)';
COMMENT ON TABLE signal_performance IS 'Historical performance tracking for closed signals';
COMMENT ON TABLE user_subscriptions IS 'User subscription plans and feature access';
COMMENT ON VIEW active_recommendations IS 'Live view of active recommendations with real-time prices';
COMMENT ON VIEW signal_performance_summary IS 'Daily performance summary for signals';

-- Migration completed successfully
SELECT 'Trading Signals Migration Completed Successfully!' as status;
