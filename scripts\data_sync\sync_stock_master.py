#!/usr/bin/env python3
"""
EGX Stock AI Oracle - Update Stock Names
Syncs stock names from real-time data to stocks_master
"""

import os
import sys
import logging
from datetime import datetime, timezone
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def sync_stock_master_from_realtime():
    """Sync stocks_master table from stocks_realtime data"""
    try:
        logger.info("🔄 Syncing stocks_master from real-time data...")
        
        # Get all real-time symbols
        realtime_result = supabase.table("stocks_realtime").select("symbol").execute()
        realtime_symbols = [item['symbol'] for item in realtime_result.data]
        
        logger.info(f"📊 Found {len(realtime_symbols)} symbols in real-time data")
        
        # Get existing stocks in master
        master_result = supabase.table("stocks_master").select("symbol").execute()
        existing_symbols = [item['symbol'] for item in master_result.data]
        
        logger.info(f"🏢 Found {len(existing_symbols)} symbols in stocks_master")
        
        # Find missing symbols
        missing_symbols = [sym for sym in realtime_symbols if sym not in existing_symbols]
        
        if missing_symbols:
            logger.info(f"➕ Adding {len(missing_symbols)} missing symbols to stocks_master")
            
            # Prepare batch insert
            batch_data = []
            for symbol in missing_symbols:
                batch_data.append({
                    "symbol": symbol,
                    "name": symbol,  # Use symbol as name initially
                    "market": "EGX",
                    "is_active": True,
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "updated_at": datetime.now(timezone.utc).isoformat()
                })
            
            # Insert in batches
            batch_size = 50
            total_inserted = 0
            
            for i in range(0, len(batch_data), batch_size):
                batch = batch_data[i:i+batch_size]
                result = supabase.table("stocks_master").insert(batch).execute()
                total_inserted += len(batch)
                logger.info(f"✅ Inserted batch of {len(batch)} stocks")
            
            logger.info(f"🎉 Successfully added {total_inserted} stocks to master table")
        else:
            logger.info("✅ All real-time symbols already exist in stocks_master")
        
        # Verify final count
        final_result = supabase.table("stocks_master").select("symbol").execute()
        final_count = len(final_result.data)
        
        logger.info(f"📈 Final stocks_master count: {final_count}")
        
        return final_count
        
    except Exception as e:
        logger.error(f"❌ Error syncing stocks_master: {e}")
        return 0

def update_market_summary():
    """Update market summary in market_indices"""
    try:
        logger.info("📊 Updating market summary...")
        
        # Get real-time data for calculations
        realtime_result = supabase.table("stocks_realtime").select("*").execute()
        stocks = realtime_result.data
        
        if not stocks:
            logger.warning("⚠️ No real-time data found")
            return
        
        # Calculate market metrics
        total_volume = sum(stock.get('volume', 0) for stock in stocks if stock.get('volume'))
        total_turnover = sum(stock.get('turnover', 0) for stock in stocks if stock.get('turnover'))
        total_symbols = len(stocks)
        
        # Count gaining/losing stocks
        gaining_stocks = len([s for s in stocks if s.get('change_percent', 0) > 0])
        losing_stocks = len([s for s in stocks if s.get('change_percent', 0) < 0])
        unchanged_stocks = total_symbols - gaining_stocks - losing_stocks
        
        # Market summary data
        market_data = {
            "symbol": "EGX_MARKET",
            "name": "Egyptian Exchange Market Summary",
            "name_ar": "ملخص البورصة المصرية",
            "volume": total_volume,
            "turnover": total_turnover,
            "constituents_count": total_symbols,
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        # Upsert market summary
        supabase.table("market_indices").upsert(market_data).execute()
        
        logger.info(f"✅ Market summary updated:")
        logger.info(f"   📊 Total symbols: {total_symbols}")
        logger.info(f"   📈 Gaining: {gaining_stocks}")
        logger.info(f"   📉 Losing: {losing_stocks}")
        logger.info(f"   ➡️ Unchanged: {unchanged_stocks}")
        logger.info(f"   💹 Volume: {total_volume:,}")
        logger.info(f"   💰 Turnover: {total_turnover:,.2f}")
        
    except Exception as e:
        logger.error(f"❌ Error updating market summary: {e}")

def main():
    logger.info("🚀 EGX Stock AI Oracle - Stock Master Sync")
    logger.info("=" * 50)
    
    # Sync stocks_master
    final_count = sync_stock_master_from_realtime()
    
    # Update market summary
    update_market_summary()
    
    logger.info("=" * 50)
    logger.info(f"🎯 Sync completed! stocks_master now has {final_count} stocks")
    logger.info("🌐 الموقع الآن يجب أن يعرض البيانات الحقيقية!")
    logger.info("   (Website should now display real data!)")

if __name__ == "__main__":
    main()
