
-- Create a table for storing backtesting requests and results
CREATE TABLE public.analytics_backtests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid, -- optional: link to user's id in your own users table, not auth.users
  symbol character varying NOT NULL,
  strategy_name character varying NOT NULL,
  timeframe character varying,
  parameters jsonb, -- any parameters/settings used by the backtest
  result_summary jsonb, -- summary statistics (return, sharpe, win rate, etc)
  result_trades jsonb,  -- arrays of trades or similar details
  started_at timestamp with time zone DEFAULT now(),
  finished_at timestamp with time zone,
  status character varying DEFAULT 'completed' -- running/completed/failed, etc.
);

-- Optional: allow insertion by anyone until a user system is set up, or write RLS later when you add authentication.
