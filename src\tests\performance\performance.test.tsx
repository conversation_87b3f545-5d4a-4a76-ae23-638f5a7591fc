import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import React, { useState, useEffect } from 'react';
import { TestWrapper } from '../test-utils';
import { PerformanceMonitor } from '../../lib/performance';

// Test data types
interface StockData {
  id: number;
  symbol: string;
  price: number;
  change: number;
  volume: number;
  timestamp: string;
}

interface PriceHistory {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Test data generators
const generateLargeDataset = (size: number): StockData[] => {
  return Array.from({ length: size }, (_, i) => ({
    id: i,
    symbol: `STOCK${i}`,
    price: Math.random() * 100,
    change: Math.random() * 10 - 5,
    volume: Math.floor(Math.random() * 1000000),
    timestamp: new Date().toISOString()
  }));
};

const generatePriceHistory = (days: number): PriceHistory[] => {
  return Array.from({ length: days }, (_, i) => ({
    date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
    open: Math.random() * 100,
    high: Math.random() * 100,
    low: Math.random() * 100,
    close: Math.random() * 100,
    volume: Math.floor(Math.random() * 1000000)
  }));
};

// Mock components for performance testing
const HeavyDataComponent: React.FC<{ data: StockData[] }> = ({ data }) => {
  const startTime = performance.now();
  
  // Simulate heavy computation
  const processedData = data.map(item => ({
    ...item,
    calculated: item.price * item.volume,
    formatted: new Intl.NumberFormat().format(item.price)
  }));
  
  const endTime = performance.now();
  
  return (
    <div data-testid="heavy-component" data-render-time={endTime - startTime}>
      {processedData.slice(0, 10).map(item => (
        <div key={item.id} data-testid="data-item">
          {item.symbol}: {item.formatted}
        </div>
      ))}
    </div>
  );
};

const VirtualizedListComponent: React.FC<{ items: StockData[] }> = ({ items }) => {
  const [visibleItems, setVisibleItems] = useState(items.slice(0, 100));
  
  useEffect(() => {
    // Simulate virtualization - only render visible items
    const timer = setTimeout(() => {
      setVisibleItems(items.slice(0, 100));
    }, 16); // ~60fps
    
    return () => clearTimeout(timer);
  }, [items]);
  
  return (
    <div data-testid="virtualized-list" className="h-96 overflow-auto">
      {visibleItems.map(item => (
        <div key={item.id} data-testid="virtual-item" className="h-10">
          {item.symbol}: ${item.price.toFixed(2)}
        </div>
      ))}
    </div>
  );
};

describe('Performance Tests', () => {
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    performanceMonitor = new PerformanceMonitor();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    // No cleanup method needed
  });

  describe('Component Rendering Performance', () => {
    it('renders large datasets efficiently', async () => {
      const largeData = generateLargeDataset(1000);
      const startTime = performance.now();

      render(
        <TestWrapper>
          <HeavyDataComponent data={largeData} />
        </TestWrapper>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Rendering should complete within reasonable time (< 100ms)
      expect(renderTime).toBeLessThan(100);

      // Component should be rendered
      expect(screen.getByTestId('heavy-component')).toBeInTheDocument();
      
      // Should limit visible items for performance
      const items = screen.getAllByTestId('data-item');
      expect(items.length).toBeLessThanOrEqual(10);
    });    it('handles frequent updates without performance degradation', async () => {
      const initialData = generateLargeDataset(100);
      const renderTimes: number[] = [];

      const { rerender } = render(
        <TestWrapper>
          <HeavyDataComponent data={initialData} />
        </TestWrapper>
      );

      // Perform multiple re-renders with updates
      for (let i = 0; i < 10; i++) {
        const startTime = performance.now();
        const updatedData = generateLargeDataset(100);
        
        rerender(
          <TestWrapper>
            <HeavyDataComponent data={updatedData} />
          </TestWrapper>
        );
        
        const endTime = performance.now();
        renderTimes.push(endTime - startTime);
        
        // Fast forward to next frame
        vi.advanceTimersByTime(16);
      }

      // Average render time should stay reasonable
      const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
      expect(avgRenderTime).toBeLessThan(50);

      // Later renders shouldn't be significantly slower than early ones
      const firstHalfAvg = renderTimes.slice(0, 5).reduce((a, b) => a + b, 0) / 5;
      const secondHalfAvg = renderTimes.slice(5).reduce((a, b) => a + b, 0) / 5;
      expect(secondHalfAvg).toBeLessThan(firstHalfAvg * 2);
    });

    it('uses virtualization for large lists effectively', async () => {
      const largeList = generateLargeDataset(10000);

      render(
        <TestWrapper>
          <VirtualizedListComponent items={largeList} />      
        </TestWrapper>
      );

      // Should only render a subset of items
      const renderedItems = screen.getAllByTestId('virtual-item');
      expect(renderedItems.length).toBeLessThanOrEqual(100);
      expect(renderedItems.length).toBeGreaterThan(0);

      // Container should be present
      expect(screen.getByTestId('virtualized-list')).toBeInTheDocument();
    });
  });
  describe('Memory Usage', () => {
    it('cleans up properly after component unmount', async () => {
      const largeData = generateLargeDataset(1000);
      
      const { unmount } = render(
        <TestWrapper>
          <HeavyDataComponent data={largeData} />
        </TestWrapper>
      );

      // Measure memory before cleanup
      const memUsage = PerformanceMonitor.getMemoryUsage();
      const beforeCleanup = memUsage.usedJSHeapSize || 0;
      
      unmount();
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      // Wait for cleanup
      await waitFor(() => {
        const afterMemUsage = PerformanceMonitor.getMemoryUsage();
        const afterCleanup = afterMemUsage.usedJSHeapSize || 0;
        // Memory should not grow significantly after cleanup
        if (beforeCleanup > 0 && afterCleanup > 0) {
          expect(afterCleanup).toBeLessThan(beforeCleanup * 1.1);
        }
      });
    });

    it('prevents memory leaks with frequent data updates', async () => {
      const initialMemUsage = PerformanceMonitor.getMemoryUsage();
      const initialMemory = initialMemUsage.usedJSHeapSize || 0;

      // Create and update component multiple times
      for (let i = 0; i < 5; i++) {
        const data = generateLargeDataset(200);
        
        const { unmount } = render(
          <TestWrapper>
            <HeavyDataComponent data={data} />
          </TestWrapper>
        );
        
        unmount();
        vi.advanceTimersByTime(100);
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemUsage = PerformanceMonitor.getMemoryUsage();
      const finalMemory = finalMemUsage.usedJSHeapSize || 0;
      
      // Memory growth should be reasonable
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryGrowth = finalMemory - initialMemory;
        expect(memoryGrowth).toBeLessThan(initialMemory * 0.5); // Less than 50% growth
      }
    });
  });

  describe('Real-time Data Performance', () => {    it('handles high-frequency price updates efficiently', async () => {
      const priceUpdates = Array.from({ length: 100 }, (_, i) => ({
        symbol: `STOCK${i % 10}`,
        price: Math.random() * 100,
        timestamp: Date.now() + i
      }));

      const updateTimes: number[] = [];
      
      // Simulate rapid price updates
      for (const update of priceUpdates) {
        const startTime = performance.now();
        
        // Simulate price update processing
        const processed = {
          ...update,
          formatted: new Intl.NumberFormat().format(update.price),
          change: Math.random() * 2 - 1
        };
        
        const endTime = performance.now();
        updateTimes.push(endTime - startTime);
        
        vi.advanceTimersByTime(10); // 100 updates per second
      }

      // Average update processing time should be minimal
      const avgUpdateTime = updateTimes.reduce((a, b) => a + b, 0) / updateTimes.length;
      expect(avgUpdateTime).toBeLessThan(1); // Less than 1ms per update
    });    it('maintains 60fps during chart animations', async () => {
      const chartData = generatePriceHistory(365);
      const frameTimes: number[] = [];
      let lastTime = performance.now();

      // Simulate 60fps animation for 1 second
      for (let i = 0; i < 60; i++) {
        const currentTime = performance.now();
        frameTimes.push(currentTime - lastTime);
        lastTime = currentTime;
        
        // Simulate chart update
        const updatedData = [...chartData, {
          date: new Date().toISOString(),
          open: Math.random() * 100,
          high: Math.random() * 100,
          low: Math.random() * 100,
          close: Math.random() * 100,
          volume: Math.floor(Math.random() * 1000000)
        }];
        
        vi.advanceTimersByTime(16.67); // ~60fps
      }

      // Average frame time should be close to 60fps (16.67ms)
      const avgFrameTime = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length;
      expect(avgFrameTime).toBeLessThan(20); // Allow some variance
      
      // No frame should be significantly longer (avoid frame drops)
      const maxFrameTime = Math.max(...frameTimes);
      expect(maxFrameTime).toBeLessThan(33); // 30fps minimum
    });
  });

  describe('Bundle Size and Load Performance', () => {
    it('measures component bundle impact', () => {
      // This would typically be measured at build time
      // For testing, we simulate bundle size checks
      
      const componentSize = JSON.stringify(HeavyDataComponent.toString()).length;
      expect(componentSize).toBeLessThan(10000); // Keep components reasonable
    });

    it('validates lazy loading effectiveness', async () => {
      const loadStart = performance.now();
      
      // Simulate dynamic import
      const LazyComponent = React.lazy(() => 
        Promise.resolve({ 
          default: () => <div data-testid="lazy-component">Lazy Loaded</div> 
        })
      );
      
      render(
        <TestWrapper>
          <React.Suspense fallback={<div>Loading...</div>}>
            <LazyComponent />
          </React.Suspense>
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('lazy-component')).toBeInTheDocument();
        const loadEnd = performance.now();
        expect(loadEnd - loadStart).toBeLessThan(100); // Should load quickly
      });
    });
  });
  describe('PerformanceMonitor Integration', () => {
    it('tracks render performance accurately', async () => {
      const measurementId = PerformanceMonitor.startMeasurement('test-component');
      
      const data = generateLargeDataset(100);
      render(
        <TestWrapper>
          <HeavyDataComponent data={data} />
        </TestWrapper>
      );
      
      const renderTime = PerformanceMonitor.endMeasurement(measurementId, 'test-component');
      
      expect(renderTime).toBeGreaterThanOrEqual(0);
      expect(renderTime).toBeLessThan(1000); // Reasonable render time
    });

    it('provides performance statistics', () => {
      // Create some measurements
      for (let i = 0; i < 5; i++) {
        const measurementId = PerformanceMonitor.startMeasurement('test-operation');
        // Simulate some work
        const start = performance.now();
        while (performance.now() - start < 10) {
          // Busy wait for 10ms
        }
        PerformanceMonitor.endMeasurement(measurementId, 'test-operation');
      }
        const stats = PerformanceMonitor.getStats('test-operation');
      
      expect(stats).toBeTruthy();
      if (stats) {
        expect(stats.count).toBe(5);
        expect(stats.avg).toBeGreaterThan(0);
        expect(stats.min).toBeGreaterThanOrEqual(0);
        expect(stats.max).toBeGreaterThanOrEqual(stats.min);
      }
    });
  });
});
