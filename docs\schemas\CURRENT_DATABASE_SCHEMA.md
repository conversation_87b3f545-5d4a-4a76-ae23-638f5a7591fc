# EGX Stock AI Oracle - Updated Database Schema (Current)

## Database: Supabase PostgreSQL
## Last Updated: June 15, 2025
## Project ID: tbzbrujqjwpatbzffmwq

This document reflects the current state of the Supabase database schema as implemented through migrations.

---

## Core Stock Data Tables

### Table: `stocks_master`
**Purpose**: Master registry of all stocks
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | SERIAL | No | | Primary Key |
| symbol | VARCHAR(20) | No | | Unique |
| name | VARCHAR(200) | No | | |
| name_ar | VARCHAR(200) | Yes | | Arabic name |
| sector | VARCHAR(100) | Yes | | |
| sector_ar | VARCHAR(100) | Yes | | Arabic sector |
| industry | VARCHAR(100) | Yes | | |
| market | VARCHAR(50) | Yes | 'EGX' | |
| is_active | BOOLEAN | Yes | true | |
| listing_date | DATE | Yes | | |
| isin | VARCHAR(20) | Yes | | |
| free_float_shares | BIGINT | Yes | | |
| total_shares | BIGINT | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

### Table: `stocks_historical`
**Purpose**: Daily OHLCV historical data
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | SERIAL | No | | Primary Key |
| symbol | VARCHAR(20) | No | | |
| date | DATE | No | | Unique with symbol |
| open | DECIMAL(12,4) | No | | |
| high | DECIMAL(12,4) | No | | |
| low | DECIMAL(12,4) | No | | |
| close | DECIMAL(12,4) | No | | |
| volume | BIGINT | Yes | 0 | |
| open_interest | BIGINT | Yes | 0 | |
| adjusted_close | DECIMAL(12,4) | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |

**Indexes**:
- `idx_stocks_historical_symbol_date` ON (symbol, date DESC)
- `idx_stocks_historical_date` ON (date DESC)

### Table: `stocks_realtime`
**Purpose**: Current real-time market data
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| symbol | VARCHAR(20) | No | | Primary Key |
| current_price | DECIMAL(12,4) | Yes | | |
| open_price | DECIMAL(12,4) | Yes | | |
| high_price | DECIMAL(12,4) | Yes | | |
| low_price | DECIMAL(12,4) | Yes | | |
| previous_close | DECIMAL(12,4) | Yes | | |
| change_amount | DECIMAL(12,4) | Yes | | |
| change_percent | DECIMAL(8,4) | Yes | | |
| volume | BIGINT | Yes | | |
| turnover | DECIMAL(15,2) | Yes | | |
| trades_count | INTEGER | Yes | | |
| bid_price | DECIMAL(12,4) | Yes | | |
| ask_price | DECIMAL(12,4) | Yes | | |
| bid_volume | BIGINT | Yes | | |
| ask_volume | BIGINT | Yes | | |
| last_trade_time | TIMESTAMPTZ | Yes | | |
| market_cap | DECIMAL(15,2) | Yes | | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

**Indexes**:
- `idx_stocks_realtime_updated` ON (updated_at DESC)

### Table: `stocks_financials`
**Purpose**: Financial statements and ratios
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | SERIAL | No | | Primary Key |
| symbol | VARCHAR(20) | No | | |
| fiscal_year | INTEGER | Yes | | |
| fiscal_quarter | INTEGER | Yes | | |
| market_cap | DECIMAL(15,2) | Yes | | |
| pe_ratio | DECIMAL(8,4) | Yes | | |
| eps_ttm | DECIMAL(8,4) | Yes | | |
| eps_growth_yoy | DECIMAL(8,4) | Yes | | |
| dividend_yield | DECIMAL(8,4) | Yes | | |
| book_value_per_share | DECIMAL(8,4) | Yes | | |
| price_to_book | DECIMAL(8,4) | Yes | | |
| revenue_ttm | DECIMAL(15,2) | Yes | | |
| net_income_ttm | DECIMAL(15,2) | Yes | | |
| total_assets | DECIMAL(15,2) | Yes | | |
| total_debt | DECIMAL(15,2) | Yes | | |
| free_cash_flow | DECIMAL(15,2) | Yes | | |
| roe | DECIMAL(8,4) | Yes | | |
| roa | DECIMAL(8,4) | Yes | | |
| debt_to_equity | DECIMAL(8,4) | Yes | | |
| current_ratio | DECIMAL(8,4) | Yes | | |
| gross_margin | DECIMAL(8,4) | Yes | | |
| operating_margin | DECIMAL(8,4) | Yes | | |
| net_margin | DECIMAL(8,4) | Yes | | |
| beta | DECIMAL(8,4) | Yes | | |
| analyst_rating | VARCHAR(20) | Yes | | |
| target_price | DECIMAL(12,4) | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

### Table: `technical_indicators`
**Purpose**: Technical analysis indicators
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | SERIAL | No | | Primary Key |
| symbol | VARCHAR(20) | No | | |
| date | DATE | No | | |
| timeframe | VARCHAR(10) | Yes | 'D' | |
| sma_5 | DECIMAL(12,4) | Yes | | |
| sma_10 | DECIMAL(12,4) | Yes | | |
| sma_20 | DECIMAL(12,4) | Yes | | |
| sma_50 | DECIMAL(12,4) | Yes | | |
| sma_100 | DECIMAL(12,4) | Yes | | |
| sma_200 | DECIMAL(12,4) | Yes | | |
| ema_12 | DECIMAL(12,4) | Yes | | |
| ema_26 | DECIMAL(12,4) | Yes | | |
| rsi_14 | DECIMAL(8,4) | Yes | | |
| macd | DECIMAL(8,4) | Yes | | |
| macd_signal | DECIMAL(8,4) | Yes | | |
| macd_histogram | DECIMAL(8,4) | Yes | | |
| bb_upper | DECIMAL(12,4) | Yes | | |
| bb_middle | DECIMAL(12,4) | Yes | | |
| bb_lower | DECIMAL(12,4) | Yes | | |
| stoch_k | DECIMAL(8,4) | Yes | | |
| stoch_d | DECIMAL(8,4) | Yes | | |
| atr_14 | DECIMAL(8,4) | Yes | | |
| adx_14 | DECIMAL(8,4) | Yes | | |
| cci_20 | DECIMAL(8,4) | Yes | | |
| williams_r | DECIMAL(8,4) | Yes | | |
| momentum_10 | DECIMAL(8,4) | Yes | | |
| roc_10 | DECIMAL(8,4) | Yes | | |
| volume_sma_20 | DECIMAL(15,2) | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |

**Indexes**:
- `idx_technical_indicators_symbol_date` ON (symbol, date DESC, timeframe)
- UNIQUE(symbol, date, timeframe)

### Table: `market_indices`
**Purpose**: Market indices (EGX30, EGX70, etc.)
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| symbol | VARCHAR(20) | No | | Primary Key |
| name | VARCHAR(200) | No | | |
| name_ar | VARCHAR(200) | Yes | | |
| current_value | DECIMAL(12,4) | Yes | | |
| open_value | DECIMAL(12,4) | Yes | | |
| high_value | DECIMAL(12,4) | Yes | | |
| low_value | DECIMAL(12,4) | Yes | | |
| previous_close | DECIMAL(12,4) | Yes | | |
| change_amount | DECIMAL(12,4) | Yes | | |
| change_percent | DECIMAL(8,4) | Yes | | |
| market_cap | DECIMAL(18,2) | Yes | | |
| volume | BIGINT | Yes | | |
| turnover | DECIMAL(15,2) | Yes | | |
| constituents_count | INTEGER | Yes | | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

---

## User & Portfolio Management Tables

### Table: `users`
**Purpose**: User accounts and API management
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| email | VARCHAR(255) | No | | Unique |
| password_hash | VARCHAR(255) | Yes | | |
| full_name | VARCHAR(200) | Yes | | |
| subscription_tier | VARCHAR(50) | Yes | 'free' | |
| api_key | VARCHAR(100) | Yes | | Unique |
| api_calls_used | INTEGER | Yes | 0 | |
| api_calls_limit | INTEGER | Yes | 100 | |
| api_calls_reset_date | DATE | Yes | CURRENT_DATE | |
| phone | VARCHAR(20) | Yes | | |
| country | VARCHAR(100) | Yes | 'Egypt' | |
| preferred_language | VARCHAR(10) | Yes | 'ar' | |
| is_active | BOOLEAN | Yes | true | |
| email_verified | BOOLEAN | Yes | false | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

**RLS**: Enabled - Users can view/update own records

### Table: `user_portfolios`
**Purpose**: User investment portfolios
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| user_id | UUID | Yes | | FK → users.id |
| name | VARCHAR(200) | No | | |
| description | TEXT | Yes | | |
| is_default | BOOLEAN | Yes | false | |
| total_value | DECIMAL(15,2) | Yes | 0 | |
| total_cost | DECIMAL(15,2) | Yes | 0 | |
| total_profit_loss | DECIMAL(15,2) | Yes | 0 | |
| currency | VARCHAR(10) | Yes | 'EGP' | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

**RLS**: Enabled - Users can CRUD own portfolios
**Indexes**: `idx_user_portfolios_user_id` ON (user_id)

### Table: `portfolio_holdings`
**Purpose**: Individual stock positions in portfolios
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| portfolio_id | UUID | Yes | | FK → user_portfolios.id |
| symbol | VARCHAR(20) | No | | |
| quantity | DECIMAL(15,4) | No | | |
| average_buy_price | DECIMAL(12,4) | No | | |
| current_price | DECIMAL(12,4) | Yes | | |
| total_cost | DECIMAL(15,2) | Yes | | |
| current_value | DECIMAL(15,2) | Yes | | |
| profit_loss | DECIMAL(15,2) | Yes | | |
| profit_loss_percent | DECIMAL(8,4) | Yes | | |
| purchase_date | DATE | Yes | | |
| notes | TEXT | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

**RLS**: Enabled - Users can CRUD holdings in own portfolios
**Indexes**: `idx_portfolio_holdings_portfolio` ON (portfolio_id)

### Table: `price_alerts`
**Purpose**: Price alert notifications for users
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| user_id | UUID | Yes | | FK → users.id |
| symbol | VARCHAR(20) | No | | |
| alert_type | VARCHAR(20) | No | | |
| target_value | DECIMAL(12,4) | No | | |
| current_value | DECIMAL(12,4) | Yes | | |
| condition_met | BOOLEAN | Yes | false | |
| is_active | BOOLEAN | Yes | true | |
| notification_methods | JSON | Yes | | |
| message_template | TEXT | Yes | | |
| triggered_at | TIMESTAMPTZ | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |

**RLS**: Enabled - Users can CRUD own alerts
**Indexes**: `idx_price_alerts_user_active` ON (user_id, is_active)

### Table: `user_notifications`
**Purpose**: Notification history for users
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| user_id | UUID | Yes | | FK → users.id |
| title | VARCHAR(200) | No | | |
| message | TEXT | No | | |
| type | VARCHAR(50) | Yes | | |
| priority | VARCHAR(20) | Yes | 'medium' | |
| is_read | BOOLEAN | Yes | false | |
| related_symbol | VARCHAR(20) | Yes | | |
| action_url | TEXT | Yes | | |
| metadata | JSON | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |

**RLS**: Enabled

---

## Paper Trading Tables

### Table: `paper_trading_accounts`
**Purpose**: Virtual trading accounts
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| user_id | UUID | Yes | | FK → users.id |
| account_name | VARCHAR(200) | No | | |
| initial_balance | DECIMAL(15,2) | Yes | 100000 | |
| current_balance | DECIMAL(15,2) | Yes | | |
| total_profit_loss | DECIMAL(15,2) | Yes | 0 | |
| total_trades | INTEGER | Yes | 0 | |
| winning_trades | INTEGER | Yes | 0 | |
| losing_trades | INTEGER | Yes | 0 | |
| win_rate | DECIMAL(6,4) | Yes | 0 | |
| sharpe_ratio | DECIMAL(8,4) | Yes | 0 | |
| max_drawdown | DECIMAL(8,4) | Yes | 0 | |
| is_active | BOOLEAN | Yes | true | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |
| updated_at | TIMESTAMPTZ | Yes | NOW() | |

**RLS**: Enabled

### Table: `paper_trades`
**Purpose**: Individual virtual trades
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| account_id | UUID | Yes | | FK → paper_trading_accounts.id |
| symbol | VARCHAR(20) | No | | |
| trade_type | VARCHAR(10) | No | | |
| quantity | DECIMAL(15,4) | No | | CHK > 0 |
| entry_price | DECIMAL(12,4) | No | | CHK > 0 |
| exit_price | DECIMAL(12,4) | Yes | | |
| stop_loss | DECIMAL(12,4) | Yes | | |
| take_profit | DECIMAL(12,4) | Yes | | |
| profit_loss | DECIMAL(15,2) | Yes | 0 | |
| commission | DECIMAL(8,2) | Yes | 0 | |
| status | VARCHAR(20) | Yes | 'open' | |
| entry_time | TIMESTAMPTZ | Yes | NOW() | |
| exit_time | TIMESTAMPTZ | Yes | | |
| notes | TEXT | Yes | | |
| strategy_name | VARCHAR(200) | Yes | | |

**RLS**: Enabled
**Constraints**: 
- `chk_positive_entry_price` (entry_price > 0)
- `chk_positive_quantity` (quantity > 0)

---

## Analytics & AI Tables

### Table: `analytics_ml_predictions`
**Purpose**: AI/ML price predictions
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| symbol | VARCHAR | No | | |
| prediction | VARCHAR | No | | e.g., 'صعود','محايد' |
| confidence | NUMERIC | No | | |
| target_price | NUMERIC | No | | |
| timeframe | VARCHAR | No | | |
| factors | JSONB | Yes | | Array of factors |
| generated_at | TIMESTAMPTZ | No | NOW() | |

**RLS**: Enabled - Public read access
**Indexes**: ON (symbol)

### Table: `analytics_patterns`
**Purpose**: Technical pattern recognition
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| symbol | VARCHAR | No | | |
| pattern | VARCHAR | No | | |
| reliability | NUMERIC | No | | % |
| expected | VARCHAR | No | | e.g., 'كسر علوي' |
| timeframe | VARCHAR | No | | |
| detected_at | TIMESTAMPTZ | No | NOW() | |

**RLS**: Enabled - Public read access
**Indexes**: ON (symbol)

### Table: `analytics_correlations`
**Purpose**: Stock correlation analysis
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| stock1 | VARCHAR | No | | |
| stock2 | VARCHAR | No | | |
| correlation | NUMERIC | No | | |
| strength | VARCHAR | No | | e.g., 'قوية','متوسطة' |
| analyzed_at | TIMESTAMPTZ | No | NOW() | |

**RLS**: Enabled - Public read access
**Indexes**: ON (stock1, stock2)

### Table: `analytics_volatility`
**Purpose**: Volatility analysis
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| symbol | VARCHAR | No | | |
| current_volatility | NUMERIC | No | | |
| avg_volatility | NUMERIC | No | | |
| trend | VARCHAR | No | | e.g., 'مرتفع','منخفض' |
| analyzed_at | TIMESTAMPTZ | No | NOW() | |

**RLS**: Enabled - Public read access
**Indexes**: ON (symbol)

### Table: `analytics_backtests`
**Purpose**: Strategy backtesting results
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| user_id | UUID | Yes | | Optional user link |
| symbol | VARCHAR | No | | |
| strategy_name | VARCHAR | No | | |
| timeframe | VARCHAR | Yes | | |
| parameters | JSONB | Yes | | Strategy parameters |
| result_summary | JSONB | Yes | | Summary statistics |
| result_trades | JSONB | Yes | | Trade details |
| started_at | TIMESTAMPTZ | Yes | NOW() | |
| finished_at | TIMESTAMPTZ | Yes | | |
| status | VARCHAR | Yes | 'completed' | |

---

## Content & API Tables

### Table: `market_news`
**Purpose**: Market news and analysis
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| title | VARCHAR(500) | No | | |
| title_ar | VARCHAR(500) | Yes | | |
| content | TEXT | Yes | | |
| content_ar | TEXT | Yes | | |
| summary | TEXT | Yes | | |
| summary_ar | TEXT | Yes | | |
| source | VARCHAR(200) | Yes | | |
| author | VARCHAR(200) | Yes | | |
| published_at | TIMESTAMPTZ | Yes | | |
| sentiment_score | DECIMAL(4,2) | Yes | | |
| impact_score | DECIMAL(4,2) | Yes | | |
| related_symbols | VARCHAR(500)[] | Yes | | |
| categories | VARCHAR(100)[] | Yes | | |
| url | TEXT | Yes | | |
| image_url | TEXT | Yes | | |
| view_count | INTEGER | Yes | 0 | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |

**Indexes**:
- `idx_market_news_published` ON (published_at DESC)
- `idx_market_news_symbols` USING GIN(related_symbols)

### Table: `api_usage_logs`
**Purpose**: API usage tracking and analytics
| Column | Type | Nullable | Default | Notes |
|--------|------|----------|---------|-------|
| id | UUID | No | gen_random_uuid() | Primary Key |
| user_id | UUID | Yes | | FK → users.id |
| api_key | VARCHAR(100) | Yes | | |
| endpoint | VARCHAR(200) | No | | |
| method | VARCHAR(10) | No | | |
| query_params | JSON | Yes | | |
| response_status | INTEGER | Yes | | |
| response_time_ms | INTEGER | Yes | | |
| data_points_returned | INTEGER | Yes | | |
| ip_address | INET | Yes | | |
| user_agent | TEXT | Yes | | |
| created_at | TIMESTAMPTZ | Yes | NOW() | |

**Indexes**:
- `idx_api_usage_user_date` ON (user_id, created_at DESC)
- `idx_api_usage_endpoint` ON (endpoint, created_at DESC)

---

## Summary

**Total Tables**: 17
**Key Features**:
- ✅ Row Level Security (RLS) enabled on user tables
- ✅ Comprehensive indexing for performance
- ✅ Support for Arabic content
- ✅ Real-time data capabilities
- ✅ AI/ML analytics integration
- ✅ Paper trading simulation
- ✅ API management and monitoring
- ✅ Multi-language support

**Data Sources Integration Ready**:
- Historical data from TXT files (/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2/)
- Real-time data from Excel (stock_synco.xlsx)
- Financial data from CSV (financial_data.csv)
