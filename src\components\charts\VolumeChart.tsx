import React from 'react';
import { ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface VolumeChartProps {
  data: Array<{ date: string; price: number; volume: number }>;
}

const VolumeChart: React.FC<VolumeChartProps> = React.memo(({ data }) => {
  // Memoize formatters to prevent recreation on each render
  const tickFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG', {
      month: 'short',
      day: 'numeric'
    });
  }, []);

  const labelFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG');
  }, []);

  const tooltipFormatter = React.useCallback((value: number, name: string) => {
    const labels: Record<string, string> = {
      'volume': 'حجم التداول',
      'price': 'السعر'
    };
    
    if (name === 'volume') {
      return [value.toLocaleString('ar-EG'), labels[name] || name];
    }
    return [value.toFixed(2), labels[name] || name];
  }, []);

  return (
    <ResponsiveContainer width="100%" height={300}>
      <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tick={{ fontSize: 12 }}
          tickFormatter={tickFormatter}
          stroke="#666"
        />
        <YAxis 
          yAxisId="price" 
          orientation="left" 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <YAxis 
          yAxisId="volume" 
          orientation="right" 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <Tooltip 
          labelFormatter={labelFormatter}
          formatter={tooltipFormatter}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <Legend />
        
        {/* Volume Bars */}
        <Bar 
          yAxisId="volume"
          dataKey="volume" 
          fill="#8884d8" 
          opacity={0.6}
          name="حجم التداول"
        />
        
        {/* Price Line */}
        <Line 
          yAxisId="price"
          type="monotone" 
          dataKey="price" 
          stroke="#ff7300" 
          strokeWidth={2}
          dot={false}
          name="السعر"
          connectNulls={false}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
});

VolumeChart.displayName = 'VolumeChart';

export default VolumeChart;
