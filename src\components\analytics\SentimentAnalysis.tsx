
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Brain, TrendingUp, TrendingDown, MessageSquare, Users, BarChart } from 'lucide-react';

interface SentimentData {
  symbol: string;
  overall: number;
  news: number;
  social: number;
  analyst: number;
  volume: number;
  trend: 'bullish' | 'bearish' | 'neutral';
  signals: string[];
}

const SentimentAnalysis = () => {
  const [sentimentData, setSentimentData] = useState<SentimentData[]>([]);
  const [selectedStock, setSelectedStock] = useState('COMI');

  useEffect(() => {
    const mockData: SentimentData[] = [
      {
        symbol: 'COMI',
        overall: 78,
        news: 85,
        social: 72,
        analyst: 80,
        volume: 95,
        trend: 'bullish',
        signals: ['إيجابية الأخبار', 'زيادة حجم التداول', 'توصيات المحللين']
      },
      {
        symbol: 'TALAAT',
        overall: 65,
        news: 70,
        social: 58,
        analyst: 68,
        volume: 78,
        trend: 'bullish',
        signals: ['نمو القطاع العقاري', 'مشاريع جديدة']
      },
      {
        symbol: 'ETEL',
        overall: 45,
        news: 40,
        social: 35,
        analyst: 60,
        volume: 55,
        trend: 'bearish',
        signals: ['ضغوط القطاع', 'تحديات التشغيل']
      }
    ];
    setSentimentData(mockData);
  }, []);

  const getSentimentColor = (score: number) => {
    if (score >= 70) return 'text-green-600 bg-green-100';
    if (score >= 50) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'bullish': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'bearish': return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <BarChart className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-purple-800">
          <Brain className="h-5 w-5" />
          تحليل المشاعر بالذكاء الاصطناعي
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Stock Selector */}
        <div className="flex gap-2">
          {sentimentData.map(stock => (
            <button
              key={stock.symbol}
              onClick={() => setSelectedStock(stock.symbol)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                selectedStock === stock.symbol
                  ? 'bg-purple-600 text-white'
                  : 'bg-white border border-purple-200 hover:bg-purple-50'
              }`}
            >
              {stock.symbol}
            </button>
          ))}
        </div>

        {/* Selected Stock Analysis */}
        {sentimentData.map(stock => (
          selectedStock === stock.symbol && (
            <div key={stock.symbol} className="space-y-6">
              {/* Overall Sentiment */}
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  {getTrendIcon(stock.trend)}
                  <span className="text-2xl font-bold">{stock.overall}%</span>
                </div>
                <Badge className={getSentimentColor(stock.overall)}>
                  {stock.trend === 'bullish' ? 'صاعد' : stock.trend === 'bearish' ? 'هابط' : 'محايد'}
                </Badge>
              </div>

              {/* Detailed Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">الأخبار</span>
                    </div>
                    <span className="font-medium">{stock.news}%</span>
                  </div>
                  <Progress value={stock.news} className="h-2" />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-600" />
                      <span className="text-sm">وسائل التواصل</span>
                    </div>
                    <span className="font-medium">{stock.social}%</span>
                  </div>
                  <Progress value={stock.social} className="h-2" />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Brain className="h-4 w-4 text-purple-600" />
                      <span className="text-sm">المحللين</span>
                    </div>
                    <span className="font-medium">{stock.analyst}%</span>
                  </div>
                  <Progress value={stock.analyst} className="h-2" />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <BarChart className="h-4 w-4 text-orange-600" />
                      <span className="text-sm">حجم التداول</span>
                    </div>
                    <span className="font-medium">{stock.volume}%</span>
                  </div>
                  <Progress value={stock.volume} className="h-2" />
                </div>
              </div>

              {/* Signals */}
              <div>
                <h4 className="font-medium mb-3">الإشارات الرئيسية:</h4>
                <div className="space-y-2">
                  {stock.signals.map((signal, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-white/60 rounded">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-sm">{signal}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )
        ))}
      </CardContent>
    </Card>
  );
};

export default SentimentAnalysis;
