#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to examine Arabic names in the Excel file
"""

import pandas as pd
import sys
import os

def examine_excel_arabic_names():
    """Examine Arabic names in Excel file"""
    try:
        # Read the Excel file
        excel_path = '/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx'
        
        if not os.path.exists(excel_path):
            print(f"Error: Excel file not found at {excel_path}")
            return
            
        print("Loading Excel file...")
        df = pd.read_excel(excel_path)
        
        print(f"Excel file loaded successfully!")
        print(f"Shape: {df.shape}")
        print()
        
        print("=== All Columns ===")
        for i, col in enumerate(df.columns):
            print(f"{i:2d}: {col}")
        print()
        
        print("=== Sample Data (First 5 rows) ===")
        print(df.head().to_string())
        print()
        
        # Look for potential Arabic name columns
        potential_arabic_cols = []
        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['name', 'اسم', 'arabic', 'ar', 'company']):
                potential_arabic_cols.append(col)
        
        print("=== Potential Arabic Name Columns ===")
        if potential_arabic_cols:
            for col in potential_arabic_cols:
                print(f"Column: {col}")
                print("Sample values:")
                for i, val in enumerate(df[col].head(10)):
                    print(f"  {i+1}: {val}")
                print()
        else:
            print("No obvious Arabic name columns found. Let's check all text columns:")
            for col in df.columns:
                if df[col].dtype == 'object':  # Text columns
                    print(f"\nColumn: {col}")
                    print("Sample values:")
                    for i, val in enumerate(df[col].head(5)):
                        if pd.notna(val) and len(str(val)) > 3:  # Skip short/empty values
                            print(f"  {i+1}: {val}")
        
        # Also check for symbols to understand the mapping
        print("\n=== Symbol Information ===")
        symbol_cols = [col for col in df.columns if 'symbol' in str(col).lower() or 'code' in str(col).lower()]
        if symbol_cols:
            for col in symbol_cols:
                print(f"Column: {col}")
                print("Sample values:", df[col].head(10).tolist())
                print()
        
    except Exception as e:
        print(f"Error examining Excel file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    examine_excel_arabic_names()
