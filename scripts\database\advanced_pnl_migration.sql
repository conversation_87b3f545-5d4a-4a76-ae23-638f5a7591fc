-- Migration: Add advanced PnL tracking fields to live_recommendations table
-- Date: 2024-12-20
-- Description: Add highest_price and protected_profit columns for enhanced trailing stop logic

BEGIN;

-- Add new columns to live_recommendations table
ALTER TABLE live_recommendations 
ADD COLUMN IF NOT EXISTS highest_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS protected_profit DECIMAL(10,4);

-- Update existing records to initialize highest_price with current_price where available
UPDATE live_recommendations 
SET highest_price = COALESCE(current_price, entry_price)
WHERE highest_price IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN live_recommendations.highest_price IS 'Highest price reached during the trade for trailing stop calculations';
COMMENT ON COLUMN live_recommendations.protected_profit IS 'Profit amount protected by trailing stop when targets are achieved';

-- Update close_reason enum to include new values
ALTER TABLE live_recommendations 
ALTER COLUMN close_reason TYPE VARCHAR(50);

COMMIT;
