from typing import List, Optional
from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "EGX Stock Oracle API"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Egyptian Stock Market Data API with Arabic Support"
    
    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30    # Database
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: str = "5432"
    DATABASE_USER: str = "postgres"
    DATABASE_PASSWORD: str = "egx123"  # Password for PostgreSQL postgres user
    DATABASE_NAME: str = "egx_stock_oracle"
    DATABASE_URL: Optional[str] = None
    
    def get_database_url(self) -> str:
        """Construct database URL from components"""
        if self.DATABASE_URL:
            return self.DATABASE_URL
        return f"postgresql://{self.DATABASE_USER}:{self.DATABASE_PASSWORD}@{self.DATABASE_HOST}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8080"
    ]
      # Redis (optional)
    REDIS_URL: Optional[str] = None
    
    # Webhook Configuration
    WEBHOOK_SECRET_KEY: str = "webhook-secret-key-here"
    WEBHOOK_TIMEOUT: int = 30
    MAX_WEBHOOK_RETRIES: int = 3
    
    # Telegram Bot Configuration
    TELEGRAM_BOT_TOKEN: Optional[str] = None
    TELEGRAM_CHAT_ID: Optional[str] = None
    TELEGRAM_WEBHOOK_URL: Optional[str] = None
    
    # WebSocket Configuration
    WEBSOCKET_HEARTBEAT_INTERVAL: int = 30
    MAX_WEBSOCKET_CONNECTIONS: int = 1000
    
    # Trading Signals Configuration
    SIGNALS_RATE_LIMIT: int = 100  # Max signals per hour
    SIGNAL_EXPIRY_MINUTES: int = 60
    AUTO_PROCESS_SIGNALS: bool = True
    
    # Notification Configuration
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = True
    ENABLE_WEBSOCKET_NOTIFICATIONS: bool = True
    ENABLE_EMAIL_NOTIFICATIONS: bool = False
    
    # Logging
    LOG_LEVEL: str = "INFO"
    
    model_config = ConfigDict(
        env_file=".env",
        case_sensitive=True
    )


settings = Settings()
