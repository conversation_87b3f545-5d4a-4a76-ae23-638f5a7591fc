#!/usr/bin/env python3
"""
Copy Database Reference Files to Main Project Directory
"""

import shutil
import os
from pathlib import Path

def copy_reference_files():
    """Copy essential database reference files to the main project directory"""
    
    # Define source and destination paths
    current_dir = Path(".")
    project_root = Path("../../")  # Go up two levels to reach project root
    
    # Files to copy with their new names
    files_to_copy = [
        ("DATABASE_SCHEMA_REFERENCE.md", "DATABASE_SCHEMA_REFERENCE.md"),
        ("database_types.ts", "src/types/database.ts"),
        ("database_quick_reference.json", "database_quick_reference.json"),
        ("database_schema_reference.json", "database_schema_reference.json")
    ]
    
    print("📋 Copying database reference files to project root...")
    
    for source_file, dest_path in files_to_copy:
        source = current_dir / source_file
        destination = project_root / dest_path
        
        if source.exists():
            # Create destination directory if it doesn't exist
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy the file
            shutil.copy2(source, destination)
            print(f"✅ Copied {source_file} → {dest_path}")
        else:
            print(f"❌ Source file not found: {source_file}")
    
    # Create a README for the database reference
    readme_content = """# Database Schema Reference

This directory contains auto-generated database schema documentation for the EGX Stock AI Oracle system.

## 📄 Files

- **`DATABASE_SCHEMA_REFERENCE.md`** - Human-readable documentation with all tables and columns
- **`database_quick_reference.json`** - Quick lookup file with table/column lists  
- **`database_schema_reference.json`** - Complete schema with sample data and metadata
- **`src/types/database.ts`** - TypeScript interfaces for type-safe queries

## 🔄 Updating

To regenerate these files, run:
```bash
cd scripts/data_sync
python inspect_database_schema.py
python generate_markdown_docs.py
python copy_reference_files.py
```

## 🚀 Frontend Usage

### Import Types
```typescript
import { StocksRealtime, StocksFinancials } from './src/types/database';
```

### Common Queries
```typescript
// Get active stocks
const { data } = await supabase
  .from('stocks_realtime')
  .select('symbol, current_price, volume')
  .gt('volume', 0);

// Get stock with financials
const { data } = await supabase
  .from('stocks_realtime')
  .select(`
    *,
    stocks_financials (*)
  `)
  .eq('symbol', 'AALR')
  .single();
```

---
*Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    readme_path = project_root / "DATABASE_README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ Created README: DATABASE_README.md")
    
    print("\n🎉 Database reference files copied successfully!")
    print("\n📁 Files available in project root:")
    print("   - DATABASE_SCHEMA_REFERENCE.md")
    print("   - database_quick_reference.json") 
    print("   - database_schema_reference.json")
    print("   - src/types/database.ts")
    print("   - DATABASE_README.md")

if __name__ == "__main__":
    copy_reference_files()
