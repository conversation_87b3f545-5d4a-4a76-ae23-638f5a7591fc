import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface OptimizedMarketSummary {
  totalStocks: number;
  gainers: number;
  losers: number;
  unchanged: number;
  totalVolume: number;
  totalTurnover: number;
  avgChange: number;
  activeStocks: number;
  bullishStocks: number;
  bearishStocks: number;
  strongUptrend: number;
  stocksWithTargets: number;
  highVolumeStocks: number;
  lastUpdated: string;
}

export interface OptimizedTopPerformer {
  symbol: string;
  name: string | null;
  current_price: number;
  change_percent: number;
  volume: number;
  turnover: number;
  sector: string | null;
  pe_ratio: number | null;
  dividend_yield: number | null;
}

export interface OptimizedMarketData {
  summary: OptimizedMarketSummary;
  topGainer: OptimizedTopPerformer | null;
  topLoser: OptimizedTopPerformer | null;
  mostActive: OptimizedTopPerformer | null;
  bestDividend: OptimizedTopPerformer | null;
  stocks: OptimizedTopPerformer[];
}

/**
 * Hook محسن لجلب بيانات السوق المحسوبة مسبقاً
 * يستخدم البيانات المتاحة في قاعدة البيانات بدلاً من الحسابات المحلية
 */
export function useOptimizedMarketData() {
  return useQuery<OptimizedMarketData>({
    queryKey: ['optimized-market-data'],
    queryFn: async () => {
      console.log('🚀 جاري جلب البيانات المحسوبة...');
      
      // جلب جميع الأسهم مع الأعمدة المحددة فقط (بدلاً من SELECT *)
      const { data: stocks, error } = await supabase
        .from('stocks_realtime')
        .select(`
          symbol,
          name,
          current_price,
          change_percent,
          volume,
          turnover,
          sector,
          ma5,
          ma20,
          ma50,
          pe_ratio,
          dividend_yield,
          target_1,
          liquidity_ratio,
          updated_at
        `)
        .not('symbol', 'like', '%EGX%')
        .not('symbol', 'like', '%ETF%')
        .not('name', 'is', null)
        .order('volume', { ascending: false });

      if (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw error;
      }

      if (!stocks || stocks.length === 0) {
        console.warn('⚠️ لا توجد بيانات متاحة');
        throw new Error('No stock data available');
      }

      console.log(`✅ تم جلب ${stocks.length} سهم`);

      // === حساب الملخص باستخدام البيانات المحسوبة ===
      const summary: OptimizedMarketSummary = {
        totalStocks: stocks.length,
        
        // حساب التوزيع (الوحيد الذي نحتاج حسابه)
        gainers: stocks.filter(s => (s.change_percent || 0) > 0).length,
        losers: stocks.filter(s => (s.change_percent || 0) < 0).length,
        unchanged: stocks.filter(s => (s.change_percent || 0) === 0).length,
        
        // إجماليات بسيطة
        totalVolume: stocks.reduce((sum, s) => sum + (s.volume || 0), 0),
        totalTurnover: stocks.reduce((sum, s) => sum + (s.turnover || 0), 0),
        
        // متوسط محسوب
        avgChange: stocks.length > 0 
          ? stocks.reduce((sum, s) => sum + (s.change_percent || 0), 0) / stocks.length 
          : 0,
          
        // عدد الأسهم النشطة
        activeStocks: stocks.filter(s => (s.volume || 0) > 0).length,
        
        // استخدام البيانات المحسوبة من قاعدة البيانات (MA)
        bullishStocks: stocks.filter(s => 
          (s.ma5 || 0) > (s.ma20 || 0) && s.ma5 && s.ma20
        ).length,
        bearishStocks: stocks.filter(s => 
          (s.ma5 || 0) < (s.ma20 || 0) && s.ma5 && s.ma20
        ).length,
        strongUptrend: stocks.filter(s => 
          (s.ma5 || 0) > (s.ma20 || 0) && 
          (s.ma20 || 0) > (s.ma50 || 0) && 
          s.ma5 && s.ma20 && s.ma50
        ).length,
        
        // استخدام البيانات المحسوبة (targets)
        stocksWithTargets: stocks.filter(s => s.target_1 && s.target_1 > 0).length,
        
        // أسهم عالية الحجم
        highVolumeStocks: stocks.filter(s => (s.volume || 0) > 100000).length,
        
        lastUpdated: new Date().toISOString()
      };

      // === أفضل الأداء (استخدام البيانات المحسوبة) ===
      const validStocks = stocks.filter(s => s.change_percent !== null);
      const activeStocks = stocks.filter(s => (s.volume || 0) > 0);
      const dividendStocks = stocks.filter(s => (s.dividend_yield || 0) > 0);

      const topGainer = validStocks.length > 0 
        ? validStocks.reduce((max, stock) => 
            (stock.change_percent || 0) > (max.change_percent || 0) ? stock : max
          )
        : null;

      const topLoser = validStocks.length > 0 
        ? validStocks.reduce((min, stock) => 
            (stock.change_percent || 0) < (min.change_percent || 0) ? stock : min
          )
        : null;

      const mostActive = activeStocks.length > 0 
        ? activeStocks.reduce((max, stock) => 
            (stock.volume || 0) > (max.volume || 0) ? stock : max
          )
        : null;

      const bestDividend = dividendStocks.length > 0 
        ? dividendStocks.reduce((max, stock) => 
            (stock.dividend_yield || 0) > (max.dividend_yield || 0) ? stock : max
          )
        : null;

      console.log('📊 تم حساب الإحصائيات:');
      console.log(`  📈 أسهم صاعدة: ${summary.gainers}`);
      console.log(`  📉 أسهم هابطة: ${summary.losers}`);
      console.log(`  🔄 اتجاه صاعد قوي: ${summary.bullishStocks}`);
      console.log(`  🎯 أسهم لها أهداف: ${summary.stocksWithTargets}`);

      return {
        summary,
        topGainer,
        topLoser,
        mostActive,
        bestDividend,
        stocks: stocks.slice(0, 20) // أول 20 سهم للعرض
      };
    },
    refetchInterval: 30000, // تحديث كل 30 ثانية
    staleTime: 15000, // البيانات صالحة لمدة 15 ثانية
  });
}
