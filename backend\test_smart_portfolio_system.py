#!/usr/bin/env python3
"""
Smart Portfolio System Integration Test
اختبار شامل لنظام المحفظة الذكية المتقدم
"""

import sys
import os
import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List

# إضافة المسار الحالي
sys.path.append('.')

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """اختبار استيراد جميع الخدمات"""
    try:
        from app.services.smart_portfolio_processor import SmartPortfolioProcessor
        from app.services.advanced_technical_analyzer import AdvancedTechnicalAnalyzer
        from app.services.advanced_position_sizer import AdvancedPositionSizer
        from app.services.copy_trading_service import AdvancedCopyTradingService
        from app.services.portfolio_analytics import PortfolioAnalytics
        
        print("✅ جميع الخدمات تم استيرادها بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد الخدمات: {e}")
        return False

def test_technical_analyzer():
    """اختبار المحلل الفني المتقدم"""
    try:
        import pandas as pd
        import numpy as np
        from app.services.advanced_technical_analyzer import AdvancedTechnicalAnalyzer
        
        # إنشاء بيانات تجريبية
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
        df = pd.DataFrame({
            'date': dates,
            'close': np.random.uniform(100, 200, len(dates)),
            'high': np.random.uniform(150, 250, len(dates)),
            'low': np.random.uniform(50, 150, len(dates)),
            'volume': np.random.uniform(1000000, 5000000, len(dates))
        })
        
        analyzer = AdvancedTechnicalAnalyzer()
        
        # تشغيل التحليل
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(
            analyzer.perform_advanced_technical_analysis(df)
        )
        
        print("✅ المحلل الفني يعمل بنجاح")
        print(f"   📊 النتيجة الإجمالية: {result.get('overall_score', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المحلل الفني: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        from app.database import get_db
        from app.models.smart_portfolio import SmartPortfolioPosition
        
        # الحصول على جلسة قاعدة البيانات
        db = next(get_db())
        
        # اختبار استعلام بسيط
        positions_count = db.query(SmartPortfolioPosition).count()
        
        print(f"✅ الاتصال بقاعدة البيانات ناجح")
        print(f"   📊 عدد المراكز الحالية: {positions_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def test_position_sizer():
    """اختبار حاسبة حجم المراكز"""
    try:
        from app.database import get_db
        from app.services.advanced_position_sizer import AdvancedPositionSizer
        
        db = next(get_db())
        sizer = AdvancedPositionSizer(db)
        
        # بيانات إشارة تجريبية
        signal_data = {
            'stock_code': 'ADCB',
            'action': 'BUY',
            'price': 150.0,
            'target_price': 165.0,
            'stop_loss': 140.0
        }
        
        stock_analysis = {
            'current_price': 150.0,
            'volatility_analysis': {'daily_volatility': 0.02},
            'liquidity_analysis': {'avg_volume': 2000000}
        }
        
        signal_quality = {
            'overall_score': 75,
            'confidence_level': 0.75
        }
        
        # حساب حجم المركز
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(
            sizer.calculate_optimal_position_size(signal_data, stock_analysis, signal_quality)
        )
        
        print("✅ حاسبة حجم المراكز تعمل بنجاح")
        print(f"   📊 الأسهم المقترحة: {result.get('shares', 'N/A')}")
        print(f"   💰 القيمة الإجمالية: {result.get('total_value', 'N/A')}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حاسبة حجم المراكز: {e}")
        return False

async def test_smart_portfolio_processor():
    """اختبار معالج المحفظة الذكية"""
    try:
        from app.database import get_db
        from app.services.smart_portfolio_processor import SmartPortfolioProcessor
        
        db = next(get_db())
        processor = SmartPortfolioProcessor(db)
        
        # بيانات إشارة تجريبية
        signal_data = {
            'stock_code': 'ADCB',
            'action': 'BUY',
            'signal_type': 'BREAKOUT',
            'current_price': 150.0,
            'target_price': 165.0,
            'stop_loss': 140.0,
            'confidence': 0.8,
            'signal_strength': 'STRONG',
            'timestamp': datetime.now().isoformat()
        }
        
        # معالجة الإشارة
        result = await processor.process_incoming_signal(signal_data)
        
        print("✅ معالج المحفظة الذكية يعمل بنجاح")
        print(f"   🎯 الإجراء: {result.get('action', 'N/A')}")
        print(f"   📊 جودة الإشارة: {result.get('signal_quality', {}).get('overall_score', 'N/A')}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في معالج المحفظة الذكية: {e}")
        return False

def test_api_endpoints():
    """اختبار نقاط النهاية الخاصة بواجهة برمجة التطبيقات"""
    try:
        import requests
        
        # اختبار نقطة النهاية الأساسية
        base_url = "http://localhost:8000"
        
        # اختبار health check
        response = requests.get(f"{base_url}/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ الخادم يعمل بنجاح")
            return True
        else:
            print(f"❌ الخادم لا يستجيب: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        print("   💡 تأكد من تشغيل الخادم بالأمر: python run_server.py")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام المحفظة الذكية المتقدم")
    print("=" * 50)
    
    tests = [
        ("استيراد الخدمات", test_imports),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("المحلل الفني المتقدم", test_technical_analyzer),
        ("حاسبة حجم المراكز", test_position_sizer),
        ("نقاط النهاية API", test_api_endpoints),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # اختبار معالج المحفظة الذكية (async)
    print(f"\n🔍 اختبار: معالج المحفظة الذكية")
    print("-" * 30)
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(test_smart_portfolio_processor())
        results.append(("معالج المحفظة الذكية", result))
    except Exception as e:
        print(f"❌ خطأ في معالج المحفظة الذكية: {e}")
        results.append(("معالج المحفظة الذكية", False))
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️  هناك مشاكل تحتاج إلى إصلاح")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
