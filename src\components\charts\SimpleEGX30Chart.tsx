import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3, Globe, ExternalLink, TrendingUp, Activity, Info } from 'lucide-react';

const SimpleEGX30Chart = () => {  const marketStats = [
    { 
      label: 'مؤشر EGX30', 
      value: '---', 
      change: 'محايد', 
      bgColor: 'bg-gradient-to-r from-blue-100 to-blue-200',
      textColor: 'text-blue-800',
      valueColor: 'text-blue-700',
      changeColor: 'text-blue-600'
    },
    { 
      label: 'حجم التداول', 
      value: 'مباشر من البورصة', 
      change: '', 
      bgColor: 'bg-gradient-to-r from-green-100 to-green-200',
      textColor: 'text-green-800',
      valueColor: 'text-green-700',
      changeColor: 'text-green-600'
    },
    { 
      label: 'عدد الصفقات', 
      value: 'بيانات حية', 
      change: '', 
      bgColor: 'bg-gradient-to-r from-purple-100 to-purple-200',
      textColor: 'text-purple-800',
      valueColor: 'text-purple-700',
      changeColor: 'text-purple-600'
    }
  ];

  return (
    <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-blue-800">
            <BarChart3 className="h-5 w-5" />
            مؤشر EGX30 - نظرة عامة على السوق المصرية
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-blue-100 text-blue-800">
              <Globe className="h-3 w-3 mr-1" />
              البورصة المصرية
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">        {/* Market Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {marketStats.map((stat, index) => (
            <div key={index} className={`p-4 ${stat.bgColor} rounded-lg text-center`}>
              <div className={`font-bold ${stat.textColor} mb-1`}>{stat.label}</div>
              <div className={`text-lg font-semibold ${stat.valueColor}`}>{stat.value}</div>
              {stat.change && <div className={`text-sm ${stat.changeColor}`}>{stat.change}</div>}
            </div>
          ))}
        </div>

        {/* Chart Area - Direct Link to TradingView */}
        <div className="bg-white rounded-lg p-8 text-center border-2 border-dashed border-gray-300">
          <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-700 mb-2">شارت مؤشر EGX30</h3>
          <p className="text-gray-600 mb-6">
            للحصول على أفضل تجربة في متابعة المؤشر المصري EGX30 مع جميع الأدوات التحليلية
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              onClick={() => window.open('https://www.tradingview.com/chart/?symbol=EGX30', '_blank')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              فتح الشارت في TradingView
            </Button>
            
            <Button 
              onClick={() => window.open('https://www.egx.com.eg/ar/indices.aspx', '_blank')}
              variant="outline"
            >
              <Globe className="h-4 w-4 mr-2" />
              موقع البورصة المصرية
            </Button>
          </div>
        </div>

        {/* Market Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg">
            <div className="flex items-center mb-3">
              <TrendingUp className="h-5 w-5 text-green-600 mr-2" />
              <span className="font-bold text-green-800">أداء المؤشر</span>
            </div>
            <p className="text-sm text-green-700">
              يعكس مؤشر EGX30 أداء أكبر 30 شركة في البورصة المصرية من حيث السيولة والنشاط
            </p>
          </div>

          <div className="p-4 bg-gradient-to-r from-purple-100 to-violet-100 rounded-lg">
            <div className="flex items-center mb-3">
              <Activity className="h-5 w-5 text-purple-600 mr-2" />
              <span className="font-bold text-purple-800">ساعات التداول</span>
            </div>
            <p className="text-sm text-purple-700">
              من 10:00 ص إلى 2:30 م (بتوقيت القاهرة) من الأحد إلى الخميس
            </p>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
          <div className="flex items-center mb-3">
            <Info className="h-5 w-5 text-yellow-600 mr-2" />
            <span className="font-bold text-yellow-800">كيفية الاستفادة:</span>
          </div>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• اضغط على "فتح الشارت في TradingView" للحصول على شارت تفاعلي كامل</li>
            <li>• يمكنك استخدام جميع أدوات التحليل الفني المتقدمة</li>
            <li>• تتبع الأخبار والأحداث المؤثرة على السوق</li>
            <li>• قارن أداء المؤشر مع المؤشرات العالمية الأخرى</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default SimpleEGX30Chart;
