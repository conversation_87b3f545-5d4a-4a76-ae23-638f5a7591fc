
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Brain, Eye, Clock, BarChart3, Target } from 'lucide-react';
import SentimentAnalysis from './SentimentAnalysis';
import PatternRecognition from './PatternRecognition';
import MultiTimeframeAnalysis from './MultiTimeframeAnalysis';
import BacktestingEngine from './BacktestingEngine';

const AdvancedAnalyticsHub = () => {
  const [activeModule, setActiveModule] = useState('sentiment');

  const modules = [
    {
      id: 'sentiment',
      label: 'تحليل المشاعر',
      icon: Brain,
      description: 'تحليل المشاعر بالذكاء الاصطناعي',
      component: SentimentAnalysis
    },
    {
      id: 'patterns',
      label: 'التعرف على الأنماط',
      icon: Eye,
      description: 'اكتشاف الأنماط الفنية تلقائياً',
      component: PatternRecognition
    },
    {
      id: 'timeframes',
      label: 'الأطر الزمنية المتعددة',
      icon: Clock,
      description: 'تحليل شامل عبر أطر زمنية مختلفة',
      component: MultiTimeframeAnalysis
    },
    {
      id: 'backtesting',
      label: 'اختبار الاستراتيجيات',
      icon: BarChart3,
      description: 'اختبر استراتيجياتك على البيانات التاريخية',
      component: BacktestingEngine
    }
  ];

  const ActiveComponent = modules.find(m => m.id === activeModule)?.component || SentimentAnalysis;

  return (
    <div className="space-y-6">
      {/* Module Selector */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Target className="h-5 w-5" />
            مركز التحليلات المتقدمة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {modules.map(module => {
              const IconComponent = module.icon;
              return (
                <Button
                  key={module.id}
                  variant={activeModule === module.id ? "default" : "outline"}
                  className={`h-auto p-4 flex flex-col items-center gap-2 ${
                    activeModule === module.id 
                      ? 'bg-blue-600 hover:bg-blue-700' 
                      : 'hover:bg-blue-50'
                  }`}
                  onClick={() => setActiveModule(module.id)}
                >
                  <IconComponent className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">{module.label}</div>
                    <div className="text-xs opacity-80 mt-1">{module.description}</div>
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Active Module */}
      <div className="animate-fade-in">
        <ActiveComponent />
      </div>
    </div>
  );
};

export default AdvancedAnalyticsHub;
