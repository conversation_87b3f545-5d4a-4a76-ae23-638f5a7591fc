import { test, expect } from '@playwright/test';

test.describe('API Integration Tests', () => {
  const baseURL = process.env.SUPABASE_URL || 'http://localhost:54321';
  
  test('Supabase connection is working', async ({ request }) => {
    // Test basic Supabase health check
    const response = await request.get(`${baseURL}/rest/v1/`, {
      headers: {
        'apikey': process.env.SUPABASE_ANON_KEY || 'test-key',
        'Authorization': `Bearer ${process.env.SUPABASE_ANON_KEY || 'test-key'}`,
      },
    });
    
    expect(response.status()).toBeLessThan(500);
  });

  test('stock data API endpoints respond correctly', async ({ request }) => {
    // Test market overview endpoint
    const marketResponse = await request.get('/api/market-overview');
    
    // Should not return server error
    expect(marketResponse.status()).toBeLessThan(500);
    
    if (marketResponse.ok()) {
      const marketData = await marketResponse.json();
      expect(marketData).toBeTruthy();
      
      // Check structure if data is returned
      if (marketData.indices) {
        expect(Array.isArray(marketData.indices)).toBe(true);
      }
    }
  });

  test('price alerts API works', async ({ request }) => {
    // Test creating a price alert
    const alertData = {
      symbol: 'ATQA',
      targetPrice: 16.0,
      condition: 'above',
      userId: 'test-user-id'
    };

    const createResponse = await request.post('/api/price-alerts', {
      data: alertData,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Should accept the request (200/201) or handle authentication (401/403)
    const validStatuses = [200, 201, 401, 403, 404];
    expect(validStatuses).toContain(createResponse.status());
  });

  test('portfolio API endpoints work', async ({ request }) => {
    // Test portfolio data retrieval
    const portfolioResponse = await request.get('/api/portfolios/test-user-id');
    
    // Should not return server error
    expect(portfolioResponse.status()).toBeLessThan(500);
    
    if (portfolioResponse.ok()) {
      const portfolioData = await portfolioResponse.json();
      expect(portfolioData).toBeTruthy();
    }
  });

  test('real-time WebSocket connection', async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
    
    // Test WebSocket connection by checking connection status
    const wsConnectionCheck = `
      new Promise((resolve) => {
        const ws = new WebSocket('ws://localhost:5173');
        ws.onopen = () => resolve({ status: 'connected' });
        ws.onerror = () => resolve({ status: 'error' });
        ws.onclose = () => resolve({ status: 'closed' });
        
        setTimeout(() => resolve({ status: 'timeout' }), 5000);
      })
    `;
      const wsResult = await page.evaluate(wsConnectionCheck) as { status: string };
    
    // WebSocket should either connect or fail gracefully
    expect(['connected', 'error', 'closed', 'timeout']).toContain(wsResult.status);
  });

  test('authentication flow works', async ({ request, page }) => {
    await page.goto('/');
    
    // Check if login functionality exists
    const loginButton = page.locator('button:has-text("Sign In"), button:has-text("Login"), [data-testid="login-button"]');
    
    if (await loginButton.isVisible()) {
      await loginButton.click();
      
      // Check if login form appears
      const loginForm = page.locator('form[data-testid="login-form"], [data-testid="auth-dialog"]');
      if (await loginForm.isVisible()) {
        // Verify form has required fields
        await expect(loginForm.locator('input[type="email"], input[name="email"]')).toBeVisible();
        await expect(loginForm.locator('input[type="password"], input[name="password"]')).toBeVisible();
      }
    }
  });

  test('data caching works correctly', async ({ page }) => {
    await page.goto('/');
    
    // Measure initial load time
    const startTime = Date.now();
    await page.waitForLoadState('networkidle');
    const firstLoadTime = Date.now() - startTime;
    
    // Reload the page
    const reloadStart = Date.now();
    await page.reload();
    await page.waitForLoadState('networkidle');
    const reloadTime = Date.now() - reloadStart;
    
    // Cached reload should be faster (allowing for some variance)
    // This is a rough check - in production you'd want more sophisticated metrics
    console.log(`First load: ${firstLoadTime}ms, Reload: ${reloadTime}ms`);
    
    // Basic check that page loads in reasonable time
    expect(reloadTime).toBeLessThan(10000); // 10 seconds max
  });

  test('error handling for API failures', async ({ page }) => {
    // Block all API calls
    await page.route('**/api/**', route => route.abort());
    await page.route('**/supabase/**', route => route.abort());
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Page should still render with error states
    await expect(page.locator('body')).toBeVisible();
    
    // Should show appropriate error messages
    const errorIndicators = page.locator('text=/error|failed|unable|offline/i');
    const errorCount = await errorIndicators.count();
    
    // Some error indication should be present
    expect(errorCount).toBeGreaterThan(0);
  });

  test('performance with large datasets', async ({ page }) => {
    await page.goto('/');
    
    // Simulate loading large dataset
    await page.evaluate(() => {
      // Mock a large dataset response
      const largeData = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        symbol: `STOCK${i}`,
        price: Math.random() * 100,
        change: Math.random() * 10 - 5
      }));
      
      // Simulate processing this data
      const processed = largeData.map(item => ({
        ...item,
        formatted: new Intl.NumberFormat().format(item.price)
      }));
      
      return processed.length;
    });
    
    // Page should remain responsive
    const isResponsive = await page.evaluate(() => {
      const start = performance.now();
      // Simulate some UI interaction
      document.body.click();
      return performance.now() - start < 100; // Should respond within 100ms
    });
    
    expect(isResponsive).toBe(true);
  });

  test('security headers are present', async ({ request }) => {
    const response = await request.get('/');
    
    // Check for basic security headers
    const headers = response.headers();
    
    // These might not all be present in development, but check what we can
    expect(response.status()).toBeLessThan(500);
    
    // Log headers for debugging
    console.log('Response headers:', headers);
  });
});
