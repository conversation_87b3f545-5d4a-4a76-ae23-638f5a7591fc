import React, { useState, useEffect, useRef, useCallback } from 'react';

interface VirtualScrollProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
}

export function VirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
}: VirtualScrollProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate visible range
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  // Total height of all items
  const totalHeight = items.length * itemHeight;

  // Visible items
  const visibleItems = items.slice(startIndex, endIndex + 1);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      {/* Spacer for items before visible range */}
      <div style={{ height: startIndex * itemHeight }} />
      
      {/* Visible items */}
      <div>
        {visibleItems.map((item, index) => (
          <div
            key={startIndex + index}
            style={{ height: itemHeight }}
            className="flex items-center"
          >
            {renderItem(item, startIndex + index)}
          </div>
        ))}
      </div>
      
      {/* Spacer for items after visible range */}
      <div style={{ height: (items.length - endIndex - 1) * itemHeight }} />
    </div>
  );
}

// Hook for virtual scrolling with dynamic heights
export function useVirtualScroll<T>({
  items,
  estimatedItemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  estimatedItemHeight: number;
  containerHeight: number;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = useState(0);
  const [itemHeights, setItemHeights] = useState<number[]>([]);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Calculate cumulative heights
  const cumulativeHeights = React.useMemo(() => {
    const heights = [0];
    for (let i = 0; i < items.length; i++) {
      const height = itemHeights[i] || estimatedItemHeight;
      heights.push(heights[i] + height);
    }
    return heights;
  }, [items.length, itemHeights, estimatedItemHeight]);

  // Find visible range based on cumulative heights
  const getVisibleRange = useCallback(() => {
    let startIndex = 0;
    let endIndex = items.length - 1;

    // Binary search for start index
    let left = 0;
    let right = items.length - 1;
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      if (cumulativeHeights[mid] < scrollTop) {
        startIndex = mid;
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }

    // Binary search for end index
    left = startIndex;
    right = items.length - 1;
    const bottomBound = scrollTop + containerHeight;
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      if (cumulativeHeights[mid] <= bottomBound) {
        endIndex = mid;
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }

    return {
      startIndex: Math.max(0, startIndex - overscan),
      endIndex: Math.min(items.length - 1, endIndex + overscan),
    };
  }, [cumulativeHeights, scrollTop, containerHeight, items.length, overscan]);

  const { startIndex, endIndex } = getVisibleRange();

  // Measure item heights
  const measureItem = useCallback((index: number, element: HTMLDivElement | null) => {
    if (element && itemHeights[index] !== element.offsetHeight) {
      setItemHeights(prev => {
        const newHeights = [...prev];
        newHeights[index] = element.offsetHeight;
        return newHeights;
      });
    }
    itemRefs.current[index] = element;
  }, [itemHeights]);

  return {
    startIndex,
    endIndex,
    totalHeight: cumulativeHeights[cumulativeHeights.length - 1],
    offsetY: cumulativeHeights[startIndex],
    measureItem,
    setScrollTop,
  };
}

// Optimized table component with virtual scrolling
interface VirtualTableProps<T> {
  data: T[];
  columns: Array<{
    key: keyof T;
    header: string;
    width?: string;
    render?: (value: T[keyof T], row: T, index: number) => React.ReactNode;
  }>;
  rowHeight?: number;
  height: number;
  className?: string;
}

export function VirtualTable<T>({
  data,
  columns,
  rowHeight = 60,
  height,
  className = '',
}: VirtualTableProps<T>) {
  return (
    <div className={`border rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gray-50 border-b sticky top-0 z-10">
        <div className="flex">
          {columns.map((column, index) => (
            <div
              key={String(column.key)}
              className="p-3 font-medium text-gray-900 border-r last:border-r-0"
              style={{ width: column.width || `${100 / columns.length}%` }}
            >
              {column.header}
            </div>
          ))}
        </div>
      </div>

      {/* Virtual scrolled body */}
      <VirtualScroll
        items={data}
        itemHeight={rowHeight}
        containerHeight={height - 60} // Account for header
        renderItem={(row, index) => (
          <div className="flex hover:bg-gray-50 border-b w-full">
            {columns.map((column) => (
              <div
                key={String(column.key)}
                className="p-3 border-r last:border-r-0 flex items-center"
                style={{ width: column.width || `${100 / columns.length}%` }}
              >
                {column.render
                  ? column.render(row[column.key], row, index)
                  : String(row[column.key])
                }
              </div>
            ))}
          </div>
        )}
        className="bg-white"
      />
    </div>
  );
}

// Optimized list component with virtual scrolling
interface VirtualListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight: number;
  height: number;
  className?: string;
  onEndReached?: () => void;
  endReachedThreshold?: number;
}

export function VirtualList<T>({
  items,
  renderItem,
  itemHeight,
  height,
  className = '',
  onEndReached,
  endReachedThreshold = 0.8,
}: VirtualListProps<T>) {
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    if (onEndReached) {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
      
      if (scrollPercentage >= endReachedThreshold) {
        onEndReached();
      }
    }
  }, [onEndReached, endReachedThreshold]);

  return (
    <div
      className={`overflow-auto ${className}`}
      style={{ height }}
      onScroll={handleScroll}
    >
      <VirtualScroll
        items={items}
        itemHeight={itemHeight}
        containerHeight={height}
        renderItem={renderItem}
      />
    </div>
  );
}
