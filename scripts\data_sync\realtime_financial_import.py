#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 Realtime & Financial Data Import - Egyptian Stock Market
Import realtime data from Excel and financial data from CSV
"""

import pandas as pd
import subprocess
import sys
from pathlib import Path
import time
from datetime import datetime
import json
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('realtime_financial_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RealtimeFinancialImporter:
    """Import realtime and financial data"""
    
    def __init__(self):
        self.excel_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx")
        self.csv_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv")
        
        self.stats = {
            'realtime': {'processed': 0, 'inserted': 0, 'updated': 0},
            'financial': {'processed': 0, 'inserted': 0, 'updated': 0}
        }
    
    def run_sql(self, sql_command, database='egx_stock_oracle'):
        """Execute SQL command as postgres user"""
        try:
            cmd = ['sudo', '-u', 'postgres', 'psql', '-d', database, '-c', sql_command]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"SQL Error: {e.stderr}")
            return None
    
    def import_realtime_data(self):
        """Import realtime data from Excel"""
        logger.info("📊 Starting Realtime Data Import from Excel")
        
        try:
            # Read Excel file
            df = pd.read_excel(self.excel_path, engine='openpyxl')
            logger.info(f"📖 Loaded {len(df)} records from Excel")
            logger.info(f"📋 Columns: {list(df.columns)}")
            
            self.stats['realtime']['processed'] = len(df)
            
            # Process each row
            successful_records = 0
            for _, row in df.iterrows():
                try:
                    # Extract key fields (adjust column names based on actual Excel structure)
                    symbol = str(row.iloc[0]).strip().upper() if pd.notna(row.iloc[0]) else None
                    
                    if not symbol or symbol in ['NAN', '']:
                        continue
                    
                    # Basic price data (adjust indices based on actual Excel structure)
                    current_price = float(row.iloc[1]) if pd.notna(row.iloc[1]) and str(row.iloc[1]).replace('.','').isdigit() else None
                    change_percent = float(row.iloc[2]) if pd.notna(row.iloc[2]) and str(row.iloc[2]).replace('.','').replace('-','').isdigit() else 0
                    volume = int(float(row.iloc[3])) if pd.notna(row.iloc[3]) and str(row.iloc[3]).replace('.','').isdigit() else 0
                    
                    # Ensure symbol exists in stocks_master
                    self.run_sql(f"""
                        INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
                        VALUES ('{symbol}', 'سهم {symbol}', '{symbol}', true)
                        ON CONFLICT (symbol) DO NOTHING;
                    """)
                    
                    # Insert/Update realtime data
                    sql_upsert = f"""
                        INSERT INTO stocks_realtime (
                            symbol, current_price, change_percent, volume, 
                            last_trade_date, updated_at
                        ) VALUES (
                            '{symbol}', 
                            {current_price if current_price else 'NULL'}, 
                            {change_percent}, 
                            {volume}, 
                            CURRENT_DATE, 
                            CURRENT_TIMESTAMP
                        )
                        ON CONFLICT (symbol) DO UPDATE SET
                            current_price = EXCLUDED.current_price,
                            change_percent = EXCLUDED.change_percent,
                            volume = EXCLUDED.volume,
                            last_trade_date = EXCLUDED.last_trade_date,
                            updated_at = EXCLUDED.updated_at;
                    """
                    
                    result = self.run_sql(sql_upsert)
                    if result is not None:
                        successful_records += 1
                        
                except Exception as e:
                    logger.warning(f"⚠️ Skipped realtime record for {symbol}: {e}")
                    continue
            
            self.stats['realtime']['inserted'] = successful_records
            logger.info(f"✅ Realtime Import: {successful_records} records processed")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Realtime import failed: {e}")
            return False
    
    def import_financial_data(self):
        """Import financial data from CSV"""
        logger.info("💰 Starting Financial Data Import from CSV")
        
        try:
            # Read CSV file
            df = pd.read_csv(self.csv_path)
            logger.info(f"📖 Loaded {len(df)} records from CSV")
            logger.info(f"📋 Columns: {list(df.columns)}")
            
            self.stats['financial']['processed'] = len(df)
            
            # Process each row
            successful_records = 0
            for _, row in df.iterrows():
                try:
                    # Extract symbol (adjust column name based on actual CSV structure)
                    symbol_col = None
                    for col in ['Symbol', 'symbol', 'الرمز', 'ticker']:
                        if col in df.columns:
                            symbol_col = col
                            break
                    
                    if not symbol_col:
                        symbol = str(row.iloc[0]).strip().upper() if pd.notna(row.iloc[0]) else None
                    else:
                        symbol = str(row[symbol_col]).strip().upper() if pd.notna(row[symbol_col]) else None
                    
                    if not symbol or symbol in ['NAN', '']:
                        continue
                    
                    # Extract financial metrics (adjust based on actual CSV structure)
                    pe_ratio = float(row.iloc[1]) if pd.notna(row.iloc[1]) and str(row.iloc[1]).replace('.','').replace('-','').isdigit() else None
                    pb_ratio = float(row.iloc[2]) if pd.notna(row.iloc[2]) and str(row.iloc[2]).replace('.','').replace('-','').isdigit() else None
                    roe = float(row.iloc[3]) if pd.notna(row.iloc[3]) and str(row.iloc[3]).replace('.','').replace('-','').isdigit() else None
                    
                    # Ensure symbol exists in stocks_master
                    self.run_sql(f"""
                        INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
                        VALUES ('{symbol}', 'سهم {symbol}', '{symbol}', true)
                        ON CONFLICT (symbol) DO NOTHING;
                    """)
                    
                    # Insert/Update financial data
                    current_year = datetime.now().year
                    sql_upsert = f"""
                        INSERT INTO stocks_financials (
                            symbol, fiscal_year, fiscal_quarter, 
                            pe_ratio, pb_ratio, roe,
                            created_at, updated_at
                        ) VALUES (
                            '{symbol}', {current_year}, 4,
                            {pe_ratio if pe_ratio else 'NULL'}, 
                            {pb_ratio if pb_ratio else 'NULL'}, 
                            {roe if roe else 'NULL'},
                            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                        )
                        ON CONFLICT (symbol, fiscal_year, fiscal_quarter) DO UPDATE SET
                            pe_ratio = EXCLUDED.pe_ratio,
                            pb_ratio = EXCLUDED.pb_ratio,
                            roe = EXCLUDED.roe,
                            updated_at = EXCLUDED.updated_at;
                    """
                    
                    result = self.run_sql(sql_upsert)
                    if result is not None:
                        successful_records += 1
                        
                except Exception as e:
                    logger.warning(f"⚠️ Skipped financial record for {symbol}: {e}")
                    continue
            
            self.stats['financial']['inserted'] = successful_records
            logger.info(f"✅ Financial Import: {successful_records} records processed")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Financial import failed: {e}")
            return False
    
    def run_complete_import(self):
        """Run complete realtime and financial import"""
        start_time = datetime.now()
        logger.info("🚀 Starting Complete Realtime & Financial Data Import")
        logger.info("=" * 80)
        
        # Check file existence
        if not self.excel_path.exists():
            logger.error(f"❌ Excel file not found: {self.excel_path}")
            return False
        
        if not self.csv_path.exists():
            logger.error(f"❌ CSV file not found: {self.csv_path}")
            return False
        
        logger.info(f"✅ Excel file: {self.excel_path.name} ({self.excel_path.stat().st_size/1024:.1f} KB)")
        logger.info(f"✅ CSV file: {self.csv_path.name} ({self.csv_path.stat().st_size/1024:.1f} KB)")
        
        # Import realtime data
        realtime_success = self.import_realtime_data()
        
        # Import financial data  
        financial_success = self.import_financial_data()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Final report
        logger.info("\n" + "=" * 80)
        logger.info("📋 REALTIME & FINANCIAL IMPORT REPORT")
        logger.info("=" * 80)
        
        logger.info(f"📊 Realtime Data:")
        logger.info(f"  📖 Processed: {self.stats['realtime']['processed']}")
        logger.info(f"  ✅ Imported: {self.stats['realtime']['inserted']}")
        logger.info(f"  📈 Status: {'SUCCESS' if realtime_success else 'FAILED'}")
        
        logger.info(f"💰 Financial Data:")
        logger.info(f"  📖 Processed: {self.stats['financial']['processed']}")
        logger.info(f"  ✅ Imported: {self.stats['financial']['inserted']}")
        logger.info(f"  📈 Status: {'SUCCESS' if financial_success else 'FAILED'}")
        
        logger.info(f"⏱️ Total Duration: {duration:.1f} seconds")
        
        # Database verification
        realtime_count = self.run_sql("SELECT COUNT(*) FROM stocks_realtime;")
        financial_count = self.run_sql("SELECT COUNT(*) FROM stocks_financials;")
        
        logger.info(f"\n🗄️ Final Database State:")
        logger.info(f"  📊 Realtime Records: {realtime_count}")
        logger.info(f"  💰 Financial Records: {financial_count}")
        
        overall_success = realtime_success and financial_success
        
        if overall_success:
            logger.info(f"\n🎉 COMPLETE DATABASE IMPORT SUCCESSFUL!")
            logger.info(f"💎 Egyptian Stock Market Database is FULLY READY!")
        else:
            logger.info(f"\n⚠️ Import completed with some issues")
        
        logger.info("=" * 80)
        
        return overall_success

def main():
    """Main execution function"""
    print("📊 Realtime & Financial Data Import - Egyptian Stock Market")
    print("=" * 80)
    
    importer = RealtimeFinancialImporter()
    success = importer.run_complete_import()
    
    if success:
        print("\n🎉 Realtime and Financial data import completed successfully!")
        print("🏛️ Complete Egyptian Stock Market Database is now ready!")
    else:
        print("\n⚠️ Import completed with some issues")
        print("📋 Check logs for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
