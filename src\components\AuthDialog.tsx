
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import AuthForm from "./AuthForm";
import { LogIn } from "lucide-react";

export default function AuthDialog({ open, onOpenChange }: { open: boolean; onOpenChange: (v: boolean) => void }) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md w-full rounded-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 justify-center">
            <LogIn className="text-blue-700" />
            تسجيل الدخول / إنشاء حساب
          </DialogTitle>
        </DialogHeader>
        <AuthForm />
      </DialogContent>
    </Dialog>
  );
}
