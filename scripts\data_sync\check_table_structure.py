#!/usr/bin/env python3
"""
فحص هيكل الجداول في قاعدة البيانات - فحص مباشر للأعمدة
Check table structure in database - direct inspection of columns
"""

import os
import sys
import json
from datetime import datetime
from supabase import create_client, Client

# إعداد البيئة
load_dotenv = lambda: None
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# إعداد الاتصال بقاعدة البيانات
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Missing Supabase configuration")
    print("Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("✅ Connected to Supabase successfully")
except Exception as e:
    print(f"❌ Error connecting to Supabase: {e}")
    sys.exit(1)

def check_table_columns(table_name: str):
    """فحص أعمدة جدول محدد"""
    try:
        print(f"\n📊 Checking table: {table_name}")
        
        # جلب بيانات عينة من الجدول
        response = supabase.table(table_name).select("*").limit(1).execute()
        
        if response.data:
            sample_row = response.data[0]
            print(f"✅ Columns in {table_name}:")
            for i, (column, value) in enumerate(sample_row.items(), 1):
                value_type = type(value).__name__
                value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"  {i:2d}. {column:<20} = {value_str:<30} ({value_type})")
        else:
            print(f"⚠️  No data found in {table_name}")
            
    except Exception as e:
        print(f"❌ Error checking {table_name}: {e}")

def find_name_columns():
    """البحث عن أعمدة الأسماء في جميع الجداول"""
    tables_to_check = [
        "stocks_realtime",
        "stocks_financials", 
        "stocks_historical"
    ]
    
    print("🔍 Searching for name columns in all tables...")
    
    for table in tables_to_check:
        try:
            response = supabase.table(table).select("*").limit(1).execute()
            if response.data:
                columns = list(response.data[0].keys())
                name_columns = [col for col in columns if 'name' in col.lower()]
                
                if name_columns:
                    print(f"\n📋 {table} - Name columns found:")
                    for col in name_columns:
                        print(f"  ✓ {col}")
                else:
                    print(f"\n📋 {table} - No name columns found")
                    # عرض أول 10 أعمدة
                    print(f"  📝 First 10 columns: {columns[:10]}")
                    
        except Exception as e:
            print(f"❌ Error checking {table}: {e}")

def check_join_possibility():
    """فحص إمكانية ربط الجداول"""
    print("\n🔗 Checking join possibilities...")
    
    try:
        # فحص stocks_realtime
        rt_response = supabase.table("stocks_realtime").select("symbol").limit(3).execute()
        rt_symbols = [row['symbol'] for row in rt_response.data] if rt_response.data else []
        
        # فحص stocks_financials
        fin_response = supabase.table("stocks_financials").select("symbol").limit(3).execute()
        fin_symbols = [row['symbol'] for row in fin_response.data] if fin_response.data else []
        
        print(f"📊 stocks_realtime symbols: {rt_symbols}")
        print(f"📊 stocks_financials symbols: {fin_symbols}")
        
        # فحص التقاطع
        common_symbols = set(rt_symbols) & set(fin_symbols)
        print(f"✅ Common symbols: {list(common_symbols)}")
        
        if common_symbols:
            print("✅ Join is possible using 'symbol' column")
        else:
            print("⚠️  No common symbols found - join might not work")
            
    except Exception as e:
        print(f"❌ Error checking join: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 Starting table structure check...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # فحص الجداول الرئيسية
    main_tables = ["stocks_realtime", "stocks_financials", "stocks_historical"]
    
    for table in main_tables:
        check_table_columns(table)
    
    # البحث عن أعمدة الأسماء
    find_name_columns()
    
    # فحص إمكانية الربط
    check_join_possibility()
    
    print("\n✅ Table structure check completed!")

if __name__ == "__main__":
    main()
