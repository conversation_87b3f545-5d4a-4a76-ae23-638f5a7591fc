
import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BarChart3 } from 'lucide-react';
import { useCorrelations } from '@/hooks/useCorrelations';

const CorrelationAnalysisSection = () => {
  const { data: correlations, isLoading, error } = useCorrelations();

  return (
    <Card className="border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-red-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <BarChart3 className="h-5 w-5" />
          تحليل الارتباط
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center text-orange-800">جاري التحميل...</div>
        ) : error ? (
          <div className="text-center text-red-600">حدث خطأ في التحميل</div>
        ) : (
          <div className="space-y-4">
            {(correlations ?? []).map((corr, index) => (
              <div key={corr.id ?? index} className="flex items-center justify-between p-4 bg-white/60 rounded-lg border border-orange-200">
                <div className="flex items-center gap-4">
                  <span className="font-medium">{corr.stock1}</span>
                  <span className="text-muted-foreground">↔</span>
                  <span className="font-medium">{corr.stock2}</span>
                </div>
                <div className="flex items-center gap-4">
                  <Badge className={`${
                    corr.strength === 'قوية' ? 'bg-red-500' :
                    corr.strength === 'متوسطة' ? 'bg-yellow-500' : 'bg-green-500'
                  } text-white`}>
                    {corr.strength}
                  </Badge>
                  <span className="font-bold">{corr.correlation.toFixed(2)}</span>
                  <Progress value={Math.abs(Number(corr.correlation)) * 100} className="w-24" />
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CorrelationAnalysisSection;
