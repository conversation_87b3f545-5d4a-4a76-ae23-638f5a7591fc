import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Search, 
  RefreshCw,
  Clock,
  BarChart3,
  Target,
  AlertTriangle,
  Loader2,
  Volume2,
  Zap
} from 'lucide-react';
import { useSimpleLiveData, SimpleStockData } from '@/hooks/useSimpleLiveData';

// مؤقتاً - سنوسع هذا تدريجياً
interface LiveStockData extends SimpleStockData {
  previous_close?: number;
  change_amount?: number;
  sector?: string;
}
import { cn } from '@/lib/utils';

interface LiveTradingScreenProps {
  className?: string;
}

const LiveTradingScreen: React.FC<LiveTradingScreenProps> = ({ className }) => {
  const { data: stocks, isLoading, error, refetch } = useSimpleLiveData();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSector, setSelectedSector] = useState('all');
  const [sortBy, setSortBy] = useState<'change_percent' | 'volume' | 'turnover' | 'symbol'>('change_percent');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'all' | 'gainers' | 'losers' | 'active'>('all');

  // تصفية وترتيب البيانات
  const filteredStocks = useMemo(() => {
    let filtered = stocks || [];

    // تصفية حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(stock => 
        stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (stock.name && stock.name.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // تصفية حسب القطاع
    if (selectedSector !== 'all') {
      filtered = filtered.filter(stock => stock.sector === selectedSector);
    }

    // تصفية حسب نوع العرض
    switch (viewMode) {
      case 'gainers':
        filtered = filtered.filter(stock => stock.change_percent > 0);
        break;
      case 'losers':
        filtered = filtered.filter(stock => stock.change_percent < 0);
        break;
      case 'active':
        filtered = filtered.filter(stock => (stock.turnover || 0) > 100000);
        break;
    }

    // ترتيب البيانات
    filtered.sort((a, b) => {
      const multiplier = sortOrder === 'desc' ? -1 : 1;
      
      switch (sortBy) {
        case 'change_percent':
          return (a.change_percent - b.change_percent) * multiplier;
        case 'volume':
          return ((a.volume || 0) - (b.volume || 0)) * multiplier;
        case 'turnover':
          return ((a.turnover || 0) - (b.turnover || 0)) * multiplier;
        case 'symbol':
          return a.symbol.localeCompare(b.symbol) * multiplier;
        default:
          return 0;
      }
    });

    return filtered;
  }, [stocks, searchTerm, selectedSector, sortBy, sortOrder, viewMode]);

  // الحصول على القطاعات المتاحة
  const availableSectors = useMemo(() => {
    const sectors = new Set(stocks?.map(s => s.sector).filter(Boolean) || []);
    return Array.from(sectors).sort();
  }, [stocks]);

  // تنسيق الأرقام
  const formatNumber = (num: number) => {
    if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toLocaleString();
  };

  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  const formatPercent = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  // مكون صف السهم مع التأثيرات البصرية
  const StockRow: React.FC<{ stock: LiveStockData }> = ({ stock }) => {
    const [flashEffect, setFlashEffect] = useState(false);

    useEffect(() => {
      if (stock.isNewUpdate && stock.flashColor !== 'none') {
        setFlashEffect(true);
        const timer = setTimeout(() => setFlashEffect(false), 1000);
        return () => clearTimeout(timer);
      }
    }, [stock.isNewUpdate, stock.flashColor]);

    const changeColor = stock.change_percent > 0 ? 'text-green-600' : 
                       stock.change_percent < 0 ? 'text-red-600' : 'text-gray-600';
    
    const flashColorClass = stock.flashColor === 'green' ? 'bg-green-100' : 
                           stock.flashColor === 'red' ? 'bg-red-100' : '';

    return (
      <div 
        className={cn(
          "grid grid-cols-8 gap-2 p-3 border-b border-gray-100 hover:bg-gray-50 transition-all duration-300",
          flashEffect && flashColorClass,
          "text-sm"
        )}
      >
        {/* رمز السهم والاسم */}
        <div className="col-span-2">
          <div className="font-bold text-gray-800">{stock.symbol}</div>
          <div className="text-xs text-gray-500 truncate">{stock.name}</div>
          {stock.sector && (
            <Badge variant="outline" className="text-xs mt-1">
              {stock.sector}
            </Badge>
          )}
        </div>

        {/* السعر الحالي */}
        <div className="text-center">
          <div className={cn("font-bold text-lg", changeColor)}>
            {formatPrice(stock.current_price)}
          </div>
          {stock.priceDirection === 'up' && (
            <TrendingUp className="h-4 w-4 text-green-500 mx-auto animate-bounce" />
          )}
          {stock.priceDirection === 'down' && (
            <TrendingDown className="h-4 w-4 text-red-500 mx-auto animate-bounce" />
          )}
        </div>

        {/* التغيير */}
        <div className="text-center">
          <div className={cn("font-semibold", changeColor)}>
            {formatPercent(stock.change_percent)}
          </div>
          <div className={cn("text-xs", changeColor)}>
            {stock.change_amount >= 0 ? '+' : ''}{formatPrice(stock.change_amount)}
          </div>
        </div>

        {/* الحجم */}
        <div className="text-center">
          <div className="font-medium">{formatNumber(stock.volume)}</div>
          {stock.volumeChange === 'up' && (
            <div className="text-xs text-green-600">⬆️ نشط</div>
          )}
          {stock.volumeChange === 'down' && (
            <div className="text-xs text-red-600">⬇️ هادئ</div>
          )}
        </div>

        {/* القيمة */}
        <div className="text-center">
          <div className="font-medium">{formatNumber(stock.turnover || 0)}</div>
          <div className="text-xs text-gray-500">جنيه</div>
        </div>

        {/* أعلى/أقل */}
        <div className="text-center">
          <div className="text-xs text-green-600">
            ↑ {formatPrice(stock.high_price || stock.current_price)}
          </div>
          <div className="text-xs text-red-600">
            ↓ {formatPrice(stock.low_price || stock.current_price)}
          </div>
        </div>

        {/* آخر تحديث */}
        <div className="text-center">
          <div className="text-xs text-gray-500">
            {new Date(stock.updated_at).toLocaleTimeString('ar-EG')}
          </div>
          {stock.isNewUpdate && (
            <div className="text-xs text-blue-600 animate-pulse">
              <Zap className="h-3 w-3 inline mr-1" />
              جديد
            </div>
          )}
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-bold mb-2">خطأ في تحميل البيانات</h3>
            <p className="mb-4">تعذر الاتصال بخادم البيانات اللحظية</p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* إحصائيات السوق السريعة */}
      <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              الشاشة اللحظية - البورصة المصرية
              {isLoading && <RefreshCw className="h-4 w-4 animate-spin ml-2" />}
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Clock className="h-4 w-4" />
              آخر تحديث: {lastUpdate}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {marketStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{marketStats.totalStocks}</div>
                <div className="text-xs text-gray-600">إجمالي الأسهم</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-green-600">{marketStats.gainers}</div>
                <div className="text-xs text-gray-600">أسهم صاعدة</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-red-600">{marketStats.losers}</div>
                <div className="text-xs text-gray-600">أسهم هابطة</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-gray-600">{marketStats.unchanged}</div>
                <div className="text-xs text-gray-600">بدون تغيير</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-purple-600">
                  {formatNumber(marketStats.totalVolume)}
                </div>
                <div className="text-xs text-gray-600">حجم التداول</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-orange-600">
                  {formatNumber(marketStats.totalTurnover)}
                </div>
                <div className="text-xs text-gray-600">قيمة التداول</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-teal-600">
                  {formatNumber(marketStats.totalTrades)}
                </div>
                <div className="text-xs text-gray-600">عدد الصفقات</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* أدوات التحكم والفلترة */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            {/* البحث */}
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="ابحث عن سهم..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>            {/* فلتر القطاع */}
            <select
              value={selectedSector}
              onChange={(e) => setSelectedSector(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
              title="اختر القطاع"
            >
              <option value="all">جميع القطاعات</option>
              {availableSectors.map(sector => (
                <option key={sector} value={sector}>{sector}</option>
              ))}
            </select>

            {/* نوع العرض */}
            <div className="flex gap-2">
              {[
                { key: 'all', label: 'الكل', icon: BarChart3 },
                { key: 'gainers', label: 'صاعدة', icon: TrendingUp },
                { key: 'losers', label: 'هابطة', icon: TrendingDown },
                { key: 'active', label: 'نشطة', icon: Volume2 },
              ].map(({ key, label, icon: Icon }) => (
                <Button
                  key={key}
                  variant={viewMode === key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode(key as typeof viewMode)}
                >
                  <Icon className="h-4 w-4 mr-1" />
                  {label}
                </Button>
              ))}
            </div>            {/* ترتيب */}
            <select
              value={`${sortBy}_${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('_');
                setSortBy(field as typeof sortBy);
                setSortOrder(order as typeof sortOrder);
              }}
              className="px-3 py-2 border border-gray-300 rounded-md"
              title="اختر طريقة الترتيب"
            >
              <option value="change_percent_desc">التغيير % (أعلى)</option>
              <option value="change_percent_asc">التغيير % (أقل)</option>
              <option value="volume_desc">الحجم (أعلى)</option>
              <option value="turnover_desc">القيمة (أعلى)</option>
              <option value="symbol_asc">الرمز (أ-ي)</option>
            </select>

            {/* تحديث يدوي */}
            <Button onClick={() => refetch()} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-1" />
              تحديث
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* جدول البيانات */}
      <Card>
        <CardHeader className="pb-2">
          <div className="grid grid-cols-8 gap-2 text-sm font-semibold text-gray-700 border-b pb-2">
            <div className="col-span-2">السهم</div>
            <div className="text-center">السعر</div>
            <div className="text-center">التغيير</div>
            <div className="text-center">الحجم</div>
            <div className="text-center">القيمة</div>
            <div className="text-center">أعلى/أقل</div>
            <div className="text-center">وقت التحديث</div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            {isLoading ? (
              <div className="flex items-center justify-center p-12">
                <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mr-3" />
                <span className="text-lg">جارِ تحميل البيانات اللحظية...</span>
              </div>
            ) : filteredStocks.length === 0 ? (
              <div className="text-center p-12 text-gray-500">
                <Target className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-bold mb-2">لا توجد نتائج</h3>
                <p>لم يتم العثور على أسهم تطابق معايير البحث</p>
              </div>
            ) : (
              filteredStocks.map((stock) => (
                <StockRow key={stock.symbol} stock={stock} />
              ))
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default LiveTradingScreen;
