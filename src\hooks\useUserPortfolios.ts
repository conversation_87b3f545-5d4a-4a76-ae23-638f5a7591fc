import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";

export interface PortfolioSummary {
  id: string;
  name: string;
  description?: string | null;
  totalValue: number;
  totalProfitLoss: number;
  totalProfitLossPercent: number;
  holdings: PortfolioHolding[];
}

export interface PortfolioHolding {
  id: string;
  symbol: string;
  quantity: number;
  averageBuyPrice: number;
  currentPrice: number | null;
  totalCost: number | null;
  currentValue: number | null;
  profitLoss: number | null;
  profitLossPercent: number | null;
}

type UseUserPortfoliosProps = {
  userId: string | null;
};

export function useUserPortfolios({ userId }: UseUserPortfoliosProps) {
  const [portfolios, setPortfolios] = useState<PortfolioSummary[]>([]);
  const [loading, setLoading] = useState<boolean>(!!userId);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setPortfolios([]);
      setLoading(false);
      setError(null);
      return;
    }
    setLoading(true);
    setError(null);
    fetchAll();
    // eslint-disable-next-line
  }, [userId]);

  // Fetch portfolios + holdings for this user
  const fetchAll = async () => {
    setLoading(true);
    setError(null);
    const { data: portfoliosData, error: pErr } = await supabase
      .from("user_portfolios")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: true });
    if (pErr) {
      setError(pErr.message);
      setLoading(false);
      return;
    }
    // For each, fetch holdings
    if (!portfoliosData) {
      setPortfolios([]);
      setLoading(false);
      return;
    }
    const output: PortfolioSummary[] = [];
    for (const p of portfoliosData) {
      const { data: holdingsData } = await supabase
        .from("portfolio_holdings")
        .select("*")
        .eq("portfolio_id", p.id);
      // Calculate summary
      let totalValue = 0,
        totalCost = 0,
        totalProfitLoss = 0;
      const holdings: PortfolioHolding[] =
        (holdingsData || []).map((h) => {
          const q = Number(h.quantity);
          const avg = Number(h.average_buy_price);
          const cur = h.current_price ? Number(h.current_price) : null;
          const cost = q * avg;
          const val =
            cur !== null ? Number((cur * q).toFixed(2)) : null;
          const pl = val !== null ? val - cost : null;
          const plPerc = pl !== null && cost > 0 ? (pl / cost) * 100 : null;
          if (val !== null) {
            totalValue += val;
            totalCost += cost;
            totalProfitLoss += pl!;
          }
          return {
            id: h.id,
            symbol: h.symbol,
            quantity: q,
            averageBuyPrice: avg,
            currentPrice: cur,
            totalCost: cost,
            currentValue: val,
            profitLoss: pl,
            profitLossPercent: plPerc,
          };
        }) ?? [];
      output.push({
        id: p.id,
        name: p.name,
        description: p.description,
        totalValue,
        totalProfitLoss,
        totalProfitLossPercent:
          totalCost > 0
            ? (totalProfitLoss / totalCost) * 100
            : 0,
        holdings,
      });
    }
    setPortfolios(output);
    setLoading(false);
  };

  // Add a new portfolio
  const addPortfolio = async ({
    name,
    description,
  }: {
    name: string;
    description?: string;
  }) => {
    if (!userId) return;
    const { data, error: insErr } = await supabase
      .from("user_portfolios")
      .insert([
        {
          name,
          description: description || "",
          user_id: userId,
        },
      ])
      .select("*")
      .single();
    if (!insErr) fetchAll();
    return { data, error: insErr };
  };

  // Remove portfolio and all associated holdings
  const removePortfolio = async (portfolioId: string) => {
    await supabase.from("user_portfolios").delete().eq("id", portfolioId);
    fetchAll();
  };

  // Add, update, or delete holdings for a portfolio
  const addHolding = async (
    portfolioId: string,
    holding: {
      symbol: string;
      quantity: number;
      averageBuyPrice: number;
    }
  ) => {
    const { error } = await supabase
      .from("portfolio_holdings")
      .insert([
        {
          portfolio_id: portfolioId,
          symbol: holding.symbol,
          quantity: holding.quantity,
          average_buy_price: holding.averageBuyPrice,
        },
      ]);
    fetchAll();
    return error;
  };

  const removeHolding = async (holdingId: string) => {
    await supabase.from("portfolio_holdings").delete().eq("id", holdingId);
    fetchAll();
  };

  return {
    portfolios,
    loading,
    error,
    addPortfolio,
    removePortfolio,
    addHolding,
    removeHolding,
    refresh: fetchAll,
  };
}
