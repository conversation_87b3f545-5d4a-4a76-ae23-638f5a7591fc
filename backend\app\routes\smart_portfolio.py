"""
Smart Portfolio API Routes - مسارات واجهة برمجة المحفظة الذكية
تحتوي على جميع endpoints للمحفظة الذكية ونسخ التداول
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import asyncio

from ..services.smart_portfolio_processor import SmartPortfolioProcessor
from ..services.copy_trading_service import AdvancedCopyTradingService
from ..services.portfolio_analytics import PortfolioAnalytics
from ..models.smart_portfolio import SmartPortfolioPosition, SmartPortfolioTransaction, SmartPortfolioMetrics
from ..schemas.smart_portfolio import *
from ..database import get_db
from ..auth import get_current_user, require_vip_access
from ..services.websocket_manager import signal_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/smart-portfolio", tags=["Smart Portfolio"])

# Dashboard و المعلومات العامة
@router.get("/dashboard", response_model=SmartPortfolioDashboard)
async def get_smart_portfolio_dashboard(
    timeframe: str = Query("1M", pattern="^(1D|1W|1M|3M|6M|1Y)$"),
    db: Session = Depends(get_db)
):
    """
    الحصول على لوحة تحكم المحفظة الذكية الكاملة
    """
    try:
        processor = SmartPortfolioProcessor(db)
        analytics = PortfolioAnalytics(db)
        
        # الحصول على البيانات بالتوازي
        dashboard_data = await asyncio.gather(
            processor.get_portfolio_summary(),
            processor.get_active_positions(),
            analytics.get_performance_metrics(timeframe),
            processor.get_recent_signal_processing(),
            analytics.get_trading_statistics(),
            analytics.get_benchmark_comparison(timeframe),
            get_copy_trading_stats(db)
        )
        
        return SmartPortfolioDashboard(
            portfolio_summary=dashboard_data[0],
            active_positions=dashboard_data[1],
            performance_metrics=dashboard_data[2],
            recent_signal_processing=dashboard_data[3],
            trading_statistics=dashboard_data[4],
            benchmark_comparison=dashboard_data[5],
            copy_trading_stats=dashboard_data[6]
        )
        
    except Exception as e:
        logger.error(f"Error getting dashboard: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/positions", response_model=List[SmartPortfolioPosition])
async def get_active_positions(
    status: Optional[PositionStatus] = None,
    stock_code: Optional[str] = None,
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """
    الحصول على المراكز النشطة مع إمكانية التصفية
    """
    try:
        query = db.query(SmartPortfolioPosition)
        
        if status:
            query = query.filter(SmartPortfolioPosition.status == status.value)
        if stock_code:
            query = query.filter(SmartPortfolioPosition.stock_code == stock_code.upper())
        
        positions = query.order_by(SmartPortfolioPosition.created_at.desc())\
                        .offset(offset).limit(limit).all()
        
        return positions
        
    except Exception as e:
        logger.error(f"Error getting positions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/positions/{position_id}", response_model=SmartPortfolioPosition)
async def get_position_details(
    position_id: str,
    db: Session = Depends(get_db)
):
    """
    الحصول على تفاصيل مركز معين
    """
    try:
        position = db.query(SmartPortfolioPosition)\
                    .filter(SmartPortfolioPosition.position_id == position_id)\
                    .first()
        
        if not position:
            raise HTTPException(status_code=404, detail="Position not found")
        
        return position
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting position details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/transactions", response_model=List[SmartPortfolioTransaction])
async def get_transactions_history(
    position_id: Optional[str] = None,
    transaction_type: Optional[TransactionType] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: int = Query(100, le=500),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """
    الحصول على تاريخ المعاملات
    """
    try:
        query = db.query(SmartPortfolioTransaction)
        
        if position_id:
            query = query.filter(SmartPortfolioTransaction.position_id == position_id)
        if transaction_type:
            query = query.filter(SmartPortfolioTransaction.transaction_type == transaction_type.value)
        if start_date:
            query = query.filter(SmartPortfolioTransaction.timestamp >= start_date)
        if end_date:
            query = query.filter(SmartPortfolioTransaction.timestamp <= end_date)
        
        transactions = query.order_by(SmartPortfolioTransaction.timestamp.desc())\
                           .offset(offset).limit(limit).all()
        
        return transactions
        
    except Exception as e:
        logger.error(f"Error getting transactions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# معالجة الإشارات
@router.post("/process-signal", response_model=SignalProcessingResult)
async def process_signal(
    signal_data: SignalProcessingRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    معالجة إشارة جديدة للمحفظة الذكية
    """
    try:
        processor = SmartPortfolioProcessor(db)
        
        # معالجة الإشارة
        result = await processor.process_incoming_signal(signal_data.dict())
        
        # إرسال التحديثات في الخلفية
        background_tasks.add_task(
            send_real_time_updates, 
            result, 
            signal_data.stock_code
        )
        
        return SignalProcessingResult(**result)
        
    except Exception as e:
        logger.error(f"Error processing signal: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/signal-processing-history")
async def get_signal_processing_history(
    days: int = Query(7, ge=1, le=30),
    result_filter: Optional[ProcessingResult] = None,
    stock_code: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    الحصول على تاريخ معالجة الإشارات
    """
    try:
        from ..models.smart_portfolio import SmartPortfolioSignalProcessing
        
        start_date = datetime.now() - timedelta(days=days)
        query = db.query(SmartPortfolioSignalProcessing)\
                .filter(SmartPortfolioSignalProcessing.processed_at >= start_date)
        
        if result_filter:
            query = query.filter(SmartPortfolioSignalProcessing.processing_result == result_filter.value)
        if stock_code:
            query = query.filter(SmartPortfolioSignalProcessing.stock_code == stock_code.upper())
        
        history = query.order_by(SmartPortfolioSignalProcessing.processed_at.desc()).all()
        
        return [
            {
                "processing_id": h.processing_id,
                "stock_code": h.stock_code,
                "signal_type": h.signal_type,
                "processing_result": h.processing_result,
                "decision_reason": h.decision_reason,
                "signal_confidence": h.signal_confidence,
                "processed_at": h.processed_at,
                "position_id": h.position_id
            }
            for h in history
        ]
        
    except Exception as e:
        logger.error(f"Error getting signal processing history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# مقاييس الأداء والتحليلات
@router.get("/performance", response_model=PortfolioPerformanceMetrics)
async def get_performance_metrics(
    timeframe: str = Query("1M", pattern="^(1D|1W|1M|3M|6M|1Y)$"),
    db: Session = Depends(get_db)
):
    """
    الحصول على مقاييس أداء المحفظة
    """
    try:
        analytics = PortfolioAnalytics(db)
        metrics = await analytics.get_performance_metrics(timeframe)
        
        return metrics
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/performance/detailed")
async def get_detailed_performance_analysis(
    timeframe: str = Query("1M", pattern="^(1D|1W|1M|3M|6M|1Y)$"),
    db: Session = Depends(get_db)
):
    """
    الحصول على تحليل أداء مفصل
    """
    try:
        analytics = PortfolioAnalytics(db)
        detailed_analysis = await analytics.get_detailed_analysis(timeframe)
        
        return detailed_analysis
        
    except Exception as e:
        logger.error(f"Error getting detailed analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/risk-metrics", response_model=RiskMetrics)
async def get_risk_metrics(
    db: Session = Depends(get_db)
):
    """
    الحصول على مقاييس المخاطر المتقدمة
    """
    try:
        analytics = PortfolioAnalytics(db)
        risk_metrics = await analytics.calculate_risk_metrics()
        
        return risk_metrics
        
    except Exception as e:
        logger.error(f"Error getting risk metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/comparison/benchmark", response_model=PerformanceComparison)
async def get_benchmark_comparison(
    timeframe: str = Query("1M", pattern="^(1D|1W|1M|3M|6M|1Y)$"),
    benchmark: str = Query("EGX30", pattern="^(EGX30|EGX70|EGX100)$"),
    db: Session = Depends(get_db)
):
    """
    مقارنة أداء المحفظة بالمؤشر
    """
    try:
        analytics = PortfolioAnalytics(db)
        comparison = await analytics.compare_with_benchmark(timeframe, benchmark)
        
        return comparison
        
    except Exception as e:
        logger.error(f"Error getting benchmark comparison: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# إدارة المراكز
@router.post("/positions/{position_id}/manage")
async def manage_position(
    position_id: str,
    management_request: PositionManagementRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    إدارة مركز معين (إغلاق، تعديل وقف الخسارة، إلخ)
    """
    try:
        processor = SmartPortfolioProcessor(db)
        
        # التحقق من وجود المركز
        position = db.query(SmartPortfolioPosition)\
                    .filter(SmartPortfolioPosition.position_id == position_id)\
                    .first()
        
        if not position:
            raise HTTPException(status_code=404, detail="Position not found")
        
        # تنفيذ الإجراء المطلوب
        result = await processor.manage_position(position_id, management_request.dict())
        
        # إرسال التحديثات
        background_tasks.add_task(
            send_position_update,
            position_id,
            result
        )
        
        return JSONResponse(
            status_code=200,
            content={"success": True, "message": "Position managed successfully", "data": result}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error managing position: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/positions/{position_id}/close")
async def close_position(
    position_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    close_percentage: float = Query(100.0, ge=1.0, le=100.0),
    reason: str = Query("MANUAL", pattern="^(MANUAL|STOP_LOSS|TARGET_REACHED|RISK_MANAGEMENT)$")
):
    """
    إغلاق مركز بالكامل أو جزئياً
    """
    try:
        processor = SmartPortfolioProcessor(db)
        
        result = await processor.close_position(
            position_id, 
            close_percentage / 100.0,
            reason
        )
        
        background_tasks.add_task(
            send_position_closure_update,
            position_id,
            result
        )
        
        return JSONResponse(
            status_code=200,
            content={"success": True, "message": "Position closed successfully", "data": result}
        )
        
    except Exception as e:
        logger.error(f"Error closing position: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# نسخ التداول (VIP فقط)
@router.post("/copy-trading/enable")
async def enable_copy_trading(
    copy_request: CopyTradingRequest,
    current_user = Depends(require_vip_access),
    db: Session = Depends(get_db)
):
    """
    تفعيل نسخ التداول للأعضاء VIP
    """
    try:
        copy_service = AdvancedCopyTradingService(db)
        
        result = await copy_service.enable_copy_trading(copy_request)
        
        return JSONResponse(
            status_code=200,
            content={"success": True, "message": "Copy trading enabled successfully", "data": result}
        )
        
    except Exception as e:
        logger.error(f"Error enabling copy trading: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/copy-trading/subscriptions", response_model=List[CopyTradingSubscription])
async def get_copy_trading_subscriptions(
    user_id: Optional[int] = None,
    active_only: bool = True,
    current_user = Depends(require_vip_access),
    db: Session = Depends(get_db)
):
    """
    الحصول على اشتراكات نسخ التداول
    """
    try:
        from ..models.smart_portfolio import CopyTradingSubscription as CopyModel
        
        query = db.query(CopyModel)
        
        if user_id:
            query = query.filter(CopyModel.user_id == user_id)
        if active_only:
            query = query.filter(CopyModel.is_active == True)
        
        subscriptions = query.all()
        
        return subscriptions
        
    except Exception as e:
        logger.error(f"Error getting copy trading subscriptions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/copy-trading/trades", response_model=List[CopiedTrade])
async def get_copied_trades(
    subscription_id: Optional[str] = None,
    status: Optional[PositionStatus] = None,
    limit: int = Query(50, le=200),
    offset: int = Query(0, ge=0),
    current_user = Depends(require_vip_access),
    db: Session = Depends(get_db)
):
    """
    الحصول على الصفقات المنسوخة
    """
    try:
        from ..models.smart_portfolio import CopiedTrade as CopiedTradeModel
        
        query = db.query(CopiedTradeModel)
        
        if subscription_id:
            query = query.filter(CopiedTradeModel.subscription_id == subscription_id)
        if status:
            query = query.filter(CopiedTradeModel.status == status.value)
        
        trades = query.order_by(CopiedTradeModel.created_at.desc())\
                     .offset(offset).limit(limit).all()
        
        return trades
        
    except Exception as e:
        logger.error(f"Error getting copied trades: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# اقتراحات التحسين
@router.get("/optimization-suggestions", response_model=PortfolioOptimizationSuggestions)
async def get_optimization_suggestions(
    db: Session = Depends(get_db)
):
    """
    الحصول على اقتراحات تحسين المحفظة
    """
    try:
        analytics = PortfolioAnalytics(db)
        suggestions = await analytics.get_optimization_suggestions()
        
        return suggestions
        
    except Exception as e:
        logger.error(f"Error getting optimization suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# إحصائيات وتقارير
@router.get("/statistics/summary")
async def get_portfolio_statistics_summary(
    period: str = Query("1M", pattern="^(1D|1W|1M|3M|6M|1Y|ALL)$"),
    db: Session = Depends(get_db)
):
    """
    الحصول على ملخص إحصائيات المحفظة
    """
    try:
        analytics = PortfolioAnalytics(db)
        summary = await analytics.get_statistics_summary(period)
        
        return summary
        
    except Exception as e:
        logger.error(f"Error getting statistics summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/export/report")
async def export_portfolio_report(
    format_type: str = Query("json", pattern="^(json|csv|pdf)$"),
    timeframe: str = Query("1M", pattern="^(1D|1W|1M|3M|6M|1Y)$"),
    include_positions: bool = True,
    include_transactions: bool = True,
    include_analytics: bool = True,
    db: Session = Depends(get_db)
):
    """
    تصدير تقرير المحفظة بصيغ مختلفة
    """
    try:
        analytics = PortfolioAnalytics(db)
        
        report_data = await analytics.generate_comprehensive_report(
            format_type=format_type,
            timeframe=timeframe,
            include_positions=include_positions,
            include_transactions=include_transactions,
            include_analytics=include_analytics
        )
        
        if format_type == "json":
            return JSONResponse(content=report_data)
        else:
            # سيتم تنفيذ CSV و PDF لاحقاً
            return JSONResponse(content={"message": f"{format_type} export not implemented yet"})
        
    except Exception as e:
        logger.error(f"Error exporting report: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Helper Functions
async def send_real_time_updates(result: Dict[str, Any], stock_code: str):
    """إرسال التحديثات الفورية عبر WebSocket"""
    try:
        await signal_manager.broadcast({
            "type": "smart_portfolio_signal_processed",
            "data": {
                "stock_code": stock_code,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        })
    except Exception as e:
        logger.error(f"Error sending real-time updates: {e}")

async def send_position_update(position_id: str, result: Dict[str, Any]):
    """إرسال تحديث المركز"""
    try:
        await signal_manager.broadcast({
            "type": "smart_portfolio_position_updated",
            "data": {
                "position_id": position_id,
                "update": result,
                "timestamp": datetime.now().isoformat()
            }
        })
    except Exception as e:
        logger.error(f"Error sending position update: {e}")

async def send_position_closure_update(position_id: str, result: Dict[str, Any]):
    """إرسال تحديث إغلاق المركز"""
    try:
        await signal_manager.broadcast({
            "type": "smart_portfolio_position_closed",
            "data": {
                "position_id": position_id,
                "closure": result,
                "timestamp": datetime.now().isoformat()
            }
        })
    except Exception as e:
        logger.error(f"Error sending position closure update: {e}")

async def get_copy_trading_stats(db: Session) -> Dict[str, Any]:
    """الحصول على إحصائيات نسخ التداول"""
    try:
        from ..models.smart_portfolio import CopyTradingSubscription, CopiedTrade
        
        # عدد المشتركين النشطين
        active_subscribers = db.query(CopyTradingSubscription)\
                              .filter(CopyTradingSubscription.is_active == True)\
                              .count()
        
        # إجمالي الصفقات المنسوخة اليوم
        today = datetime.now().date()
        todays_copies = db.query(CopiedTrade)\
                         .filter(CopiedTrade.entry_date >= today)\
                         .count()
        
        # إجمالي الأرباح للمشتركين
        total_copy_pnl = db.query(CopiedTrade.total_pnl).scalar() or 0.0
        
        return {
            "active_subscribers": active_subscribers,
            "todays_copies": todays_copies,
            "total_copy_pnl": total_copy_pnl,
            "copy_success_rate": 73.5  # سيتم حسابها من البيانات الفعلية
        }
        
    except Exception as e:
        logger.error(f"Error getting copy trading stats: {e}")
        return {
            "active_subscribers": 0,
            "todays_copies": 0,
            "total_copy_pnl": 0.0,
            "copy_success_rate": 0.0
        }
