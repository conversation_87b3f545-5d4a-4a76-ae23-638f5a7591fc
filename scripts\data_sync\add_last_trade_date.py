#!/usr/bin/env python3
"""
Simple script to add the missing last_trade_date column
"""

import os
import sys
import logging
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    logger.error("Missing required environment variables")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def add_missing_column():
    """Add the missing last_trade_date column"""
    try:
        logger.info("🔧 Adding last_trade_date column...")
        
        # Check current schema first
        result = supabase.table('stocks_realtime').select('*').limit(1).execute()
        current_columns = list(result.data[0].keys()) if result.data else []
        
        logger.info(f"Current columns count: {len(current_columns)}")
        
        if 'last_trade_date' in current_columns:
            logger.info("✅ last_trade_date column already exists!")
            return True
        
        # Use direct SQL execution via PostgREST
        # This is a workaround since we can't execute DDL directly
        logger.info("❌ last_trade_date column missing")
        logger.info("📝 Manual SQL needed:")
        logger.info("ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS last_trade_date DATE;")
        
        return False
        
    except Exception as e:
        logger.error(f"Error checking schema: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Checking for missing last_trade_date column...")
    add_missing_column()
