
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

type Props = {
  loading: boolean;
  onAdd: (
    symbol: string,
    targetPrice: number,
    condition: "above" | "below"
  ) => Promise<boolean>;
};

export const AddAlertForm: React.FC<Props> = ({ loading, onAdd }) => {
  const [symbol, setSymbol] = useState("");
  const [targetPrice, setTargetPrice] = useState("");
  const [condition, setCondition] = useState<"above" | "below">("above");

  const handleAdd = async () => {
    if (!symbol || !targetPrice) return;
    await onAdd(symbol, parseFloat(targetPrice), condition);
    setSymbol("");
    setTargetPrice("");
    setCondition("above");
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-white/60 rounded-lg">
      <Input
        placeholder="رمز السهم (COMI)"
        value={symbol}
        onChange={(e) => setSymbol(e.target.value)}
      />
      <Input
        type="number"
        placeholder="السعر المستهدف"
        value={targetPrice}
        onChange={(e) => setTargetPrice(e.target.value)}
      />
      <select
        className="px-3 py-2 border rounded-md"
        value={condition}
        onChange={(e) => setCondition(e.target.value as "above" | "below")}
      >
        <option value="above">أعلى من</option>
        <option value="below">أقل من</option>
      </select>
      <Button
        onClick={handleAdd}
        className="bg-blue-600 hover:bg-blue-700"
        disabled={loading}
      >
        إضافة تنبيه
      </Button>
    </div>
  );
};
