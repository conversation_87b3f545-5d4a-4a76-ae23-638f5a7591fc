"""
Copy Trading Service - خدمة نسخ التداول المتقدمة للأعضاء VIP
تتيح للمستخدمين نسخ صفقات المحفظة الذكية تلقائياً مع إدارة مخاطر متقدمة
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
import numpy as np

from ..models.smart_portfolio import (
    SmartPortfolioPosition, 
    SmartPortfolioTransaction,
    CopyTradingSubscription, 
    CopiedTrade
)
from ..schemas.smart_portfolio import CopyTradingRequest
from ..services.notification_service import NotificationService
from ..services.telegram_service import TelegramService
from ..services.advanced_position_sizer import AdvancedPositionSizer
from ..services.websocket_manager import signal_manager

logger = logging.getLogger(__name__)

class AdvancedCopyTradingService:
    """خدمة نسخ التداول المتقدمة والذكية"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService()
        self.telegram_service = TelegramService()
        self.position_sizer = AdvancedPositionSizer(db)
        
        # إعدادات نسخ التداول المتقدمة
        self.copy_trading_fee = 0.001          # 0.1% رسوم إضافية
        self.min_copy_amount = 5000.0          # حد أدنى 5000 جنيه للصفقة
        self.max_copy_percentage = 1.5         # حد أقصى 150% من حجم المحفظة الذكية
        self.max_portfolio_exposure = 0.8      # حد أقصى 80% من رأس المال
        self.risk_scaling_factor = 0.8         # تقليل المخاطرة لنسخ التداول
        
        # مستويات العضوية
        self.vip_levels = {
            'VIP': {'max_copy_percentage': 1.0, 'priority': 'MEDIUM'},
            'VIP_PLUS': {'max_copy_percentage': 1.5, 'priority': 'HIGH'},
            'VIP_PREMIUM': {'max_copy_percentage': 2.0, 'priority': 'HIGHEST'}
        }
    
    async def enable_advanced_copy_trading(self, copy_request: CopyTradingRequest) -> Dict:
        """تفعيل نسخ التداول المتقدم مع التحليل الذكي"""
        try:
            logger.info(f"🎯 Enabling advanced copy trading for user {copy_request.user_id}")
            
            # 1. التحقق من صلاحيات العضوية
            membership_check = await self._verify_vip_membership(copy_request.user_id)
            if not membership_check['is_eligible']:
                return {
                    'success': False,
                    'error': 'VIP membership required for copy trading',
                    'membership_info': membership_check
                }
            
            # 2. التحقق من عدم وجود اشتراك نشط
            existing_subscription = self.db.query(CopyTradingSubscription)\
                .filter(and_(
                    CopyTradingSubscription.user_id == copy_request.user_id,
                    CopyTradingSubscription.is_active == True
                )).first()
            
            if existing_subscription:
                return {
                    'success': False,
                    'error': 'Copy trading already enabled',
                    'existing_subscription': {
                        'created_at': existing_subscription.created_at,
                        'copy_percentage': existing_subscription.copy_percentage,
                        'total_capital': existing_subscription.total_capital
                    }
                }
            
            # 3. تحليل رأس المال والمخاطر
            capital_analysis = await self._analyze_user_capital(copy_request)
            if not capital_analysis['is_sufficient']:
                return {
                    'success': False,
                    'error': f"Insufficient capital. Minimum required: {capital_analysis['min_required']} EGP",
                    'capital_analysis': capital_analysis
                }
            
            # 4. إنشاء اشتراك نسخ التداول المتقدم
            subscription = await self._create_advanced_subscription(copy_request, membership_check, capital_analysis)
            
            # 5. إعداد المراقبة والإشعارات
            await self._setup_copy_trading_monitoring(subscription)
            
            # 6. نسخ المراكز النشطة الحالية (اختياري)
            if copy_request.copy_existing_positions:
                copy_results = await self._copy_existing_positions(subscription)
            else:
                copy_results = {'copied_positions': 0, 'skipped_positions': 0}
            
            logger.info(f"✅ Copy trading enabled successfully for user {copy_request.user_id}")
            
            return {
                'success': True,
                'subscription_id': subscription.subscription_id,
                'subscription_details': {
                    'user_id': subscription.user_id,
                    'copy_percentage': subscription.copy_percentage,
                    'total_capital': subscription.total_capital,
                    'risk_level': subscription.risk_level,
                    'vip_level': membership_check['vip_level'],
                    'copy_settings': subscription.copy_settings
                },
                'initial_copy_results': copy_results,
                'estimated_performance': await self._estimate_copy_performance(subscription),
                'next_steps': [
                    'Monitor smart portfolio for new signals',
                    'Automatic position copying enabled',
                    'Real-time notifications activated'
                ]
            }
            
        except Exception as e:
            logger.error(f"💥 Error enabling copy trading: {e}")
            return {'success': False, 'error': str(e)}
    
    async def process_smart_portfolio_trade(self, original_trade: SmartPortfolioTransaction) -> Dict:
        """معالجة صفقة من المحفظة الذكية ونسخها للمشتركين"""
        try:
            logger.info(f"🔄 Processing smart portfolio trade for copying: {original_trade.transaction_id}")
            
            # الحصول على جميع المشتركين النشطين
            active_subscriptions = self.db.query(CopyTradingSubscription)\
                .filter(CopyTradingSubscription.is_active == True)\
                .order_by(CopyTradingSubscription.priority.desc())\
                .all()
            
            if not active_subscriptions:
                logger.info("No active copy trading subscriptions found")
                return {'copied_trades': 0, 'total_subscriptions': 0}
            
            copy_results = {
                'successful_copies': 0,
                'failed_copies': 0,
                'total_subscriptions': len(active_subscriptions),
                'copy_details': []
            }
            
            # معالجة كل اشتراك
            for subscription in active_subscriptions:
                try:
                    copy_result = await self._execute_copy_trade(original_trade, subscription)
                    copy_results['copy_details'].append(copy_result)
                    
                    if copy_result['success']:
                        copy_results['successful_copies'] += 1
                        await self._send_copy_success_notification(subscription, copy_result)
                    else:
                        copy_results['failed_copies'] += 1
                        await self._send_copy_failure_notification(subscription, copy_result)
                        
                except Exception as e:
                    logger.error(f"Error copying trade for user {subscription.user_id}: {e}")
                    copy_results['failed_copies'] += 1
            
            # تحديث إحصائيات النسخ
            await self._update_copy_trading_metrics(original_trade, copy_results)
            
            logger.info(f"✅ Copy trading completed: {copy_results['successful_copies']}/{copy_results['total_subscriptions']} successful")
            
            return copy_results
            
        except Exception as e:
            logger.error(f"💥 Error processing smart portfolio trade for copying: {e}")
            return {'error': str(e)}
    
    async def _execute_copy_trade(self, original_trade: SmartPortfolioTransaction, subscription: CopyTradingSubscription) -> Dict:
        """تنفيذ نسخ صفقة واحدة مع التحليل المتقدم"""
        try:
            # 1. فحص إعدادات النسخ
            copy_settings = subscription.copy_settings or {}
            if not self._should_copy_trade(original_trade, copy_settings):
                return {
                    'success': False,
                    'reason': 'Trade filtered by copy settings',
                    'user_id': subscription.user_id
                }
            
            # 2. تحليل رأس المال المتاح
            available_capital = await self._get_user_available_capital(subscription.user_id)
            if available_capital < self.min_copy_amount:
                return {
                    'success': False,
                    'reason': f'Insufficient capital: {available_capital} EGP',
                    'user_id': subscription.user_id
                }
            
            # 3. حساب حجم المركز المنسوخ
            copy_size_analysis = await self._calculate_copy_position_size(
                original_trade, subscription, available_capital
            )
            
            if copy_size_analysis['shares'] <= 0:
                return {
                    'success': False,
                    'reason': 'Calculated position size too small',
                    'user_id': subscription.user_id,
                    'analysis': copy_size_analysis
                }
            
            # 4. تنفيذ الصفقة المنسوخة
            copied_trade = await self._create_copied_trade(
                original_trade, subscription, copy_size_analysis
            )
            
            # 5. تحديث إحصائيات المستخدم
            await self._update_user_copy_metrics(subscription, copied_trade)
            
            return {
                'success': True,
                'copied_trade_id': copied_trade.copied_trade_id,
                'user_id': subscription.user_id,
                'original_value': original_trade.total_value,
                'copied_value': copied_trade.total_value,
                'copy_percentage': copy_size_analysis['copy_percentage'],
                'shares_copied': copied_trade.shares,
                'fees_applied': copied_trade.fees,
                'risk_analysis': copy_size_analysis.get('risk_analysis', {})
            }
            
        except Exception as e:
            logger.error(f"Error executing copy trade for user {subscription.user_id}: {e}")
            return {
                'success': False,
                'reason': f'Execution error: {str(e)}',
                'user_id': subscription.user_id
            }
    
    async def _calculate_copy_position_size(self, original_trade: SmartPortfolioTransaction, 
                                          subscription: CopyTradingSubscription, available_capital: float) -> Dict:
        """حساب حجم المركز المنسوخ مع إدارة المخاطر المتقدمة"""
        try:
            # 1. حساب النسبة الأساسية للنسخ
            base_copy_percentage = subscription.copy_percentage / 100
            
            # 2. تطبيق حدود العضوية
            vip_level = subscription.vip_level or 'VIP'
            max_allowed = self.vip_levels.get(vip_level, {}).get('max_copy_percentage', 1.0)
            copy_percentage = min(base_copy_percentage, max_allowed)
            
            # 3. حساب القيمة الأساسية
            base_copy_value = original_trade.total_value * copy_percentage
            
            # 4. تطبيق حدود رأس المال
            max_by_capital = available_capital * self.max_portfolio_exposure
            limited_copy_value = min(base_copy_value, max_by_capital)
            
            # 5. تطبيق الحد الأدنى
            if limited_copy_value < self.min_copy_amount:
                return {
                    'shares': 0,
                    'copy_value': 0,
                    'reason': f'Below minimum copy amount: {self.min_copy_amount}'
                }
            
            # 6. حساب عدد الأسهم
            price_per_share = original_trade.price
            shares = int(limited_copy_value / price_per_share)
            actual_copy_value = shares * price_per_share
            
            # 7. حساب الرسوم
            trading_fees = actual_copy_value * self.copy_trading_fee
            total_cost = actual_copy_value + trading_fees
            
            # 8. تحليل المخاطر
            original_position = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.position_id == original_trade.position_id)\
                .first()
            
            risk_analysis = {}
            if original_position:
                position_risk = (original_position.entry_price - original_position.stop_loss_price) / original_position.entry_price
                copy_risk_amount = shares * price_per_share * position_risk
                risk_percentage = (copy_risk_amount / available_capital) * 100
                
                risk_analysis = {
                    'position_risk_percentage': round(position_risk * 100, 2),
                    'copy_risk_amount': round(copy_risk_amount, 2),
                    'risk_to_capital_percentage': round(risk_percentage, 2),
                    'risk_level': 'HIGH' if risk_percentage > 3 else 'MEDIUM' if risk_percentage > 1.5 else 'LOW'
                }
            
            return {
                'shares': shares,
                'copy_value': actual_copy_value,
                'total_cost': total_cost,
                'trading_fees': trading_fees,
                'copy_percentage': round((actual_copy_value / original_trade.total_value) * 100, 2),
                'capital_utilization': round((total_cost / available_capital) * 100, 2),
                'risk_analysis': risk_analysis,
                'pricing': {
                    'original_trade_value': original_trade.total_value,
                    'requested_copy_percentage': subscription.copy_percentage,
                    'actual_copy_percentage': copy_percentage * 100,
                    'price_per_share': price_per_share
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating copy position size: {e}")
            return {'shares': 0, 'error': str(e)}
    
    async def _create_copied_trade(self, original_trade: SmartPortfolioTransaction, 
                                 subscription: CopyTradingSubscription, copy_analysis: Dict) -> CopiedTrade:
        """إنشاء صفقة منسوخة في قاعدة البيانات"""
        try:
            copied_trade = CopiedTrade(
                copied_trade_id=f"COPY_{original_trade.transaction_id}_{subscription.user_id}_{int(datetime.now().timestamp())}",
                original_transaction_id=original_trade.transaction_id,
                copy_subscription_id=subscription.subscription_id,
                user_id=subscription.user_id,
                
                # تفاصيل الصفقة
                stock_code=original_trade.position.stock_code,
                transaction_type=original_trade.transaction_type,
                shares=copy_analysis['shares'],
                price=original_trade.price,
                total_value=copy_analysis['copy_value'],
                fees=copy_analysis['trading_fees'],
                
                # تفاصيل النسخ
                copy_percentage=copy_analysis['copy_percentage'],
                original_trade_value=original_trade.total_value,
                
                # إدارة المخاطر
                risk_analysis=copy_analysis.get('risk_analysis', {}),
                
                # معلومات إضافية
                execution_quality='GOOD',  # سيتم تحسينها لاحقاً
                notes=f"Copied from smart portfolio trade {original_trade.transaction_id}",
                
                # التوقيتات
                executed_at=datetime.now(),
                created_at=datetime.now()
            )
            
            self.db.add(copied_trade)
            self.db.commit()
            
            logger.info(f"✅ Created copied trade {copied_trade.copied_trade_id} for user {subscription.user_id}")
            return copied_trade
            
        except Exception as e:
            logger.error(f"Error creating copied trade: {e}")
            self.db.rollback()
            raise
    
    # =============== وظائف التحليل والفحص ===============
    
    async def _verify_vip_membership(self, user_id: str) -> Dict:
        """التحقق من عضوية VIP"""
        try:
            # في التطبيق الحقيقي، سيتم فحص عضوية المستخدم من قاعدة البيانات
            # هنا نفترض أن جميع المستخدمين VIP للاختبار
            return {
                'is_eligible': True,
                'vip_level': 'VIP_PLUS',
                'benefits': {
                    'max_copy_percentage': 1.5,
                    'priority_execution': True,
                    'advanced_analytics': True,
                    'personal_support': True
                }
            }
        except Exception as e:
            logger.error(f"Error verifying VIP membership: {e}")
            return {'is_eligible': False, 'error': str(e)}
    
    async def _analyze_user_capital(self, copy_request: CopyTradingRequest) -> Dict:
        """تحليل رأس مال المستخدم"""
        try:
            total_capital = copy_request.total_capital
            min_required = self.min_copy_amount * 10  # على الأقل 10 صفقات
            
            return {
                'total_capital': total_capital,
                'min_required': min_required,
                'is_sufficient': total_capital >= min_required,
                'max_position_size': total_capital * self.max_portfolio_exposure,
                'recommended_copy_percentage': min(100, (min_required / total_capital) * 100),
                'risk_assessment': {
                    'capital_adequacy': 'EXCELLENT' if total_capital > min_required * 5 else 
                                      'GOOD' if total_capital > min_required * 2 else 'MINIMUM',
                    'diversification_potential': total_capital > 100000,
                    'recommended_max_exposure': 60 if total_capital < 50000 else 80
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing user capital: {e}")
            return {'is_sufficient': False, 'error': str(e)}
    
    async def _should_copy_trade(self, original_trade: SmartPortfolioTransaction, copy_settings: Dict) -> bool:
        """تحديد ما إذا كان يجب نسخ الصفقة"""
        try:
            # فحص نوع المعاملة
            if copy_settings.get('copy_buy_only', False) and original_trade.transaction_type != 'BUY':
                return False
            
            # فحص الحد الأدنى لقيمة الصفقة
            min_trade_value = copy_settings.get('min_trade_value', 0)
            if original_trade.total_value < min_trade_value:
                return False
            
            # فحص القطاعات المستبعدة
            excluded_sectors = copy_settings.get('excluded_sectors', [])
            if excluded_sectors:  # سيتم ربطها ببيانات القطاعات لاحقاً
                pass
            
            # فحص أوقات التداول
            trading_hours = copy_settings.get('trading_hours', {})
            if trading_hours:
                current_hour = datetime.now().hour
                start_hour = trading_hours.get('start', 0)
                end_hour = trading_hours.get('end', 24)
                if not (start_hour <= current_hour <= end_hour):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking if should copy trade: {e}")
            return True  # افتراضياً نسخ الصفقة
    
    async def _get_user_available_capital(self, user_id: str) -> float:
        """حساب رأس المال المتاح للمستخدم"""
        try:
            # في التطبيق الحقيقي، سيتم حساب رأس المال الفعلي
            # هنا نفترض قيماً افتراضية للاختبار
            subscription = self.db.query(CopyTradingSubscription)\
                .filter(CopyTradingSubscription.user_id == user_id)\
                .first()
            
            if subscription:
                # حساب رأس المال المستثمر في الصفقات المنسوخة
                copied_trades = self.db.query(CopiedTrade)\
                    .filter(and_(
                        CopiedTrade.user_id == user_id,
                        CopiedTrade.status == 'ACTIVE'
                    )).all()
                
                invested_amount = sum(trade.total_value for trade in copied_trades)
                available = subscription.total_capital - invested_amount
                
                return max(0, available)            
            return 0
            
        except Exception as e:
            logger.error(f"Error getting user available capital: {e}")
            return 0
            
            # إنشاء معرف اشتراك جديد
            subscription_id = f"CT_{copy_request.user_id}_{int(datetime.now().timestamp())}"
            
            # إنشاء اشتراك نسخ التداول
            subscription = CopyTradingSubscription(
                user_id=copy_request.user_id,
                subscription_id=subscription_id,
                is_active=True,
                subscription_type='VIP',
                copy_percentage=copy_request.copy_percentage,
                max_position_size=copy_request.max_position_size,
                max_positions_count=copy_request.max_positions_count,
                min_signal_confidence=copy_request.min_signal_confidence,
                max_risk_per_trade=copy_request.max_risk_per_trade,
                excluded_stocks=copy_request.excluded_stocks,
                included_sectors=copy_request.included_sectors,
                available_balance=copy_request.available_balance,
                reserved_balance=0.0,
                notifications_enabled=copy_request.notifications_enabled,
                telegram_chat_id=copy_request.telegram_chat_id,
                last_activity=datetime.now()
            )
            
            self.db.add(subscription)
            self.db.commit()
            
            # إرسال إشعار تفعيل
            await self._send_activation_notification(subscription)
            
            logger.info(f"Copy trading enabled for user {copy_request.user_id}")
            
            return {
                'success': True,
                'message': 'تم تفعيل نسخ التداول بنجاح',
                'subscription_id': subscription_id,
                'settings': {
                    'copy_percentage': copy_request.copy_percentage,
                    'max_position_size': copy_request.max_position_size,
                    'available_balance': copy_request.available_balance,
                    'min_signal_confidence': copy_request.min_signal_confidence
                }
            }
            
        except Exception as e:
            logger.error(f"Error enabling copy trading: {e}")
            self.db.rollback()
            return {'success': False, 'message': f'خطأ في تفعيل نسخ التداول: {str(e)}'}
    
    async def process_new_position_for_copying(self, original_position: SmartPortfolioPosition) -> List[Dict]:
        """معالجة مركز جديد في المحفظة الذكية لنسخه للمشتركين"""
        try:
            # الحصول على المشتركين النشطين
            active_subscriptions = self.db.query(CopyTradingSubscription)\
                .filter(CopyTradingSubscription.is_active == True)\
                .all()
            
            if not active_subscriptions:
                return []
            
            copy_results = []
            
            for subscription in active_subscriptions:
                try:
                    # فحص معايير النسخ
                    should_copy = await self._should_copy_position(subscription, original_position)
                    
                    if should_copy['copy']:
                        # تنفيذ نسخ الصفقة
                        copy_result = await self._execute_copy_trade(subscription, original_position)
                        copy_results.append(copy_result)
                        
                        # إرسال إشعار للمشترك
                        await self._send_copy_notification(subscription, copy_result)
                    else:
                        logger.info(f"Position not copied for user {subscription.user_id}: {should_copy['reason']}")
                        copy_results.append({
                            'user_id': subscription.user_id,
                            'copied': False,
                            'reason': should_copy['reason']
                        })
                        
                except Exception as e:
                    logger.error(f"Error copying position for user {subscription.user_id}: {e}")
                    copy_results.append({
                        'user_id': subscription.user_id,
                        'copied': False,
                        'reason': f'خطأ في النسخ: {str(e)}'
                    })
            
            return copy_results
            
        except Exception as e:
            logger.error(f"Error processing position for copying: {e}")
            return []
    
    async def _should_copy_position(self, subscription: CopyTradingSubscription, position: SmartPortfolioPosition) -> Dict:
        """فحص ما إذا كان يجب نسخ المركز للمشترك"""
        
        # 1. فحص الأسهم المستبعدة
        if position.stock_code in subscription.excluded_stocks:
            return {'copy': False, 'reason': 'السهم في قائمة الاستبعاد'}
        
        # 2. فحص ثقة الإشارة
        if position.signal_confidence and position.signal_confidence < subscription.min_signal_confidence:
            return {'copy': False, 'reason': f'ثقة الإشارة منخفضة: {position.signal_confidence:.2%}'}
        
        # 3. فحص الرصيد المتاح
        position_value = position.initial_shares * position.entry_price
        copy_value = position_value * subscription.copy_percentage
        
        if copy_value > subscription.available_balance:
            return {'copy': False, 'reason': 'الرصيد المتاح غير كافي'}
        
        # 4. فحص حد حجم المركز
        if subscription.max_position_size and copy_value > subscription.max_position_size:
            return {'copy': False, 'reason': 'قيمة المركز تتجاوز الحد الأقصى'}
        
        # 5. فحص عدد المراكز النشطة
        active_copies = self.db.query(CopiedTrade)\
            .filter(and_(
                CopiedTrade.subscription_id == subscription.subscription_id,
                CopiedTrade.status == 'ACTIVE'
            )).count()
        
        if active_copies >= subscription.max_positions_count:
            return {'copy': False, 'reason': 'تم الوصول للحد الأقصى لعدد المراكز'}
        
        # 6. فحص نسبة المخاطرة
        risk_amount = copy_value * subscription.max_risk_per_trade
        if risk_amount > subscription.available_balance * 0.1:  # حد أقصى 10% من الرصيد
            return {'copy': False, 'reason': 'المخاطرة مرتفعة جداً'}
        
        # 7. فحص القطاعات المحددة (إذا كانت محددة)
        if subscription.included_sectors:
            # سنحتاج للحصول على قطاع السهم من قاعدة البيانات
            stock_sector = await self._get_stock_sector(position.stock_code)
            if stock_sector not in subscription.included_sectors:
                return {'copy': False, 'reason': 'القطاع غير مدرج في القائمة المسموحة'}
        
        return {'copy': True, 'reason': 'جميع المعايير مستوفاة'}
    
    async def _execute_copy_trade(self, subscription: CopyTradingSubscription, original_position: SmartPortfolioPosition) -> Dict:
        """تنفيذ نسخ الصفقة"""
        try:
            # حساب تفاصيل النسخ
            original_value = original_position.initial_shares * original_position.entry_price
            copy_value = original_value * subscription.copy_percentage
            copied_shares = int(copy_value / original_position.entry_price)
            actual_copy_value = copied_shares * original_position.entry_price
            
            # حساب الرسوم
            fees = actual_copy_value * self.copy_trading_fee
            total_cost = actual_copy_value + fees
            
            # التحقق من الرصيد مرة أخرى
            if total_cost > subscription.available_balance:
                return {
                    'success': False,
                    'reason': 'الرصيد غير كافي بعد احتساب الرسوم',
                    'required': total_cost,
                    'available': subscription.available_balance
                }
            
            # إنشاء معرف الصفقة المنسوخة
            copy_trade_id = f"CP_{subscription.subscription_id}_{original_position.position_id}"
            
            # إنشاء سجل الصفقة المنسوخة
            copied_trade = CopiedTrade(
                copy_trade_id=copy_trade_id,
                subscription_id=subscription.subscription_id,
                original_position_id=original_position.position_id,
                stock_code=original_position.stock_code,
                stock_name_ar=original_position.stock_name_ar,
                original_shares=original_position.initial_shares,
                copied_shares=copied_shares,
                copy_ratio=subscription.copy_percentage,
                entry_price=original_position.entry_price,
                entry_date=datetime.now(),
                total_investment=actual_copy_value,
                fees_paid=fees,
                status='ACTIVE',
                remaining_shares=copied_shares
            )
            
            self.db.add(copied_trade)
            
            # تحديث رصيد المشترك
            subscription.available_balance -= total_cost
            subscription.reserved_balance += actual_copy_value
            subscription.total_copied_trades += 1
            subscription.last_activity = datetime.now()
            
            self.db.commit()
            
            logger.info(f"Copy trade executed: {copy_trade_id}")
            
            return {
                'success': True,
                'copy_trade_id': copy_trade_id,
                'user_id': subscription.user_id,
                'stock_code': original_position.stock_code,
                'copied_shares': copied_shares,
                'entry_price': original_position.entry_price,
                'total_investment': actual_copy_value,
                'fees_paid': fees,
                'copy_percentage': subscription.copy_percentage * 100
            }
            
        except Exception as e:
            logger.error(f"Error executing copy trade: {e}")
            self.db.rollback()
            return {'success': False, 'reason': f'خطأ في تنفيذ النسخ: {str(e)}'}
    
    async def process_position_closure_for_copying(self, original_position: SmartPortfolioPosition, closure_details: Dict):
        """معالجة إغلاق مركز في المحفظة الذكية وتطبيقه على النسخ"""
        try:
            # البحث عن النسخ المرتبطة بهذا المركز
            copied_trades = self.db.query(CopiedTrade)\
                .filter(and_(
                    CopiedTrade.original_position_id == original_position.position_id,
                    CopiedTrade.status == 'ACTIVE'
                )).all()
            
            closure_results = []
            
            for copied_trade in copied_trades:
                try:
                    # حساب الربح/الخسارة
                    current_price = closure_details.get('close_price', original_position.entry_price)
                    pnl = (current_price - copied_trade.entry_price) * copied_trade.remaining_shares
                    pnl_percentage = ((current_price - copied_trade.entry_price) / copied_trade.entry_price) * 100
                    
                    # تحديث الصفقة المنسوخة
                    copied_trade.status = 'CLOSED'
                    copied_trade.realized_pnl = pnl
                    copied_trade.total_pnl = pnl
                    copied_trade.close_reason = closure_details.get('reason', 'FOLLOWING_ORIGINAL')
                    copied_trade.close_date = datetime.now()
                    
                    # تحديث رصيد المشترك
                    subscription = self.db.query(CopyTradingSubscription)\
                        .filter(CopyTradingSubscription.subscription_id == copied_trade.subscription_id)\
                        .first()
                    
                    if subscription:
                        # إعادة الرصيد المحجوز + الربح/الخسارة
                        proceeds = copied_trade.total_investment + pnl
                        subscription.available_balance += proceeds
                        subscription.reserved_balance -= copied_trade.total_investment
                        subscription.total_pnl += pnl
                        
                        if pnl > 0:
                            subscription.successful_copies += 1
                    
                    closure_result = {
                        'copy_trade_id': copied_trade.copy_trade_id,
                        'user_id': subscription.user_id if subscription else None,
                        'pnl': pnl,
                        'pnl_percentage': pnl_percentage,
                        'proceeds': proceeds,
                        'close_reason': copied_trade.close_reason
                    }
                    
                    closure_results.append(closure_result)
                    
                    # إرسال إشعار الإغلاق
                    await self._send_closure_notification(subscription, copied_trade, closure_result)
                    
                except Exception as e:
                    logger.error(f"Error closing copied trade {copied_trade.copy_trade_id}: {e}")
            
            self.db.commit()
            
            return closure_results
            
        except Exception as e:
            logger.error(f"Error processing position closure for copying: {e}")
            self.db.rollback()
            return []
    
    async def process_partial_profit_for_copying(self, original_position: SmartPortfolioPosition, profit_details: Dict):
        """معالجة الربح الجزئي وتطبيقه على النسخ"""
        try:
            copied_trades = self.db.query(CopiedTrade)\
                .filter(and_(
                    CopiedTrade.original_position_id == original_position.position_id,
                    CopiedTrade.status == 'ACTIVE'
                )).all()
            
            partial_results = []
            
            for copied_trade in copied_trades:
                try:
                    # حساب الكمية للبيع الجزئي
                    sell_percentage = profit_details.get('sell_percentage', 0.25)
                    shares_to_sell = int(copied_trade.remaining_shares * sell_percentage)
                    
                    if shares_to_sell > 0:
                        # حساب الربح المحقق
                        sell_price = profit_details.get('target_price', copied_trade.entry_price * 1.05)
                        partial_pnl = (sell_price - copied_trade.entry_price) * shares_to_sell
                        
                        # تحديث الصفقة المنسوخة
                        copied_trade.remaining_shares -= shares_to_sell
                        copied_trade.realized_pnl += partial_pnl
                        copied_trade.total_pnl += partial_pnl
                        
                        if copied_trade.remaining_shares == 0:
                            copied_trade.status = 'CLOSED'
                            copied_trade.close_date = datetime.now()
                            copied_trade.close_reason = 'ALL_TARGETS_REACHED'
                        else:
                            copied_trade.status = 'PARTIAL_CLOSED'
                        
                        # تحديث رصيد المشترك
                        subscription = self.db.query(CopyTradingSubscription)\
                            .filter(CopyTradingSubscription.subscription_id == copied_trade.subscription_id)\
                            .first()
                        
                        if subscription:
                            # إضافة عوائد البيع الجزئي
                            proceeds = shares_to_sell * sell_price
                            subscription.available_balance += proceeds
                            released_reserve = shares_to_sell * copied_trade.entry_price
                            subscription.reserved_balance -= released_reserve
                            subscription.total_pnl += partial_pnl
                        
                        partial_result = {
                            'copy_trade_id': copied_trade.copy_trade_id,
                            'user_id': subscription.user_id if subscription else None,
                            'shares_sold': shares_to_sell,
                            'sell_price': sell_price,
                            'partial_pnl': partial_pnl,
                            'remaining_shares': copied_trade.remaining_shares,
                            'proceeds': proceeds
                        }
                        
                        partial_results.append(partial_result)
                        
                        # إرسال إشعار الربح الجزئي
                        await self._send_partial_profit_notification(subscription, copied_trade, partial_result)
                
                except Exception as e:
                    logger.error(f"Error processing partial profit for copied trade {copied_trade.copy_trade_id}: {e}")
            
            self.db.commit()
            
            return partial_results
            
        except Exception as e:
            logger.error(f"Error processing partial profit for copying: {e}")
            self.db.rollback()
            return []
    
    async def get_subscription_performance(self, subscription_id: str) -> Dict:
        """الحصول على أداء اشتراك نسخ التداول"""
        try:
            subscription = self.db.query(CopyTradingSubscription)\
                .filter(CopyTradingSubscription.subscription_id == subscription_id)\
                .first()
            
            if not subscription:
                return {'error': 'Subscription not found'}
            
            # الحصول على جميع الصفقات المنسوخة
            copied_trades = self.db.query(CopiedTrade)\
                .filter(CopiedTrade.subscription_id == subscription_id)\
                .all()
            
            # حساب الإحصائيات
            total_trades = len(copied_trades)
            winning_trades = len([t for t in copied_trades if t.total_pnl > 0])
            losing_trades = len([t for t in copied_trades if t.total_pnl < 0])
            
            total_investment = sum(t.total_investment for t in copied_trades)
            total_pnl = sum(t.total_pnl for t in copied_trades)
            total_fees = sum(t.fees_paid for t in copied_trades)
            
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            roi = (total_pnl / total_investment * 100) if total_investment > 0 else 0
            
            # أداء المراكز النشطة
            active_trades = [t for t in copied_trades if t.status == 'ACTIVE']
            active_investment = sum(t.total_investment for t in active_trades)
            active_unrealized_pnl = sum(t.unrealized_pnl for t in active_trades)
            
            return {
                'subscription_id': subscription_id,
                'user_id': subscription.user_id,
                'is_active': subscription.is_active,
                'performance': {
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'losing_trades': losing_trades,
                    'win_rate': win_rate,
                    'total_investment': total_investment,
                    'total_pnl': total_pnl,
                    'total_fees': total_fees,
                    'roi_percentage': roi,
                    'active_trades': len(active_trades),
                    'active_investment': active_investment,
                    'active_unrealized_pnl': active_unrealized_pnl
                },
                'balances': {
                    'available_balance': subscription.available_balance,
                    'reserved_balance': subscription.reserved_balance,
                    'total_balance': subscription.available_balance + subscription.reserved_balance
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting subscription performance: {e}")
            return {'error': str(e)}
    
    # Helper Methods
    async def _get_stock_sector(self, stock_code: str) -> str:
        """الحصول على قطاع السهم"""
        # سيتم ربطها بجدول الأسهم لاحقاً
        return "GENERAL"
    
    async def _send_activation_notification(self, subscription: CopyTradingSubscription):
        """إرسال إشعار تفعيل نسخ التداول"""
        try:
            message = f"""
🎉 تم تفعيل نسخ التداول بنجاح!

👤 معرف المستخدم: {subscription.user_id}
📊 نسبة النسخ: {subscription.copy_percentage * 100:.1f}%
💰 الرصيد المتاح: {subscription.available_balance:,.0f} جنيه
🎯 حد الثقة الأدنى: {subscription.min_signal_confidence:.0%}
📈 حد المخاطرة: {subscription.max_risk_per_trade:.1%}

سيتم نسخ صفقات المحفظة الذكية تلقائياً حسب الإعدادات المحددة.
            """
            
            # إرسال عبر تيليجرام إذا كان متاحاً
            if subscription.telegram_chat_id:
                await self.telegram_service.send_message(message, subscription.telegram_chat_id)
              # إرسال عبر WebSocket
            await signal_manager.send_personal_message(
                subscription.user_id,
                {
                    "type": "copy_trading_activated",
                    "message": message,
                    "subscription_id": subscription.subscription_id
                }
            )
            
        except Exception as e:
            logger.error(f"Error sending activation notification: {e}")
    
    async def _send_copy_notification(self, subscription: CopyTradingSubscription, copy_result: Dict):
        """إرسال إشعار نسخ صفقة"""
        try:
            if not copy_result.get('success'):
                return
                
            message = f"""
📋 تم نسخ صفقة جديدة!

📈 السهم: {copy_result['stock_code']}
💰 سعر الدخول: {copy_result['entry_price']:.2f}
📊 الكمية: {copy_result['copied_shares']:,}
💵 إجمالي الاستثمار: {copy_result['total_investment']:,.0f} جنيه
💸 الرسوم: {copy_result['fees_paid']:.2f} جنيه
📈 نسبة النسخ: {copy_result['copy_percentage']:.1f}%
🆔 معرف الصفقة: {copy_result['copy_trade_id']}
            """
            
            if subscription.notifications_enabled:
                if subscription.telegram_chat_id:
                    await self.telegram_service.send_message(message, subscription.telegram_chat_id)                
                await signal_manager.send_personal_message(
                    subscription.user_id,
                    {
                        "type": "copy_trade_executed",
                        "message": message,
                        "data": copy_result
                    }
                )
            
        except Exception as e:
            logger.error(f"Error sending copy notification: {e}")
    
    async def _send_closure_notification(self, subscription: CopyTradingSubscription, copied_trade: CopiedTrade, closure_result: Dict):
        """إرسال إشعار إغلاق الصفقة المنسوخة"""
        try:
            pnl = closure_result['pnl']
            pnl_emoji = "🟢" if pnl >= 0 else "🔴"
            
            message = f"""
{pnl_emoji} تم إغلاق صفقة منسوخة

📈 السهم: {copied_trade.stock_code}
💰 سعر الدخول: {copied_trade.entry_price:.2f}
📊 الكمية: {copied_trade.copied_shares:,}
💵 الربح/الخسارة: {pnl:+,.0f} جنيه ({closure_result['pnl_percentage']:+.1f}%)
💸 إجمالي العوائد: {closure_result['proceeds']:,.0f} جنيه
📅 سبب الإغلاق: {closure_result['close_reason']}
🆔 معرف الصفقة: {copied_trade.copy_trade_id}
            """
            
            if subscription and subscription.notifications_enabled:
                if subscription.telegram_chat_id:
                    await self.telegram_service.send_message(message, subscription.telegram_chat_id)                
                await signal_manager.send_personal_message(
                    subscription.user_id,
                    {
                        "type": "copy_trade_closed",
                        "message": message,
                        "data": closure_result
                    }
                )
            
        except Exception as e:
            logger.error(f"Error sending closure notification: {e}")
    
    async def _send_partial_profit_notification(self, subscription: CopyTradingSubscription, copied_trade: CopiedTrade, partial_result: Dict):
        """إرسال إشعار الربح الجزئي"""
        try:
            message = f"""
💰 تم تحقيق ربح جزئي!

📈 السهم: {copied_trade.stock_code}
💸 تم بيع: {partial_result['shares_sold']:,} سهم
💰 سعر البيع: {partial_result['sell_price']:.2f}
📊 الربح المحقق: +{partial_result['partial_pnl']:,.0f} جنيه
📈 المتبقي: {partial_result['remaining_shares']:,} سهم
💵 عوائد البيع: {partial_result['proceeds']:,.0f} جنيه
🆔 معرف الصفقة: {copied_trade.copy_trade_id}
            """
            
            if subscription and subscription.notifications_enabled:
                if subscription.telegram_chat_id:
                    await self.telegram_service.send_message(message, subscription.telegram_chat_id)                
                await signal_manager.send_personal_message(
                    subscription.user_id,
                    {
                        "type": "copy_trade_partial_profit",
                        "message": message,
                        "data": partial_result
                    }
                )
            
        except Exception as e:
            logger.error(f"Error sending partial profit notification: {e}")
