import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// أنواع البيانات البسيطة
interface SimpleStockData {
  symbol: string;
  name: string;
  current_price: number;
  change_percent: number;
  volume: number;
  turnover: number;
  ma5?: number;
  ma20?: number;
  target_1?: number;
}

interface SimpleMarketOverview {
  summary: {
    totalStocks: number;
    gainers: number;
    losers: number;
    unchanged: number;
    totalValue: number;
    totalVolume: number;
    avgChange: number;
    marketTrend: 'bullish' | 'bearish' | 'neutral';
  };
  topGainers: SimpleStockData[];
  topLosers: SimpleStockData[];
  mostActive: SimpleStockData[];
  technicalCounts: {
    bullishMA: number;
    bearishMA: number;
    strongUptrend: number;
    nearTargets: number;
  };
}

export function useSimpleMarketOverview() {
  return useQuery<SimpleMarketOverview>({
    queryKey: ['simple-market-overview'],
    queryFn: async () => {
      console.log('🔍 جاري جلب النظرة العامة البسيطة...');

      // استعلام مباشر وبسيط
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select(`
          symbol,
          name,
          current_price,
          change_percent,
          volume,
          turnover,
          ma5,
          ma20,
          target_1
        `)
        .not('symbol', 'like', '%EGX%')
        .not('name', 'is', null)
        .not('current_price', 'is', null)
        .order('volume', { ascending: false });

      if (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        throw new Error('لا توجد بيانات متاحة');
      }

      return processMarketData(data);
    },
    staleTime: 30 * 1000, // 30 ثانية
    refetchInterval: 60 * 1000, // كل دقيقة
  });
}

// دالة معالجة البيانات
function processMarketData(rawData: Record<string, unknown>[]): SimpleMarketOverview {
  console.log(`📊 معالجة ${rawData.length} سهم...`);

  // تصفية وتنظيف البيانات
  const validStocks = rawData
    .filter(item => 
      item.name && 
      item.current_price !== null && 
      item.change_percent !== null
    )
    .map(item => ({
      symbol: item.symbol || '',
      name: item.name || '',
      current_price: Number(item.current_price) || 0,
      change_percent: Number(item.change_percent) || 0,
      volume: Number(item.volume) || 0,
      turnover: Number(item.turnover) || 0,
      ma5: item.ma5 ? Number(item.ma5) : undefined,
      ma20: item.ma20 ? Number(item.ma20) : undefined,
      target_1: item.target_1 ? Number(item.target_1) : undefined,
    })) as SimpleStockData[];

  console.log(`✅ تمت معالجة ${validStocks.length} سهم صالح`);

  // === حساب الملخص العام ===
  const gainers = validStocks.filter(s => s.change_percent > 0);
  const losers = validStocks.filter(s => s.change_percent < 0);
  const unchanged = validStocks.filter(s => s.change_percent === 0);
  
  const totalValue = validStocks.reduce((sum, s) => sum + s.turnover, 0);
  const totalVolume = validStocks.reduce((sum, s) => sum + s.volume, 0);
  const avgChange = validStocks.length > 0 
    ? validStocks.reduce((sum, s) => sum + s.change_percent, 0) / validStocks.length
    : 0;

  // تحديد اتجاه السوق
  let marketTrend: 'bullish' | 'bearish' | 'neutral' = 'neutral';
  if (gainers.length > losers.length * 1.2) {
    marketTrend = 'bullish';
  } else if (losers.length > gainers.length * 1.2) {
    marketTrend = 'bearish';
  }

  // === أفضل الأسهم ===
  const topGainers = gainers
    .sort((a, b) => b.change_percent - a.change_percent)
    .slice(0, 5);

  const topLosers = losers
    .sort((a, b) => a.change_percent - b.change_percent)
    .slice(0, 5);

  const mostActive = validStocks
    .filter(s => s.volume > 0)
    .sort((a, b) => b.volume - a.volume)
    .slice(0, 5);

  // === التحليل الفني ===
  const bullishMA = validStocks.filter(s => 
    s.ma5 && s.ma20 && s.ma5 > s.ma20
  ).length;

  const bearishMA = validStocks.filter(s => 
    s.ma5 && s.ma20 && s.ma5 < s.ma20
  ).length;

  const strongUptrend = validStocks.filter(s => 
    s.ma5 && s.ma20 && s.ma5 > s.ma20 && s.change_percent > 0
  ).length;

  const nearTargets = validStocks.filter(s => 
    s.target_1 && s.target_1 > 0 && s.current_price >= s.target_1 * 0.95
  ).length;

  return {
    summary: {
      totalStocks: validStocks.length,
      gainers: gainers.length,
      losers: losers.length,
      unchanged: unchanged.length,
      totalValue: Math.round(totalValue),
      totalVolume: Math.round(totalVolume),
      avgChange: Math.round(avgChange * 100) / 100,
      marketTrend
    },
    topGainers,
    topLosers,
    mostActive,
    technicalCounts: {
      bullishMA,
      bearishMA,
      strongUptrend,
      nearTargets
    }
  };
}

// Hook للاستخدام السريع
export function useMarketStats() {
  const { data, isLoading, error } = useSimpleMarketOverview();
  
  return {
    stats: data?.summary || {
      totalStocks: 0,
      gainers: 0,
      losers: 0,
      unchanged: 0,
      totalValue: 0,
      totalVolume: 0,
      avgChange: 0,
      marketTrend: 'neutral' as const
    },
    topStocks: {
      gainers: data?.topGainers || [],
      losers: data?.topLosers || [],
      active: data?.mostActive || []
    },
    technical: data?.technicalCounts || {
      bullishMA: 0,
      bearishMA: 0,
      strongUptrend: 0,
      nearTargets: 0
    },
    isLoading,
    error
  };
}
