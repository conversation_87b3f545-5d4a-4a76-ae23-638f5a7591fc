import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface MACDChartProps {
  data: Array<{ date: string; macd: number; signal: number }>;
}

const MACDChart: React.FC<MACDChartProps> = React.memo(({ data }) => {
  // Memoize formatters to prevent recreation on each render
  const tickFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG', {
      month: 'short',
      day: 'numeric'
    });
  }, []);

  const labelFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG');
  }, []);

  const tooltipFormatter = React.useCallback((value: number, name: string) => {
    const labels: Record<string, string> = {
      'macd': 'MACD',
      'signal': 'الإشارة'
    };
    return [value.toFixed(4), labels[name] || name];
  }, []);

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tick={{ fontSize: 12 }}
          tickFormatter={tickFormatter}
          stroke="#666"
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <Tooltip 
          labelFormatter={labelFormatter}
          formatter={tooltipFormatter}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <Legend />
        
        {/* MACD Line */}
        <Line 
          type="monotone" 
          dataKey="macd" 
          stroke="#8884d8" 
          strokeWidth={2}
          dot={false}
          name="MACD"
          connectNulls={false}
        />
        
        {/* Signal Line */}
        <Line 
          type="monotone" 
          dataKey="signal" 
          stroke="#82ca9d" 
          strokeWidth={2}
          dot={false}
          name="الإشارة"
          connectNulls={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

MACDChart.displayName = 'MACDChart';

export default MACDChart;
