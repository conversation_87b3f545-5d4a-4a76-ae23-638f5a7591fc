
# Comprehensive Mock Data Usage in Frontend (By Page)
# Aim: For migration to real API/Supabase calls

---

## **Main Dashboard / Overview Page**
- **src/components/StockTicker.tsx**
  - Usage: Shows live stock data in a scrollable ticker using internal static array and randomization logic.
  - Example to replace:
    ```ts
    // Fetch from Supabase Realtime or REST API
    const { data, error } = await supabase
      .from('stocks_realtime')
      .select('symbol,name,current_price:current_price,change_amount:change_amount,change_percent:change_percent,volume')
      .order('symbol');
    ```

- **src/components/dashboard/QuickStatsGrid.tsx**
  - Subcomponents/Sections using mock data:
    - "أفضل الأسهم أداءً اليوم" (Top gainers)
    - "أكثر الأسهم تداولاً" (Most active by volume)
    - "فرص استثمارية" (Investment Opportunities)
  - Each section pulls from arrays defined inside the component.
  - Example API replacement:
    - Top gainers:
      ```ts
      supabase
        .from('stocks_realtime')
        .select('symbol,change_percent')
        .order('change_percent', { ascending: false })
        .limit(5)
      ```
    - Most active:
      ```ts
      supabase
        .from('stocks_realtime')
        .select('symbol,volume')
        .order('volume', { ascending: false })
        .limit(5)
      ```

---

## **Analytics Page**
- **src/components/analytics/PatternRecognitionSection.tsx**
  - Uses `usePatternRecognition` hook (likely relying on local/mock data or a hook with mock fallback).
  - Example API:
    ```ts
    supabase
      .from('analytics_patterns')
      .select('*')
    ```

- **src/components/analytics/PatternRecognition.tsx**
  - Also uses `usePatternRecognition`.
  - Usage: Technical pattern cards, time-frame filtering—all from mock/hook.
  - Same API as above.

- **src/components/analytics/VolatilityAnalysisSection.tsx**
  - Uses `useVolatilityAnalysis` (again, might fetch mock data internally).
  - Example API:
    ```ts
    supabase
      .from('analytics_volatility')
      .select('*')
    ```

- **src/components/analytics/SentimentAnalysis.tsx**
  - Usage: Has its own internal mock `SentimentData[]` with analysis results.
  - Example API (Supabase or external AI sentiment endpoint based on news/tweets):
    ```ts
    supabase
      .from('market_news')
      .select('symbol,sentiment_score')
      .order('published_at', { ascending: false })
      .limit(10)
    // or call your own /api/sentiment endpoint
    ```
  - Signals and scores: could be a join/table or computed in backend/API.

---

## **Charts Page**
- **src/components/charts/TechnicalIndicators.tsx**
  - Usage: Generates 30 days of mock data for indicators (RSI, MACD, BB).
  - Example API:
    ```ts
    supabase
      .from('technical_indicators')
      .select('date, symbol, rsi_14, macd, macd_signal, bb_upper, bb_middle, bb_lower, price')
      .eq('symbol', symbol)
      .gte('date', startDate)
      .order('date')
    ```

---

## **Screener Page**
- **src/components/StockScreener.tsx**
  - Usage: Uses useEffect on mount to fill with mock stock list.
  - Example API:
    ```ts
    supabase
      .from('stocks_master')
      .select('symbol,name,sector,market_cap,pe_ratio')
      .limit(50)
    ```

---

## **News Page**
- **src/components/NewsIntegration.tsx**
  - Usage: Fills mock news list (title, summary, date).
  - Example API:
    ```ts
    supabase
      .from('market_news')
      .select('title,summary,published_at,source,related_symbols')
      .order('published_at', { descending: true })
      .limit(20)
    ```

---

## **Market Overview Page**
- **src/components/MarketOverview.tsx** (and child components)
  - Uses a custom hook that may itself use mock data (for aggregates, market indices, etc).
  - Example API:
    ```ts
    supabase
      .from('market_indices')
      .select('*')
    ```
  - May also read from 'stocks_realtime' for summary stats.

---

## **Calendar Page**
- **src/components/MarketCalendar.tsx**
  - Usage: Populates events (earnings, dividends) from mock useEffect data.
  - Example API:
    ```ts
    supabase
      .from('market_news')
      .select('title,published_at,categories')
      .ilike('categories', '%calendar%')
    ```

---

## **Analytics (Advanced)**
- **src/components/analytics/BacktestingEngine.tsx**
  - Usage: Likely uses a backend/hook with mock return for "backtest results".
  - Example API:
    ```ts
    supabase
      .from('analytics_backtests')
      .select('*')
      .eq('user_id', currentUser.id)
    ```

---

## **Other Pages / Areas to Check**
- **Portfolio / Paper trading tabs:**  
  Most now use API, but if any component/hook shows simulated transactions, double-check for mock usage.
- **Custom hooks:**  
  For any logic like `usePatternRecognition`, `useVolatilityAnalysis`, always check if they pull from mock arrays or internal hardcoded data.

---

## **Summary Table**  
| Page             | Component/Section                             | Mock Usage Notes                                | Example API/Supabase Query                                              |
|------------------|-----------------------------------------------|-------------------------------------------------|-------------------------------------------------------------------------|
| Dashboard        | StockTicker                                   | Array & randomization for ticker                | stocks_realtime                                                         |
| Dashboard        | QuickStatsGrid                                | Array for gainers, active, opportunities        | stocks_realtime                                                         |
| Analytics        | PatternRecognitionSection/PatternRecognition  | Hook, static patterns (per timeframe)           | analytics_patterns                                                      |
| Analytics        | VolatilityAnalysisSection                     | Hook, static volatility array                   | analytics_volatility                                                    |
| Analytics        | SentimentAnalysis                             | Local SentimentData[]                           | market_news (with sentiment) or /api/sentiment                          |
| Charts           | TechnicalIndicators                           | useEffect mock time series                      | technical_indicators                                                    |
| Screener         | StockScreener                                 | useEffect adds mock list                        | stocks_master                                                           |
| News             | NewsIntegration                               | useEffect for dummy news                        | market_news                                                             |
| Overview         | MarketOverview, custom hook                   | May use data aggregates from array              | market_indices, stocks_realtime                                         |
| Calendar         | MarketCalendar                                | useEffect for mock events                       | market_news (category "calendar"/"event")                               |
| Analytics Adv.   | BacktestingEngine                             | Backend/hook returns hardcoded results          | analytics_backtests                                                     |

---

# **Instructions:**
- Replace each local/mock data source with the relevant Supabase query or REST API call shown.
- If hooks are used, update them to fetch using the above Supabase syntax.
- For any missing backend data: update/extend your Supabase tables to include the necessary fields.

