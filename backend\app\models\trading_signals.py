# Trading Signals Models
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

Base = declarative_base()

class TradingSignal(Base):
    """Model for storing trading signals received from external webhook"""
    __tablename__ = 'trading_signals'
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), nullable=False, index=True)
    signal_type = Column(String(20), nullable=False)  # buy, sell, tp1done, tp2done, tp3done, tsl
    
    # Buy signal fields
    buy_price = Column(Float, nullable=True)
    tp1 = Column(Float, nullable=True)
    tp2 = Column(Float, nullable=True) 
    tp3 = Column(Float, nullable=True)
    stop_loss = Column(Float, nullable=True)
    
    # Sell signal fields
    sell_price = Column(Float, nullable=True)
    
    # Status tracking
    status = Column(String(20), default='active')  # active, closed, cancelled
    confidence = Column(Float, nullable=True)
    
    # Metadata
    raw_data = Column(JSON)  # Store original webhook payload
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<TradingSignal {self.stock_code} {self.signal_type} {self.created_at}>"

class LiveRecommendations(Base):
    """Model for live trading recommendations displayed to users"""
    __tablename__ = 'live_recommendations'
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), nullable=False, index=True)
    action = Column(String(10), nullable=False)  # buy, sell
    
    # Entry and current prices
    entry_price = Column(Float, nullable=False)
    current_price = Column(Float, nullable=True)
    
    # Target levels
    tp1 = Column(Float, nullable=True)
    tp2 = Column(Float, nullable=True)
    tp3 = Column(Float, nullable=True)
    
    # Target achievement tracking
    tp1_hit = Column(Boolean, default=False)
    tp1_hit_at = Column(DateTime(timezone=True), nullable=True)
    tp2_hit = Column(Boolean, default=False)
    tp2_hit_at = Column(DateTime(timezone=True), nullable=True)
    tp3_hit = Column(Boolean, default=False)
    tp3_hit_at = Column(DateTime(timezone=True), nullable=True)
    
    # Stop loss
    stop_loss = Column(Float, nullable=True)
    stop_loss_hit = Column(Boolean, default=False)
    stop_loss_hit_at = Column(DateTime(timezone=True), nullable=True)
      # Performance tracking
    current_pnl = Column(Float, nullable=True)  # Current profit/loss percentage
    max_pnl = Column(Float, nullable=True)      # Maximum profit achieved
    min_pnl = Column(Float, nullable=True)      # Maximum loss (drawdown)
    highest_price = Column(Float, nullable=True)  # Highest price reached during the trade
    protected_profit = Column(Float, nullable=True)  # Profit protected by trailing stop
    
    # Status
    status = Column(String(20), default='active')  # active, closed, cancelled, stopped
    close_reason = Column(String(50), nullable=True)  # tp1, tp2, tp3, stop_loss, manual, sell_signal
    
    # Risk management
    risk_reward_ratio = Column(Float, nullable=True)
    position_size = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    closed_at = Column(DateTime(timezone=True), nullable=True)
    
    def calculate_pnl(self, current_price: float) -> float:
        """Calculate current P&L percentage"""
        if self.action == 'buy':
            return ((current_price - self.entry_price) / self.entry_price) * 100
        else:  # sell
            return ((self.entry_price - current_price) / self.entry_price) * 100
    
    def calculate_risk_reward(self) -> Optional[float]:
        """Calculate risk-reward ratio"""
        if not self.stop_loss or not self.tp1:
            return None
        
        if self.action == 'buy':
            risk = self.entry_price - self.stop_loss
            reward = self.tp1 - self.entry_price
        else:  # sell
            risk = self.stop_loss - self.entry_price
            reward = self.entry_price - self.tp1
        
        return reward / risk if risk > 0 else None
    
    def __repr__(self):
        return f"<LiveRecommendation {self.stock_code} {self.action} {self.status}>"

class SignalPerformance(Base):
    """Model for tracking signal performance metrics"""
    __tablename__ = 'signal_performance'
    
    id = Column(Integer, primary_key=True, index=True)
    signal_id = Column(Integer, nullable=False, index=True)
    stock_code = Column(String(10), nullable=False)
    
    # Performance metrics
    total_return = Column(Float, nullable=True)
    max_profit = Column(Float, nullable=True)
    max_loss = Column(Float, nullable=True)
    duration_hours = Column(Float, nullable=True)
    
    # Classification
    outcome = Column(String(20), nullable=True)  # profit, loss, breakeven
    targets_hit = Column(Integer, default=0)     # Number of targets achieved
    
    # Analysis
    market_condition = Column(String(20), nullable=True)  # bullish, bearish, sideways
    volatility = Column(Float, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<SignalPerformance {self.stock_code} {self.outcome} {self.total_return}%>"

class UserSubscription(Base):
    """Model for user subscription management"""
    __tablename__ = 'user_subscriptions'
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(50), nullable=False, index=True)
    telegram_id = Column(String(20), nullable=True, index=True)
    
    # Subscription details
    plan = Column(String(20), nullable=False)  # free, premium, professional, enterprise
    status = Column(String(20), default='active')  # active, cancelled, expired, suspended
    
    # Features access
    live_signals = Column(Boolean, default=False)
    ai_alerts = Column(Boolean, default=False)
    backtesting = Column(Boolean, default=False)
    advanced_analytics = Column(Boolean, default=False)
    telegram_alerts = Column(Boolean, default=False)
    
    # Limits
    alerts_limit = Column(Integer, default=5)      # Monthly alerts limit
    alerts_used = Column(Integer, default=0)       # Alerts used this month
    
    # Subscription period
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    
    # Payment tracking
    payment_method = Column(String(20), nullable=True)
    last_payment_at = Column(DateTime(timezone=True), nullable=True)
    next_billing_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<UserSubscription {self.user_id} {self.plan} {self.status}>"
