import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface RSIChartProps {
  data: Array<{ date: string; rsi: number }>;
}

const RSIChart: React.FC<RSIChartProps> = React.memo(({ data }) => {
  // Memoize formatters to prevent recreation on each render
  const tickFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG', {
      month: 'short',
      day: 'numeric'
    });
  }, []);

  const labelFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG');
  }, []);

  const tooltipFormatter = React.useCallback((value: number) => {
    return [value.toFixed(2), 'مؤشر القوة النسبية'];
  }, []);

  // Add RSI reference lines to data
  const enhancedData = React.useMemo(() => 
    data.map(item => ({
      ...item,
      overbought: 70,
      oversold: 30
    })), [data]
  );

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={enhancedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tick={{ fontSize: 12 }}
          tickFormatter={tickFormatter}
          stroke="#666"
        />
        <YAxis 
          domain={[0, 100]} 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <Tooltip 
          labelFormatter={labelFormatter}
          formatter={tooltipFormatter}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <Legend />
        
        {/* Main RSI Line */}
        <Line 
          type="monotone" 
          dataKey="rsi" 
          stroke="#8884d8" 
          strokeWidth={2}
          dot={false}
          name="RSI"
          connectNulls={false}
        />
        
        {/* Overbought Line (70) */}
        <Line 
          type="monotone" 
          dataKey="overbought" 
          stroke="#ff4444" 
          strokeDasharray="5 5"
          strokeWidth={1}
          dot={false}
          name="مفرط الشراء (70)"
        />
        
        {/* Oversold Line (30) */}
        <Line 
          type="monotone" 
          dataKey="oversold" 
          stroke="#44ff44" 
          strokeDasharray="5 5"
          strokeWidth={1}
          dot={false}
          name="مفرط البيع (30)"
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

RSIChart.displayName = 'RSIChart';

export default RSIChart;
