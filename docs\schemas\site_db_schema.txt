
# EGX Stock AI Oracle - Updated Database Schema

## Database: Supabase PostgreSQL
## Project ID: tbzbrujqjwpatbzffmwq
## Last Updated: June 15, 2025

**NOTE: This is a summary. For the complete, detailed schema with indexes, constraints, and RLS policies, see `CURRENT_DATABASE_SCHEMA.md`**

This document provides a concise overview of the Supabase/PostgreSQL database schema supporting the EGX Stock AI Oracle platform. Each table is listed with its columns, data types, nullability, and relationships.

---

## Core Stock Data Tables

### Table: `stocks_master`
Master registry of all Egyptian stocks
| Column             | Type           | Nullable | Default             | Notes                |
|--------------------|----------------|----------|---------------------|----------------------|
| id                 | SERIAL         | No       |                     | Primary Key          |
| symbol             | VARCHAR(20)    | No       |                     | Unique               |
| name               | VARCHAR(200)   | No       |                     |                      |
| name_ar            | VARCHAR(200)   | Yes      |                     | Arabic name          |
| sector             | VARCHAR(100)   | Yes      |                     |                      |
| sector_ar          | VARCHAR(100)   | Yes      |                     | Arabic sector        |
| industry           | VARCHAR(100)   | Yes      |                     |                      |
| market             | VARCHAR(50)    | Yes      | 'EGX'               |                      |
| is_active          | BOOLEAN        | Yes      | true                |                      |
| listing_date       | DATE           | Yes      |                     |                      |
| isin               | VARCHAR(20)    | Yes      |                     |                      |
| free_float_shares  | BIGINT         | Yes      |                     |                      |
| total_shares       | BIGINT         | Yes      |                     |                      |
| created_at         | TIMESTAMPTZ    | Yes      | NOW()               |                      |
| updated_at         | TIMESTAMPTZ    | Yes      | NOW()               |                      |

### Table: `stocks_historical`
Daily OHLCV historical data for backtesting and analysis
| Column           | Type        | Nullable | Default            | Notes          |
|------------------|-------------|----------|--------------------|----------------|
| id               | SERIAL      | No       |                    | Primary Key    |
| symbol           | VARCHAR(20) | No       |                    |                |
| date             | DATE        | No       |                    | Unique (symbol,date)|
| open             | DECIMAL(12,4)| No      |                    |                |
| high             | DECIMAL(12,4)| No      |                    |                |
| low              | DECIMAL(12,4)| No      |                    |                |
| close            | DECIMAL(12,4)| No      |                    |                |
| volume           | BIGINT      | Yes      | 0                  |                |
| open_interest    | BIGINT      | Yes      | 0                  |                |
| adjusted_close   | DECIMAL(12,4)| Yes     |                    |                |
| created_at       | TIMESTAMPTZ | Yes      | NOW()              |                |

### Table: `stocks_realtime`
Current real-time market data
| Column          | Type         | Nullable | Default   | Notes         |
|-----------------|--------------|----------|-----------|---------------|
| symbol          | VARCHAR(20)  | No       |           | Primary Key   |
| current_price   | DECIMAL(12,4)| Yes      |           |               |
| open_price      | DECIMAL(12,4)| Yes      |           |               |
| high_price      | DECIMAL(12,4)| Yes      |           |               |
| low_price       | DECIMAL(12,4)| Yes      |           |               |
| previous_close  | DECIMAL(12,4)| Yes      |           |               |
| change_amount   | DECIMAL(12,4)| Yes      |           |               |
| change_percent  | DECIMAL(8,4) | Yes      |           |               |
| volume          | BIGINT       | Yes      |           |               |
| turnover        | DECIMAL(15,2)| Yes      |           |               |
| trades_count    | INTEGER      | Yes      |           |               |
| bid_price       | DECIMAL(12,4)| Yes      |           |               |
| ask_price       | DECIMAL(12,4)| Yes      |           |               |
| bid_volume      | BIGINT       | Yes      |           |               |
| ask_volume      | BIGINT       | Yes      |           |               |
| last_trade_time | TIMESTAMPTZ  | Yes      |           |               |
| market_cap      | DECIMAL(15,2)| Yes      |           |               |
| updated_at      | TIMESTAMPTZ  | Yes      | NOW()     |               |

### Table: `stocks_financials`
Financial statements and fundamental analysis data
| Column                 | Type         | Nullable | Notes           |
|------------------------|--------------|----------|-----------------|
| id                     | SERIAL       | No       | Primary Key     |
| symbol                 | VARCHAR(20)  | No       |                 |
| fiscal_year            | INTEGER      | Yes      |                 |
| fiscal_quarter         | INTEGER      | Yes      |                 |
| market_cap             | DECIMAL(15,2)| Yes      |                 |
| pe_ratio               | DECIMAL(8,4) | Yes      |                 |
| eps_ttm                | DECIMAL(8,4) | Yes      |                 |
| eps_growth_yoy         | DECIMAL(8,4) | Yes      |                 |
| dividend_yield         | DECIMAL(8,4) | Yes      |                 |
| book_value_per_share   | DECIMAL(8,4) | Yes      |                 |
| price_to_book          | DECIMAL(8,4) | Yes      |                 |
| revenue_ttm            | DECIMAL(15,2)| Yes      |                 |
| net_income_ttm         | DECIMAL(15,2)| Yes      |                 |
| total_assets           | DECIMAL(15,2)| Yes      |                 |
| total_debt             | DECIMAL(15,2)| Yes      |                 |
| free_cash_flow         | DECIMAL(15,2)| Yes      |                 |
| roe                    | DECIMAL(8,4) | Yes      |                 |
| roa                    | DECIMAL(8,4) | Yes      |                 |
| debt_to_equity         | DECIMAL(8,4) | Yes      |                 |
| current_ratio          | DECIMAL(8,4) | Yes      |                 |
| gross_margin           | DECIMAL(8,4) | Yes      |                 |
| operating_margin       | DECIMAL(8,4) | Yes      |                 |
| net_margin             | DECIMAL(8,4) | Yes      |                 |
| beta                   | DECIMAL(8,4) | Yes      |                 |
| analyst_rating         | VARCHAR(20)  | Yes      |                 |
| target_price           | DECIMAL(12,4)| Yes      |                 |
| created_at             | TIMESTAMPTZ  | Yes      |                 |
| updated_at             | TIMESTAMPTZ  | Yes      |                 |

### Table: `technical_indicators`
Technical analysis indicators for trading strategies
| Column          | Type        | Nullable | Notes  |
|-----------------|-------------|----------|--------|
| id              | SERIAL      | No       | Primary Key |
| symbol          | VARCHAR(20) | No       |           |
| date            | DATE        | No       |           |
| timeframe       | VARCHAR(10) | Yes      | Default: 'D'|
| sma_5/10/20/... | DECIMAL(12,4)| Yes     |           |
| ema_12/26       | DECIMAL(12,4)| Yes     |           |
| rsi_14          | DECIMAL(8,4) | Yes     |           |
| macd, macd_signal, macd_histogram | DECIMAL(8,4) | Yes | |
| bb_upper/bb_middle/bb_lower | DECIMAL(12,4) | Yes |     |
| stoch_k/d       | DECIMAL(8,4) | Yes     |           |
| atr_14, adx_14  | DECIMAL(8,4) | Yes     |           |
| cci_20, williams_r, momentum_10, roc_10 | DECIMAL(8,4) | Yes | |
| volume_sma_20   | DECIMAL(15,2)| Yes     |           |
| created_at      | TIMESTAMPTZ  | Yes     |           |

### Table: `market_indices`
Market indices (EGX30, EGX70, etc.)
| Column            | Type         | Nullable | Notes     |
|-------------------|--------------|----------|-----------|
| symbol            | VARCHAR(20)  | No       | Primary Key |
| name              | VARCHAR(200) | No       |           |
| name_ar           | VARCHAR(200) | Yes      |           |
| current_value     | DECIMAL(12,4)| Yes      |           |
| open_value        | DECIMAL(12,4)| Yes      |           |
| high_value        | DECIMAL(12,4)| Yes      |           |
| low_value         | DECIMAL(12,4)| Yes      |           |
| previous_close    | DECIMAL(12,4)| Yes      |           |
| change_amount     | DECIMAL(12,4)| Yes      |           |
| change_percent    | DECIMAL(8,4) | Yes      |           |
| market_cap        | DECIMAL(18,2)| Yes      |           |
| volume            | BIGINT       | Yes      |           |
| turnover          | DECIMAL(15,2)| Yes      |           |
| constituents_count| INTEGER      | Yes      |           |
| updated_at        | TIMESTAMPTZ  | Yes      |           |

---

## User & Portfolio Management Tables

### Table: `users`
User accounts and API management
| Column             | Type           | Nullable | Default             | Notes                |
|--------------------|----------------|----------|---------------------|----------------------|
| id                 | UUID           | No       | gen_random_uuid()   | Primary Key          |
| email              | VARCHAR(255)   | No       |                     | Unique               |
| password_hash      | VARCHAR(255)   | Yes      |                     | Password (optional)  |
| full_name          | VARCHAR(200)   | Yes      |                     |                      |
| subscription_tier  | VARCHAR(50)    | Yes      | 'free'              |                      |
| api_key            | VARCHAR(100)   | Yes      |                     | Unique, for API      |
| api_calls_used     | INTEGER        | Yes      | 0                   |                      |
| api_calls_limit    | INTEGER        | Yes      | 100                 |                      |
| api_calls_reset_date| DATE          | Yes      | CURRENT_DATE        |                      |
| phone              | VARCHAR(20)    | Yes      |                     |                      |
| country            | VARCHAR(100)   | Yes      | 'Egypt'             |                      |
| preferred_language | VARCHAR(10)    | Yes      | 'ar'                |                      |
| is_active          | BOOLEAN        | Yes      | true                |                      |
| email_verified     | BOOLEAN        | Yes      | false               |                      |
| created_at         | TIMESTAMPTZ    | Yes      | NOW()               |                      |
| updated_at         | TIMESTAMPTZ    | Yes      | NOW()               |                      |

### Table: `user_portfolios`
User investment portfolios
| Column         | Type         | Nullable | Default   | Notes                   |
|----------------|--------------|----------|-----------|-------------------------|
| id             | UUID         | No       | gen_random_uuid() | Primary Key   |
| user_id        | UUID         | Yes      |           | FK → users.id           |
| name           | VARCHAR(200) | No       |           |                         |
| description    | TEXT         | Yes      |           |                         |
| is_default     | BOOLEAN      | Yes      | false     |                         |
| total_value    | DECIMAL(15,2)| Yes      | 0         |                         |
| total_cost     | DECIMAL(15,2)| Yes      | 0         |                         |
| total_profit_loss | DECIMAL(15,2)| Yes   | 0         |                         |
| currency       | VARCHAR(10)  | Yes      | 'EGP'     |                         |
| created_at     | TIMESTAMPTZ  | Yes      | NOW()     |                         |
| updated_at     | TIMESTAMPTZ  | Yes      | NOW()     |                         |

### Table: `portfolio_holdings`
Individual stock positions in portfolios
| Column            | Type         | Nullable | Default | Notes                  |
|-------------------|--------------|----------|---------|------------------------|
| id                | UUID         | No       | gen_random_uuid() | Primary Key |
| portfolio_id      | UUID         | Yes      |         | FK → user_portfolios.id|
| symbol            | VARCHAR(20)  | No       |         |                        |
| quantity          | DECIMAL(15,4)| No       |         |                        |
| average_buy_price | DECIMAL(12,4)| No       |         |                        |
| current_price     | DECIMAL(12,4)| Yes      |         |                        |
| total_cost        | DECIMAL(15,2)| Yes      |         |                        |
| current_value     | DECIMAL(15,2)| Yes      |         |                        |
| profit_loss       | DECIMAL(15,2)| Yes      |         |                        |
| profit_loss_percent | DECIMAL(8,4)| Yes     |         |                        |
| purchase_date     | DATE         | Yes      |         |                        |
| notes             | TEXT         | Yes      |         |                        |
| created_at        | TIMESTAMPTZ  | Yes      | NOW()   |                        |
| updated_at        | TIMESTAMPTZ  | Yes      | NOW()   |                        |

---

## Additional Tables

### Alerts & Notifications
- `price_alerts` - Price alert notifications for users
- `user_notifications` - Notification history

### Paper Trading
- `paper_trading_accounts` - Virtual trading accounts
- `paper_trades` - Individual virtual trades

### Analytics & AI
- `analytics_ml_predictions` - AI/ML price predictions
- `analytics_patterns` - Technical pattern recognition
- `analytics_correlations` - Stock correlation analysis
- `analytics_volatility` - Volatility analysis
- `analytics_backtests` - Strategy backtesting results

### Content & API
- `market_news` - Market news and analysis
- `api_usage_logs` - API usage tracking and analytics

---

## Data Integration Status

**✅ READY FOR DATA IMPORT**

### Historical Data Integration
- **Source**: TXT files from `/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2/`
- **Format**: OHLCV data in CSV format
- **Script**: `scripts/data_sync/load_historical.py`
- **Target**: `stocks_historical` table

### Real-time Data Integration  
- **Source**: Excel file `stock_synco.xlsx`
- **Format**: Current market data with Arabic columns
- **Script**: `scripts/data_sync/load_realtime.py`
- **Target**: `stocks_realtime` table

### Financial Data Integration
- **Source**: CSV file `financial_data.csv` 
- **Format**: Financial ratios and fundamentals
- **Script**: `scripts/data_sync/load_financials.py`
- **Target**: `stocks_financials` table

### Automation & Monitoring
- **Scheduler**: `scripts/data_sync/scheduler.py`
- **Validation**: `scripts/data_sync/validate_data.py`
- **Setup**: `scripts/data_sync/setup.py`

---

## Key Features

- ✅ **Row Level Security (RLS)** enabled on user tables
- ✅ **Comprehensive indexing** for query performance
- ✅ **Arabic language support** for Egyptian market
- ✅ **Real-time data capabilities** with WebSocket support
- ✅ **AI/ML analytics integration** ready
- ✅ **Paper trading simulation** for testing strategies
- ✅ **API management and monitoring** built-in
- ✅ **Multi-language support** (Arabic/English)
- ✅ **Data validation and quality checks** automated
- ✅ **Automated data synchronization** from multiple sources

---

## Next Steps

1. **Set up data sync**: Run `scripts/data_sync/setup.py`
2. **Configure credentials**: Update `.env` with Supabase service role key
3. **Test data import**: Run individual sync scripts
4. **Start automation**: Launch `scheduler.py` for continuous sync
5. **Monitor quality**: Use `validate_data.py` for health checks

For detailed setup instructions, see `scripts/data_sync/README.md`

