# تصميم قاعدة البيانات المخصصة - كنز البيانات المصري
## Custom Database Design - Egyptian Stock Market Data Treasure

📅 **تاريخ التصميم**: 2025-06-16  
💎 **مصادر البيانات**: 321 سهم × 8 سنوات + بيانات حية + بيانات مالية شاملة

---

## تحليل مصادر البيانات الفعلية 📊

### 1. البيانات التاريخية (meta2 folder)
- **📁 المجلد**: `/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2/`
- **📊 العدد**: 321 ملف TXT (321 سهم)
- **📈 البيانات**: ~2000 يوم لكل سهم (من 2017-2025)
- **📝 التنسيق**: TICKER,PER,DATE,TIME,OPEN,HIGH,LOW,CLOSE,VOL,OPENINT
- **💾 الحجم المتوقع**: ~640,000 سجل تاريخي

**مثال من COMID.TXT:**
```
COMI,D,20170312,000000,30.5144,31.4995,30.5144,31.1286,2534498,0
```

### 2. البيانات الحية (stock_synco.xlsx)
- **📁 الملف**: `/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx`
- **📊 الأعمدة**: 45+ عمود شامل
- **🔄 التحديث**: يومي
- **📝 المحتوى**: أسعار حية + مؤشرات فنية + أهداف + سيولة

**الأعمدة الرئيسية:**
- الأساسية: الرمز، الاسم، الافتتاح، الأعلى، الأدنى، الإغلاق، التغير%
- التحليل الفني: MA5, MA10, MA20, MA50, MA100, MA200, TK, KJ
- الأهداف: هدف 1،2،3 + وقف الخسارة + حالة السهم
- السيولة: نسبة السيولة، صافي السيولة، تدفق السيولة

### 3. البيانات المالية (financial_data.csv)
- **📁 الملف**: `/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv`
- **📊 الأعمدة**: 130+ عمود مالي شامل
- **💰 المحتوى**: بيانات مالية عميقة وتحليلات متقدمة

**الأقسام الرئيسية:**
- أساسيات: P/E, EPS, Market Cap, Dividend Yield
- الأداء: Revenue, Net Income, EBITDA, Free Cash Flow
- الميزانية: Assets, Liabilities, Equity, Debt
- النسب: ROE, ROA, Current Ratio, Debt/Equity
- التقييم: Price to Book, Enterprise Value

---

## تصميم قاعدة البيانات المحسنة 🏗️

### هيكل الجداول الأساسية

#### 1. الجدول الرئيسي: `stocks_master`
```sql
CREATE TABLE stocks_master (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) UNIQUE NOT NULL,
    name_ar VARCHAR(200),
    name_en VARCHAR(200),
    sector VARCHAR(100),
    industry VARCHAR(150),
    isin VARCHAR(20),
    total_shares BIGINT,
    free_shares BIGINT,
    listing_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. البيانات التاريخية: `stocks_historical`
```sql
CREATE TABLE stocks_historical (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL REFERENCES stocks_master(symbol),
    trade_date DATE NOT NULL,
    open_price DECIMAL(12,4),
    high_price DECIMAL(12,4),
    low_price DECIMAL(12,4),
    close_price DECIMAL(12,4),
    volume BIGINT,
    turnover DECIMAL(15,2),
    trades_count INTEGER,
    
    -- مؤشرات محسوبة
    price_change DECIMAL(12,4),
    price_change_pct DECIMAL(8,4),
    
    -- فهارس محسنة
    UNIQUE(symbol, trade_date)
);

-- فهارس محسنة للأداء
CREATE INDEX idx_historical_symbol_date ON stocks_historical(symbol, trade_date DESC);
CREATE INDEX idx_historical_date ON stocks_historical(trade_date DESC);
CREATE INDEX idx_historical_volume ON stocks_historical(volume DESC) WHERE volume > 0;
```

#### 3. البيانات الحية: `stocks_realtime`
```sql
CREATE TABLE stocks_realtime (
    symbol VARCHAR(10) PRIMARY KEY REFERENCES stocks_master(symbol),
    
    -- أسعار أساسية
    open_price DECIMAL(12,4),
    high_price DECIMAL(12,4),
    low_price DECIMAL(12,4),
    current_price DECIMAL(12,4),
    previous_close DECIMAL(12,4),
    change_amount DECIMAL(12,4),
    change_percent DECIMAL(8,4),
    
    -- حجم وسيولة
    volume BIGINT,
    turnover DECIMAL(15,2),
    trades_count INTEGER,
    
    -- السيولة المتقدمة
    liquidity_ratio DECIMAL(8,4),
    net_liquidity DECIMAL(15,2),
    liquidity_inflow DECIMAL(15,2),
    liquidity_outflow DECIMAL(15,2),
    volume_inflow BIGINT,
    volume_outflow BIGINT,
    liquidity_flow DECIMAL(8,4),
    
    -- المؤشرات الفنية - المتوسطات المتحركة
    ma5 DECIMAL(12,4),
    ma10 DECIMAL(12,4),
    ma20 DECIMAL(12,4),
    ma50 DECIMAL(12,4),
    ma100 DECIMAL(12,4),
    ma200 DECIMAL(12,4),
    
    -- مؤشرات تكنيكال متقدمة
    tk_indicator DECIMAL(12,4),
    kj_indicator DECIMAL(12,4),
    
    -- الأهداف ووقف الخسارة
    target_1 DECIMAL(12,4),
    target_2 DECIMAL(12,4),
    target_3 DECIMAL(12,4),
    stop_loss DECIMAL(12,4),
    
    -- تصنيفات واشارات
    stock_status INTEGER, -- 1-5 (ممتاز - ضعيف)
    speculation_opportunity BOOLEAN,
    price_range DECIMAL(8,4),
    
    -- إحصائيات متقدمة
    avg_net_volume_3d DECIMAL(15,2),
    avg_net_volume_5d DECIMAL(15,2),
    
    -- بيانات أساسية سريعة
    eps_annual DECIMAL(8,4),
    book_value DECIMAL(8,4),
    pe_ratio DECIMAL(8,4),
    dividend_yield DECIMAL(8,4),
    
    -- الوقت
    last_trade_date DATE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. البيانات المالية الشاملة: `stocks_financials`
```sql
CREATE TABLE stocks_financials (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL REFERENCES stocks_master(symbol),
    fiscal_year INTEGER,
    fiscal_quarter INTEGER,
    
    -- تقييم السوق
    market_cap DECIMAL(15,2),
    enterprise_value DECIMAL(15,2),
    
    -- نسب التقييم
    pe_ratio DECIMAL(10,4),
    pb_ratio DECIMAL(10,4),
    ps_ratio DECIMAL(10,4),
    pcf_ratio DECIMAL(10,4),
    
    -- الربحية
    eps_basic DECIMAL(10,4),
    eps_diluted DECIMAL(10,4),
    eps_growth_yoy DECIMAL(8,4),
    
    -- الأرباح
    dividend_yield DECIMAL(8,4),
    dividend_payout_ratio DECIMAL(8,4),
    dividends_per_share DECIMAL(8,4),
    
    -- قائمة الدخل
    total_revenue DECIMAL(15,2),
    gross_profit DECIMAL(15,2),
    operating_income DECIMAL(15,2),
    net_income DECIMAL(15,2),
    ebitda DECIMAL(15,2),
    
    -- الميزانية العمومية
    total_assets DECIMAL(15,2),
    total_liabilities DECIMAL(15,2),
    total_equity DECIMAL(15,2),
    total_debt DECIMAL(15,2),
    cash_and_equivalents DECIMAL(15,2),
    
    -- التدفقات النقدية
    operating_cash_flow DECIMAL(15,2),
    investing_cash_flow DECIMAL(15,2),
    financing_cash_flow DECIMAL(15,2),
    free_cash_flow DECIMAL(15,2),
    capital_expenditures DECIMAL(15,2),
    
    -- نسب السيولة
    current_ratio DECIMAL(8,4),
    quick_ratio DECIMAL(8,4),
    cash_ratio DECIMAL(8,4),
    
    -- نسب الرافعة
    debt_to_equity DECIMAL(8,4),
    debt_to_assets DECIMAL(8,4),
    debt_to_ebitda DECIMAL(8,4),
    
    -- نسب الربحية
    roe DECIMAL(8,4),
    roa DECIMAL(8,4),
    roic DECIMAL(8,4),
    gross_margin DECIMAL(8,4),
    operating_margin DECIMAL(8,4),
    net_margin DECIMAL(8,4),
    
    -- نسب النشاط
    asset_turnover DECIMAL(8,4),
    inventory_turnover DECIMAL(8,4),
    receivables_turnover DECIMAL(8,4),
    
    -- متقدم
    beta DECIMAL(8,4),
    analyst_rating VARCHAR(50),
    target_price DECIMAL(12,4),
    target_performance_1y DECIMAL(8,4),
    
    -- توقيتات
    recent_earnings_date DATE,
    upcoming_earnings_date DATE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(symbol, fiscal_year, fiscal_quarter)
);
```

#### 5. المؤشرات الفنية المتقدمة: `technical_indicators`
```sql
CREATE TABLE technical_indicators (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL REFERENCES stocks_master(symbol),
    calculation_date DATE NOT NULL,
    
    -- متوسطات متحركة بفترات مختلفة
    ema_12 DECIMAL(12,4),
    ema_26 DECIMAL(12,4),
    ema_50 DECIMAL(12,4),
    
    -- MACD
    macd DECIMAL(12,4),
    macd_signal DECIMAL(12,4),
    macd_histogram DECIMAL(12,4),
    
    -- RSI
    rsi_14 DECIMAL(8,4),
    rsi_30 DECIMAL(8,4),
    
    -- Bollinger Bands
    bb_upper DECIMAL(12,4),
    bb_middle DECIMAL(12,4),
    bb_lower DECIMAL(12,4),
    bb_width DECIMAL(8,4),
    bb_position DECIMAL(8,4),
    
    -- أدوات أخرى
    stochastic_k DECIMAL(8,4),
    stochastic_d DECIMAL(8,4),
    williams_r DECIMAL(8,4),
    cci DECIMAL(8,4),
    atr_14 DECIMAL(12,4),
    adx_14 DECIMAL(8,4),
    
    -- مستويات الدعم والمقاومة
    resistance_1 DECIMAL(12,4),
    resistance_2 DECIMAL(12,4),
    support_1 DECIMAL(12,4),
    support_2 DECIMAL(12,4),
    
    -- إشارات
    buy_signal BOOLEAN,
    sell_signal BOOLEAN,
    trend_direction VARCHAR(10), -- UP, DOWN, SIDEWAYS
    trend_strength DECIMAL(8,4),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, calculation_date)
);
```

---

## Views المحسوبة المتقدمة 📈

### 1. ملخص السوق الشامل
```sql
CREATE MATERIALIZED VIEW market_summary_advanced AS
SELECT 
    -- إحصائيات عامة
    COUNT(*) as total_stocks,
    COUNT(CASE WHEN volume > 0 THEN 1 END) as active_stocks,
    COUNT(CASE WHEN volume > 1000000 THEN 1 END) as high_volume_stocks,
    
    -- توزيع الأداء
    COUNT(CASE WHEN change_percent > 5 THEN 1 END) as strong_gainers,
    COUNT(CASE WHEN change_percent BETWEEN 0 AND 5 THEN 1 END) as moderate_gainers,
    COUNT(CASE WHEN change_percent < -5 THEN 1 END) as strong_losers,
    COUNT(CASE WHEN change_percent BETWEEN -5 AND 0 THEN 1 END) as moderate_losers,
    
    -- المتوسطات والمجاميع
    ROUND(AVG(change_percent), 3) as avg_change,
    ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY change_percent), 3) as median_change,
    SUM(volume) as total_volume,
    SUM(turnover) as total_turnover,
    
    -- التحليل الفني
    COUNT(CASE WHEN ma5 > ma20 AND ma20 > ma50 THEN 1 END) as strong_uptrend_stocks,
    COUNT(CASE WHEN ma5 < ma20 AND ma20 < ma50 THEN 1 END) as strong_downtrend_stocks,
    COUNT(CASE WHEN current_price > target_1 * 0.95 THEN 1 END) as near_target_stocks,
    
    -- السيولة
    AVG(liquidity_ratio) as avg_liquidity_ratio,
    SUM(net_liquidity) as market_net_liquidity,
    
    -- القطاعات الأكثر نشاطاً
    (SELECT sm.sector FROM stocks_realtime sr 
     JOIN stocks_master sm ON sr.symbol = sm.symbol 
     GROUP BY sm.sector ORDER BY SUM(sr.turnover) DESC LIMIT 1) as most_active_sector,
    
    -- آخر تحديث
    MAX(updated_at) as last_updated
FROM stocks_realtime sr
JOIN stocks_master sm ON sr.symbol = sm.symbol
WHERE sm.is_active = true;

-- تحديث كل 5 دقائق
CREATE UNIQUE INDEX ON market_summary_advanced ((1));
```

### 2. أداء القطاعات المتقدم
```sql
CREATE MATERIALIZED VIEW sector_performance_advanced AS
SELECT 
    sm.sector,
    COUNT(*) as total_stocks,
    
    -- الأداء
    AVG(sr.change_percent) as avg_change,
    STDDEV(sr.change_percent) as volatility,
    MAX(sr.change_percent) as best_performer,
    MIN(sr.change_percent) as worst_performer,
    
    -- الحجم والقيمة
    SUM(sr.volume) as total_volume,
    SUM(sr.turnover) as total_turnover,
    AVG(sf.market_cap) as avg_market_cap,
    
    -- النسب المالية
    AVG(sf.pe_ratio) as avg_pe,
    AVG(sf.pb_ratio) as avg_pb,
    AVG(sf.dividend_yield) as avg_dividend_yield,
    AVG(sf.roe) as avg_roe,
    
    -- التحليل الفني
    COUNT(CASE WHEN sr.ma5 > sr.ma20 THEN 1 END) as bullish_count,
    COUNT(CASE WHEN sr.speculation_opportunity = true THEN 1 END) as speculation_opportunities,
    
    -- التقييم
    CASE 
        WHEN AVG(sr.change_percent) > 2 THEN 'قوي جداً'
        WHEN AVG(sr.change_percent) > 0.5 THEN 'قوي'
        WHEN AVG(sr.change_percent) > -0.5 THEN 'متوازن'
        WHEN AVG(sr.change_percent) > -2 THEN 'ضعيف'
        ELSE 'ضعيف جداً'
    END as sector_rating,
    
    MAX(sr.updated_at) as last_updated
FROM stocks_master sm
JOIN stocks_realtime sr ON sm.symbol = sr.symbol
LEFT JOIN stocks_financials sf ON sm.symbol = sf.symbol 
WHERE sm.is_active = true AND sm.sector IS NOT NULL
GROUP BY sm.sector
ORDER BY avg_change DESC;
```

### 3. الفرص الاستثمارية المتقدمة
```sql
CREATE MATERIALIZED VIEW investment_opportunities AS
SELECT 
    sr.symbol,
    sm.name_ar,
    sr.current_price,
    sr.change_percent,
    
    -- فرص النمو
    CASE 
        WHEN sr.current_price < sr.target_1 * 0.8 THEN 'فرصة ممتازة'
        WHEN sr.current_price < sr.target_1 * 0.9 THEN 'فرصة جيدة'
        WHEN sr.current_price < sr.target_1 * 0.95 THEN 'فرصة متوسطة'
        ELSE 'قريب من الهدف'
    END as opportunity_level,
    
    -- المخاطر
    CASE 
        WHEN sr.current_price > sr.stop_loss * 1.2 THEN 'مخاطر منخفضة'
        WHEN sr.current_price > sr.stop_loss * 1.1 THEN 'مخاطر متوسطة'
        ELSE 'مخاطر عالية'
    END as risk_level,
    
    -- التقييم الفني
    CASE 
        WHEN sr.ma5 > sr.ma20 AND sr.ma20 > sr.ma50 THEN 'اتجاه صاعد قوي'
        WHEN sr.ma5 > sr.ma20 THEN 'اتجاه صاعد'
        WHEN sr.ma5 < sr.ma20 AND sr.ma20 < sr.ma50 THEN 'اتجاه هابط قوي'
        WHEN sr.ma5 < sr.ma20 THEN 'اتجاه هابط'
        ELSE 'متذبذب'
    END as technical_trend,
    
    -- التقييم المالي
    ROUND((sr.target_1 - sr.current_price) / sr.current_price * 100, 2) as potential_gain_pct,
    sf.pe_ratio,
    sf.dividend_yield,
    sf.roe,
    
    -- السيولة والحجم
    sr.volume,
    sr.liquidity_ratio,
    
    -- النقاط الإجمالية (scoring system)
    (
        CASE WHEN sr.current_price < sr.target_1 * 0.8 THEN 5 
             WHEN sr.current_price < sr.target_1 * 0.9 THEN 4
             WHEN sr.current_price < sr.target_1 * 0.95 THEN 3 ELSE 1 END +
        CASE WHEN sr.ma5 > sr.ma20 AND sr.ma20 > sr.ma50 THEN 5
             WHEN sr.ma5 > sr.ma20 THEN 3 ELSE 1 END +
        CASE WHEN sf.pe_ratio BETWEEN 5 AND 15 THEN 3
             WHEN sf.pe_ratio BETWEEN 15 AND 25 THEN 2 ELSE 1 END +
        CASE WHEN sr.volume > 1000000 THEN 3
             WHEN sr.volume > 100000 THEN 2 ELSE 1 END
    ) as investment_score,
    
    sr.updated_at
FROM stocks_realtime sr
JOIN stocks_master sm ON sr.symbol = sm.symbol
LEFT JOIN stocks_financials sf ON sr.symbol = sf.symbol
WHERE sm.is_active = true 
  AND sr.target_1 IS NOT NULL
  AND sr.current_price > 0
ORDER BY investment_score DESC, potential_gain_pct DESC;
```

---

## خطة التطبيق العملية 🚀

### المرحلة 1: إعداد البيئة (يوم 1)
1. **Docker Setup للتطوير**
2. **PostgreSQL 15 مع Extensions**
3. **Python Scripts للاستيراد**

### المرحلة 2: استيراد البيانات (يوم 2)
1. **استيراد 321 ملف TXT** → `stocks_historical`
2. **استيراد Excel** → `stocks_realtime`  
3. **استيراد CSV** → `stocks_financials`

### المرحلة 3: API Development (يوم 3-4)
1. **Express.js API** مع endpoints محسنة
2. **Caching Strategy** مع Redis
3. **WebSocket** للبيانات الحية

### المرحلة 4: Frontend Migration (يوم 5)
1. **تحديث الـ hooks** لاستخدام API الجديد
2. **اختبار شامل**
3. **تحسين الأداء**

هل تريد أن نبدأ فوراً في التطبيق؟ 🎯
