import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface DatabaseTest {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  data?: unknown;
}

const DatabaseDiagnostics: React.FC = () => {
  const [tests, setTests] = useState<DatabaseTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    const results: DatabaseTest[] = [];

    // Test 1: اتصال قاعدة البيانات
    try {
      const { data, error } = await supabase.from('stocks_realtime').select('count(*)', { count: 'exact' });
      results.push({
        name: 'اتصال قاعدة البيانات',
        status: error ? 'error' : 'success',
        message: error ? error.message : `تم الاتصال بنجاح`,
        data: data
      });
    } catch (error) {
      results.push({
        name: 'اتصال قاعدة البيانات',
        status: 'error',
        message: `خطأ في الاتصال: ${error}`,
      });
    }

    // Test 2: عدد الأسهم في stocks_realtime
    try {
      const { count, error } = await supabase
        .from('stocks_realtime')
        .select('*', { count: 'exact', head: true });
      
      results.push({
        name: 'عدد أسهم stocks_realtime',
        status: error ? 'error' : 'success',
        message: error ? error.message : `يوجد ${count} سهم في الجدول`,
        data: { count }
      });
    } catch (error) {
      results.push({
        name: 'عدد أسهم stocks_realtime',
        status: 'error',
        message: `خطأ: ${error}`,
      });
    }

    // Test 3: أسهم بأسعار أكبر من 0
    try {
      const { count, error } = await supabase
        .from('stocks_realtime')
        .select('*', { count: 'exact', head: true })
        .gt('current_price', 0);
      
      results.push({
        name: 'أسهم بأسعار صالحة',
        status: error ? 'error' : 'success',
        message: error ? error.message : `يوجد ${count} سهم بسعر > 0`,
        data: { count }
      });
    } catch (error) {
      results.push({
        name: 'أسهم بأسعار صالحة',
        status: 'error',
        message: `خطأ: ${error}`,
      });
    }

    // Test 4: عينة من البيانات
    try {
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select('symbol, current_price, change_percent, volume')
        .gt('current_price', 0)
        .limit(5);
      
      results.push({
        name: 'عينة البيانات',
        status: error ? 'error' : 'success',
        message: error ? error.message : `تم جلب ${data?.length || 0} سهم كعينة`,
        data: data
      });
    } catch (error) {
      results.push({
        name: 'عينة البيانات',
        status: 'error',
        message: `خطأ: ${error}`,
      });
    }

    // Test 5: اختبار stocks_master
    try {
      const { count, error } = await supabase
        .from('stocks_master')
        .select('*', { count: 'exact', head: true });
      
      results.push({
        name: 'جدول stocks_master',
        status: error ? 'error' : 'success',
        message: error ? error.message : `يوجد ${count} سهم في stocks_master`,
        data: { count }
      });
    } catch (error) {
      results.push({
        name: 'جدول stocks_master',
        status: 'error',
        message: `خطأ: ${error}`,
      });
    }

    setTests(results);
    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-600" />;
      default: return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'border-green-200 bg-green-50';
      case 'error': return 'border-red-200 bg-red-50';
      default: return 'border-yellow-200 bg-yellow-50';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            تشخيص قاعدة البيانات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={runTests} disabled={isRunning} className="mb-4">
            <RefreshCw className={`h-4 w-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
            {isRunning ? 'جاري الفحص...' : 'تشغيل فحص شامل'}
          </Button>

          {tests.length > 0 && (
            <div className="space-y-4">
              {tests.map((test, index) => (
                <Card key={index} className={getStatusColor(test.status)}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      {getStatusIcon(test.status)}
                      <div className="flex-1">
                        <div className="font-bold">{test.name}</div>
                        <div className="text-sm text-gray-600 mt-1">{test.message}</div>
                        {test.data && (
                          <div className="mt-2">
                            <Badge variant="outline">
                              <Info className="h-3 w-3 mr-1" />
                              البيانات متاحة
                            </Badge>
                            <details className="mt-2">
                              <summary className="cursor-pointer text-xs text-blue-600">
                                عرض التفاصيل
                              </summary>
                              <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                                {JSON.stringify(test.data, null, 2)}
                              </pre>
                            </details>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DatabaseDiagnostics;
