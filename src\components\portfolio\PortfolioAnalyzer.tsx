import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Pie<PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Target,
  AlertTriangle,
  PieChart as PieChartIcon,
  BarChart3,
  Activity,
  Shield,
  Zap,
  Star,
  Eye
} from 'lucide-react';
import { useEnhancedMarketData, EnhancedStock } from '@/hooks/useEnhancedMarketData';

interface PortfolioStock {
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  totalValue: number;
  gain: number;
  gainPercent: number;
  weight: number;
  sector: string;
  dividendYield: number;
  beta: number;
  pe: number;
}

interface PortfolioMetrics {
  totalValue: number;
  totalGain: number;
  totalGainPercent: number;
  totalDividendYield: number;
  portfolioBeta: number;
  portfolioPE: number;
  diversificationScore: number;
  riskScore: number;
}

const PortfolioAnalyzer = () => {
  const { data: marketData } = useEnhancedMarketData();
  const [portfolioStocks, setPortfolioStocks] = useState<PortfolioStock[]>([]);
  const [portfolioMetrics, setPortfolioMetrics] = useState<PortfolioMetrics>({
    totalValue: 0,
    totalGain: 0,
    totalGainPercent: 0,
    totalDividendYield: 0,
    portfolioBeta: 1,
    portfolioPE: 0,
    diversificationScore: 0,
    riskScore: 0
  });

  // Sample portfolio data for demonstration
  useEffect(() => {
    if (marketData?.stocks) {
      const samplePortfolio = [
        { symbol: 'EAST', quantity: 1000, averagePrice: 28.5 },
        { symbol: 'CIB', quantity: 500, averagePrice: 45.2 },
        { symbol: 'ETEL', quantity: 2000, averagePrice: 16.8 },
        { symbol: 'HELI', quantity: 800, averagePrice: 22.1 },
        { symbol: 'SWDY', quantity: 1500, averagePrice: 12.4 }
      ];

      const portfolioWithData = samplePortfolio.map(holding => {
        const stockData = marketData.stocks.find(s => s.symbol === holding.symbol);
        if (!stockData) return null;

        const currentPrice = stockData.current_price || 0;
        const totalValue = holding.quantity * currentPrice;
        const gain = (currentPrice - holding.averagePrice) * holding.quantity;
        const gainPercent = ((currentPrice - holding.averagePrice) / holding.averagePrice) * 100;

        return {
          ...holding,
          currentPrice,
          totalValue,
          gain,
          gainPercent,
          weight: 0, // Will be calculated later
          sector: stockData.sector || 'غير محدد',
          dividendYield: stockData.dividend_yield || 0,
          beta: 1, // Mock data
          pe: stockData.pe_ratio || 0
        };
      }).filter(Boolean) as PortfolioStock[];

      // Calculate weights
      const totalPortfolioValue = portfolioWithData.reduce((sum, stock) => sum + stock.totalValue, 0);
      portfolioWithData.forEach(stock => {
        stock.weight = (stock.totalValue / totalPortfolioValue) * 100;
      });

      setPortfolioStocks(portfolioWithData);

      // Calculate portfolio metrics
      const totalGain = portfolioWithData.reduce((sum, stock) => sum + stock.gain, 0);
      const totalGainPercent = (totalGain / (totalPortfolioValue - totalGain)) * 100;
      const weightedDividendYield = portfolioWithData.reduce((sum, stock) => 
        sum + (stock.dividendYield * stock.weight / 100), 0);
      const weightedPE = portfolioWithData.reduce((sum, stock) => 
        sum + (stock.pe * stock.weight / 100), 0);

      // Diversification score based on sector distribution
      const sectorWeights = new Map();
      portfolioWithData.forEach(stock => {
        const current = sectorWeights.get(stock.sector) || 0;
        sectorWeights.set(stock.sector, current + stock.weight);
      });
      
      const maxSectorWeight = Math.max(...Array.from(sectorWeights.values()));
      const diversificationScore = Math.max(0, 100 - maxSectorWeight);

      // Risk score based on concentration and volatility
      const concentrationRisk = maxSectorWeight;
      const riskScore = Math.min(100, concentrationRisk + 10);

      setPortfolioMetrics({
        totalValue: totalPortfolioValue,
        totalGain,
        totalGainPercent,
        totalDividendYield: weightedDividendYield,
        portfolioBeta: 1.2, // Mock data
        portfolioPE: weightedPE,
        diversificationScore,
        riskScore
      });
    }
  }, [marketData]);

  const formatCurrency = (num: number) => {
    return new Intl.NumberFormat('ar-EG', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(num);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  // Prepare data for charts
  const sectorDistribution = portfolioStocks.reduce((acc, stock) => {
    const existing = acc.find(item => item.sector === stock.sector);
    if (existing) {
      existing.value += stock.weight;
      existing.amount += stock.totalValue;
    } else {
      acc.push({
        sector: stock.sector,
        value: stock.weight,
        amount: stock.totalValue
      });
    }
    return acc;
  }, [] as Array<{sector: string, value: number, amount: number}>);

  const performanceData = portfolioStocks.map(stock => ({
    symbol: stock.symbol,
    gain: stock.gainPercent,
    value: stock.totalValue
  }));

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  const getRiskLevel = (score: number) => {
    if (score < 30) return { level: 'منخفض', color: 'text-green-600 bg-green-100' };
    if (score < 60) return { level: 'متوسط', color: 'text-yellow-600 bg-yellow-100' };
    return { level: 'عالي', color: 'text-red-600 bg-red-100' };
  };

  const getDiversificationLevel = (score: number) => {
    if (score > 70) return { level: 'ممتاز', color: 'text-green-600 bg-green-100' };
    if (score > 50) return { level: 'جيد', color: 'text-blue-600 bg-blue-100' };
    if (score > 30) return { level: 'متوسط', color: 'text-yellow-600 bg-yellow-100' };
    return { level: 'ضعيف', color: 'text-red-600 bg-red-100' };
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">تحليل المحفظة المتقدم</h2>
        <Badge variant="secondary" className="text-lg px-4 py-2">
          {portfolioStocks.length} سهم
        </Badge>
      </div>

      {/* Portfolio Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-600 to-blue-700 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">القيمة الإجمالية</p>
                <p className="text-2xl font-bold">{formatCurrency(portfolioMetrics.totalValue)}</p>
                <p className="text-blue-200 text-xs">جنيه مصري</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className={`${portfolioMetrics.totalGain >= 0 ? 
          'bg-gradient-to-br from-green-600 to-green-700' : 
          'bg-gradient-to-br from-red-600 to-red-700'} text-white`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/80 text-sm">الربح/الخسارة</p>
                <p className="text-2xl font-bold">
                  {portfolioMetrics.totalGain >= 0 ? '+' : ''}{formatCurrency(portfolioMetrics.totalGain)}
                </p>
                <p className="text-white/70 text-xs">
                  {portfolioMetrics.totalGainPercent >= 0 ? '+' : ''}{portfolioMetrics.totalGainPercent.toFixed(2)}%
                </p>
              </div>
              {portfolioMetrics.totalGain >= 0 ? 
                <TrendingUp className="h-8 w-8 text-white/80" /> :
                <TrendingDown className="h-8 w-8 text-white/80" />
              }
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-600 to-purple-700 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">عائد التوزيعات</p>
                <p className="text-2xl font-bold">{portfolioMetrics.totalDividendYield.toFixed(2)}%</p>
                <p className="text-purple-200 text-xs">متوسط مرجح</p>
              </div>
              <Target className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-600 to-orange-700 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">نسبة P/E</p>
                <p className="text-2xl font-bold">{portfolioMetrics.portfolioPE.toFixed(1)}</p>
                <p className="text-orange-200 text-xs">متوسط مرجح</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Risk and Diversification */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              تقييم المخاطر
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">مستوى المخاطر</span>
                <Badge className={getRiskLevel(portfolioMetrics.riskScore).color}>
                  {getRiskLevel(portfolioMetrics.riskScore).level}
                </Badge>
              </div>
              <Progress value={portfolioMetrics.riskScore} className="h-2" />
              <div className="text-xs text-gray-500 text-right">
                {portfolioMetrics.riskScore.toFixed(0)}/100
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">التنويع</span>
                <Badge className={getDiversificationLevel(portfolioMetrics.diversificationScore).color}>
                  {getDiversificationLevel(portfolioMetrics.diversificationScore).level}
                </Badge>
              </div>
              <Progress value={portfolioMetrics.diversificationScore} className="h-2" />
              <div className="text-xs text-gray-500 text-right">
                {portfolioMetrics.diversificationScore.toFixed(0)}/100
              </div>
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>التوصية:</strong> {
                  portfolioMetrics.diversificationScore > 70 
                    ? 'محفظة متنوعة جيداً، استمر في المراقبة'
                    : portfolioMetrics.diversificationScore > 50
                    ? 'فكر في إضافة قطاعات أخرى لتحسين التنويع'
                    : 'محفظة مركزة - يُنصح بإعادة التوازن'
                }
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChartIcon className="h-5 w-5 mr-2" />
              توزيع القطاعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={sectorDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ sector, value }) => `${sector}: ${value.toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {sectorDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [`${value.toFixed(1)}%`, 'النسبة']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            أداء الأسهم
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="symbol" />
              <YAxis />
              <Tooltip 
                formatter={(value: number, name: string) => [
                  name === 'gain' ? `${value.toFixed(2)}%` : formatCurrency(value),
                  name === 'gain' ? 'التغيير %' : 'القيمة'
                ]}
              />
              <Bar dataKey="gain" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Detailed Holdings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            تفاصيل المحفظة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {portfolioStocks.map((stock) => (
              <div key={stock.symbol} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div>
                    <h3 className="font-bold text-gray-900">{stock.symbol}</h3>
                    <p className="text-sm text-gray-600">{stock.sector}</p>
                    <p className="text-xs text-gray-500">{stock.quantity} سهم</p>
                  </div>
                </div>
                
                <div className="text-center">
                  <p className="text-sm text-gray-600">متوسط الشراء</p>
                  <p className="font-medium">{formatCurrency(stock.averagePrice)}</p>
                </div>
                
                <div className="text-center">
                  <p className="text-sm text-gray-600">السعر الحالي</p>
                  <p className="font-medium">{formatCurrency(stock.currentPrice)}</p>
                </div>
                
                <div className="text-center">
                  <p className="text-sm text-gray-600">القيمة الإجمالية</p>
                  <p className="font-bold">{formatCurrency(stock.totalValue)}</p>
                  <p className="text-xs text-gray-500">{stock.weight.toFixed(1)}%</p>
                </div>
                
                <div className="text-center">
                  <p className="text-sm text-gray-600">الربح/الخسارة</p>
                  <p className={`font-bold ${stock.gain >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stock.gain >= 0 ? '+' : ''}{formatCurrency(stock.gain)}
                  </p>
                  <p className={`text-xs ${stock.gainPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stock.gainPercent >= 0 ? '+' : ''}{stock.gainPercent.toFixed(2)}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PortfolioAnalyzer;
