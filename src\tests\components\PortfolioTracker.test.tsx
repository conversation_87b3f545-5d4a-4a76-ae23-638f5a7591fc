import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PortfolioTracker from '../../components/PortfolioTracker';
import { createTestQueryClient, TestWrapper } from '../test-utils';
import { QueryClient } from '@tanstack/react-query';

// Mock the portfolio hooks
vi.mock('../../hooks/useUserPortfolios', () => ({
  useUserPortfolios: vi.fn(() => ({
    data: [
      {
        id: 1,
        name: 'My Portfolio',
        totalValue: 125000,
        dailyChange: 2500,
        dailyChangePercent: 2.04,
        holdings: [
          {
            symbol: 'ATQA',
            shares: 100,
            avgPrice: 14.5,
            currentPrice: 15.5,
            value: 1550,
            change: 100,
            changePercent: 6.9
          },
          {
            symbol: 'CLHO',
            shares: 200,
            avgPrice: 8.0,
            currentPrice: 8.2,
            value: 1640,
            change: 40,
            changePercent: 2.5
          }
        ]
      }
    ],
    isLoading: false,
    error: null
  }))
}));

vi.mock('../../hooks/usePaperTrading', () => ({
  usePaperTrading: vi.fn(() => ({
    balance: 50000,
    positions: [],
    transactions: [],
    buyStock: vi.fn(),
    sellStock: vi.fn(),
    isLoading: false
  }))
}));

describe('PortfolioTracker', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  beforeEach(() => {
    queryClient = createTestQueryClient();
    vi.clearAllMocks();
  });

  it('renders portfolio overview with total value and performance', () => {
    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    // Check portfolio total value
    expect(screen.getByText('125,000')).toBeInTheDocument();
    expect(screen.getByText('+2,500')).toBeInTheDocument();
    expect(screen.getByText('+2.04%')).toBeInTheDocument();
  });

  it('displays individual holdings with correct data', () => {
    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    // Check individual stock holdings
    expect(screen.getByText('ATQA')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument(); // shares
    expect(screen.getByText('14.50')).toBeInTheDocument(); // avg price
    expect(screen.getByText('15.50')).toBeInTheDocument(); // current price
    expect(screen.getByText('1,550')).toBeInTheDocument(); // value

    expect(screen.getByText('CLHO')).toBeInTheDocument();
    expect(screen.getByText('200')).toBeInTheDocument(); // shares
    expect(screen.getByText('8.00')).toBeInTheDocument(); // avg price
    expect(screen.getByText('8.20')).toBeInTheDocument(); // current price
    expect(screen.getByText('1,640')).toBeInTheDocument(); // value
  });

  it('shows profit/loss indicators correctly', () => {
    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    // Check for gain indicators
    expect(screen.getByText('+100')).toBeInTheDocument(); // ATQA gain
    expect(screen.getByText('+6.9%')).toBeInTheDocument();
    expect(screen.getByText('+40')).toBeInTheDocument(); // CLHO gain
    expect(screen.getByText('+2.5%')).toBeInTheDocument();
  });

  it('handles portfolio selection and switching', async () => {
    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    // Check if portfolio name is displayed
    expect(screen.getByText('My Portfolio')).toBeInTheDocument();

    // If there's a dropdown or selector, test it
    const portfolioSelector = screen.queryByRole('combobox');
    if (portfolioSelector) {
      await user.click(portfolioSelector);
      // Test portfolio switching functionality
    }
  });

  it('displays paper trading balance when available', () => {
    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    // Check paper trading balance
    expect(screen.getByText('50,000')).toBeInTheDocument();
  });

  it('handles loading state correctly', () => {
    vi.doMock('../../hooks/useUserPortfolios', () => ({
      useUserPortfolios: () => ({
        data: null,
        isLoading: true,
        error: null
      })
    }));

    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    expect(screen.getByText(/Loading portfolio/i)).toBeInTheDocument();
  });

  it('handles error state correctly', () => {
    vi.doMock('../../hooks/useUserPortfolios', () => ({
      useUserPortfolios: () => ({
        data: null,
        isLoading: false,
        error: new Error('Failed to load portfolio')
      })
    }));

    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    expect(screen.getByText(/Error loading portfolio/i)).toBeInTheDocument();
  });

  it('calculates and displays portfolio metrics correctly', () => {
    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    // Verify calculated totals match the displayed values
    const totalValue = 125000;
    const dailyChange = 2500;
    const changePercent = 2.04;

    expect(screen.getByText(totalValue.toLocaleString())).toBeInTheDocument();
    expect(screen.getByText(`+${dailyChange.toLocaleString()}`)).toBeInTheDocument();
    expect(screen.getByText(`+${changePercent}%`)).toBeInTheDocument();
  });

  it('handles empty portfolio state', () => {
    vi.doMock('../../hooks/useUserPortfolios', () => ({
      useUserPortfolios: () => ({
        data: [{
          id: 1,
          name: 'Empty Portfolio',
          totalValue: 0,
          dailyChange: 0,
          dailyChangePercent: 0,
          holdings: []
        }],
        isLoading: false,
        error: null
      })
    }));

    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    expect(screen.getByText(/No holdings/i)).toBeInTheDocument();
  });

  it('formats currency values correctly', () => {
    render(
      <TestWrapper>
        <PortfolioTracker />
      </TestWrapper>
    );

    // Check that values are formatted with proper separators
    expect(screen.getByText('125,000')).toBeInTheDocument();
    expect(screen.getByText('1,550')).toBeInTheDocument();
    expect(screen.getByText('1,640')).toBeInTheDocument();
  });
});
