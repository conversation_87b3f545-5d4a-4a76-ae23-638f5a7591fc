#!/usr/bin/env python3
"""
إنشاء مناظر SQL مباشرة في قاعدة البيانات - طريقة بسيطة
Create SQL views directly in database - simple approach
"""

import os
import sys
from supabase import create_client, Client

# إعداد البيئة
load_dotenv = lambda: None
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# إعداد الاتصال بقاعدة البيانات
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Missing Supabase configuration")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("✅ Connected to Supabase successfully")
except Exception as e:
    print(f"❌ Error connecting to Supabase: {e}")
    sys.exit(1)

def execute_sql_directly(sql_query: str, description: str = ""):
    """تنفيذ استعلام SQL مباشر"""
    try:
        if description:
            print(f"🔧 {description}...")
        
        # محاولة استخدام استعلام مباشر
        response = supabase.postgrest.session.post(
            f"{SUPABASE_URL}/rest/v1/rpc/query",
            json={"query": sql_query},
            headers={
                "apikey": SUPABASE_KEY,
                "Authorization": f"Bearer {SUPABASE_KEY}",
                "Content-Type": "application/json"
            }
        )
        
        if response.status_code == 200:
            print(f"✅ {description} - تم بنجاح!")
            return True
        else:
            print(f"❌ {description} - فشل: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في {description}: {e}")
        return False

def create_simple_materialized_views():
    """إنشاء مناظر مادية بسيطة"""
    
    # منظر الملخص اليومي
    summary_view_sql = '''
    DROP MATERIALIZED VIEW IF EXISTS daily_market_summary;
    CREATE MATERIALIZED VIEW daily_market_summary AS
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers_count,
        COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers_count,
        COUNT(CASE WHEN change_percent = 0 THEN 1 END) as unchanged_count,
        COALESCE(SUM(turnover), 0) as total_turnover,
        COALESCE(SUM(volume), 0) as total_volume,
        COALESCE(ROUND(AVG(change_percent), 2), 0) as avg_change_percent,
        CASE 
            WHEN COUNT(CASE WHEN change_percent > 0 THEN 1 END) > 
                 COUNT(CASE WHEN change_percent < 0 THEN 1 END) * 1.2 THEN 'bullish'
            WHEN COUNT(CASE WHEN change_percent < 0 THEN 1 END) > 
                 COUNT(CASE WHEN change_percent > 0 THEN 1 END) * 1.2 THEN 'bearish'
            ELSE 'neutral'
        END as market_trend,
        NOW() as calculated_at
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL 
      AND current_price IS NOT NULL;
    '''
    
    # منظر الأسهم الأكثر نشاطاً
    active_stocks_sql = '''
    DROP MATERIALIZED VIEW IF EXISTS top_active_stocks;
    CREATE MATERIALIZED VIEW top_active_stocks AS
    SELECT 
        symbol,
        name,
        current_price,
        change_percent,
        volume,
        turnover,
        ma5,
        ma20,
        ROW_NUMBER() OVER (ORDER BY volume DESC) as volume_rank
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL 
      AND current_price IS NOT NULL
      AND volume > 0
    ORDER BY volume DESC
    LIMIT 20;
    '''
    
    # منظر أفضل الرابchون
    top_gainers_sql = '''
    DROP MATERIALIZED VIEW IF EXISTS top_gainers;
    CREATE MATERIALIZED VIEW top_gainers AS
    SELECT 
        symbol,
        name,
        current_price,
        change_percent,
        volume,
        turnover,
        ROW_NUMBER() OVER (ORDER BY change_percent DESC) as gain_rank
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL 
      AND current_price IS NOT NULL
      AND change_percent > 0
    ORDER BY change_percent DESC
    LIMIT 10;
    '''
    
    # منظر أكبر الخاسرين
    top_losers_sql = '''
    DROP MATERIALIZED VIEW IF EXISTS top_losers;
    CREATE MATERIALIZED VIEW top_losers AS
    SELECT 
        symbol,
        name,
        current_price,
        change_percent,
        volume,
        turnover,
        ROW_NUMBER() OVER (ORDER BY change_percent ASC) as loss_rank
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL 
      AND current_price IS NOT NULL
      AND change_percent < 0
    ORDER BY change_percent ASC
    LIMIT 10;
    '''
    
    # التحليل الفني
    technical_analysis_sql = '''
    DROP MATERIALIZED VIEW IF EXISTS technical_summary;
    CREATE MATERIALIZED VIEW technical_summary AS
    SELECT 
        COUNT(CASE WHEN ma5 > ma20 AND ma5 IS NOT NULL AND ma20 IS NOT NULL THEN 1 END) as bullish_ma_count,
        COUNT(CASE WHEN ma5 < ma20 AND ma5 IS NOT NULL AND ma20 IS NOT NULL THEN 1 END) as bearish_ma_count,
        COUNT(CASE WHEN ma5 > ma20 AND change_percent > 0 AND ma5 IS NOT NULL AND ma20 IS NOT NULL THEN 1 END) as strong_uptrend_count,
        COUNT(CASE WHEN target_1 IS NOT NULL AND target_1 > 0 AND current_price >= target_1 * 0.95 THEN 1 END) as near_target_count,
        COUNT(CASE WHEN ma5 IS NOT NULL AND ma20 IS NOT NULL THEN 1 END) as stocks_with_ma_data,
        NOW() as calculated_at
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL 
      AND current_price IS NOT NULL;
    '''
    
    views = [
        (summary_view_sql, "إنشاء منظر الملخص اليومي"),
        (active_stocks_sql, "إنشاء منظر الأسهم النشطة"),
        (top_gainers_sql, "إنشاء منظر أفضل الرابحين"),
        (top_losers_sql, "إنشاء منظر أكبر الخاسرين"),
        (technical_analysis_sql, "إنشاء منظر التحليل الفني")
    ]
    
    success_count = 0
    for sql, description in views:
        if execute_sql_directly(sql, description):
            success_count += 1
    
    print(f"\n📊 تم إنشاء {success_count}/{len(views)} منظر بنجاح")
    return success_count == len(views)

def test_created_views():
    """اختبار المناظر المُنشأة"""
    test_queries = [
        ("SELECT * FROM daily_market_summary;", "اختبار منظر الملخص اليومي"),
        ("SELECT COUNT(*) FROM top_active_stocks;", "اختبار منظر الأسهم النشطة"),
        ("SELECT COUNT(*) FROM top_gainers;", "اختبار منظر الرابحين"),
        ("SELECT COUNT(*) FROM top_losers;", "اختبار منظر الخاسرين"),
        ("SELECT * FROM technical_summary;", "اختبار منظر التحليل الفني")
    ]
    
    print("\n🧪 اختبار المناظر المُنشأة...")
    success_count = 0
    
    for query, description in test_queries:
        try:
            print(f"🔍 {description}...")
            
            # تجربة استعلام بسيط
            if "FROM daily_market_summary" in query:
                response = supabase.table('daily_market_summary').select('*').execute()
            elif "FROM top_active_stocks" in query:
                response = supabase.table('top_active_stocks').select('count').execute()
            elif "FROM top_gainers" in query:
                response = supabase.table('top_gainers').select('count').execute()
            elif "FROM top_losers" in query:
                response = supabase.table('top_losers').select('count').execute()
            elif "FROM technical_summary" in query:
                response = supabase.table('technical_summary').select('*').execute()
            
            if response.data:
                print(f"✅ {description} - يعمل بنجاح!")
                success_count += 1
            else:
                print(f"⚠️ {description} - لا توجد بيانات")
                
        except Exception as e:
            print(f"❌ {description} - خطأ: {e}")
    
    print(f"\n📊 نجح {success_count}/{len(test_queries)} اختبار")

def refresh_materialized_views():
    """تحديث المناظر المادية"""
    views_to_refresh = [
        'daily_market_summary',
        'top_active_stocks', 
        'top_gainers',
        'top_losers',
        'technical_summary'
    ]
    
    print("\n🔄 تحديث المناظر المادية...")
    success_count = 0
    
    for view_name in views_to_refresh:
        refresh_sql = f"REFRESH MATERIALIZED VIEW {view_name};"
        if execute_sql_directly(refresh_sql, f"تحديث {view_name}"):
            success_count += 1
    
    print(f"📊 تم تحديث {success_count}/{len(views_to_refresh)} منظر")

def main():
    """الدالة الرئيسية"""
    from datetime import datetime
    
    print("🚀 بدء إنشاء المناظر المادية البسيطة...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # إنشاء المناظر
    if create_simple_materialized_views():
        print("✅ تم إنشاء جميع المناظر بنجاح!")
        
        # اختبار المناظر
        test_created_views()
        
        # تحديث المناظر
        refresh_materialized_views()
    else:
        print("❌ فشل في إنشاء بعض المناظر")
    
    print("\n✅ انتهى إنشاء المناظر المحسنة!")

if __name__ == "__main__":
    main()
