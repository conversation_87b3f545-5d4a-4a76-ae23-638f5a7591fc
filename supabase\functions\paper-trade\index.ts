import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.50.0";
import type { Database } from "../../../src/integrations/supabase/types.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Authenticate the user with the JWT from the request header
  const supabaseClient = createClient<Database>(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
    {
      global: {
        headers: { Authorization: req.headers.get("Authorization") || "" },
      },
    }
  );

  try {
    const input = await req.json();
    const { account_id, symbol, quantity, entry_price, trade_type } = input;

    if (!account_id || !symbol || !quantity || !entry_price || !trade_type) {
      return new Response(JSON.stringify({ error: "Missing input data" }), { status: 400, headers: corsHeaders });
    }

    // Fetch account info
    const { data: account, error: accountError } = await supabaseClient
      .from("paper_trading_accounts")
      .select("*")
      .eq("id", account_id)
      .maybeSingle();

    if (accountError || !account) {
      return new Response(JSON.stringify({ error: "Account not found" }), { status: 404, headers: corsHeaders });
    }

    // Calculate new balance; for "buy" - decrease, for "sell" - increase
    const tradeValue = Number(entry_price) * Number(quantity);
    let newBalance = Number(account.current_balance) || 0;

    if (trade_type === "buy") {
      if (tradeValue > newBalance) {
        return new Response(JSON.stringify({ error: "Insufficient balance" }), { status: 400, headers: corsHeaders });
      }
      newBalance -= tradeValue;
    } else if (trade_type === "sell") {
      newBalance += tradeValue;
    } else {
      return new Response(JSON.stringify({ error: "Invalid trade type" }), { status: 400, headers: corsHeaders });
    }

    // Begin "transaction"
    // 1. Insert the trade
    const { data: newTrade, error: tradeError } = await supabaseClient
      .from("paper_trades")
      .insert([
        {
          account_id,
          symbol,
          quantity: Number(quantity),
          entry_price: Number(entry_price),
          trade_type,
        },
      ])
      .select("*")
      .single();

    if (tradeError) {
      return new Response(JSON.stringify({ error: tradeError.message }), { status: 400, headers: corsHeaders });
    }

    // 2. Update account: balance, trade stats
    const { error: updateError } = await supabaseClient
      .from("paper_trading_accounts")
      .update({
        current_balance: newBalance,
        total_trades: (account.total_trades || 0) + 1,
        // Other stats can be computed further, e.g., win/loss, max_drawdown on real exit
      })
      .eq("id", account_id);

    if (updateError) {
      // Optionally: Rollback trade (delete it) if fail, but as this is not atomic, we warn user
      await supabaseClient.from("paper_trades").delete().eq("id", newTrade.id);
      return new Response(JSON.stringify({ error: updateError.message }), { status: 400, headers: corsHeaders });
    }

    return new Response(JSON.stringify({ trade: newTrade }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 201,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    });
  }
});
