
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, 
  Zap, 
  Palette, 
  Smartphone, 
  Shield,
  Globe,
  Target,
  Award
} from 'lucide-react';
import PerformanceOptimizer from './advanced/PerformanceOptimizer';
import UIThemeCustomizer from './advanced/UIThemeCustomizer';

const EnhancedExperience = () => {
  const [activeSection, setActiveSection] = useState('overview');

  const sections = [
    {
      id: 'overview',
      title: 'نظرة عامة',
      icon: Sparkles,
      description: 'ملخص تحسينات التجربة'
    },
    {
      id: 'performance',
      title: 'تحسين الأداء',
      icon: Zap,
      description: 'تسريع المنصة وتحسين الاستجابة'
    },
    {
      id: 'themes',
      title: 'تخصيص المظهر',
      icon: Palette,
      description: 'تخصيص الألوان والواجهة'
    }
  ];

  if (activeSection === 'performance') {
    return (
      <div className="space-y-6">
        <div className="flex gap-4 mb-6">
          {sections.map(section => (
            <Button
              key={section.id}
              variant={activeSection === section.id ? 'default' : 'outline'}
              onClick={() => setActiveSection(section.id)}
              className="gap-2"
            >
              <section.icon className="h-4 w-4" />
              {section.title}
            </Button>
          ))}
        </div>
        <PerformanceOptimizer />
      </div>
    );
  }

  if (activeSection === 'themes') {
    return (
      <div className="space-y-6">
        <div className="flex gap-4 mb-6">
          {sections.map(section => (
            <Button
              key={section.id}
              variant={activeSection === section.id ? 'default' : 'outline'}
              onClick={() => setActiveSection(section.id)}
              className="gap-2"
            >
              <section.icon className="h-4 w-4" />
              {section.title}
            </Button>
          ))}
        </div>
        <UIThemeCustomizer />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex gap-4 mb-6">
        {sections.map(section => (
          <Button
            key={section.id}
            variant={activeSection === section.id ? 'default' : 'outline'}
            onClick={() => setActiveSection(section.id)}
            className="gap-2"
          >
            <section.icon className="h-4 w-4" />
            {section.title}
          </Button>
        ))}
      </div>

      {/* Phase 4 Overview */}
      <Card className="border-2 border-gradient-to-r from-purple-200 to-pink-200 bg-gradient-to-br from-purple-50 via-pink-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-purple-800">
            <Award className="h-6 w-6" />
            المرحلة الرابعة - تحسين التجربة والأداء
            <Badge className="bg-purple-100 text-purple-800 border-purple-200">Phase 4</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Performance Features */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <Zap className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold arabic">تحسين الأداء</h3>
                    <Badge variant="outline" className="mt-1">متاح</Badge>
                  </div>
                </div>
                <ul className="space-y-2 text-sm text-muted-foreground arabic">
                  <li>• التخزين المؤقت الذكي</li>
                  <li>• ضغط البيانات المتقدم</li>
                  <li>• التحميل التدريجي</li>
                  <li>• مقاييس الأداء المباشرة</li>
                </ul>
                <Button 
                  className="w-full mt-4" 
                  onClick={() => setActiveSection('performance')}
                >
                  عرض التفاصيل
                </Button>
              </CardContent>
            </Card>

            {/* UI Customization */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <Palette className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold arabic">تخصيص المظهر</h3>
                    <Badge variant="outline" className="mt-1">متاح</Badge>
                  </div>
                </div>
                <ul className="space-y-2 text-sm text-muted-foreground arabic">
                  <li>• ألوان مخصصة</li>
                  <li>• خطوط ومسافات قابلة للتعديل</li>
                  <li>• تأثيرات بصرية متقدمة</li>
                  <li>• معاينة مباشرة</li>
                </ul>
                <Button 
                  className="w-full mt-4" 
                  onClick={() => setActiveSection('themes')}
                >
                  بدء التخصيص
                </Button>
              </CardContent>
            </Card>

            {/* Enhanced Mobile Experience */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <Smartphone className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold arabic">التجربة المحمولة</h3>
                    <Badge variant="outline" className="mt-1">نشط</Badge>
                  </div>
                </div>
                <ul className="space-y-2 text-sm text-muted-foreground arabic">
                  <li>• إيماءات اللمس المتقدمة</li>
                  <li>• تطبيق ويب تقدمي (PWA)</li>
                  <li>• العمل بدون اتصال</li>
                  <li>• تحسينات خاصة بالهواتف</li>
                </ul>
                <Button className="w-full mt-4" variant="outline">
                  مدمج بالفعل
                </Button>
              </CardContent>
            </Card>

            {/* Security & Privacy */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-indigo-100 rounded-lg">
                    <Shield className="h-6 w-6 text-indigo-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold arabic">الأمان والخصوصية</h3>
                    <Badge variant="outline" className="mt-1">محمي</Badge>
                  </div>
                </div>
                <ul className="space-y-2 text-sm text-muted-foreground arabic">
                  <li>• تشفير البيانات المحلية</li>
                  <li>• حماية الخصوصية</li>
                  <li>• مصادقة آمنة</li>
                  <li>• نسخ احتياطية مشفرة</li>
                </ul>
                <Button className="w-full mt-4" variant="outline">
                  نشط بالفعل
                </Button>
              </CardContent>
            </Card>

            {/* Accessibility */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <Globe className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold arabic">سهولة الوصول</h3>
                    <Badge variant="outline" className="mt-1">محسن</Badge>
                  </div>
                </div>
                <ul className="space-y-2 text-sm text-muted-foreground arabic">
                  <li>• دعم قارئ الشاشة</li>
                  <li>• التنقل بالكيبورد</li>
                  <li>• تباين الألوان المحسن</li>
                  <li>• نصوص قابلة للتكبير</li>
                </ul>
                <Button className="w-full mt-4" variant="outline">
                  مدمج بالفعل
                </Button>
              </CardContent>
            </Card>

            {/* Advanced Integration */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-teal-100 rounded-lg">
                    <Target className="h-6 w-6 text-teal-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold arabic">التكاملات المتقدمة</h3>
                    <Badge variant="outline" className="mt-1">جاهز</Badge>
                  </div>
                </div>
                <ul className="space-y-2 text-sm text-muted-foreground arabic">
                  <li>• API متقدمة للمطورين</li>
                  <li>• ربط أنظمة خارجية</li>
                  <li>• تصدير واستيراد البيانات</li>
                  <li>• تحليلات مخصصة</li>
                </ul>
                <Button className="w-full mt-4" variant="outline">
                  متاح في المراحل السابقة
                </Button>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Implementation Status */}
      <Card>
        <CardHeader>
          <CardTitle className="arabic">حالة التنفيذ - المرحلة الرابعة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="font-medium arabic">تحسين الأداء والتخزين المؤقت</span>
              </div>
              <Badge className="bg-green-100 text-green-800">مكتمل</Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="font-medium arabic">تخصيص المظهر والواجهة</span>
              </div>
              <Badge className="bg-green-100 text-green-800">مكتمل</Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="font-medium arabic">التجربة المحمولة المحسنة</span>
              </div>
              <Badge className="bg-blue-100 text-blue-800">نشط بالفعل</Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="font-medium arabic">الأمان وسهولة الوصول</span>
              </div>
              <Badge className="bg-blue-100 text-blue-800">مدمج بالتصميم</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedExperience;
