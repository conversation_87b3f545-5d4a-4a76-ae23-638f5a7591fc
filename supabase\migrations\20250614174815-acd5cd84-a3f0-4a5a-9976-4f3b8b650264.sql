
-- Table for ML Predictions
create table public.analytics_ml_predictions (
  id uuid primary key default gen_random_uuid(),
  symbol varchar not null,
  prediction varchar not null, -- e.g., 'صعود','محايد'
  confidence numeric not null,
  target_price numeric not null,
  timeframe varchar not null,
  factors jsonb, -- Array of factors as JSON
  generated_at timestamptz not null default now()
);

-- Table for Pattern Recognition
create table public.analytics_patterns (
  id uuid primary key default gen_random_uuid(),
  symbol varchar not null,
  pattern varchar not null,
  reliability numeric not null, -- %
  expected varchar not null, -- e.g., 'كسر علوي'
  timeframe varchar not null,
  detected_at timestamptz not null default now()
);

-- Table for Correlation Analysis
create table public.analytics_correlations (
  id uuid primary key default gen_random_uuid(),
  stock1 varchar not null,
  stock2 varchar not null,
  correlation numeric not null,
  strength varchar not null, -- e.g., 'قوية','متوسطة'
  analyzed_at timestamptz not null default now()
);

-- Table for Volatility Analysis
create table public.analytics_volatility (
  id uuid primary key default gen_random_uuid(),
  symbol varchar not null,
  current_volatility numeric not null,
  avg_volatility numeric not null,
  trend varchar not null, -- e.g., 'مرتفع','منخفض'
  analyzed_at timestamptz not null default now()
);

-- Index for fast lookups
create index on public.analytics_ml_predictions(symbol);
create index on public.analytics_patterns(symbol);
create index on public.analytics_correlations(stock1,stock2);
create index on public.analytics_volatility(symbol);

-- OPTIONAL: Add RLS to allow querying for all users (analytics are public data)
alter table public.analytics_ml_predictions enable row level security;
create policy "public analytics ml predictions" on public.analytics_ml_predictions for select using (true);

alter table public.analytics_patterns enable row level security;
create policy "public analytics patterns" on public.analytics_patterns for select using (true);

alter table public.analytics_correlations enable row level security;
create policy "public analytics correlations" on public.analytics_correlations for select using (true);

alter table public.analytics_volatility enable row level security;
create policy "public analytics volatility" on public.analytics_volatility for select using (true);
