
import { useEffect } from "react";
import { PriceAlert } from "@/hooks/usePriceAlerts";
import { useToast } from "@/hooks/use-toast";

interface UseMockPriceUpdaterProps {
  alerts: PriceAlert[];
  setAlerts: React.Dispatch<React.SetStateAction<PriceAlert[]>>;
  toggleAlert: (id: string, forcedStatus?: boolean) => void;
}

export function useMockPriceUpdater({
  alerts,
  setAlerts,
  toggleAlert,
}: UseMockPriceUpdaterProps) {
  const { toast } = useToast();

  useEffect(() => {
    if (!alerts.length) return;

    const interval = setInterval(() => {
      setAlerts((prevAlerts) =>
        prevAlerts.map((alert) => {
          if (!alert.isActive) return alert;

          // Simulate current price changing slightly each interval
          const mockCurrentPrice =
            (alert.currentPrice ?? 50) + (Math.random() - 0.5) * 2;

          // -- Trigger logic --
          const shouldTrigger =
            (alert.condition === "above" &&
              mockCurrentPrice >= alert.targetPrice) ||
            (alert.condition === "below" &&
              mockCurrentPrice <= alert.targetPrice);

          if (shouldTrigger) {
            // Deactivate alert and notify
            toggleAlert(alert.id, false);
            toast({
              title: "🚨 تم تفعيل تنبيه السعر",
              description: `تم تفعيل تنبيه ${alert.symbol}: ${mockCurrentPrice.toFixed(2)} EGP (${alert.condition === "above" ? "أعلى" : "أقل"} من ${alert.targetPrice})`,
            });
            // Browser notification (if allowed)
            if ("Notification" in window && Notification.permission === "granted") {
              new Notification(`تنبيه سعر - ${alert.symbol}`, {
                body: `وصل السعر إلى ${mockCurrentPrice.toFixed(2)} EGP`,
                icon: "/favicon.ico",
              });
            }
          }

          return { ...alert, currentPrice: mockCurrentPrice };
        })
      );
    }, 3000);

    return () => clearInterval(interval);
  }, [alerts, setAlerts, toggleAlert, toast]);
}
