
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Bell, Settings, Link, CheckCircle, AlertCircle, MessageCircle, Zap, Shield } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const TelegramIntegration = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [telegramUsername, setTelegramUsername] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [alertSettings, setAlertSettings] = useState({
    priceAlerts: true,
    volumeAlerts: true,
    technicalAlerts: false,
    aiRecommendations: true
  });
  const { toast } = useToast();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleTelegramConnect = async () => {
    if (!telegramUsername) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم المستخدم في تيليجرام",
        variant: "destructive",
      });
      return;
    }

    setIsConnecting(true);
    
    // Simulate connection process with loading states
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsConnected(true);
      toast({
        title: "تم الربط بنجاح! 🎉",
        description: `تم ربط حسابك مع بوت @egx_stock_analyzer_bot`,
      });
    } catch (error) {
      toast({
        title: "فشل في الاتصال",
        description: "حدث خطأ أثناء ربط الحساب. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setTelegramUsername('');
    toast({
      title: "تم قطع الاتصال",
      description: "تم قطع الاتصال مع بوت تيليجرام",
      variant: "destructive",
    });
  };

  const toggleAlert = (alertType: keyof typeof alertSettings) => {
    setAlertSettings(prev => ({
      ...prev,
      [alertType]: !prev[alertType]
    }));
    
    toast({
      title: "تم التحديث",
      description: `تم ${alertSettings[alertType] ? 'إيقاف' : 'تفعيل'} التنبيهات`,
    });
  };

  const alertTypes = [
    {
      key: 'priceAlerts' as const,
      label: 'تنبيهات الأسعار',
      description: 'عند وصول السهم لسعر محدد',
      icon: Bell,
      color: 'text-blue-600'
    },
    {
      key: 'volumeAlerts' as const,
      label: 'تنبيهات الحجم',
      description: 'عند زيادة حجم التداول بشكل كبير',
      icon: Settings,
      color: 'text-green-600'
    },
    {
      key: 'technicalAlerts' as const,
      label: 'التحليل الفني',
      description: 'إشارات فنية مهمة ونقاط دعم ومقاومة',
      icon: AlertCircle,
      color: 'text-orange-600'
    },
    {
      key: 'aiRecommendations' as const,
      label: 'توصيات الذكاء الاصطناعي',
      description: 'توصيات شراء وبيع من الذكاء الاصطناعي',
      icon: Zap,
      color: 'text-purple-600'
    }
  ];

  const recentAlerts = [
    {
      time: 'منذ 5 دقائق',
      message: 'COMI وصل إلى مستوى المقاومة 52.50 EGP',
      type: 'price',
      color: 'bg-market-green'
    },
    {
      time: 'منذ 15 دقيقة',
      message: 'حجم تداول عالي على TALAAT - فرصة استثمارية',
      type: 'volume',
      color: 'bg-egx-blue-600'
    },
    {
      time: 'منذ 30 دقيقة',
      message: 'توصية AI: شراء ETEL عند مستوى 18.20',
      type: 'ai',
      color: 'bg-egx-gold-600'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Enhanced Connection Status */}
      <Card className={`border-egx-blue-200 hover:shadow-elevated transition-all duration-500 card-hover overflow-hidden relative
        ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
        
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 30% 40%, rgba(37, 99, 235, 0.3) 0%, transparent 50%)`
          }}></div>
        </div>

        <CardHeader className="relative z-10">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-egx-blue-600 rounded-lg">
              <MessageCircle className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-egx-blue-800">ربط بوت تيليجرام</h3>
              <p className="text-sm text-egx-blue-600 mt-1">احصل على تنبيهات فورية ومخصصة</p>
            </div>
            {isConnected && (
              <div className="ml-auto">
                <Badge className="bg-market-green hover:bg-market-green/80 shadow-lg">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  متصل
                </Badge>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6 relative z-10">
          {!isConnected ? (
            <div className="space-y-6">
              {/* Instructions */}
              <div className="bg-gradient-to-r from-egx-blue-50 to-blue-50 p-6 rounded-xl border border-egx-blue-200">
                <h4 className="font-bold text-egx-blue-800 mb-4 flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  كيفية الربط السريع:
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-white/60 rounded-lg">
                    <div className="w-12 h-12 bg-egx-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                      <span className="text-white font-bold">1</span>
                    </div>
                    <p className="text-sm text-egx-blue-700">ابحث عن @egx_stock_analyzer_bot</p>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg">
                    <div className="w-12 h-12 bg-egx-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                      <span className="text-white font-bold">2</span>
                    </div>
                    <p className="text-sm text-egx-blue-700">أرسل /start للبوت</p>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg">
                    <div className="w-12 h-12 bg-egx-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                      <span className="text-white font-bold">3</span>
                    </div>
                    <p className="text-sm text-egx-blue-700">أدخل اسم المستخدم هنا</p>
                  </div>
                </div>
              </div>
              
              {/* Connection Form */}
              <div className="space-y-4">
                <div className="flex gap-3">
                  <Input
                    placeholder="@username أو اسم المستخدم في تيليجرام"
                    value={telegramUsername}
                    onChange={(e) => setTelegramUsername(e.target.value)}
                    className="arabic text-right flex-1 h-12 text-lg border-2 focus:border-egx-blue-500"
                    dir="rtl"
                    disabled={isConnecting}
                  />
                  <Button 
                    onClick={handleTelegramConnect}
                    disabled={isConnecting || !telegramUsername}
                    className="bg-egx-blue-600 hover:bg-egx-blue-700 whitespace-nowrap h-12 px-6 btn-animate"
                  >
                    {isConnecting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        جاري الربط...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Link className="h-4 w-4" />
                        ربط الحساب
                      </div>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Connected Status */}
              <div className="flex items-center justify-between p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border-2 border-green-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-market-green rounded-full">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <div className="font-bold text-green-800 text-lg">
                      متصل مع @{telegramUsername}
                    </div>
                    <div className="text-sm text-green-600">البوت جاهز لإرسال التنبيهات</div>
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  onClick={handleDisconnect}
                  className="text-market-red border-market-red hover:bg-market-red hover:text-white btn-animate"
                >
                  قطع الاتصال
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Alert Settings */}
      <Card className={`border-egx-gold-200 hover:shadow-elevated transition-all duration-500 card-hover overflow-hidden relative
        ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
        
        <CardHeader className="relative z-10">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-egx-gold-600 rounded-lg">
              <Bell className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-egx-gold-800">إعدادات التنبيهات المتقدمة</h3>
              <p className="text-sm text-egx-gold-600 mt-1">خصص تنبيهاتك حسب احتياجاتك</p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {alertTypes.map((alertType, index) => (
              <Card 
                key={alertType.key}
                className={`border-2 transition-all duration-300 hover:shadow-lg card-hover
                  ${alertSettings[alertType.key] ? 'border-green-200 bg-green-50/50' : 'border-gray-200 bg-gray-50/50'}`}
                style={{ 
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className={`p-2 rounded-lg ${alertSettings[alertType.key] ? 'bg-green-100' : 'bg-gray-100'}`}>
                        <alertType.icon className={`h-5 w-5 ${alertSettings[alertType.key] ? 'text-green-600' : 'text-gray-500'}`} />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-800 mb-1">{alertType.label}</h4>
                        <p className="text-xs text-gray-600 leading-relaxed">{alertType.description}</p>
                      </div>
                    </div>
                    <Button
                      variant={alertSettings[alertType.key] ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleAlert(alertType.key)}
                      className={`ml-3 transition-all duration-300 btn-animate ${
                        alertSettings[alertType.key] 
                          ? "bg-market-green hover:bg-market-green/80 text-white shadow-lg" 
                          : "border-gray-300 hover:border-gray-400"
                      }`}
                    >
                      {alertSettings[alertType.key] ? 'مفعل' : 'معطل'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {/* Recent Alerts Section */}
          {isConnected && (
            <div className="mt-8 p-6 bg-gradient-to-br from-egx-gold-50 to-yellow-50 rounded-xl border border-egx-gold-200">
              <h4 className="font-bold text-egx-gold-800 mb-4 flex items-center gap-2">
                <Bell className="h-5 w-5" />
                آخر التنبيهات المرسلة:
              </h4>
              <div className="space-y-3">
                {recentAlerts.map((alert, index) => (
                  <div 
                    key={index}
                    className="flex items-start gap-3 p-3 bg-white/60 rounded-lg border border-egx-gold-200/50 hover:bg-white/80 transition-colors duration-300"
                  >
                    <div className={`w-3 h-3 ${alert.color} rounded-full mt-2 flex-shrink-0`}></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-800 mb-1">{alert.message}</div>
                      <div className="text-xs text-gray-500">{alert.time}</div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 text-center">
                <Button variant="outline" size="sm" className="text-egx-gold-700 border-egx-gold-300 hover:bg-egx-gold-50 btn-animate">
                  عرض جميع التنبيهات
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TelegramIntegration;
