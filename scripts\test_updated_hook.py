#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_updated_hook_logic():
    """Test the updated hook logic by simulating the same queries"""
    print("🧪 اختبار منطق الـ Hook المحدث...")
    print("=" * 50)
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}"
    }
    
    try:
        # 1. Test indices query (same as hook)
        print("1️⃣ جلب بيانات المؤشرات...")
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/market_indices?select=*&symbol=in.(EGX30,EGX70)",
            headers=headers
        )
        
        if response.status_code == 200:
            indices = response.json()
            print(f"   ✅ تم جلب {len(indices)} مؤشر")
            
            egx30 = next((idx for idx in indices if idx['symbol'] == 'EGX30'), None)
            egx70 = next((idx for idx in indices if idx['symbol'] == 'EGX70'), None)
            
            if egx30:
                volume_billions = egx30['volume'] / 1000000000 if egx30['volume'] else 0
                turnover_millions = egx30['turnover'] / 1000000 if egx30['turnover'] else 0
                print(f"   📊 EGX30: {egx30['current_value']} ({egx30['change_percent']}%)")
                print(f"   💰 الحجم: {volume_billions:.2f}B, القيمة: {turnover_millions:.0f}M")
            
            if egx70:
                print(f"   📊 EGX70: {egx70['current_value']} ({egx70['change_percent']}%)")
          # 2. Test stocks query for distribution (same as hook)
        print("\n2️⃣ حساب توزيع السوق...")
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol,change_percent&symbol=not.like.*EGX*",
            headers=headers
        )
        
        if response.status_code == 200:
            stocks = response.json()
            print(f"   ✅ تم جلب {len(stocks)} سهم (بدون المؤشرات)")
            
            # Calculate distribution (same logic as hook)
            gainers = 0
            losers = 0
            unchanged = 0
            
            for stock in stocks:
                change_percent = float(stock.get('change_percent', 0) or 0)
                if change_percent > 0:
                    gainers += 1
                elif change_percent < 0:
                    losers += 1
                else:
                    unchanged += 1
            
            total = gainers + losers + unchanged
            print(f"   📈 إجمالي الأسهم: {total}")
            print(f"   🟢 صاعد: {gainers} ({gainers/total*100:.1f}%)")
            print(f"   🔴 هابط: {losers} ({losers/total*100:.1f}%)")
            print(f"   ⚪ ثابت: {unchanged} ({unchanged/total*100:.1f}%)")
            
            # Show sample of each category
            sample_gainers = [s for s in stocks if float(s.get('change_percent', 0) or 0) > 0][:3]
            sample_losers = [s for s in stocks if float(s.get('change_percent', 0) or 0) < 0][:3]
            
            if sample_gainers:
                print(f"\n   📈 عينة الأسهم الصاعدة:")
                for stock in sample_gainers:
                    print(f"      {stock['symbol']}: +{stock['change_percent']}%")
            
            if sample_losers:
                print(f"\n   📉 عينة الأسهم الهابطة:")
                for stock in sample_losers:
                    print(f"      {stock['symbol']}: {stock['change_percent']}%")
        
        print("\n" + "=" * 50)
        print("✅ سيعرض الفرونت اند الآن البيانات الحقيقية!")
        print("🔄 قم بتحديث المتصفح لرؤية التوزيع المحدث")
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    test_updated_hook_logic()
