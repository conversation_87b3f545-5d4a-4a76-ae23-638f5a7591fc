#!/usr/bin/env python3
"""
Add missing sector column to stocks_financials table
"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def add_sector_column():
    """Add sector column to stocks_financials if it doesn't exist"""
    
    print("🔧 Adding sector column to stocks_financials...")
    
    try:
        # Test if sector column exists by trying to select it
        result = supabase.table('stocks_financials').select('sector').limit(1).execute()
        print("✅ Sector column already exists!")
        return True
        
    except Exception as e:
        if "Could not find the 'sector' column" in str(e):
            print("❌ Sector column missing, need to add it manually in Supabase SQL editor:")
            print("   ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS sector VARCHAR(100);")
            return False
        else:
            print(f"❌ Error checking sector column: {e}")
            return False

def verify_all_columns():
    """Verify all required columns exist"""
    
    print("\n🔍 Verifying enhanced financial columns...")
    
    # Get current schema
    result = supabase.table('stocks_financials').select('*').limit(1).execute()
    if result.data:
        columns = list(result.data[0].keys())
        print(f"✅ Total columns in stocks_financials: {len(columns)}")
        
        # Check for key missing columns
        required_columns = ['sector', 'industry', 'description', 'assets_to_equity', 'average_volume_10d']
        missing = []
        present = []
        
        for col in required_columns:
            if col in columns:
                present.append(col)
            else:
                missing.append(col)
        
        if missing:
            print(f"❌ Missing columns: {missing}")
            print("   Please execute this SQL in Supabase:")
            for col in missing:
                if col == 'sector':
                    print(f"   ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS {col} VARCHAR(100);")
                else:
                    print(f"   ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS {col} TEXT;")
        else:
            print(f"✅ All key columns present: {present}")
        
        return len(missing) == 0
    
    return False

if __name__ == "__main__":
    print("🚀 Financial Schema Column Check")
    print("=" * 50)
    
    sector_exists = add_sector_column()
    all_columns_ok = verify_all_columns()
    
    if sector_exists and all_columns_ok:
        print("\n🎉 All columns ready! You can now run:")
        print("   python load_financials_enhanced.py")
    else:
        print("\n⚠️  Please add missing columns in Supabase SQL editor first.")
