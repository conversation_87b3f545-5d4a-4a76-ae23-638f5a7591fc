from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer
from sqlalchemy.sql import func
from ..database import Base


class StockMaster(Base):
    """Stock master data model"""
    __tablename__ = "stocks_master"
    
    symbol = Column(String(15), primary_key=True, index=True)
    name_ar = Column(String(200), nullable=False)
    name_en = Column(String(200))
    sector = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_suspended = Column(Boolean, default=False)
    listing_date = Column(DateTime)
    isin = Column(String(20))
    market_cap = Column(String(20))
    shares_outstanding = Column(String(20))
    free_float = Column(String(20))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Additional metadata
    description = Column(Text)
    website = Column(String(200))
    phone = Column(String(50))
    address = Column(Text)
