#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Quick Historical Data Import Test
Test script to import a few TXT files and verify the system works
"""

import pandas as pd
import subprocess
import sys
from pathlib import Path
import json

def run_sql_as_postgres(sql_command, database='egx_stock_oracle'):
    """Execute SQL command as postgres user"""
    try:
        cmd = ['sudo', '-u', 'postgres', 'psql', '-d', database, '-c', sql_command]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"❌ SQL Error: {e.stderr}")
        return None

def test_data_import():
    """Test importing a few TXT files"""
    print("🧪 Testing Historical Data Import System")
    print("=" * 50)
    
    # Check current database state
    result = run_sql_as_postgres("SELECT COUNT(*) FROM stocks_master;")
    if result:
        print(f"📊 Current stocks_master records: {result}")
    
    # Get first 3 TXT files for testing
    meta2_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2")
    txt_files = list(meta2_path.glob("*.TXT"))[:3]
    
    print(f"📁 Testing with {len(txt_files)} TXT files:")
    for file in txt_files:
        print(f"  - {file.name}")
    
    successful_imports = 0
    total_records = 0
    
    for txt_file in txt_files:
        try:
            print(f"\n📖 Processing {txt_file.name}...")
            
            # Read and parse the file
            df = pd.read_csv(txt_file)
            print(f"  📊 Loaded {len(df)} records")
            
            # Extract symbol from filename
            symbol = txt_file.stem.replace('D', '') if txt_file.stem.endswith('D') else txt_file.stem
            
            # Insert symbol into stocks_master if not exists
            sql_insert_symbol = f"""
            INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
            VALUES ('{symbol}', 'سهم {symbol}', '{symbol}', true)
            ON CONFLICT (symbol) DO NOTHING;
            """
            
            run_sql_as_postgres(sql_insert_symbol)
            
            # Process first 100 records from this file as test
            test_df = df.head(100)
            
            # Convert and clean data
            records_inserted = 0
            for _, row in test_df.iterrows():
                try:
                    if len(row) >= 6:  # Ensure minimum columns
                        # Parse date (assuming format YYYYMMDD)
                        date_str = str(row.iloc[2])  # DATE column
                        if len(date_str) == 8:
                            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                            
                            # Extract prices
                            open_price = float(row.iloc[4]) if pd.notna(row.iloc[4]) else 0
                            high_price = float(row.iloc[5]) if pd.notna(row.iloc[5]) else 0
                            low_price = float(row.iloc[6]) if pd.notna(row.iloc[6]) else 0
                            close_price = float(row.iloc[7]) if pd.notna(row.iloc[7]) else 0
                            volume = int(row.iloc[8]) if pd.notna(row.iloc[8]) else 0
                            
                            if all(p > 0 for p in [open_price, high_price, low_price, close_price]):
                                sql_insert_historical = f"""
                                INSERT INTO stocks_historical (
                                    symbol, trade_date, period, open_price, high_price, 
                                    low_price, close_price, volume
                                ) VALUES (
                                    '{symbol}', '{formatted_date}', 'D', 
                                    {open_price}, {high_price}, {low_price}, {close_price}, {volume}
                                )
                                ON CONFLICT (symbol, trade_date, period) DO NOTHING;
                                """
                                
                                result = run_sql_as_postgres(sql_insert_historical)
                                if result is not None:
                                    records_inserted += 1
                                
                except Exception as e:
                    print(f"    ⚠️ Skipped invalid record: {e}")
                    continue
            
            print(f"  ✅ Inserted {records_inserted} historical records for {symbol}")
            successful_imports += 1
            total_records += records_inserted
            
        except Exception as e:
            print(f"  ❌ Failed to process {txt_file.name}: {e}")
    
    print("\n" + "=" * 50)
    print("📋 Import Test Results:")
    print(f"✅ Successful files: {successful_imports}/{len(txt_files)}")
    print(f"📊 Total records imported: {total_records}")
    
    # Final database check
    master_count = run_sql_as_postgres("SELECT COUNT(*) FROM stocks_master;")
    historical_count = run_sql_as_postgres("SELECT COUNT(*) FROM stocks_historical;")
    
    print(f"📈 Final database state:")
    print(f"  - stocks_master: {master_count} records")
    print(f"  - stocks_historical: {historical_count} records")
    
    if successful_imports > 0:
        print("\n🎉 Import test successful! System is working correctly.")
        return True
    else:
        print("\n❌ Import test failed!")
        return False

if __name__ == "__main__":
    success = test_data_import()
    sys.exit(0 if success else 1)
