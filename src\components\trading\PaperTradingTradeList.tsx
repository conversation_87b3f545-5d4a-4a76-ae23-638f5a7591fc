import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import type { PaperTrade } from "@/hooks/usePaperTrading";
import { Trash2 } from "lucide-react";
import StockAnalysisLink from "@/components/analysis/StockAnalysisLink";
import { useAnalysis } from "@/hooks/useAnalysis";

type Props = {
  trades: PaperTrade[];
  onDelete: (tradeId: string) => void;
};

const PaperTradingTradeList: React.FC<Props> = ({ trades, onDelete }) => {
  const { openAnalysis } = useAnalysis();

  if (!trades.length)
    return (
      <Card>
        <CardHeader>
          <CardTitle>الصفقات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground text-center py-6">لا توجد صفقات بعد</div>
        </CardContent>
      </Card>
    );
  return (
    <Card>
      <CardHeader>
        <CardTitle>الصفقات</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {trades.map(trade => (
          <div
            key={trade.id}
            className="flex items-center justify-between border rounded p-2 hover:bg-gray-50 transition-colors"
          >
            <div className="flex flex-col gap-1">
              <div className="font-semibold">{trade.symbol}</div>
              <div className="text-xs">
                كمية: {trade.quantity} | دخول: {trade.entry_price} ج.م
                {trade.exit_price ? ` | خروج: ${trade.exit_price} ج.م` : ""}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(trade.id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <StockAnalysisLink 
                symbol={trade.symbol} 
                size="sm" 
                onAnalyze={openAnalysis}
                showText={false}
              />
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default PaperTradingTradeList;
