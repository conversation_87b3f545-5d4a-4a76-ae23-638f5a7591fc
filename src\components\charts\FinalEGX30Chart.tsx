import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3, Globe, Info, TrendingUp, Activity, TrendingDown, RefreshCw, AlertCircle } from 'lucide-react';
import { useEGX30Data } from '@/hooks/useEGX30Data';

const FinalEGX30Chart = () => {
  const [interval, setInterval] = useState('1D');
  const [theme, setTheme] = useState('light');
  const [showChart, setShowChart] = useState(false);
  
  // جلب البيانات الحقيقية من قاعدة البيانات
  const { data: egx30Data, isLoading, error, refetch } = useEGX30Data();
  
  // تنسيق الأرقام
  const formatNumber = (num: number) => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    } else if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toLocaleString();
  };
  
  const formatCurrency = (num: number) => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(2)} مليار جنيه`;
    } else if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)} مليون جنيه`;
    }
    return `${num.toLocaleString()} جنيه`;
  };
  
  const formatPercent = (num: number) => {
    const sign = num >= 0 ? '+' : '';
    return `${sign}${num.toFixed(2)}%`;
  };

  const intervals = [
    { value: '1D', label: 'يومي' },
    { value: '1W', label: 'أسبوعي' },
    { value: '1M', label: 'شهري' },
  ];
  return (
    <div className="w-full">
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-blue-800">
              <BarChart3 className="h-5 w-5" />
              مؤشر EGX30 - نظرة عامة على السوق المصرية
              {isLoading && <RefreshCw className="h-4 w-4 animate-spin ml-2" />}
            </div>
            <div className="flex items-center gap-2">
              {!isLoading && egx30Data && (
                <>
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">
                    <Globe className="h-3 w-3 mr-1" />
                    {egx30Data.indexValue.toFixed(2)}
                  </Badge>
                  <Badge variant={egx30Data.indexChange >= 0 ? 'default' : 'destructive'} className="text-white">
                    {formatPercent(egx30Data.indexChangePercent)}
                  </Badge>
                </>
              )}
              {error && (
                <Badge variant="destructive">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  خطأ في البيانات
                </Badge>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center p-8">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mr-2" />
              <span className="text-blue-800">جارٍ تحميل البيانات...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center mb-2">
                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                <span className="font-bold text-red-800">خطأ في تحميل البيانات</span>
              </div>
              <p className="text-red-700 text-sm mb-3">تعذر الحصول على بيانات السوق الحالية</p>
              <Button 
                onClick={() => refetch()} 
                size="sm" 
                variant="outline"
                className="text-red-700 border-red-300 hover:bg-red-100"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                إعادة المحاولة
              </Button>
            </div>
          )}

          {/* Market Summary with Real Data */}
          {!isLoading && !error && egx30Data && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-gradient-to-r from-blue-100 to-blue-200 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="font-bold text-blue-800">قيمة التداول</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-800 mb-1">{formatCurrency(egx30Data.totalTurnover)}</div>
                  <div className="text-xs text-blue-600 mb-1">الحجم: {formatNumber(egx30Data.totalVolume)} سهم</div>
                  <div className={`text-sm font-semibold ${egx30Data.volumeChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercent(egx30Data.volumeChangePercent)} من أمس
                  </div>
                </div>
                
                <div className="p-4 bg-gradient-to-r from-green-100 to-green-200 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Activity className="h-5 w-5 text-green-600 mr-2" />
                    <span className="font-bold text-green-800">عدد الصفقات</span>
                  </div>
                  <div className="text-2xl font-bold text-green-800 mb-1">{formatNumber(egx30Data.totalTrades)}</div>
                  <div className={`text-sm font-semibold ${egx30Data.tradesChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercent(egx30Data.tradesChangePercent)} من أمس
                  </div>
                </div>
                
                <div className="p-4 bg-gradient-to-r from-purple-100 to-purple-200 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    {egx30Data.trend === 'up' ? (
                      <TrendingUp className="h-5 w-5 text-purple-600 mr-2" />
                    ) : egx30Data.trend === 'down' ? (
                      <TrendingDown className="h-5 w-5 text-purple-600 mr-2" />
                    ) : (
                      <Activity className="h-5 w-5 text-purple-600 mr-2" />
                    )}
                    <span className="font-bold text-purple-800">الاتجاه العام</span>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    egx30Data.trend === 'up' ? 'text-green-600' : 
                    egx30Data.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {egx30Data.trend === 'up' ? 'صاعد' : egx30Data.trend === 'down' ? 'هابط' : 'مستقر'}
                  </div>                <div className={`text-sm font-semibold ${egx30Data.indexChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercent(egx30Data.indexChangePercent)} اليوم
                  </div>
                </div>
              </div>
              
              {/* Last Update Info */}
              <div className="text-center text-xs text-gray-500 mt-2">
                آخر تحديث: {new Date(egx30Data.lastUpdated).toLocaleString('ar-EG', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </>
          )}

          {/* Chart Controls */}
          <div className="flex flex-wrap gap-2 justify-center">
            {intervals.map((int) => (
              <Button
                key={int.value}
                variant={interval === int.value ? "default" : "outline"}
                size="sm"
                onClick={() => setInterval(int.value)}
                className="text-xs"
              >
                {int.label}
              </Button>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
              className="text-xs"
            >
              {theme === 'light' ? '🌙 ليلي' : '☀️ نهاري'}
            </Button>
            <Button
              variant={showChart ? "secondary" : "default"}
              size="sm"
              onClick={() => setShowChart(!showChart)}
              className="text-xs"
            >
              {showChart ? 'إخفاء الشارت' : 'عرض الشارت'}
            </Button>
          </div>

          {/* Chart Section */}
          {showChart ? (
            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
              <div className="h-[500px]">
                <iframe
                  src={`https://www.tradingview.com/embed-widget/advanced-chart/?symbol=EGX30&interval=${interval}&theme=${theme}&style=1&locale=ar&range=1M&timezone=Africa%2FCairo&hide_side_toolbar=false&save_image=true&calendar=true&hide_volume=false&support_host=https%3A%2F%2Fwww.tradingview.com`}
                  width="100%"
                  height="500"
                  frameBorder="0"
                  allowTransparency={true}
                  scrolling="no"
                  className="rounded-lg"
                  title="TradingView EGX30 Chart"
                />
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg p-8 text-center shadow-sm">
              <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">شارت مؤشر EGX30</h3>
                  <p className="text-gray-600 mb-4">اضغط على "عرض الشارت" لإظهار الشارت التفاعلي</p>
                  <Button 
                    onClick={() => setShowChart(true)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    عرض الشارت التفاعلي
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
            <h4 className="font-bold text-yellow-800 mb-2">كيفية الاستخدام:</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• اختر الإطار الزمني المناسب (يومي، أسبوعي، شهري)</li>
              <li>• يمكنك تغيير الثيم بين النهاري والليلي</li>
              <li>• اضغط "عرض الشارت" لإظهار الشارت التفاعلي</li>
              <li>• استخدم أدوات الرسم والتحليل المدمجة في الشارت</li>
              <li>• البيانات محدثة لحظياً من البورصة المصرية</li>
            </ul>
          </div>

          {/* Market Info */}
          <div className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200">
            <h4 className="font-bold text-indigo-800 mb-3 flex items-center">
              <Info className="h-4 w-4 mr-2" />
              معلومات مهمة عن مؤشر EGX30
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">تعريف المؤشر:</h5>
                <p className="text-indigo-600">المؤشر الرئيسي للبورصة المصرية يضم أكبر 30 شركة من حيث السيولة والنشاط</p>
              </div>
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">ساعات التداول:</h5>
                <p className="text-indigo-600">من 10:00 ص إلى 2:30 م (بتوقيت القاهرة) من الأحد إلى الخميس</p>
              </div>
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">العملة:</h5>
                <p className="text-indigo-600">الجنيه المصري (EGP) - يتأثر بسعر صرف الدولار</p>
              </div>
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">أهمية المؤشر:</h5>
                <p className="text-indigo-600">مرآة لأداء السوق المصرية ومؤشر رئيسي لاتجاهات الاستثمار</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FinalEGX30Chart;
