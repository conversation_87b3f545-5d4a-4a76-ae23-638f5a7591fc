# Database Schema Reference

This directory contains auto-generated database schema documentation for the EGX Stock AI Oracle system.

## 📄 Files

- **`DATABASE_SCHEMA_REFERENCE.md`** - Human-readable documentation with all tables and columns
- **`database_quick_reference.json`** - Quick lookup file with table/column lists  
- **`database_schema_reference.json`** - Complete schema with sample data and metadata
- **`src/types/database.ts`** - TypeScript interfaces for type-safe queries

## 🔄 Updating

To regenerate these files, run:
```bash
cd scripts/data_sync
python inspect_database_schema.py
python generate_markdown_docs.py
python copy_reference_files.py
```

## 🚀 Frontend Usage

### Import Types
```typescript
import { StocksRealtime, StocksFinancials } from './src/types/database';
```

### Common Queries
```typescript
// Get active stocks
const { data } = await supabase
  .from('stocks_realtime')
  .select('symbol, current_price, volume')
  .gt('volume', 0);

// Get stock with financials
const { data } = await supabase
  .from('stocks_realtime')
  .select(`
    *,
    stocks_financials (*)
  `)
  .eq('symbol', 'AALR')
  .single();
```

---
*Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
