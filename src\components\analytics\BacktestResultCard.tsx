 

/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

type BacktestResult = {
  id: string;
  strategy_name: string;
  symbol: string;
  timeframe: string;
  status: string;
  started_at: string;
  finished_at: string | null;
  parameters: any;
  result_summary: null | {
    total_return?: number;
    win_rate?: number;
    max_drawdown?: number;
    sharpe_ratio?: number;
    total_trades?: number;
    winning_trades?: number;
    losing_trades?: number;
    avg_win?: number;
    avg_loss?: number;
  };
};

const getReturnColor = (value?: number) => {
  if (value === undefined || value === null) return 'text-gray-600';
  if (value > 0) return 'text-green-600';
  if (value < 0) return 'text-red-600';
  return 'text-gray-600';
};

export default function BacktestResultCard({ result }: { result: BacktestResult }) {
  const s = result.result_summary || {};

  return (
    <div className="p-4 bg-white/80 rounded-lg border border-emerald-200">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="font-bold text-lg">{result.strategy_name}</h3>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>{result.symbol}</span>
            <span>•</span>
            <span>{result.timeframe}</span>
            <span>•</span>
            <span>{s.total_trades ?? '--'} صفقة</span>
          </div>
        </div>
        <Badge className={`
          ${s.total_return && +s.total_return > 0 ? 'bg-green-500' : 'bg-red-500'}
          text-white
        `}>
          {s.total_return !== undefined 
            ? `${s.total_return > 0 ? '+' : ''}${Number(s.total_return).toFixed(1)}%`
            : '--'}
        </Badge>
      </div>
      {/* Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-sm text-muted-foreground">معدل النجاح</div>
          <div className="font-bold text-lg">{s.win_rate !== undefined ? Number(s.win_rate).toFixed(0) + '%' : '--'}</div>
          <Progress value={s.win_rate ?? 0} className="h-2 mt-1" />
        </div>
        <div className="text-center">
          <div className="text-sm text-muted-foreground">أقصى انخفاض</div>
          <div className="font-bold text-lg text-red-600">
            {s.max_drawdown !== undefined ? Number(s.max_drawdown).toFixed(1) + '%' : '--'}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-muted-foreground">نسبة شارب</div>
          <div className={`font-bold text-lg ${getReturnColor(s.sharpe_ratio)}`}>
            {s.sharpe_ratio !== undefined ? Number(s.sharpe_ratio).toFixed(2) : '--'}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-muted-foreground">متوسط الربح/الخسارة</div>
          <div className="text-sm">
            <span className="text-green-600">
              {s.avg_win !== undefined ? `+${Number(s.avg_win).toFixed(1)}%` : '--'}
            </span>
            {' / '}
            <span className="text-red-600">
              {s.avg_loss !== undefined ? `${Number(s.avg_loss).toFixed(1)}%` : '--'}
            </span>
          </div>
        </div>
      </div>
      {/* Trade Summary */}
      <div className="mt-4 p-3 bg-emerald-50 rounded border">
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-muted-foreground">صفقات رابحة</div>
            <div className="font-medium text-green-600">{s.winning_trades ?? '--'}</div>
          </div>
          <div className="text-center">
            <div className="text-muted-foreground">صفقات خاسرة</div>
            <div className="font-medium text-red-600">{s.losing_trades ?? '--'}</div>
          </div>
          <div className="text-center">
            <div className="text-muted-foreground">إجمالي الصفقات</div>
            <div className="font-medium">{s.total_trades ?? '--'}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
