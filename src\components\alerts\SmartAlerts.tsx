import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bell, 
  TrendingUp, 
  TrendingDown, 
  Target,
  AlertTriangle,
  Zap,
  Volume2,
  DollarSign,
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Trash2,
  Settings,
  Sparkles
} from 'lucide-react';
import { useEnhancedMarketData, EnhancedStock } from '@/hooks/useEnhancedMarketData';

interface Alert {
  id: string;
  type: 'price' | 'volume' | 'technical' | 'news' | 'ai';
  symbol: string;
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  isRead: boolean;
  action?: 'buy' | 'sell' | 'hold' | 'watch';
  triggerValue?: number;
  currentValue?: number;
}

interface AlertRule {
  id: string;
  name: string;
  type: 'price_above' | 'price_below' | 'volume_spike' | 'technical_breakout' | 'ai_signal';
  symbol: string;
  condition: {
    value?: number;
    operator?: string;
    threshold?: number;
  };
  isActive: boolean;
  lastTriggered?: Date;
}

const SmartAlerts = () => {
  const { data: marketData } = useEnhancedMarketData();
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
  const [activeTab, setActiveTab] = useState('alerts');

  // Generate smart alerts based on market data
  useEffect(() => {
    if (!marketData?.stocks) return;

    const newAlerts: Alert[] = [];

    marketData.stocks.forEach(stock => {
      const price = stock.current_price || 0;
      const change = stock.change_percent || 0;
      const volume = stock.volume || 0;
      const ma5 = stock.ma5 || 0;
      const ma20 = stock.ma20 || 0;
      const ma50 = stock.ma50 || 0;

      // Price movement alerts
      if (Math.abs(change) > 5) {
        newAlerts.push({
          id: `price_${stock.symbol}_${Date.now()}`,
          type: 'price',
          symbol: stock.symbol,
          title: change > 0 ? 'ارتفاع كبير في السعر' : 'انخفاض كبير في السعر',
          message: `${stock.symbol} ${change > 0 ? 'ارتفع' : 'انخفض'} بنسبة ${Math.abs(change).toFixed(2)}%`,
          severity: Math.abs(change) > 10 ? 'critical' : Math.abs(change) > 7 ? 'high' : 'medium',
          timestamp: new Date(),
          isRead: false,
          action: change > 5 ? 'sell' : 'buy',
          triggerValue: Math.abs(change),
          currentValue: price
        });
      }

      // Volume spike alerts
      const avgVolume = (stock.avg_net_volume_3d || 0) + (stock.avg_net_volume_5d || 0) / 2;
      if (volume > avgVolume * 2 && avgVolume > 0) {
        newAlerts.push({
          id: `volume_${stock.symbol}_${Date.now()}`,
          type: 'volume',
          symbol: stock.symbol,
          title: 'نشاط تداول مرتفع',
          message: `${stock.symbol} يشهد حجم تداول أعلى من المعتاد بـ ${((volume / avgVolume) * 100).toFixed(0)}%`,
          severity: 'medium',
          timestamp: new Date(),
          isRead: false,
          action: 'watch',
          triggerValue: volume / avgVolume,
          currentValue: volume
        });
      }

      // Technical breakout alerts
      if (ma5 > 0 && ma20 > 0 && price > ma20 && ma5 > ma20) {
        newAlerts.push({
          id: `technical_${stock.symbol}_${Date.now()}`,
          type: 'technical',
          symbol: stock.symbol,
          title: 'اختراق فني صاعد',
          message: `${stock.symbol} اخترق المقاومة مع تقاطع المتوسطات المتحركة`,
          severity: 'high',
          timestamp: new Date(),
          isRead: false,
          action: 'buy',
          triggerValue: price,
          currentValue: ma20
        });
      }

      // Oversold/Overbought alerts
      if (ma5 > 0 && price < ma5 * 0.92) {
        newAlerts.push({
          id: `oversold_${stock.symbol}_${Date.now()}`,
          type: 'technical',
          symbol: stock.symbol,
          title: 'منطقة تشبع بيعي',
          message: `${stock.symbol} في منطقة تشبع بيعي - فرصة شراء محتملة`,
          severity: 'medium',
          timestamp: new Date(),
          isRead: false,
          action: 'buy',
          triggerValue: (price / ma5) * 100,
          currentValue: price
        });
      }

      if (ma5 > 0 && price > ma5 * 1.08) {
        newAlerts.push({
          id: `overbought_${stock.symbol}_${Date.now()}`,
          type: 'technical',
          symbol: stock.symbol,
          title: 'منطقة تشبع شرائي',
          message: `${stock.symbol} في منطقة تشبع شرائي - احذر من التصحيح`,
          severity: 'medium',
          timestamp: new Date(),
          isRead: false,
          action: 'sell',
          triggerValue: (price / ma5) * 100,
          currentValue: price
        });
      }

      // Strong trend alerts
      if (ma5 > ma20 && ma20 > ma50 && ma5 > 0 && ma20 > 0 && ma50 > 0) {
        newAlerts.push({
          id: `trend_${stock.symbol}_${Date.now()}`,
          type: 'technical',
          symbol: stock.symbol,
          title: 'اتجاه صاعد قوي',
          message: `${stock.symbol} في اتجاه صاعد قوي مع ترتيب المتوسطات`,
          severity: 'high',
          timestamp: new Date(),
          isRead: false,
          action: 'hold',
          triggerValue: ma5,
          currentValue: price
        });
      }

      // AI-powered alerts based on multiple indicators
      const aiScore = calculateAIScore(stock);
      if (aiScore > 80) {
        newAlerts.push({
          id: `ai_${stock.symbol}_${Date.now()}`,
          type: 'ai',
          symbol: stock.symbol,
          title: 'توصية الذكاء الاصطناعي',
          message: `الذكاء الاصطناعي يوصي بـ${stock.symbol} بناءً على تحليل شامل للمؤشرات`,
          severity: 'high',
          timestamp: new Date(),
          isRead: false,
          action: 'buy',
          triggerValue: aiScore,
          currentValue: price
        });
      } else if (aiScore < 20) {
        newAlerts.push({
          id: `ai_sell_${stock.symbol}_${Date.now()}`,
          type: 'ai',
          symbol: stock.symbol,
          title: 'تحذير الذكاء الاصطناعي',
          message: `الذكاء الاصطناعي يحذر من ${stock.symbol} بناءً على المؤشرات السلبية`,
          severity: 'critical',
          timestamp: new Date(),
          isRead: false,
          action: 'sell',
          triggerValue: aiScore,
          currentValue: price
        });
      }
    });

    // Limit to most recent 50 alerts to avoid overwhelming
    setAlerts(newAlerts.slice(0, 50));
  }, [marketData]);

  const calculateAIScore = (stock: EnhancedStock): number => {
    let score = 50; // Base score

    // Technical indicators
    const price = stock.current_price || 0;
    const ma5 = stock.ma5 || 0;
    const ma20 = stock.ma20 || 0;
    const ma50 = stock.ma50 || 0;
    const change = stock.change_percent || 0;
    const volume = stock.volume || 0;
    const avgVolume = Math.max(stock.avg_net_volume_3d || 0, stock.avg_net_volume_5d || 0);

    // Price vs moving averages
    if (ma5 > 0 && price > ma5) score += 10;
    if (ma20 > 0 && price > ma20) score += 15;
    if (ma50 > 0 && price > ma50) score += 10;
    
    // Moving average alignment
    if (ma5 > ma20 && ma20 > ma50 && ma5 > 0 && ma20 > 0 && ma50 > 0) score += 20;
    
    // Performance
    if (change > 0) score += Math.min(15, change * 2);
    else score += Math.max(-15, change * 2);
    
    // Volume
    if (avgVolume > 0 && volume > avgVolume * 1.5) score += 10;
    
    // Fundamental factors
    const pe = stock.pe_ratio || 0;
    const dividendYield = stock.dividend_yield || 0;
    
    if (pe > 0 && pe < 15) score += 10;
    if (pe > 25) score -= 5;
    if (dividendYield > 5) score += 5;

    return Math.max(0, Math.min(100, score));
  };

  const getSeverityColor = (severity: Alert['severity']) => {
    switch (severity) {
      case 'critical': return 'bg-red-600 text-white';
      case 'high': return 'bg-orange-600 text-white';
      case 'medium': return 'bg-yellow-600 text-white';
      case 'low': return 'bg-blue-600 text-white';
      default: return 'bg-gray-600 text-white';
    }
  };

  const getActionColor = (action: Alert['action']) => {
    switch (action) {
      case 'buy': return 'text-green-700 bg-green-100';
      case 'sell': return 'text-red-700 bg-red-100';
      case 'hold': return 'text-blue-700 bg-blue-100';
      case 'watch': return 'text-purple-700 bg-purple-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getActionIcon = (action: Alert['action']) => {
    switch (action) {
      case 'buy': return TrendingUp;
      case 'sell': return TrendingDown;
      case 'hold': return Target;
      case 'watch': return Activity;
      default: return Bell;
    }
  };

  const getTypeIcon = (type: Alert['type']) => {
    switch (type) {
      case 'price': return DollarSign;
      case 'volume': return Volume2;
      case 'technical': return Activity;
      case 'ai': return Sparkles;
      default: return Bell;
    }
  };

  const markAsRead = (alertId: string) => {
    setAlerts(alerts.map(alert => 
      alert.id === alertId ? { ...alert, isRead: true } : alert
    ));
  };

  const deleteAlert = (alertId: string) => {
    setAlerts(alerts.filter(alert => alert.id !== alertId));
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('ar-EG', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);
  };

  const unreadCount = alerts.filter(alert => !alert.isRead).length;
  const criticalCount = alerts.filter(alert => alert.severity === 'critical').length;
  const highCount = alerts.filter(alert => alert.severity === 'high').length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <h2 className="text-2xl font-bold text-gray-900">التنبيهات الذكية</h2>
          <Badge variant="secondary" className="text-lg px-4 py-2">
            {unreadCount} غير مقروء
          </Badge>
        </div>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          إعدادات التنبيهات
        </Button>
      </div>

      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-red-600 to-red-700 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-100 text-sm">حرجة</p>
                <p className="text-2xl font-bold">{criticalCount}</p>
              </div>
              <AlertTriangle className="h-6 w-6 text-red-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-600 to-orange-700 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">مهمة</p>
                <p className="text-2xl font-bold">{highCount}</p>
              </div>
              <Zap className="h-6 w-6 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-600 to-blue-700 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">إجمالي</p>
                <p className="text-2xl font-bold">{alerts.length}</p>
              </div>
              <Bell className="h-6 w-6 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-600 to-green-700 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">مقروءة</p>
                <p className="text-2xl font-bold">{alerts.length - unreadCount}</p>
              </div>
              <CheckCircle className="h-6 w-6 text-green-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="alerts">التنبيهات الحالية</TabsTrigger>
          <TabsTrigger value="rules">قواعد التنبيه</TabsTrigger>
          <TabsTrigger value="ai">توصيات الذكاء الاصطناعي</TabsTrigger>
        </TabsList>

        <TabsContent value="alerts" className="space-y-4">
          {alerts.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد تنبيهات جديدة</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {alerts.map((alert) => {
                const TypeIcon = getTypeIcon(alert.type);
                const ActionIcon = getActionIcon(alert.action);
                
                return (
                  <Card key={alert.id} className={`transition-all hover:shadow-md ${!alert.isRead ? 'border-l-4 border-l-blue-500' : ''}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 space-x-reverse flex-1">
                          <div className="p-2 bg-gray-100 rounded-full">
                            <TypeIcon className="h-4 w-4 text-gray-600" />
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 space-x-reverse mb-1">
                              <h3 className="font-semibold text-gray-900">{alert.title}</h3>
                              <Badge className={getSeverityColor(alert.severity)} variant="secondary">
                                {alert.severity === 'critical' ? 'حرج' : 
                                 alert.severity === 'high' ? 'مهم' :
                                 alert.severity === 'medium' ? 'متوسط' : 'منخفض'}
                              </Badge>
                              <Badge variant="outline">{alert.symbol}</Badge>
                            </div>
                            
                            <p className="text-gray-700 text-sm mb-2">{alert.message}</p>
                            
                            <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                              <span className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {formatTime(alert.timestamp)}
                              </span>
                              
                              {alert.action && (
                                <Badge className={getActionColor(alert.action)} variant="secondary">
                                  <ActionIcon className="h-3 w-3 mr-1" />
                                  {alert.action === 'buy' ? 'شراء' :
                                   alert.action === 'sell' ? 'بيع' :
                                   alert.action === 'hold' ? 'احتفاظ' : 'متابعة'}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 space-x-reverse">
                          {!alert.isRead && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsRead(alert.id)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteAlert(alert.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        <TabsContent value="rules" className="space-y-4">
          <Card>
            <CardContent className="p-8 text-center">
              <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">إعداد قواعد التنبيه المخصصة</p>
              <Button>
                <Bell className="h-4 w-4 mr-2" />
                إضافة قاعدة جديدة
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai" className="space-y-4">
          <div className="space-y-4">
            {alerts.filter(alert => alert.type === 'ai').map((alert) => (
              <Card key={alert.id} className="border-2 border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <div className="p-2 bg-purple-600 rounded-full">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <h3 className="font-bold text-gray-900">{alert.title}</h3>
                        <Badge variant="outline">{alert.symbol}</Badge>
                        <Badge className="bg-purple-600 text-white">
                          AI Score: {alert.triggerValue?.toFixed(0)}
                        </Badge>
                      </div>
                      
                      <p className="text-gray-700 mb-3">{alert.message}</p>
                      
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                          <Sparkles className="h-4 w-4 mr-1" />
                          تفاصيل التحليل
                        </Button>
                        <Button variant="outline" size="sm">
                          إضافة للمتابعة
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SmartAlerts;
