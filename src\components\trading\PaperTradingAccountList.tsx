
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import type { PaperTradingAccount } from "@/hooks/usePaperTrading";

type Props = {
  accounts: PaperTradingAccount[];
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
  selectedId: string | null;
};

const PaperTradingAccountList: React.FC<Props> = ({
  accounts, onSelect, onDelete, selectedId
}) => (
  <Card>
    <CardHeader>
      <CardTitle>حسابات التداول التجريبي</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="flex flex-wrap gap-2">
        {accounts.map(acc => (
          <Button
            key={acc.id}
            variant={acc.id === selectedId ? "default" : "outline"}
            onClick={() => onSelect(acc.id)}
            className={`${acc.id === selectedId ? "bg-indigo-600 text-white" : ""} relative min-w-28`}
          >
            <span>{acc.account_name}</span>
            <span className="text-xs text-gray-400 ml-1">
              {acc.current_balance?.toLocaleString()} ج.م
            </span>
            <Trash2
              className="ml-2 h-3 w-3 cursor-pointer text-red-500 hover:text-red-600 absolute right-2 top-2"
              onClick={e => {
                e.stopPropagation();
                onDelete(acc.id);
              }}
            />
          </Button>
        ))}
        {accounts.length === 0 && (
          <span className="text-muted-foreground text-sm">لا يوجد حسابات بعد</span>
        )}
      </div>
    </CardContent>
  </Card>
);

export default PaperTradingAccountList;
