#!/bin/bash
# Database Cleanup Script
# This script clears test data before receiving live trading signals

echo "🧹 تنظيف قاعدة البيانات من البيانات التجريبية..."
echo "================================================"

# Database connection parameters
DB_HOST="localhost"
DB_USER="postgres"
DB_NAME="egx_stock_oracle"
export PGPASSWORD="egx123"

echo "🗑️ محو البيانات التجريبية..."

# Clear trading signals test data
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
DELETE FROM trading_signals 
WHERE created_at > NOW() - INTERVAL '1 day' 
   OR stock_code IN ('ADVNCD', 'TRAIL', 'REGULAR', 'TEST');
"

# Clear live recommendations test data
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
DELETE FROM live_recommendations 
WHERE created_at > NOW() - INTERVAL '1 day' 
   OR stock_code IN ('ADVNCD', 'TRAIL', 'REGULAR', 'TEST');
"

# Clear performance test data
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
DELETE FROM signal_performance 
WHERE created_at > NOW() - INTERVAL '1 day' 
   OR stock_code IN ('ADVNCD', 'TRAIL', 'REGULAR', 'TEST');
"

# Reset sequences
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT setval('trading_signals_id_seq', COALESCE((SELECT MAX(id) FROM trading_signals), 1));
SELECT setval('live_recommendations_id_seq', COALESCE((SELECT MAX(id) FROM live_recommendations), 1));
SELECT setval('signal_performance_id_seq', COALESCE((SELECT MAX(id) FROM signal_performance), 1));
"

echo "✅ تم تنظيف قاعدة البيانات بنجاح!"
echo ""
echo "📊 الإحصائيات الحالية:"

# Show current counts
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT 
    'Trading Signals' as table_name,
    COUNT(*) as record_count
FROM trading_signals
UNION ALL
SELECT 
    'Live Recommendations' as table_name,
    COUNT(*) as record_count
FROM live_recommendations
UNION ALL
SELECT 
    'Signal Performance' as table_name,
    COUNT(*) as record_count
FROM signal_performance;
"

echo ""
echo "🚀 النظام جاهز لاستقبال إشارات التداول الفعلية!"
echo "================================================"
