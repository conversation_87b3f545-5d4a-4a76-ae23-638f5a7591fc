import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Search, 
  RefreshCw,
  AlertTriangle,
  Loader2,
  Settings,
  Database
} from 'lucide-react';
import { useSimpleLiveData } from '@/hooks/useSimpleLiveData';
import DatabaseDiagnostics from '@/components/debug/DatabaseDiagnostics';

const SimpleLiveTradingScreen: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const { data: stocks, isLoading, error, refetch } = useSimpleLiveData();

  console.log('🔍 Component state:', { 
    stocksCount: stocks?.length, 
    isLoading, 
    hasError: !!error,
    errorMessage: error?.message 
  });

  // تصفية البيانات حسب البحث
  const filteredStocks = stocks?.filter(stock => 
    stock.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // معالجة حالة التحميل
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>جاري تحميل البيانات اللحظية...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // معالجة حالة الخطأ
  if (error) {
    return (
      <Card className="w-full border-red-200">
        <CardContent className="p-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-bold text-red-700 mb-2">خطأ في تحميل البيانات</h3>
            <p className="text-red-600 mb-4">{error.message}</p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // معالجة حالة عدم وجود بيانات
  if (!stocks || stocks.length === 0) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-bold text-gray-700 mb-2">لا توجد بيانات</h3>
            <p className="text-gray-600 mb-4">لم يتم العثور على بيانات أسهم في قاعدة البيانات</p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              تحديث
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const gainers = filteredStocks.filter(s => s.change_percent > 0);
  const losers = filteredStocks.filter(s => s.change_percent < 0);

  return (
    <div className="space-y-6">
      {/* الإحصائيات السريعة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              الشاشة اللحظية - البورصة المصرية
            </div>
            <Button onClick={() => refetch()} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-1" />
              تحديث
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{filteredStocks.length}</div>
              <div className="text-xs text-gray-600">إجمالي الأسهم</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{gainers.length}</div>
              <div className="text-xs text-gray-600">أسهم صاعدة</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{losers.length}</div>
              <div className="text-xs text-gray-600">أسهم هابطة</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* البحث */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="ابحث عن سهم..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* جدول الأسهم */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الأسهم ({filteredStocks.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredStocks.map((stock) => (
              <div 
                key={stock.symbol}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex-1">
                  <div className="font-bold">{stock.symbol}</div>
                  <div className="text-sm text-gray-600">{stock.name}</div>
                </div>
                
                <div className="text-center">
                  <div className="font-bold">
                    {stock.current_price?.toFixed(2) || '0.00'}
                  </div>
                  <div className="text-xs text-gray-500">السعر</div>
                </div>
                
                <div className="text-center">
                  <div className={`font-bold ${
                    (stock.change_percent || 0) > 0 ? 'text-green-600' : 
                    (stock.change_percent || 0) < 0 ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {stock.change_percent > 0 ? '+' : ''}{stock.change_percent?.toFixed(2) || '0.00'}%
                  </div>
                  <div className="text-xs text-gray-500">التغيير</div>
                </div>
                
                <div className="text-center">
                  <div className="font-medium">
                    {stock.volume?.toLocaleString() || '0'}
                  </div>
                  <div className="text-xs text-gray-500">الحجم</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleLiveTradingScreen;
