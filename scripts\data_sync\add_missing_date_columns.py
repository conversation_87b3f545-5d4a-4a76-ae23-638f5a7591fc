#!/usr/bin/env python3
"""
Add missing date/time columns to stocks_realtime table
"""

import os
import sys
import logging
from supabase import create_client, Client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def add_missing_columns():
    """Add missing last_trade_date and last_trade_time columns"""
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_ANON_KEY")
    
    if not url or not key:
        logging.error("Missing SUPABASE_URL or SUPABASE_ANON_KEY environment variables")
        return False
    
    supabase: Client = create_client(url, key)
    
    # SQL to add missing columns
    missing_columns_sql = [
        "ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS last_trade_date DATE;",
        "ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS last_trade_time TIME;"
    ]
    
    logging.info("🔧 Adding missing date/time columns to stocks_realtime...")
    
    try:
        for sql in missing_columns_sql:
            logging.info(f"Executing: {sql}")
            result = supabase.rpc('execute_sql', {'sql': sql}).execute()
            if result.data:
                logging.info(f"✅ Success: {sql}")
            else:
                logging.warning(f"⚠️  No data returned for: {sql}")
        
        # Verify columns were added
        logging.info("🔍 Verifying columns were added...")
        result = supabase.table('stocks_realtime').select('*').limit(1).execute()
        
        if result.data:
            sample_record = result.data[0]
            has_date = 'last_trade_date' in sample_record
            has_time = 'last_trade_time' in sample_record
            
            logging.info(f"✅ last_trade_date column exists: {has_date}")
            logging.info(f"✅ last_trade_time column exists: {has_time}")
            
            if has_date and has_time:
                logging.info("🎉 All missing columns added successfully!")
                return True
            else:
                logging.error("❌ Some columns still missing")
                return False
        else:
            logging.error("❌ Could not verify - no data in table")
            return False
            
    except Exception as e:
        logging.error(f"❌ Error adding columns: {str(e)}")
        return False

def create_manual_sql():
    """Create manual SQL file for adding missing columns"""
    
    sql_content = """-- Add missing date/time columns to stocks_realtime table
-- Execute this in Supabase SQL editor if the Python script fails

ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS last_trade_date DATE;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS last_trade_time TIME;

-- Verify columns were added
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'stocks_realtime' 
AND column_name IN ('last_trade_date', 'last_trade_time')
ORDER BY column_name;
"""
    
    with open('add_missing_date_columns.sql', 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    logging.info("💾 Manual SQL script saved to: add_missing_date_columns.sql")

if __name__ == "__main__":
    logging.info("🚀 Adding missing date/time columns...")
    logging.info("=" * 60)
    
    # Create manual SQL file as backup
    create_manual_sql()
    
    # Try to add columns programmatically
    success = add_missing_columns()
    
    if success:
        logging.info("✅ Column addition completed successfully!")
        logging.info("🔄 You can now re-run the enhanced real-time import:")
        logging.info("   python load_realtime_enhanced.py")
    else:
        logging.error("❌ Column addition failed!")
        logging.info("📝 Please execute 'add_missing_date_columns.sql' in Supabase SQL editor manually")
    
    logging.info("=" * 60)
