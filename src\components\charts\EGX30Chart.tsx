import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, Activity, BarChart3, Globe, ExternalLink, Info, AlertTriangle, RefreshCw } from 'lucide-react';

// Custom TradingView Embed Component
const TradingViewEmbed = ({ symbol, theme, interval }: { symbol: string; theme: string; interval: string }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [symbol, interval]);

  const embedUrl = `https://www.tradingview.com/embed-widget/advanced-chart/?symbol=${symbol}&interval=${interval}&theme=${theme}&style=1&locale=ar&range=1M&timezone=Africa%2FCairo&hide_side_toolbar=false&save_image=true&calendar=true&hide_volume=false&support_host=https%3A%2F%2Fwww.tradingview.com`;

  if (hasError) {
    return (
      <div className="h-[500px] flex items-center justify-center bg-gray-50 rounded-lg">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-amber-500 mx-auto mb-4" />
          <p className="text-gray-700 font-semibold mb-2">تعذر تحميل الشارت</p>
          <p className="text-gray-500 text-sm mb-4">يرجى التحقق من الاتصال بالإنترنت</p>
          <Button 
            onClick={() => setHasError(false)} 
            variant="outline" 
            size="sm"
            className="mb-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            إعادة المحاولة
          </Button>
          <div className="flex gap-2 justify-center">
            <Button 
              onClick={() => window.open(`https://www.tradingview.com/chart/?symbol=${symbol}`, '_blank')} 
              variant="link" 
              size="sm"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              فتح في TradingView
            </Button>
            <Button 
              onClick={() => window.open('https://www.egx.com.eg/ar/indices.aspx', '_blank')} 
              variant="link" 
              size="sm"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              موقع البورصة
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-[500px] bg-white rounded-lg overflow-hidden">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الشارت...</p>
          </div>
        </div>
      )}
      <iframe
        src={embedUrl}
        className="w-full h-full border-0"
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setIsLoading(false);
          setHasError(true);
        }}
        title={`TradingView Chart - ${symbol}`}
      />
    </div>
  );
};

const EGX30Chart = () => {
  const [interval, setInterval] = useState<'1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1D' | '1W' | '1M'>('1D');
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Error boundary for the entire component
  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('EGX30Chart Error:', error);
      setHasError(true);
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return (
      <Card className="border-2 border-red-200 bg-gradient-to-br from-red-50 to-pink-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            خطأ في تحميل شارت EGX30
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-red-600">حدث خطأ في تحميل الشارت. يرجى إعادة تحميل الصفحة.</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={() => window.location.reload()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              إعادة تحميل الصفحة
            </Button>
            <Button 
              onClick={() => window.open('https://www.egx.com.eg/ar/indices.aspx', '_blank')} 
              variant="link"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              موقع البورصة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const intervals = [
    { value: '1D' as const, label: 'يومي' },
    { value: '1W' as const, label: 'أسبوعي' },
    { value: '1M' as const, label: 'شهري' },
    { value: '4h' as const, label: '4 ساعات' },
    { value: '1h' as const, label: 'ساعة' },
    { value: '30m' as const, label: '30 دقيقة' },
  ];
  const marketIndicators = [
    {
      name: 'حجم التداول',
      description: 'إجمالي قيمة الأسهم المتداولة',
      icon: BarChart3,
      bgColor: 'bg-gradient-to-r from-blue-100 to-blue-200',
      textColor: 'text-blue-800',
      iconColor: 'text-blue-600',
      descColor: 'text-blue-700'
    },
    {
      name: 'عدد الصفقات',
      description: 'العدد الإجمالي للمعاملات المنفذة',
      icon: Activity,
      bgColor: 'bg-gradient-to-r from-green-100 to-green-200',
      textColor: 'text-green-800',
      iconColor: 'text-green-600',
      descColor: 'text-green-700'
    },
    {
      name: 'الاتجاه العام',
      description: 'اتجاه السوق العام للجلسة',
      icon: TrendingUp,
      bgColor: 'bg-gradient-to-r from-purple-100 to-purple-200',
      textColor: 'text-purple-800',
      iconColor: 'text-purple-600',
      descColor: 'text-purple-700'
    }
  ];

  return (
    <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-blue-800">
            <BarChart3 className="h-5 w-5" />
            مؤشر EGX30 - نظرة عامة على السوق المصرية
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-blue-100 text-blue-800">
              <Globe className="h-3 w-3 mr-1" />
              البورصة المصرية
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open('https://www.egx.com.eg/ar/indices.aspx', '_blank')}
              className="text-blue-600 hover:text-blue-800"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              EGX
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">        {/* Market Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {marketIndicators.map((indicator, index) => (
            <div key={index} className={`p-4 ${indicator.bgColor} rounded-lg`}>
              <div className="flex items-center justify-center mb-2">
                <indicator.icon className={`h-5 w-5 ${indicator.iconColor} mr-2`} />
                <span className={`font-bold ${indicator.textColor}`}>{indicator.name}</span>
              </div>
              <div className={`text-sm ${indicator.descColor} text-center`}>
                {indicator.description}
              </div>
            </div>
          ))}
        </div>

        {/* Interval Selection */}
        <div className="flex flex-wrap gap-2 justify-center">
          {intervals.map((int) => (
            <Button
              key={int.value}
              variant={interval === int.value ? "default" : "outline"}
              size="sm"
              onClick={() => setInterval(int.value)}
              className="text-xs"
            >
              {int.label}
            </Button>
          ))}
        </div>

        {/* Theme and Advanced Options */}
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            className="text-xs"
          >
            {theme === 'light' ? '🌙 ليلي' : '☀️ نهاري'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-xs"
          >
            <Info className="h-3 w-3 mr-1" />
            {showAdvanced ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
          </Button>
        </div>        {/* TradingView Chart */}
        <div className="bg-white rounded-lg overflow-hidden shadow-sm">
          <TradingViewEmbed 
            symbol="EGX30" 
            theme={theme} 
            interval={interval} 
          />
        </div>

        {/* Advanced Information */}
        {showAdvanced && (
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200">
              <h4 className="font-bold text-indigo-800 mb-3 flex items-center">
                <Info className="h-4 w-4 mr-2" />
                معلومات متقدمة عن مؤشر EGX30
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">تعريف المؤشر:</h5>
                  <p className="text-indigo-600">
                    مؤشر EGX30 هو المؤشر الرئيسي للبورصة المصرية ويضم أكبر 30 شركة من حيث السيولة والنشاط.
                  </p>
                </div>
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">أهمية المؤشر:</h5>
                  <p className="text-indigo-600">
                    يُعتبر مرآة لأداء السوق المصرية ومؤشر رئيسي لاتجاهات الاستثمار.
                  </p>
                </div>
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">ساعات التداول:</h5>
                  <p className="text-indigo-600">
                    من 10:00 ص إلى 2:30 م (بتوقيت القاهرة) من الأحد إلى الخميس.
                  </p>
                </div>
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">العملة:</h5>
                  <p className="text-indigo-600">
                    الجنيه المصري (EGP) - يتأثر بسعر صرف الدولار.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
          <h4 className="font-bold text-yellow-800 mb-2">إرشادات الاستخدام:</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• اختر الإطار الزمني المناسب من الأزرار أعلاه</li>
            <li>• استخدم أدوات الرسم والتحليل المدمجة في الشارت</li>
            <li>• يمكنك تغيير الثيم بين النهاري والليلي</li>
            <li>• الشارت يحتوي على مؤشرات فنية مثل RSI و MACD</li>
            <li>• الضغط على الشارت يفتح نافذة مكبرة للتحليل التفصيلي</li>
            <li>• اضغط على "عرض التفاصيل" لمعرفة المزيد عن المؤشر</li>
          </ul>
        </div>

        {/* Fallback Message */}
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="text-sm text-blue-800 text-center">
            <strong>ملاحظة:</strong> إذا لم يظهر الشارت، يمكنك زيارة 
            <a 
              href="https://www.tradingview.com/chart/?symbol=EGX30" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline mx-1"
            >
              TradingView مباشرة
            </a>
            أو 
            <a 
              href="https://www.egx.com.eg/ar/indices.aspx" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline mx-1"
            >
              موقع البورصة المصرية            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EGX30Chart;
