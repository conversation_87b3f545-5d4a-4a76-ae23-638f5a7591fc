#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 إصدار محسن - استيراد البيانات الحية والمالية
Enhanced Realtime & Financial Data Importer

📅 Created: 2025-06-16
🎯 Purpose: استيراد محسن للبيانات الحية والمالية مع معالجة أفضل للأخطاء
"""

import pandas as pd
import logging
import sys
from datetime import datetime
import subprocess
import re
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EnhancedDataImporter:
    """مستورد محسن للبيانات الحية والمالية"""
    
    def __init__(self):
        self.excel_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx"
        self.csv_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv"
        self.stats = {
            'realtime_processed': 0,
            'realtime_success': 0,
            'financial_processed': 0,
            'financial_success': 0
        }
    
    def clean_symbol(self, symbol_str):
        """تنظيف رمز السهم"""
        if pd.isna(symbol_str):
            return None
        
        symbol = str(symbol_str).strip().upper()
        # إزالة الأحرف غير المرغوب فيها
        symbol = re.sub(r'[^A-Z0-9]', '', symbol)
        
        # تحديد الحد الأقصى لطول الرمز
        if len(symbol) > 15:
            symbol = symbol[:15]
        
        return symbol if symbol and symbol != 'NAN' else None
    
    def safe_numeric_convert(self, value, max_value=9999.9999):
        """تحويل آمن للقيم الرقمية مع حد أقصى"""
        try:
            if pd.isna(value) or value == '' or str(value).upper() in ['NAN', 'NULL', 'NONE']:
                return None
            
            # تحويل إلى float
            num_value = float(str(value).replace(',', '').replace('%', ''))
            
            # تطبيق الحد الأقصى
            if abs(num_value) > max_value:
                logger.warning(f"Value {num_value} exceeds maximum {max_value}, capping it")
                return max_value if num_value > 0 else -max_value
            
            return num_value
            
        except (ValueError, TypeError):
            return None
    
    def execute_sql_command(self, sql_command, description):
        """تنفيذ أمر SQL باستخدام psql"""
        try:
            cmd = [
                'sudo', '-u', 'postgres', 'psql', 
                '-d', 'egx_stock_oracle', 
                '-c', sql_command
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"✅ {description}")
            return True, result.stdout
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ SQL Error in {description}: {e.stderr}")
            return False, e.stderr
    
    def insert_stock_master_record(self, symbol, name_ar):
        """إدراج سهم في جدول stocks_master"""
        sql = f"""
        INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
        VALUES ('{symbol}', '{name_ar}', '{symbol}', true)
        ON CONFLICT (symbol) DO NOTHING;
        """
        return self.execute_sql_command(sql, f"Insert stock {symbol}")
    
    def insert_realtime_record(self, data):
        """إدراج سجل البيانات الحية"""
        # تحضير البيانات
        symbol = self.clean_symbol(data.get('الرمز'))
        if not symbol:
            return False, "Invalid symbol"
        
        # تحويل البيانات الرقمية بأمان
        open_price = self.safe_numeric_convert(data.get('إفتتاح'))
        high_price = self.safe_numeric_convert(data.get('أعلى'))
        low_price = self.safe_numeric_convert(data.get('أدنى'))
        current_price = self.safe_numeric_convert(data.get('الاغلاق'))
        change_percent = self.safe_numeric_convert(data.get('التغير %'), 999.99)
        
        volume = self.safe_numeric_convert(data.get('الحجم'), 999999999)
        turnover = self.safe_numeric_convert(data.get('القيمة'), 999999999)
        trades_count = self.safe_numeric_convert(data.get('الصفقات'), 999999)
        
        # نسب وأهداف
        liquidity_ratio = self.safe_numeric_convert(data.get('نسبة السيولة %'), 999.99)
        target_1 = self.safe_numeric_convert(data.get('هدف 1 (يوم)'))
        target_2 = self.safe_numeric_convert(data.get('هدف 2 (يوم)'))
        target_3 = self.safe_numeric_convert(data.get('هدف 3 (يوم)'))
        stop_loss = self.safe_numeric_convert(data.get('وقف خسارة (يوم)'))
        
        # المتوسطات المتحركة
        ma5 = self.safe_numeric_convert(data.get('ma5 (يوم)'))
        ma10 = self.safe_numeric_convert(data.get('ma10 (يوم)'))
        ma20 = self.safe_numeric_convert(data.get('ma20 (يوم)'))
        ma50 = self.safe_numeric_convert(data.get('ma50 (يوم)'))
        ma100 = self.safe_numeric_convert(data.get('ma100 (يوم)'))
        ma200 = self.safe_numeric_convert(data.get('ma200 (يوم)'))
        
        # مؤشرات فنية
        tk_indicator = self.safe_numeric_convert(data.get('TK (يوم)'))
        kj_indicator = self.safe_numeric_convert(data.get('KJ (يوم)'))
        
        # بيانات مالية أساسية
        eps_annual = self.safe_numeric_convert(data.get('ربح السهم (سنوي)'), 999.99)
        book_value = self.safe_numeric_convert(data.get('القيمة الدفترية'))
        pe_ratio = self.safe_numeric_convert(data.get('مكرر الأرباح'), 999.99)
        dividend_yield = self.safe_numeric_convert(data.get('العائد الربحي (٪)'), 999.99)
        
        # إنشاء أمر SQL
        sql = f"""
        INSERT INTO stocks_realtime (
            symbol, open_price, high_price, low_price, current_price,
            change_percent, volume, turnover, trades_count,
            liquidity_ratio, target_1, target_2, target_3, stop_loss,
            ma5, ma10, ma20, ma50, ma100, ma200,
            tk_indicator, kj_indicator,
            eps_annual, book_value, pe_ratio, dividend_yield,
            last_trade_date, updated_at
        ) VALUES (
            '{symbol}',
            {open_price if open_price is not None else 'NULL'},
            {high_price if high_price is not None else 'NULL'},
            {low_price if low_price is not None else 'NULL'},
            {current_price if current_price is not None else 'NULL'},
            {change_percent if change_percent is not None else 'NULL'},
            {int(volume) if volume is not None else 'NULL'},
            {turnover if turnover is not None else 'NULL'},
            {int(trades_count) if trades_count is not None else 'NULL'},
            {liquidity_ratio if liquidity_ratio is not None else 'NULL'},
            {target_1 if target_1 is not None else 'NULL'},
            {target_2 if target_2 is not None else 'NULL'},
            {target_3 if target_3 is not None else 'NULL'},
            {stop_loss if stop_loss is not None else 'NULL'},
            {ma5 if ma5 is not None else 'NULL'},
            {ma10 if ma10 is not None else 'NULL'},
            {ma20 if ma20 is not None else 'NULL'},
            {ma50 if ma50 is not None else 'NULL'},
            {ma100 if ma100 is not None else 'NULL'},
            {ma200 if ma200 is not None else 'NULL'},
            {tk_indicator if tk_indicator is not None else 'NULL'},
            {kj_indicator if kj_indicator is not None else 'NULL'},
            {eps_annual if eps_annual is not None else 'NULL'},
            {book_value if book_value is not None else 'NULL'},
            {pe_ratio if pe_ratio is not None else 'NULL'},
            {dividend_yield if dividend_yield is not None else 'NULL'},
            CURRENT_DATE,
            CURRENT_TIMESTAMP
        )
        ON CONFLICT (symbol) DO UPDATE SET
            open_price = EXCLUDED.open_price,
            high_price = EXCLUDED.high_price,
            low_price = EXCLUDED.low_price,
            current_price = EXCLUDED.current_price,
            change_percent = EXCLUDED.change_percent,
            volume = EXCLUDED.volume,
            turnover = EXCLUDED.turnover,
            trades_count = EXCLUDED.trades_count,
            liquidity_ratio = EXCLUDED.liquidity_ratio,
            target_1 = EXCLUDED.target_1,
            target_2 = EXCLUDED.target_2,
            target_3 = EXCLUDED.target_3,
            stop_loss = EXCLUDED.stop_loss,
            ma5 = EXCLUDED.ma5,
            ma10 = EXCLUDED.ma10,
            ma20 = EXCLUDED.ma20,
            ma50 = EXCLUDED.ma50,
            ma100 = EXCLUDED.ma100,
            ma200 = EXCLUDED.ma200,
            tk_indicator = EXCLUDED.tk_indicator,
            kj_indicator = EXCLUDED.kj_indicator,
            eps_annual = EXCLUDED.eps_annual,
            book_value = EXCLUDED.book_value,
            pe_ratio = EXCLUDED.pe_ratio,
            dividend_yield = EXCLUDED.dividend_yield,
            updated_at = CURRENT_TIMESTAMP;
        """
        
        return self.execute_sql_command(sql, f"Upsert realtime data for {symbol}")
    
    def import_realtime_data(self):
        """استيراد البيانات الحية من Excel"""
        logger.info("📊 بدء استيراد البيانات الحية من Excel")
        
        try:
            # قراءة ملف Excel
            df = pd.read_excel(self.excel_path, engine='openpyxl')
            logger.info(f"📖 تم تحميل {len(df)} سجل من Excel")
            
            self.stats['realtime_processed'] = len(df)
            
            # معالجة كل سجل
            for index, row in df.iterrows():
                symbol = self.clean_symbol(row.get('الرمز'))
                if not symbol:
                    continue
                
                # إضافة السهم إلى stocks_master
                name_ar = str(row.get('الاسم', symbol))[:200]  # تحديد الطول
                self.insert_stock_master_record(symbol, name_ar)
                
                # إدراج البيانات الحية
                success, message = self.insert_realtime_record(row)
                if success:
                    self.stats['realtime_success'] += 1
                    if (index + 1) % 50 == 0:
                        logger.info(f"📊 تم معالجة {index + 1}/{len(df)} سجل")
                else:
                    logger.warning(f"⚠️ فشل في معالجة {symbol}: {message}")
            
            logger.info(f"✅ اكتمل استيراد البيانات الحية: {self.stats['realtime_success']}/{self.stats['realtime_processed']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في استيراد البيانات الحية: {e}")
            return False
    
    def import_financial_data(self):
        """استيراد البيانات المالية من CSV"""
        logger.info("💰 بدء استيراد البيانات المالية من CSV")
        
        try:
            # قراءة ملف CSV
            df = pd.read_csv(self.csv_path, encoding='utf-8')
            logger.info(f"📖 تم تحميل {len(df)} سجل من CSV")
            
            self.stats['financial_processed'] = len(df)
            
            # معالجة البيانات المالية (مبسطة)
            for index, row in df.iterrows():
                symbol = self.clean_symbol(row.get('Symbol', row.get('الرمز')))
                if not symbol:
                    continue
                
                # إدراج بيانات مالية أساسية
                fiscal_year = 2024  # افتراضي
                fiscal_quarter = 4  # افتراضي
                
                market_cap = self.safe_numeric_convert(row.get('Market Cap'), 999999999)
                pe_ratio = self.safe_numeric_convert(row.get('P/E Ratio'), 999.99)
                pb_ratio = self.safe_numeric_convert(row.get('P/B Ratio'), 999.99)
                roe = self.safe_numeric_convert(row.get('ROE'), 999.99)
                
                sql = f"""
                INSERT INTO stocks_financials (
                    symbol, fiscal_year, fiscal_quarter,
                    market_cap, pe_ratio, pb_ratio, roe,
                    created_at, updated_at
                ) VALUES (
                    '{symbol}', {fiscal_year}, {fiscal_quarter},
                    {market_cap if market_cap is not None else 'NULL'},
                    {pe_ratio if pe_ratio is not None else 'NULL'},
                    {pb_ratio if pb_ratio is not None else 'NULL'},
                    {roe if roe is not None else 'NULL'},
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                ON CONFLICT (symbol, fiscal_year, fiscal_quarter) DO UPDATE SET
                    market_cap = EXCLUDED.market_cap,
                    pe_ratio = EXCLUDED.pe_ratio,
                    pb_ratio = EXCLUDED.pb_ratio,
                    roe = EXCLUDED.roe,
                    updated_at = CURRENT_TIMESTAMP;
                """
                
                success, message = self.execute_sql_command(sql, f"Upsert financial data for {symbol}")
                if success:
                    self.stats['financial_success'] += 1
                    if (index + 1) % 50 == 0:
                        logger.info(f"💰 تم معالجة {index + 1}/{len(df)} سجل مالي")
            
            logger.info(f"✅ اكتمل استيراد البيانات المالية: {self.stats['financial_success']}/{self.stats['financial_processed']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في استيراد البيانات المالية: {e}")
            return False
    
    def run_enhanced_import(self):
        """تشغيل الاستيراد المحسن"""
        start_time = datetime.now()
        logger.info("🚀 بدء الاستيراد المحسن للبيانات الحية والمالية")
        logger.info("=" * 80)
        
        # استيراد البيانات الحية
        realtime_success = self.import_realtime_data()
        
        # استيراد البيانات المالية
        financial_success = self.import_financial_data()
        
        # تقرير نهائي
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info("=" * 80)
        logger.info("📋 تقرير الاستيراد المحسن")
        logger.info("=" * 80)
        logger.info(f"📊 البيانات الحية: {self.stats['realtime_success']}/{self.stats['realtime_processed']}")
        logger.info(f"💰 البيانات المالية: {self.stats['financial_success']}/{self.stats['financial_processed']}")
        logger.info(f"⏱️ المدة الإجمالية: {duration:.1f} ثانية")
        
        if realtime_success and financial_success:
            logger.info("🎉 اكتمل الاستيراد المحسن بنجاح!")
            return True
        else:
            logger.warning("⚠️ اكتمل الاستيراد مع بعض المشاكل")
            return False

def main():
    """الدالة الرئيسية"""
    importer = EnhancedDataImporter()
    success = importer.run_enhanced_import()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
