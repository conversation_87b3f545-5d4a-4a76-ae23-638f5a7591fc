
import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Eye } from 'lucide-react';
import { usePatternRecognition } from '@/hooks/usePatternRecognition';

const PatternRecognitionSection = () => {
  const { data: patterns, isLoading, error } = usePatternRecognition();

  return (
    <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-violet-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-purple-800">
          <Eye className="h-5 w-5" />
          التعرف على الأنماط
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center text-purple-800">جاري التحميل...</div>
        ) : error ? (
          <div className="text-center text-red-600">حدث خطأ في التحميل</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(patterns ?? []).map((pattern, index) => (
              <div key={pattern.id ?? index} className="p-4 bg-white/60 rounded-lg border border-purple-200">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-bold text-lg">{pattern.symbol}</span>
                  <Badge className="bg-purple-500 text-white">
                    {pattern.reliability}% دقة
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">النمط المكتشف</span>
                    <span className="font-medium">{pattern.pattern}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">التوقع</span>
                    <span className="font-medium text-green-600">{pattern.expected}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">الإطار الزمني</span>
                    <span className="font-medium">{pattern.timeframe}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PatternRecognitionSection;
