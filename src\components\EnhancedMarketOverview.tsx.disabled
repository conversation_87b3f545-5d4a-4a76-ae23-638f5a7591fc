// @ts-nocheck
/* eslint-disable */
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Activity, BarChart3, Target, AlertTriangle, Sparkles } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// استخدام التايبات الجديدة
import { StocksRealtime, StocksFinancials } from '@/types/database';

interface EnhancedMarketData {
  // الإحصائيات الأساسية
  totalStocks: number;
  activeStocks: number;
  totalVolume: number;
  totalTurnover: number;
  
  // التوزيع حسب الأداء
  gainers: number;
  losers: number;
  unchanged: number;
  
  // الفرص الاستثمارية
  speculationOpportunities: number;
  stopsLossHit: number;
  targetReached: number;
  
  // التحليل الفني
  technicalSignals: {
    bullish: number;
    bearish: number;
    neutral: number;
  };
  
  // القطاعات النشطة
  topSectors: Array<{
    sector: string;
    stocks_count: number;
    avg_performance: number;
  }>;
  
  // أهم الأسهم
  topPerformers: StocksRealtime[];
  topVolume: StocksRealtime[];
  speculationStocks: StocksRealtime[];
}

// eslint-disable-next-line react-refresh/only-export-components
export function useEnhancedMarketOverview() {
  return useQuery<EnhancedMarketData>({
    queryKey: ['enhanced_market_overview'],
    queryFn: async () => {
      console.log('🔄 جاري تحميل بيانات السوق المحسنة...');
        // 1. الحصول على كل الأسهم النشطة مع البيانات المحسنة
      const { data: allStocks, error: stocksError } = await supabase
        .from('stocks_realtime')
        .select(`
          symbol,
          current_price,
          change_percent,
          volume,
          turnover,
          stock_status,
          sector,
          high_price,
          low_price
        `)
          ma50,
          sector,
          name
        `)
        .gt('volume', 0) // فقط الأسهم النشطة
        .order('volume', { ascending: false });

      if (stocksError) throw stocksError;

      // 2. الحصول على البيانات المالية للقطاعات
      const { data: financialData, error: financialError } = await supabase
        .from('stocks_financials')
        .select('symbol, sector, market_cap, pe_ratio')
        .not('sector', 'is', null);

      if (financialError) {
        console.warn('⚠️ لم يتم تحميل البيانات المالية:', financialError);
      }

      const stocks = allStocks || [];
      
      // 3. حساب الإحصائيات الأساسية
      const totalStocks = stocks.length;
      const activeStocks = stocks.filter(s => s.volume > 0).length;
      const totalVolume = stocks.reduce((sum, s) => sum + (s.volume || 0), 0);
      const totalTurnover = stocks.reduce((sum, s) => sum + (s.turnover || 0), 0);

      // 4. توزيع الأداء
      const gainers = stocks.filter(s => (s.change_percent || 0) > 0).length;
      const losers = stocks.filter(s => (s.change_percent || 0) < 0).length;
      const unchanged = stocks.filter(s => (s.change_percent || 0) === 0).length;

      // 5. الفرص الاستثمارية
      const speculationOpportunities = stocks.filter(s => s.speculation_opportunity === true).length;
      const stopsLossHit = stocks.filter(s => {
        return s.stop_loss && s.current_price <= s.stop_loss;
      }).length;
      const targetReached = stocks.filter(s => {
        return s.target_1 && s.current_price >= s.target_1;
      }).length;

      // 6. التحليل الفني (باستخدام المتوسطات المتحركة)
      let bullish = 0, bearish = 0, neutral = 0;
      
      stocks.forEach(stock => {
        const price = stock.current_price || 0;
        const ma20 = stock.ma20 || 0;
        const ma50 = stock.ma50 || 0;
        
        if (price > ma20 && ma20 > ma50) {
          bullish++;
        } else if (price < ma20 && ma20 < ma50) {
          bearish++;
        } else {
          neutral++;
        }
      });

      // 7. أهم القطاعات النشطة
      const sectorMap = new Map<string, { count: number; totalPerf: number }>();
      
      stocks.forEach(stock => {
        const sector = stock.sector || 'غير محدد';
        const perf = stock.change_percent || 0;
        
        if (!sectorMap.has(sector)) {
          sectorMap.set(sector, { count: 0, totalPerf: 0 });
        }
        
        const sectorData = sectorMap.get(sector)!;
        sectorData.count++;
        sectorData.totalPerf += perf;
      });

      const topSectors = Array.from(sectorMap.entries())
        .map(([sector, data]) => ({
          sector,
          stocks_count: data.count,
          avg_performance: data.totalPerf / data.count
        }))
        .sort((a, b) => b.stocks_count - a.stocks_count)
        .slice(0, 5);

      // 8. أهم الأسهم
      const topPerformers = stocks
        .filter(s => (s.change_percent || 0) > 0)
        .sort((a, b) => (b.change_percent || 0) - (a.change_percent || 0))
        .slice(0, 10);

      const topVolume = stocks
        .sort((a, b) => (b.volume || 0) - (a.volume || 0))
        .slice(0, 10);

      const speculationStocks = stocks
        .filter(s => s.speculation_opportunity === true)
        .slice(0, 10);      console.log('تم تحميل البيانات المحسنة بنجاح!');
      console.log('إجمالي الأسهم: ' + totalStocks + ', النشطة: ' + activeStocks);
      console.log('فرص المضاربة: ' + speculationOpportunities);

      return {
        totalStocks,
        activeStocks,
        totalVolume,
        totalTurnover,
        gainers,
        losers,
        unchanged,
        speculationOpportunities,
        stopsLossHit,
        targetReached,
        technicalSignals: { bullish, bearish, neutral },
        topSectors,
        topPerformers,
        topVolume,
        speculationStocks
      };
    },
    refetchInterval: 30000, // تحديث كل 30 ثانية
    staleTime: 15000 // البيانات صالحة لمدة 15 ثانية
  });
}

const EnhancedMarketOverview = () => {
  const { data: marketData, isLoading, error } = useEnhancedMarketOverview();

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-EG').format(num);
  };
  const formatCurrency = (num: number) => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)} مليار`;
    } else if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)} مليون`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)} ألف`;
    }
    return formatNumber(num);
  };

  const formatPercent = (num: number) => {
    return `${num.toFixed(2)}%`;
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {[1, 2, 3, 4].map(i => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="mb-8">
        <CardContent className="p-8 text-center text-red-600">
          ❌ حدث خطأ أثناء تحميل بيانات السوق المحسنة
        </CardContent>
      </Card>
    );
  }

  if (!marketData) return null;

  return (
    <div className="space-y-6">
      {/* الإحصائيات الأساسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">إجمالي الأسهم</p>
                <p className="text-2xl font-bold text-blue-900">{formatNumber(marketData.totalStocks)}</p>
                <p className="text-xs text-blue-600">النشطة: {formatNumber(marketData.activeStocks)}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">إجمالي الحجم</p>
                <p className="text-2xl font-bold text-green-900">{formatCurrency(marketData.totalVolume)}</p>
                <p className="text-xs text-green-600">القيمة: {formatCurrency(marketData.totalTurnover)}</p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">فرص المضاربة</p>
                <p className="text-2xl font-bold text-purple-900">{formatNumber(marketData.speculationOpportunities)}</p>
                <p className="text-xs text-purple-600">أهداف محققة: {formatNumber(marketData.targetReached)}</p>
              </div>
              <Sparkles className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">التحليل الفني</p>
                <div className="flex space-x-2 rtl:space-x-reverse">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    صاعد {marketData.technicalSignals.bullish}
                  </Badge>
                  <Badge variant="secondary" className="bg-red-100 text-red-800">
                    هابط {marketData.technicalSignals.bearish}
                  </Badge>
                </div>
              </div>
              <Target className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* توزيع الأداء */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            توزيع أداء الأسهم
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{formatNumber(marketData.gainers)}</div>
              <div className="text-sm text-gray-600">مرتفعة</div>
              <div className="text-xs text-gray-500">{formatPercent((marketData.gainers / marketData.totalStocks) * 100)}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">{formatNumber(marketData.losers)}</div>
              <div className="text-sm text-gray-600">منخفضة</div>
              <div className="text-xs text-gray-500">{formatPercent((marketData.losers / marketData.totalStocks) * 100)}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-600">{formatNumber(marketData.unchanged)}</div>
              <div className="text-sm text-gray-600">ثابتة</div>
              <div className="text-xs text-gray-500">{formatPercent((marketData.unchanged / marketData.totalStocks) * 100)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* أهم القطاعات */}
      <Card>
        <CardHeader>
          <CardTitle>أهم القطاعات النشطة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {marketData.topSectors.map((sector, index) => (
              <div key={sector.sector} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge variant="outline">#{index + 1}</Badge>
                  <div>
                    <div className="font-medium">{sector.sector}</div>
                    <div className="text-xs text-gray-500">{formatNumber(sector.stocks_count)} شركة</div>
                  </div>
                </div>
                <div className={`font-bold ${sector.avg_performance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatPercent(sector.avg_performance)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* أهم الأسهم */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* أعلى أداء */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">أعلى أداء</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {marketData.topPerformers.slice(0, 5).map((stock) => (
                <div key={stock.symbol} className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">{stock.symbol}</div>
                    <div className="text-xs text-gray-500">{formatCurrency(stock.current_price || 0)}</div>
                  </div>
                  <div className="text-green-600 font-bold">
                    +{formatPercent(stock.change_percent || 0)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* أعلى حجم */}
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-600">أعلى حجم تداول</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {marketData.topVolume.slice(0, 5).map((stock) => (
                <div key={stock.symbol} className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">{stock.symbol}</div>
                    <div className="text-xs text-gray-500">{formatCurrency(stock.current_price || 0)}</div>
                  </div>
                  <div className="text-blue-600 font-bold">
                    {formatCurrency(stock.volume || 0)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* فرص المضاربة */}
        <Card>
          <CardHeader>
            <CardTitle className="text-purple-600">فرص المضاربة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {marketData.speculationStocks.slice(0, 5).map((stock) => (
                <div key={stock.symbol} className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">{stock.symbol}</div>
                    <div className="text-xs text-gray-500">{formatCurrency(stock.current_price || 0)}</div>
                  </div>
                  <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                    <Sparkles className="h-3 w-3 mr-1" />
                    فرصة
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EnhancedMarketOverview;
