INFO:     Will watch for changes in these directories: ['/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend']
INFO:     Uvicorn running on http://0.0.0.0:3001 (Press CTRL+C to quit)
INFO:     Started reloader process [163817] using WatchFiles
INFO:     Started server process [163821]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
🚀 EGX Stock Oracle API Starting...
🌍 Environment: development
🗄️ Database: postgresql://postgres:postgres@localhost:5432/egx_stock_oracle
INFO:     127.0.0.1:56498 - "GET /api/v1/health HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:56498 - "GET /api/v1/health/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:56512 - "GET /api/v1/stocks HTTP/1.1" 307 Temporary Redirect
2025-06-17 00:03:25,030 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-06-17 00:03:25,030 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-17 00:03:25,033 INFO sqlalchemy.engine.Engine select current_schema()
2025-06-17 00:03:25,033 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-17 00:03:25,035 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-06-17 00:03:25,036 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-17 00:03:25,037 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-17 00:03:25,041 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT stocks_master.id AS stocks_master_id, stocks_master.symbol AS stocks_master_symbol, stocks_master.name_ar AS stocks_master_name_ar, stocks_master.name_en AS stocks_master_name_en, stocks_master.sector AS stocks_master_sector, stocks_master.sub_sector AS stocks_master_sub_sector, stocks_master.isin_code AS stocks_master_isin_code, stocks_master.is_active AS stocks_master_is_active, stocks_master.listing_date AS stocks_master_listing_date, stocks_master.market_cap AS stocks_master_market_cap, stocks_master.created_at AS stocks_master_created_at, stocks_master.updated_at AS stocks_master_updated_at 
FROM stocks_master 
WHERE stocks_master.is_active = true) AS anon_1
2025-06-17 00:03:25,042 INFO sqlalchemy.engine.Engine [generated in 0.00052s] {}
2025-06-17 00:03:25,043 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:56512 - "GET /api/v1/stocks/ HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:56518 - "GET /api/v1/market/stats HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47438 - "GET /api/v1/health HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:47438 - "GET /api/v1/health/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:47450 - "GET /api/v1/stocks HTTP/1.1" 307 Temporary Redirect
2025-06-17 00:03:45,757 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-17 00:03:45,757 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT stocks_master.id AS stocks_master_id, stocks_master.symbol AS stocks_master_symbol, stocks_master.name_ar AS stocks_master_name_ar, stocks_master.name_en AS stocks_master_name_en, stocks_master.sector AS stocks_master_sector, stocks_master.sub_sector AS stocks_master_sub_sector, stocks_master.isin_code AS stocks_master_isin_code, stocks_master.is_active AS stocks_master_is_active, stocks_master.listing_date AS stocks_master_listing_date, stocks_master.market_cap AS stocks_master_market_cap, stocks_master.created_at AS stocks_master_created_at, stocks_master.updated_at AS stocks_master_updated_at 
FROM stocks_master 
WHERE stocks_master.is_active = true) AS anon_1
2025-06-17 00:03:45,758 INFO sqlalchemy.engine.Engine [cached since 20.72s ago] {}
2025-06-17 00:03:45,759 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:47450 - "GET /api/v1/stocks/ HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:47452 - "GET /api/v1/market/stats HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'app/models/stocks.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [163821]
⏹️ EGX Stock Oracle API Shutting down...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/usr/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend/app/main.py", line 16, in <module>
    from app.api.v1.api import api_router
  File "/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend/app/api/v1/api.py", line 9, in <module>
    from app.api.v1.endpoints import stocks, market, health
  File "/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend/app/api/v1/endpoints/stocks.py", line 14, in <module>
    from app.models.stocks import StockMaster, StockRealtime, StockHistorical, StockFinancial
  File "/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend/app/models/stocks.py", line 16
    id = Column(Integer, primary_key=True, index=True)
IndentationError: unexpected indent
WARNING:  WatchFiles detected changes in 'app/models/stocks.py'. Reloading...
INFO:     Started server process [164398]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/stocks.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [164398]
🚀 EGX Stock Oracle API Starting...
🌍 Environment: development
🗄️ Database: postgresql://postgres:postgres@localhost:5432/egx_stock_oracle
⏹️ EGX Stock Oracle API Shutting down...
Process SpawnProcess-4:
Traceback (most recent call last):
  File "/usr/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/home/<USER>/.local/lib/python3.10/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend/app/main.py", line 16, in <module>
    from app.api.v1.api import api_router
  File "/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend/app/api/v1/api.py", line 9, in <module>
    from app.api.v1.endpoints import stocks, market, health
  File "/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend/app/api/v1/endpoints/stocks.py", line 90
    return {
IndentationError: unexpected indent
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/stocks.py'. Reloading...
INFO:     Started server process [164932]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/market.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [164932]
🚀 EGX Stock Oracle API Starting...
🌍 Environment: development
🗄️ Database: postgresql://postgres:postgres@localhost:5432/egx_stock_oracle
⏹️ EGX Stock Oracle API Shutting down...
INFO:     Started server process [165223]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
🚀 EGX Stock Oracle API Starting...
🌍 Environment: development
🗄️ Database: postgresql://postgres:postgres@localhost:5432/egx_stock_oracle
INFO:     127.0.0.1:33332 - "GET /api/v1/health HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:33332 - "GET /api/v1/health/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:33346 - "GET /api/v1/stocks HTTP/1.1" 307 Temporary Redirect
2025-06-17 00:08:09,623 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-06-17 00:08:09,624 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-17 00:08:09,627 INFO sqlalchemy.engine.Engine select current_schema()
2025-06-17 00:08:09,627 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-17 00:08:09,629 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-06-17 00:08:09,630 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-17 00:08:09,631 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-17 00:08:09,635 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT stocks_master.id AS stocks_master_id, stocks_master.symbol AS stocks_master_symbol, stocks_master.name_ar AS stocks_master_name_ar, stocks_master.name_en AS stocks_master_name_en, stocks_master.sector AS stocks_master_sector, stocks_master.isin_code AS stocks_master_isin_code, stocks_master.is_active AS stocks_master_is_active, stocks_master.listing_date AS stocks_master_listing_date, stocks_master.market_cap AS stocks_master_market_cap, stocks_master.created_at AS stocks_master_created_at, stocks_master.updated_at AS stocks_master_updated_at 
FROM stocks_master 
WHERE stocks_master.is_active = true) AS anon_1
2025-06-17 00:08:09,636 INFO sqlalchemy.engine.Engine [generated in 0.00086s] {}
2025-06-17 00:08:09,640 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:33346 - "GET /api/v1/stocks/ HTTP/1.1" 500 Internal Server Error
2025-06-17 00:08:09,644 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-17 00:08:09,646 INFO sqlalchemy.engine.Engine SELECT count(stocks_master.id) AS count_1 
FROM stocks_master
2025-06-17 00:08:09,646 INFO sqlalchemy.engine.Engine [generated in 0.00063s] {}
2025-06-17 00:08:09,649 INFO sqlalchemy.engine.Engine SELECT count(stocks_master.id) AS count_1 
FROM stocks_master 
WHERE stocks_master.is_active = true
2025-06-17 00:08:09,649 INFO sqlalchemy.engine.Engine [generated in 0.00071s] {}
2025-06-17 00:08:09,651 INFO sqlalchemy.engine.Engine SELECT count(stocks_realtime.id) AS count_1 
FROM stocks_realtime
2025-06-17 00:08:09,651 INFO sqlalchemy.engine.Engine [generated in 0.00063s] {}
2025-06-17 00:08:09,654 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:33348 - "GET /api/v1/market/stats HTTP/1.1" 500 Internal Server Error
INFO:     Will watch for changes in these directories: ['/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/backend']
ERROR:    [Errno 98] Address already in use
INFO:     127.0.0.1:42578 - "GET /api/v1/health HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:42578 - "GET /api/v1/health/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:42590 - "GET /api/v1/stocks HTTP/1.1" 307 Temporary Redirect
2025-06-17 00:10:02,762 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-17 00:10:02,763 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT stocks_master.id AS stocks_master_id, stocks_master.symbol AS stocks_master_symbol, stocks_master.name_ar AS stocks_master_name_ar, stocks_master.name_en AS stocks_master_name_en, stocks_master.sector AS stocks_master_sector, stocks_master.isin_code AS stocks_master_isin_code, stocks_master.is_active AS stocks_master_is_active, stocks_master.listing_date AS stocks_master_listing_date, stocks_master.market_cap AS stocks_master_market_cap, stocks_master.created_at AS stocks_master_created_at, stocks_master.updated_at AS stocks_master_updated_at 
FROM stocks_master 
WHERE stocks_master.is_active = true) AS anon_1
2025-06-17 00:10:02,763 INFO sqlalchemy.engine.Engine [cached since 113.1s ago] {}
2025-06-17 00:10:02,764 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:42590 - "GET /api/v1/stocks/ HTTP/1.1" 500 Internal Server Error
2025-06-17 00:10:02,769 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-17 00:10:02,770 INFO sqlalchemy.engine.Engine SELECT count(stocks_master.id) AS count_1 
FROM stocks_master
2025-06-17 00:10:02,770 INFO sqlalchemy.engine.Engine [cached since 113.1s ago] {}
2025-06-17 00:10:02,772 INFO sqlalchemy.engine.Engine SELECT count(stocks_master.id) AS count_1 
FROM stocks_master 
WHERE stocks_master.is_active = true
2025-06-17 00:10:02,773 INFO sqlalchemy.engine.Engine [cached since 113.1s ago] {}
2025-06-17 00:10:02,774 INFO sqlalchemy.engine.Engine SELECT count(stocks_realtime.id) AS count_1 
FROM stocks_realtime
2025-06-17 00:10:02,775 INFO sqlalchemy.engine.Engine [cached since 113.1s ago] {}
2025-06-17 00:10:02,776 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:42606 - "GET /api/v1/market/stats HTTP/1.1" 500 Internal Server Error
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [165223]
⏹️ EGX Stock Oracle API Shutting down...
