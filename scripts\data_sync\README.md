# EGX Stock AI Oracle - Enhanced Data Synchronization System

This directory contains the comprehensive and enhanced data synchronization system for the EGX Stock AI Oracle project. It handles importing and updating extensive stock data from multiple sources into the Supabase/PostgreSQL database with full data utilization.

## � **What's New in Enhanced Version**

### ✨ **Complete Data Utilization**
- **Real-time Data**: Now imports 40+ columns from Excel including technical indicators, targets, liquidity flow, and financial metrics
- **Historical Data**: Enhanced processing with data validation and incremental updates
- **Financial Data**: Comprehensive mapping of 80+ financial metrics including ratios, growth rates, and analyst data

### 📊 **Enhanced Data Sources**

#### 1. **Real-time Stock Data** - Excel file `stock_synco.xlsx`
**New Comprehensive Column Mapping:**
- **Basic Data**: الرمز، الاسم، إفتتاح، أعلى، أدنى، الاغلاق، التغير %
- **Technical Indicators**: ma5-ma200، TK، KJ، هدف 1-3، وقف خسارة
- **Liquidity Analysis**: نسبة السيولة، تدفق السيولة، السيولة الداخلة/الخارجة
- **Financial Metrics**: ربح السهم، القيمة الدفترية، مكرر الأرباح، العائد الربحي
- **Market Data**: الحجم، القيمة، الصفقات، القطاع، الأسهم الحرة

#### 2. **Historical Stock Data** - TXT files from `meta2/`
**Enhanced Features:**
- **Smart Symbol Detection**: Handles both `SYMBOL.TXT` and `SYMBOLD.TXT` formats
- **Incremental Updates**: Only processes new data after last import date
- **Data Validation**: OHLC validation and error handling
- **Format**: `TICKER,PER,DTYYYYMMDD,TIME,OPEN,HIGH,LOW,CLOSE,VOL,OPENINT`

#### 3. **Financial Data** - CSV file `financial_data.csv`
**Comprehensive 80+ Metrics:**
- **Valuation**: P/E, P/B, P/S, EV/EBITDA, Market Cap
- **Earnings**: EPS, Growth rates, Analyst estimates
- **Dividends**: Yield, Payout ratio, Growth
- **Financial Statements**: Revenue, Profit, Assets, Debt
- **Cash Flow**: Operating, Free cash flow, Capex
- **Ratios**: Current ratio, Debt/Equity, ROE, ROA
- **Performance**: Beta, Volatility, Returns

## 🛠 **Enhanced Scripts**

### **New Enhanced Loaders:**
- `load_realtime_enhanced.py` - Complete real-time data with 40+ columns
- `load_historical_enhanced.py` - Smart incremental historical data processing  
- `load_financials_enhanced.py` - Comprehensive financial data with 80+ metrics

### **Legacy Scripts (Still Available):**
- `load_realtime.py` - Basic real-time data loader
- `load_historical.py` - Basic historical data loader
- `load_financials.py` - Basic financial data loader

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Run the setup script
python setup.py

# Edit environment variables
cp .env.example .env
# Update .env with your Supabase credentials
```

### 2. Required Environment Variables

```env
SUPABASE_URL=https://tbzbrujqjwpatbzffmwq.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here

# Data file paths (update as needed)
HISTORICAL_DATA_FOLDER=/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2
REALTIME_EXCEL_PATH=/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx
FINANCIAL_CSV_PATH=/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. **Enhanced Usage (Recommended)**

```bash
# Test enhanced real-time data sync (40+ columns)
python load_realtime_enhanced.py

# Test enhanced historical data sync with incremental updates
python load_historical_enhanced.py

# Test comprehensive financial data sync (80+ metrics)
python load_financials_enhanced.py --validate

# For specific symbol historical data
python load_historical_enhanced.py --symbol COMI

# For full historical reload (not incremental)
python load_historical_enhanced.py --full
```

### 5. **Legacy Usage (Basic)**

```bash
# Basic loaders (limited data)
python load_realtime.py
python load_historical.py  
python load_financials.py
```

### 6. **Data Validation & Monitoring**

```bash
# Comprehensive data validation
python validate_data.py

# Check specific data integrity
python load_financials_enhanced.py --validate --create-indices
```

### 7. Start Automated Scheduler

```bash
# Update scheduler to use enhanced loaders
python scheduler.py

# Platform-specific scripts
# Windows: start_scheduler.bat
# Linux/Mac: ./start_scheduler.sh
```

## 📁 File Structure

```
scripts/data_sync/
├── setup.py                 # Initial setup script
├── scheduler.py              # Main scheduler for automated syncing
├── load_historical.py        # Historical data import
├── load_realtime.py          # Real-time data sync
├── load_financials.py        # Financial data import
├── validate_data.py          # Data validation and monitoring
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
├── README.md                # This file
├── start_scheduler.bat      # Windows startup script
├── start_scheduler.sh       # Linux/Mac startup script
└── logs/                    # Log files directory
    ├── data_sync_scheduler.log
    ├── realtime_data_sync.log
    ├── historical_data_sync.log
    ├── financial_data_sync.log
    └── data_validation.log
```

## ⚙️ Script Details

### `load_historical.py`
- **Purpose**: Import historical OHLCV data from TXT files
- **Schedule**: Weekly or on-demand
- **Features**:
  - Batch processing for performance
  - Date format validation (YYYYMMDD → YYYY-MM-DD)
  - Flexible column name mapping
  - Stock registration in `stocks_master`
  - Error handling and logging

### `load_realtime.py`
- **Purpose**: Sync current market data from Excel
- **Schedule**: Every 15 minutes
- **Features**:
  - Arabic column name support
  - Change calculation (amount/percentage)
  - Market indices update
  - Real-time timestamp tracking
  - Batch upsert operations

### `load_financials.py`
- **Purpose**: Import financial ratios and fundamentals
- **Schedule**: Daily
- **Features**:
  - Comprehensive financial metrics mapping
  - Safe type conversion
  - Multiple encoding support (UTF-8, Latin-1)
  - Flexible column matching

### `validate_data.py`
- **Purpose**: Data quality monitoring and validation
- **Schedule**: On-demand or after sync operations
- **Features**:
  - Completeness checks
  - Data freshness validation
  - Quality metrics (missing/invalid data)
  - Comprehensive reporting
  - Automated recommendations

### `scheduler.py`
- **Purpose**: Orchestrate all sync operations
- **Features**:
  - Configurable sync intervals
  - Full sync coordination
  - Error recovery
  - Notification support
  - Performance monitoring

## 📊 Database Schema Integration

The system integrates with the following database tables:

### `stocks_master`
- Master registry of all stocks
- Auto-registration of new symbols
- Market and sector information

### `stocks_historical`
- Daily OHLCV historical data
- Optimized with date-based indexing
- Supports backtesting and analysis

### `stocks_realtime`
- Current market prices and volume
- Change calculations
- Bid/ask spreads
- Last trade timestamps

### `stocks_financials`
- Financial ratios and fundamentals
- P/E, ROE, debt ratios
- Analyst ratings and targets
- TTM (Trailing Twelve Months) data

### `market_indices`
- Market-wide statistics
- Index values and constituents
- Aggregated market metrics

## 🔧 Configuration Options

### Sync Intervals (Environment Variables)
```env
REALTIME_SYNC_MINUTES=15      # Real-time sync frequency
FINANCIAL_SYNC_HOURS=24       # Financial data sync frequency
HISTORICAL_SYNC_HOURS=168     # Historical sync frequency (weekly)
BATCH_SIZE=100                # Database batch size
MAX_RETRIES=3                 # Retry attempts on failure
```

### Logging Configuration
```env
LOG_LEVEL=INFO                # Logging level
LOG_FILE_PATH=./logs/         # Log files directory
```

## 🚨 Monitoring & Alerts

### Log Files
- `data_sync_scheduler.log` - Main scheduler activity
- `*_data_sync.log` - Individual sync script logs
- `data_validation.log` - Data quality checks

### Data Validation Reports
- Automated quality checks
- Metrics tracking
- Issue identification
- Recommendations for fixes

### Health Checks
```bash
# Check data freshness
python validate_data.py

# View recent logs
tail -f logs/data_sync_scheduler.log

# Check sync status
python -c "
from supabase import create_client
import os
from dotenv import load_dotenv
load_dotenv()
supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_ROLE_KEY'))
result = supabase.table('stocks_realtime').select('symbol,updated_at').order('updated_at', desc=True).limit(5).execute()
print('Recent updates:', result.data)
"
```

## 🛠️ Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```bash
   # Check .env file exists and has correct values
   cat .env
   ```

2. **File Path Issues**
   ```bash
   # Verify data files exist
   ls -la /mnt/c/Users/<USER>/OneDrive/Documents/stocks/
   ```

3. **Database Connection Issues**
   ```bash
   # Test Supabase connection
   python -c "
   from supabase import create_client
   import os
   from dotenv import load_dotenv
   load_dotenv()
   try:
       supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_ROLE_KEY'))
       result = supabase.table('stocks_master').select('count').execute()
       print('Connection successful')
   except Exception as e:
       print(f'Connection failed: {e}')
   "
   ```

4. **Permission Issues**
   ```bash
   # Make scripts executable
   chmod +x *.py
   chmod +x start_scheduler.sh
   ```

### Error Codes
- Exit 0: Success
- Exit 1: Configuration error
- Exit 2: Data source not found
- Exit 3: Database connection failed

## 📈 Performance Optimization

### Batch Processing
- Use `BATCH_SIZE` environment variable (default: 100)
- Larger batches = faster processing, more memory usage

### Database Optimization
- Historical data uses date-based indexing
- Upsert operations for data deduplication
- Connection pooling for concurrent operations

### Resource Management
- Progress bars for long operations
- Memory-efficient DataFrame processing
- Automatic cleanup of temporary files

## 🔄 Maintenance

### Regular Tasks
1. **Weekly**: Review validation reports
2. **Monthly**: Clean old log files
3. **Quarterly**: Update data source mappings
4. **As needed**: Update database schema

### Data Quality Checks
```bash
# Run comprehensive validation
python validate_data.py

# Check specific table
python -c "
from validate_data import DataValidator
validator = DataValidator()
validator.validate_realtime_data()
print(validator.validation_results)
"
```

### Backup Recommendations
- Database: Use Supabase automated backups
- Logs: Archive monthly
- Configuration: Version control .env.example

## 📞 Support

For issues or questions:
1. Check logs in `logs/` directory
2. Run `python validate_data.py` for health check
3. Review this README for troubleshooting
4. Check Supabase dashboard for database issues

## 🚀 Future Enhancements

Planned improvements:
- [ ] WebSocket real-time streaming
- [ ] Machine learning prediction integration
- [ ] Advanced data validation rules
- [ ] Multi-source data reconciliation
- [ ] Automated anomaly detection
- [ ] Performance metrics dashboard
- [ ] Telegram/email notifications
- [ ] Data retention policies
