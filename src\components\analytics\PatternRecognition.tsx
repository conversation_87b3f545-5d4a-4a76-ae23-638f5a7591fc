import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Eye, TrendingUp, AlertCircle } from 'lucide-react';
import { usePatternRecognition, PatternResult } from '@/hooks/usePatternRecognition';
import StockAnalysisLink from '@/components/analysis/StockAnalysisLink';
import { useAnalysis } from '@/hooks/useAnalysis';

const PatternRecognition = () => {
  const { openAnalysis } = useAnalysis();
  const { data: patterns, isLoading, error } = usePatternRecognition();

  // Extract unique timeframes from the backend data for the filter buttons
  const timeframes = useMemo(
    () => Array.from(new Set((patterns ?? []).map((p: PatternResult) => p.timeframe))).sort(),
    [patterns]
  );
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>(() =>
    timeframes.length > 0 ? timeframes[0] : ''
  );

  // Ensure timeframe filter updates if data changes
  React.useEffect(() => {
    if (timeframes.length > 0 && !timeframes.includes(selectedTimeframe)) {
      setSelectedTimeframe(timeframes[0]);
    }
  }, [timeframes, selectedTimeframe]);

  const filteredPatterns = useMemo(() =>
    (patterns ?? []).filter(p => !selectedTimeframe || p.timeframe === selectedTimeframe),
    [patterns, selectedTimeframe]
  );

  const getStatusColor = (reliability: number) => {
    // More reliability = more green, less = yellow/red
    if (reliability >= 85) return 'bg-green-500 text-white';
    if (reliability >= 70) return 'bg-yellow-500 text-white';
    return 'bg-red-500 text-white';
  };

  // Map backend "expected" to "الحركة المتوقعة" and icon
  const getExpectedMoveInfo = (expected: string) => {
    if (expected.includes('صعود') || expected.includes('كسر علوي') || expected === 'bullish') {
      return { color: 'text-green-600', label: 'صعود', icon: <TrendingUp className="h-4 w-4" /> };
    }
    if (expected.includes('هبوط') || expected.includes('كسر سفلي') || expected === 'bearish') {
      return { color: 'text-red-600', label: 'هبوط', icon: <TrendingUp className="h-4 w-4 rotate-180" /> };
    }
    return { color: 'text-gray-600', label: 'محايد', icon: <AlertCircle className="h-4 w-4" /> };
  };

  return (
    <Card className="border-2 border-cyan-200 bg-gradient-to-br from-cyan-50 to-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-cyan-800">
          <Eye className="h-5 w-5" />
          التعرف على الأنماط الفنية
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Timeframe Selector */}
        {timeframes.length > 0 && (
          <div className="flex gap-2">
            {timeframes.map(tf => (
              <button
                key={tf}
                onClick={() => setSelectedTimeframe(tf)}
                className={`px-3 py-1 rounded transition-colors ${
                  selectedTimeframe === tf
                    ? 'bg-cyan-600 text-white'
                    : 'bg-white border border-cyan-200 hover:bg-cyan-50'
                }`}
              >
                {tf}
              </button>
            ))}
          </div>
        )}

        {/* Patterns Grid */}
        {isLoading ? (
          <div className="text-center text-cyan-800 py-6">جاري التحميل...</div>
        ) : error ? (
          <div className="text-center text-red-600 py-6">حدث خطأ أثناء تحميل الأنماط</div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredPatterns.map((pattern) => {
                const expectedInfo = getExpectedMoveInfo(pattern.expected);
                return (
                  <div key={pattern.id} className="p-4 bg-white/60 rounded-lg border border-cyan-200">                    {/* Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-lg">{pattern.symbol}</span>
                        <StockAnalysisLink 
                          symbol={pattern.symbol} 
                          size="sm" 
                          onAnalyze={openAnalysis}
                          showText={false}
                        />
                      </div>
                      <Badge className={getStatusColor(Number(pattern.reliability))}>
                        {Number(pattern.reliability).toFixed(0)}% دقة
                      </Badge>
                    </div>
                    {/* Pattern Info */}
                    <div className="space-y-3">
                      <div>
                        <div className="font-medium text-cyan-700">{pattern.pattern}</div>
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>مستوى الدقة</span>
                          <span>{Number(pattern.reliability).toFixed(0)}%</span>
                        </div>
                        <Progress value={Number(pattern.reliability)} className="h-2" />
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">التوقع</span>
                        <span className={`font-medium ${expectedInfo.color} flex items-center gap-1`}>
                          {expectedInfo.icon}
                          {expectedInfo.label}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">الإطار الزمني</span>
                        <span className="font-medium">{pattern.timeframe}</span>
                      </div>
                      <div className="flex text-xs text-gray-600 justify-end">
                        تم الكشف: {pattern.detected_at ? new Date(pattern.detected_at).toLocaleString('ar-EG') : '--'}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            {filteredPatterns.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>لا توجد أنماط مكتشفة في هذا الإطار الزمني</p>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default PatternRecognition;
