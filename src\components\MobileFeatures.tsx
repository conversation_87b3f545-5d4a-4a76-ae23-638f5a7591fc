import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Smartphone, 
  Download, 
  Bell, 
  Wifi, 
  WifiOff,
  Home,
  Settings,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useMobileGestures } from '@/hooks/useMobileGestures';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const MobileFeatures = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isInstallable, setIsInstallable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [gestureDemo, setGestureDemo] = useState('');
  const demoRef = useMobileGestures({
    onSwipeLeft: () => setGestureDemo('سحب لليسار - الصفحة التالية'),
    onSwipeRight: () => setGestureDemo('سحب لليمين - الصفحة السابقة'),
    onSwipeUp: () => setGestureDemo('سحب للأعلى - قائمة سريعة'),
    onSwipeDown: () => setGestureDemo('سحب للأسفل - تحديث البيانات'),
    onLongPress: () => setGestureDemo('لمس طويل - المزيد من الخيارات'),
    onPinch: (scale) => setGestureDemo(`قرص للتكبير - مقياس: ${scale.toFixed(2)}`)
  });
  const { toast } = useToast();

  useEffect(() => {
    // Check if app is installable
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
    };

    // Check online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check notification permission
    if ('Notification' in window) {
      setNotificationsEnabled(Notification.permission === 'granted');
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    if (gestureDemo) {
      const timer = setTimeout(() => setGestureDemo(''), 2000);
      return () => clearTimeout(timer);
    }
  }, [gestureDemo]);

  const installPWA = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        toast({
          title: "تم التثبيت بنجاح",
          description: "تم تثبيت التطبيق على جهازك"
        });
      }
      
      setDeferredPrompt(null);
      setIsInstallable(false);
    }
  };

  const enableNotifications = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setNotificationsEnabled(permission === 'granted');
      
      if (permission === 'granted') {
        toast({
          title: "تم تفعيل التنبيهات",
          description: "ستصلك تنبيهات فورية عن تحركات السوق"
        });
        
        // Send a test notification
        new Notification('مرحباً بك في EGX Analytics', {
          body: 'ستصلك الآن تنبيهات فورية عن أخبار السوق المصري',
          icon: '/favicon.ico'
        });
      }
    }
  };

  const registerServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        toast({
          title: "تم تفعيل العمل دون اتصال",
          description: "يمكنك الآن استخدام التطبيق حتى بدون انترنت"
        });
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  };

  const shareApp = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'EGX Analytics - تحليل البورصة المصرية',
          text: 'أفضل منصة لتحليل البورصة المصرية مع ميزات الذكاء الاصطناعي',
          url: window.location.href
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: "تم نسخ الرابط",
        description: "تم نسخ رابط التطبيق للحافظة"
      });
    }
  };

  const mobileGestures = [
    { gesture: 'السحب لليسار', action: 'الانتقال للصفحة التالية' },
    { gesture: 'السحب لليمين', action: 'العودة للصفحة السابقة' },
    { gesture: 'اللمس المطول', action: 'إظهار المزيد من الخيارات' },
    { gesture: 'القرص للتكبير', action: 'تكبير/تصغير الرسوم البيانية' }
  ];

  return (
    <div className="space-y-6">
      {/* PWA Status */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Smartphone className="h-5 w-5" />
            إعدادات التطبيق المحمول
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Connection Status */}
            <div className="flex items-center justify-between p-4 bg-white/60 rounded-lg">
              <div className="flex items-center gap-3">
                {isOnline ? <Wifi className="h-5 w-5 text-green-600" /> : <WifiOff className="h-5 w-5 text-red-600" />}
                <div>
                  <div className="font-medium">حالة الاتصال</div>
                  <div className="text-sm text-muted-foreground">
                    {isOnline ? 'متصل بالإنترنت' : 'غير متصل - وضع دون اتصال'}
                  </div>
                </div>
              </div>
              <Badge className={isOnline ? 'bg-green-500' : 'bg-red-500'}>
                {isOnline ? 'متصل' : 'منقطع'}
              </Badge>
            </div>

            {/* PWA Installation */}
            <div className="flex items-center justify-between p-4 bg-white/60 rounded-lg">
              <div className="flex items-center gap-3">
                <Download className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="font-medium">تثبيت التطبيق</div>
                  <div className="text-sm text-muted-foreground">
                    {isInstallable ? 'متاح للتثبيت' : 'مثبت بالفعل'}
                  </div>
                </div>
              </div>
              <Button 
                variant={isInstallable ? "default" : "outline"}
                size="sm"
                onClick={installPWA}
                disabled={!isInstallable}
              >
                {isInstallable ? 'تثبيت' : 'مثبت'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Bell className="h-5 w-5" />
            إعدادات التنبيهات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-white/60 rounded-lg">
              <div>
                <div className="font-medium">التنبيهات الفورية</div>
                <div className="text-sm text-muted-foreground">
                  احصل على تنبيهات فورية عن تحركات السوق والأخبار المهمة
                </div>
              </div>
              <Button 
                variant={notificationsEnabled ? "default" : "outline"}
                onClick={enableNotifications}
                disabled={notificationsEnabled}
              >
                {notificationsEnabled ? 'مفعل' : 'تفعيل'}
              </Button>
            </div>

            {notificationsEnabled && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {[
                  'تحركات الأسعار المهمة',
                  'الأخبار العاجلة',
                  'تنبيهات المحفظة',
                  'تحديثات السوق اليومية'
                ].map((notification, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/80 rounded-lg">
                    <span className="text-sm">{notification}</span>
                    <Badge className="bg-green-500 text-white">مفعل</Badge>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Gesture Demo */}
      <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Eye className="h-5 w-5" />
            تجربة الإيماءات المطورة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div 
            ref={demoRef}
            className="p-8 bg-white/60 rounded-lg border-2 border-dashed border-green-300 text-center min-h-[150px] flex flex-col items-center justify-center touch-manipulation"
          >
            <div className="text-lg font-medium mb-4">جرب الإيماءات هنا</div>
            {gestureDemo ? (
              <Badge className="bg-green-500 text-white text-lg px-4 py-2 animate-pulse">
                {gestureDemo}
              </Badge>
            ) : (
              <div className="text-sm text-muted-foreground">
                اسحب، اضغط لفترة طويلة، أو استخدم القرص للتكبير
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Mobile Gestures Guide */}
      <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-violet-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-purple-800">
            <Eye className="h-5 w-5" />
            دليل اللمس والإيماءات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mobileGestures.map((item, index) => (
              <div key={index} className="p-4 bg-white/60 rounded-lg border border-purple-200">
                <div className="font-medium text-purple-800">{item.gesture}</div>
                <div className="text-sm text-muted-foreground mt-1">{item.action}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-amber-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-800">
            <Settings className="h-5 w-5" />
            إجراءات سريعة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" onClick={shareApp} className="h-16 flex flex-col gap-2">
              <Home className="h-5 w-5" />
              <span className="text-xs">مشاركة التطبيق</span>
            </Button>
            
            <Button variant="outline" onClick={registerServiceWorker} className="h-16 flex flex-col gap-2">
              <WifiOff className="h-5 w-5" />
              <span className="text-xs">تفعيل العمل دون اتصال</span>
            </Button>
            
            <Button variant="outline" className="h-16 flex flex-col gap-2">
              <Settings className="h-5 w-5" />
              <span className="text-xs">إعدادات التطبيق</span>
            </Button>
            
            <Button variant="outline" className="h-16 flex flex-col gap-2">
              <Eye className="h-5 w-5" />
              <span className="text-xs">وضع القراءة</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Offline Features */}
      {!isOnline && (
        <Card className="border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <WifiOff className="h-5 w-5" />
              الميزات المتاحة دون اتصال
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                'عرض البيانات المحفوظة',
                'تحليل المحفظة الشخصية',
                'مراجعة الصفقات السابقة',
                'قراءة الأخبار المحفوظة'
              ].map((feature, index) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-white/60 rounded-lg">
                  <Badge className="bg-orange-500 text-white">متاح</Badge>
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MobileFeatures;
