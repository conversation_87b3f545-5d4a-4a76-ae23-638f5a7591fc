#!/usr/bin/env python3
"""
إنشاء Views محسوبة بسيطة في قاعدة البيانات
Create Simple Computed Views in Database
"""

import os
import sys
from supabase import create_client, Client

# إعداد البيئة
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# إعداد الاتصال بقاعدة البيانات
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Missing Supabase configuration")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("✅ Connected to Supabase successfully")
except Exception as e:
    print(f"❌ Error connecting to Supabase: {e}")
    sys.exit(1)

# استعلامات إنشاء Views بسيطة
views_sql = [
    """
    -- 1. ملخص السوق العام (View بسيط)
    CREATE OR REPLACE VIEW market_summary AS
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(CASE WHEN volume > 0 THEN 1 END) as active_stocks,
        COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
        COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
        COUNT(CASE WHEN change_percent = 0 THEN 1 END) as unchanged,
        ROUND(AVG(change_percent), 2) as avg_change,
        SUM(volume) as total_volume,
        SUM(turnover) as total_turnover,
        MAX(updated_at) as last_updated
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL
      AND current_price IS NOT NULL;
    """,
    
    """
    -- 2. إحصائيات التحليل الفني
    CREATE OR REPLACE VIEW technical_summary AS
    SELECT 
        COUNT(CASE WHEN ma5 > ma20 THEN 1 END) as bullish_stocks,
        COUNT(CASE WHEN ma5 < ma20 THEN 1 END) as bearish_stocks,
        COUNT(CASE WHEN ma5 > ma20 AND ma20 > ma50 THEN 1 END) as strong_uptrend,
        COUNT(CASE WHEN target_1 IS NOT NULL THEN 1 END) as stocks_with_targets,
        COUNT(CASE WHEN speculation_opportunity = true THEN 1 END) as speculation_opportunities,
        ROUND(AVG(CASE WHEN pe_ratio > 0 AND pe_ratio < 100 THEN pe_ratio END), 2) as avg_pe_ratio,
        ROUND(AVG(CASE WHEN dividend_yield > 0 THEN dividend_yield END), 2) as avg_dividend_yield
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL
      AND current_price IS NOT NULL;
    """,
    
    """
    -- 3. أفضل الأسهم أداءً (TOP 10)
    CREATE OR REPLACE VIEW top_gainers AS
    SELECT 
        symbol,
        name,
        current_price,
        change_percent,
        volume,
        turnover
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL
      AND current_price IS NOT NULL
      AND change_percent IS NOT NULL
    ORDER BY change_percent DESC
    LIMIT 10;
    """,
    
    """
    -- 4. أسوأ الأسهم أداءً (TOP 10)
    CREATE OR REPLACE VIEW top_losers AS
    SELECT 
        symbol,
        name,
        current_price,
        change_percent,
        volume,
        turnover
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL
      AND current_price IS NOT NULL
      AND change_percent IS NOT NULL
    ORDER BY change_percent ASC
    LIMIT 10;
    """,
    
    """
    -- 5. الأسهم الأكثر نشاطاً (حجم التداول)
    CREATE OR REPLACE VIEW most_active AS
    SELECT 
        symbol,
        name,
        current_price,
        change_percent,
        volume,
        turnover
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL
      AND current_price IS NOT NULL
      AND volume > 0
    ORDER BY volume DESC
    LIMIT 10;
    """,
    
    """
    -- 6. إحصائيات السيولة
    CREATE OR REPLACE VIEW liquidity_summary AS
    SELECT 
        COUNT(CASE WHEN liquidity_ratio > 1 THEN 1 END) as inflow_stocks,
        COUNT(CASE WHEN liquidity_ratio < 1 THEN 1 END) as outflow_stocks,
        ROUND(AVG(liquidity_ratio), 2) as avg_liquidity_ratio,
        SUM(liquidity_inflow) as total_inflow,
        SUM(liquidity_outflow) as total_outflow,
        SUM(net_liquidity) as net_market_liquidity
    FROM stocks_realtime 
    WHERE symbol NOT LIKE '%EGX%' 
      AND name IS NOT NULL
      AND liquidity_ratio IS NOT NULL;
    """
]

def execute_sql_direct(sql_query):
    """تنفيذ SQL مباشرة باستخدام supabase client"""
    try:
        # محاولة استخدام supabase.rpc إذا كان متاحاً
        result = supabase.rpc('exec_sql', {'sql_query': sql_query}).execute()
        return True, result.data
    except Exception as e:
        # إذا فشل، نحاول طريقة أخرى
        try:
            # استخدام postgrest مباشرة
            import requests
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
                json={"sql_query": sql_query},
                headers={
                    "apikey": SUPABASE_KEY,
                    "Authorization": f"Bearer {SUPABASE_KEY}",
                    "Content-Type": "application/json"
                }
            )
            return response.status_code == 200, response.text
        except Exception as e2:
            return False, str(e2)

def create_views():
    """إنشاء Views في قاعدة البيانات"""
    print("🔧 بدء إنشاء Views المحسوبة...")
    
    success_count = 0
    total_views = len(views_sql)
    
    for i, sql in enumerate(views_sql, 1):
        print(f"📊 إنشاء View {i}/{total_views}...")
        
        success, result = execute_sql_direct(sql)
        
        if success:
            print(f"✅ تم إنشاء View {i} بنجاح")
            success_count += 1
        else:
            print(f"❌ خطأ في إنشاء View {i}: {result}")
    
    print(f"\n🎯 تم إنشاء {success_count}/{total_views} views بنجاح")
    return success_count

def test_views_simple():
    """اختبار Views بطريقة بسيطة"""
    print("\n🧪 اختبار Views المُنشأة...")
    
    test_queries = [
        "market_summary",
        "technical_summary", 
        "top_gainers",
        "top_losers",
        "most_active",
        "liquidity_summary"
    ]
    
    for view_name in test_queries:
        try:
            print(f"🔍 اختبار {view_name}...")
            # استعلام بسيط للتحقق من وجود View
            result = supabase.table(view_name).select("*").limit(1).execute()
            
            if result.data is not None:
                print(f"✅ {view_name} يعمل بشكل صحيح")
            else:
                print(f"⚠️ {view_name} فارغ أو لا يُرجع بيانات")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار {view_name}: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء Views المحسوبة...")
    
    # إنشاء Views
    success_count = create_views()
    
    if success_count > 0:
        print(f"✅ تم إنشاء {success_count} views بنجاح!")
        
        # اختبار Views
        test_views_simple()
        
        print("\n✅ اكتمل إنشاء واختبار Views المحسوبة!")
        print("💡 يمكنك الآن استخدام هذه Views في الـ hooks للحصول على بيانات محسوبة مسبقاً")
    else:
        print("❌ فشل في إنشاء Views")
        return False
    
    return True

if __name__ == "__main__":
    main()
