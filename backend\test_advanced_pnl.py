#!/usr/bin/env python3
"""
Advanced PnL Logic Test Script
Tests the enhanced trailing stop and protected profit logic
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000/api/v1"
WEBHOOK_URL = f"{BASE_URL}/webhook"

def test_advanced_pnl_logic():
    """Test the advanced PnL logic with various scenarios"""
    
    print("🚀 Testing Advanced PnL Logic for Trading Signals")
    print("=" * 60)
    
    # Test Scenario 1: Buy signal followed by TP1, price updates, then sell (protected profit)
    print("\n📊 Scenario 1: Protected Profit with Trailing Stop")
    print("-" * 50)
    
    # Step 1: Send buy signal
    buy_signal = {
        "stock_code": "ADVNCD",
        "report": "buy",
        "buy_price": "100.00",
        "tp1": "110.00",
        "tp2": "120.00", 
        "tp3": "130.00",
        "sl": "95.00"
    }
    
    print("1. Sending buy signal...")
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=buy_signal)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   Response: {response.json()}")
    
    time.sleep(1)
    
    # Step 2: Simulate price movement to TP1
    print("\n2. Sending TP1 achievement...")
    tp1_signal = {
        "stock_code": "ADVNCD",
        "report": "tp1done", 
        "sl": "102.00"  # Move SL to breakeven+
    }
    
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=tp1_signal)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   Response: {response.json()}")
    
    time.sleep(1)
    
    # Step 3: Simulate price going higher (new highest price)
    print("\n3. Updating price to new high...")
    price_update = {
        "stock_code": "ADVNCD",
        "current_price": 115.00,
        "timestamp": datetime.now().isoformat()
    }
    
    response = requests.post(f"{WEBHOOK_URL}/price-update", json=price_update)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   Response: {response.json()}")
    
    time.sleep(1)
    
    # Step 4: Send sell signal at price lower than highest but higher than entry (trailing stop scenario)
    print("\n4. Sending sell signal (trailing stop)...")
    sell_signal = {
        "stock_code": "ADVNCD",
        "report": "sell",
        "sell_price": "108.00"  # Lower than highest (115) but higher than entry (100)
    }
    
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=sell_signal)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   Response: {response.json()}")
    
    time.sleep(2)
    
    # Test Scenario 2: Multiple TPs achieved then stop loss (protected profit)
    print("\n📊 Scenario 2: Multiple TPs then Trailing Stop")
    print("-" * 50)
    
    # Step 1: New buy signal
    buy_signal_2 = {
        "stock_code": "TRAIL",
        "report": "buy",
        "buy_price": "50.00",
        "tp1": "55.00",
        "tp2": "60.00",
        "tp3": "65.00", 
        "sl": "47.00"
    }
    
    print("1. Sending buy signal for TRAIL...")
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=buy_signal_2)
    print(f"   Status: {response.status_code}")
    
    time.sleep(1)
    
    # Step 2: Hit TP1
    tp1_signal_2 = {
        "stock_code": "TRAIL",
        "report": "tp1done",
        "sl": "52.00"
    }
    
    print("2. Achieving TP1...")
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=tp1_signal_2)
    print(f"   Status: {response.status_code}")
    
    time.sleep(1)
    
    # Step 3: Hit TP2
    tp2_signal_2 = {
        "stock_code": "TRAIL", 
        "report": "tp2done",
        "sl": "57.00"
    }
    
    print("3. Achieving TP2...")
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=tp2_signal_2)
    print(f"   Status: {response.status_code}")
    
    time.sleep(1)
    
    # Step 4: Price goes even higher
    price_update_2 = {
        "stock_code": "TRAIL",
        "current_price": 62.00
    }
    
    print("4. Updating to higher price...")
    response = requests.post(f"{WEBHOOK_URL}/price-update", json=price_update_2)
    print(f"   Status: {response.status_code}")
    
    time.sleep(1)
    
    # Step 5: Trailing stop hit (should be protected profit)
    stop_signal = {
        "stock_code": "TRAIL",
        "report": "tsl",
        "sl": "58.00"  # Profit protection stop
    }
    
    print("5. Triggering trailing stop...")
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=stop_signal)
    print(f"   Status: {response.status_code}")
    
    time.sleep(2)
    
    # Test Scenario 3: Regular stop loss (no targets hit)
    print("\n📊 Scenario 3: Regular Stop Loss (No Protection)")
    print("-" * 50)
    
    # Step 1: Buy signal
    buy_signal_3 = {
        "stock_code": "REGULAR",
        "report": "buy", 
        "buy_price": "75.00",
        "tp1": "80.00",
        "tp2": "85.00",
        "tp3": "90.00",
        "sl": "70.00"
    }
    
    print("1. Sending buy signal for REGULAR...")
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=buy_signal_3)
    print(f"   Status: {response.status_code}")
    
    time.sleep(1)
    
    # Step 2: Price drops and hits stop loss (regular loss)
    stop_signal_3 = {
        "stock_code": "REGULAR",
        "report": "tsl",
        "sl": "70.00"
    }
    
    print("2. Triggering regular stop loss...")
    response = requests.post(f"{WEBHOOK_URL}/trading-signals", json=stop_signal_3)
    print(f"   Status: {response.status_code}")
    
    time.sleep(2)
    
    # Check results
    print("\n📋 Checking Results...")
    print("-" * 30)
    
    # Get performance data
    print("Fetching performance metrics...")
    response = requests.get(f"{BASE_URL}/analytics/performance-metrics")
    if response.status_code == 200:
        performance_data = response.json()
        print("✅ Performance Data:")
        print(json.dumps(performance_data, indent=2, ensure_ascii=False))
    
    # Get live recommendations
    print("\nFetching live recommendations...")
    response = requests.get(f"{BASE_URL}/signals/live-recommendations")
    if response.status_code == 200:
        live_data = response.json()
        print("✅ Live Recommendations:")
        print(json.dumps(live_data, indent=2, ensure_ascii=False))
    
    print("\n🎉 Advanced PnL Logic Test Completed!")
    print("=" * 60)

if __name__ == "__main__":
    try:
        test_advanced_pnl_logic()
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
