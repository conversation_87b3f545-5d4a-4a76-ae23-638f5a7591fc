import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3, Globe, Info, TrendingUp, Activity, TrendingDown, ExternalLink } from 'lucide-react';

const TestEGX30Chart = () => {
  const [interval, setInterval] = useState('1D');
  const [theme, setTheme] = useState('light');
  const [showChart, setShowChart] = useState(false);
  
  // محاكاة بيانات السوق الحقيقية
  const [marketData, setMarketData] = useState({
    volume: '2.85B',
    volumeChange: '+12.5%',
    trades: '45,230',
    tradesChange: '+8.2%',
    trend: 'up',
    trendValue: '+1.8%',
    indexValue: '18,450.75',
    indexChange: '+324.50'
  });  // تحديث البيانات كل 30 ثانية (محاكاة)
  useEffect(() => {
    const dataInterval = window.setInterval(() => {
      setMarketData(prev => ({
        ...prev,
        volume: (Math.random() * 3 + 2).toFixed(2) + 'B',
        volumeChange: (Math.random() > 0.5 ? '+' : '-') + (Math.random() * 15 + 5).toFixed(1) + '%',
        trades: (Math.floor(Math.random() * 20000) + 35000).toLocaleString(),
        tradesChange: (Math.random() > 0.5 ? '+' : '-') + (Math.random() * 10 + 3).toFixed(1) + '%',
        trend: Math.random() > 0.4 ? 'up' : 'down',
        trendValue: (Math.random() > 0.4 ? '+' : '-') + (Math.random() * 3 + 0.5).toFixed(1) + '%',
        indexValue: (Math.floor(Math.random() * 2000) + 17000).toLocaleString() + '.75',
        indexChange: (Math.random() > 0.4 ? '+' : '-') + (Math.random() * 500 + 100).toFixed(2)
      }));
    }, 30000);

    return () => {
      if (dataInterval) {
        clearInterval(dataInterval);
      }
    };
  }, []);

  const intervals = [
    { value: '1D', label: 'يومي' },
    { value: '1W', label: 'أسبوعي' },
    { value: '1M', label: 'شهري' },
  ];

  return (
    <div className="w-full">
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-blue-800">
              <BarChart3 className="h-5 w-5" />
              مؤشر EGX30 - نظرة عامة على السوق المصرية
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                <Globe className="h-3 w-3 mr-1" />
                {marketData.indexValue}
              </Badge>
              <Badge variant={marketData.trend === 'up' ? 'default' : 'destructive'} className="text-white">
                {marketData.indexChange}
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Market Summary with Real Data */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-gradient-to-r from-blue-100 to-blue-200 rounded-lg text-center">
              <div className="flex items-center justify-center mb-2">
                <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
                <span className="font-bold text-blue-800">حجم التداول</span>
              </div>
              <div className="text-2xl font-bold text-blue-800 mb-1">{marketData.volume} جنيه</div>
              <div className={`text-sm font-semibold ${marketData.volumeChange.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                {marketData.volumeChange} من أمس
              </div>
            </div>
            
            <div className="p-4 bg-gradient-to-r from-green-100 to-green-200 rounded-lg text-center">
              <div className="flex items-center justify-center mb-2">
                <Activity className="h-5 w-5 text-green-600 mr-2" />
                <span className="font-bold text-green-800">عدد الصفقات</span>
              </div>
              <div className="text-2xl font-bold text-green-800 mb-1">{marketData.trades}</div>
              <div className={`text-sm font-semibold ${marketData.tradesChange.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                {marketData.tradesChange} من أمس
              </div>
            </div>
            
            <div className="p-4 bg-gradient-to-r from-purple-100 to-purple-200 rounded-lg text-center">
              <div className="flex items-center justify-center mb-2">
                {marketData.trend === 'up' ? (
                  <TrendingUp className="h-5 w-5 text-purple-600 mr-2" />
                ) : (
                  <TrendingDown className="h-5 w-5 text-purple-600 mr-2" />
                )}
                <span className="font-bold text-purple-800">الاتجاه العام</span>
              </div>
              <div className={`text-2xl font-bold mb-1 ${marketData.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                {marketData.trend === 'up' ? 'صاعد' : 'هابط'}
              </div>
              <div className={`text-sm font-semibold ${marketData.trendValue.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                {marketData.trendValue} اليوم
              </div>
            </div>
          </div>

          {/* Chart Controls */}
          <div className="flex flex-wrap gap-2 justify-center">
            {intervals.map((int) => (
              <Button
                key={int.value}
                variant={interval === int.value ? "default" : "outline"}
                size="sm"
                onClick={() => setInterval(int.value)}
                className="text-xs"
              >
                {int.label}
              </Button>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
              className="text-xs"
            >
              {theme === 'light' ? '🌙 ليلي' : '☀️ نهاري'}
            </Button>
            <Button
              variant={showChart ? "secondary" : "default"}
              size="sm"
              onClick={() => setShowChart(!showChart)}
              className="text-xs"
            >
              {showChart ? 'إخفاء الشارت' : 'عرض الشارت'}
            </Button>
          </div>

          {/* Chart Section */}
          {showChart ? (
            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
              <div className="h-[500px]">
                <iframe
                  src={`https://www.tradingview.com/embed-widget/advanced-chart/?symbol=EGX30&interval=${interval}&theme=${theme}&style=1&locale=ar&range=1M&timezone=Africa%2FCairo&hide_side_toolbar=false&save_image=true&calendar=true&hide_volume=false&support_host=https%3A%2F%2Fwww.tradingview.com`}
                  width="100%"
                  height="500"
                  frameBorder="0"
                  allowTransparency={true}
                  scrolling="no"
                  className="rounded-lg"
                  title="TradingView EGX30 Chart"
                />
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg p-8 text-center shadow-sm">
              <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">شارت مؤشر EGX30</h3>
                  <p className="text-gray-600 mb-4">اضغط على "عرض الشارت" لإظهار الشارت التفاعلي</p>
                  <div className="flex gap-3 justify-center">
                    <Button 
                      onClick={() => setShowChart(true)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      عرض الشارت التفاعلي
                    </Button>
                    
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
            <h4 className="font-bold text-yellow-800 mb-2">كيفية الاستخدام:</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• اختر الإطار الزمني المناسب (يومي، أسبوعي، شهري)</li>
              <li>• يمكنك تغيير الثيم بين النهاري والليلي</li>
              <li>• اضغط "عرض الشارت" لإظهار الشارت التفاعلي</li>
              <li>• استخدم أدوات الرسم والتحليل المدمجة في الشارت</li>
              <li>• اضغط على TradingView لفتح نافذة مخصصة للتحليل</li>
            </ul>
          </div>

          {/* Market Info */}
          <div className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200">
            <h4 className="font-bold text-indigo-800 mb-3 flex items-center">
              <Info className="h-4 w-4 mr-2" />
              معلومات مهمة عن مؤشر EGX30
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">تعريف المؤشر:</h5>
                <p className="text-indigo-600">المؤشر الرئيسي للبورصة المصرية يضم أكبر 30 شركة من حيث السيولة والنشاط</p>
              </div>
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">ساعات التداول:</h5>
                <p className="text-indigo-600">من 10:00 ص إلى 2:30 م (بتوقيت القاهرة) من الأحد إلى الخميس</p>
              </div>
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">العملة:</h5>
                <p className="text-indigo-600">الجنيه المصري (EGP) - يتأثر بسعر صرف الدولار</p>
              </div>
              <div>
                <h5 className="font-semibold text-indigo-700 mb-1">أهمية المؤشر:</h5>
                <p className="text-indigo-600">مرآة لأداء السوق المصرية ومؤشر رئيسي لاتجاهات الاستثمار</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestEGX30Chart;
