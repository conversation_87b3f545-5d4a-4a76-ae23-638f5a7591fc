
def validate_financial_data():
    """Validate financial data integrity - FIXED VERSION"""
    try:
        logger.info("Validating financial data...")
        
        # Count total records
        response = supabase.table("stocks_financials").select("symbol", count="exact").execute()
        total_count = response.count
        logger.info(f"Total financial records: {total_count}")
        
        # Check for records with essential data - FIXED
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("market_capitalization", "is", "null").execute()
        market_cap_count = response.count
        logger.info(f"Records with market cap data: {market_cap_count}")
        
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("pe_ratio", "is", "null").execute()
        pe_count = response.count
        logger.info(f"Records with P/E ratio data: {pe_count}")
        
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("dividend_yield", "is", "null").execute()
        dividend_count = response.count
        logger.info(f"Records with dividend data: {dividend_count}")
        
        logger.info("Financial data validation completed")
        
    except Exception as e:
        logger.error(f"Error in financial data validation: {e}")
