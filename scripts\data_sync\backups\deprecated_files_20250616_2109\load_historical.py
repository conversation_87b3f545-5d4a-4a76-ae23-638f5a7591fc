
import os
import glob
import pandas as pd
from supabase import create_client, Client
from tqdm import tqdm
import logging
from datetime import datetime
from dotenv import load_dotenv
import sys

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('historical_data_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
DATA_FOLDER = os.getenv("HISTORICAL_DATA_FOLDER", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def validate_and_convert_date(date_str):
    """Convert YYYYMMDD format to YYYY-MM-DD"""
    try:
        if isinstance(date_str, str) and len(date_str) == 8:
            # Format: YYYYMMDD
            year = date_str[:4]
            month = date_str[4:6]
            day = date_str[6:8]
            return f"{year}-{month}-{day}"
        elif isinstance(date_str, (int, float)):
            # Convert to string first
            date_str = str(int(date_str))
            if len(date_str) == 8:
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
                return f"{year}-{month}-{day}"
        
        logger.warning(f"Invalid date format: {date_str}")
        return None
    except Exception as e:
        logger.error(f"Error converting date {date_str}: {e}")
        return None

def batch_upsert_historical(df, symbol):
    """Batch upsert historical data for better performance"""
    batch_size = 100
    total_rows = len(df)
    successful_inserts = 0
    
    for i in tqdm(range(0, total_rows, batch_size), desc=f"Processing {symbol}"):
        batch = df.iloc[i:i+batch_size]
        batch_payload = []
        
        for _, row in batch.iterrows():
            try:
                # Handle different possible column names
                date_col = None
                for col in ['<DTYYYYMMDD>', 'Date', 'DATE', 'date']:
                    if col in row.index:
                        date_col = col
                        break
                
                if not date_col:
                    logger.warning(f"No date column found in row for {symbol}")
                    continue
                
                formatted_date = validate_and_convert_date(row[date_col])
                if not formatted_date:
                    continue
                
                payload = {
                    "symbol": symbol,
                    "date": formatted_date,
                    "open": float(row.get('<OPEN>', row.get('Open', row.get('OPEN', 0)))),
                    "high": float(row.get('<HIGH>', row.get('High', row.get('HIGH', 0)))),
                    "low": float(row.get('<LOW>', row.get('Low', row.get('LOW', 0)))),
                    "close": float(row.get('<CLOSE>', row.get('Close', row.get('CLOSE', 0)))),
                    "volume": int(row.get('<VOL>', row.get('Volume', row.get('VOL', 0)))),
                    "open_interest": int(row.get('<OPENINT>', row.get('OpenInt', 0))),
                }
                batch_payload.append(payload)
                
            except Exception as e:
                logger.error(f"Error processing row for {symbol}: {e}")
                continue
        
        if batch_payload:
            try:
                result = supabase.table("stocks_historical").upsert(batch_payload).execute()
                successful_inserts += len(batch_payload)
                logger.info(f"Inserted {len(batch_payload)} records for {symbol}")
            except Exception as e:
                logger.error(f"Error inserting batch for {symbol}: {e}")
    
    return successful_inserts

def register_stock_master(symbol):
    """Register stock in stocks_master table if not exists"""
    try:
        # Check if stock exists
        existing = supabase.table("stocks_master").select("symbol").eq("symbol", symbol).execute()
        
        if not existing.data:
            # Insert new stock
            payload = {
                "symbol": symbol,
                "name": symbol,  # Default to symbol, can be updated later
                "market": "EGX",
                "is_active": True
            }
            supabase.table("stocks_master").insert(payload).execute()
            logger.info(f"Registered new stock: {symbol}")
            
    except Exception as e:
        logger.error(f"Error registering stock {symbol}: {e}")

def main():
    if not os.path.exists(DATA_FOLDER):
        logger.error(f"Data folder not found: {DATA_FOLDER}")
        return
    
    files = glob.glob(os.path.join(DATA_FOLDER, "*.TXT"))
    
    if not files:
        logger.warning(f"No TXT files found in {DATA_FOLDER}")
        return
    
    logger.info(f"Found {len(files)} historical data files to process")
    
    total_processed = 0
    
    for file in files:
        try:
            # Extract symbol from filename
            filename = os.path.basename(file)
            code = filename.replace(".TXT", "").replace(".txt", "")
            
            # Handle different naming conventions
            if code.endswith("D"):
                symbol = code[:-1]  # Remove 'D' suffix
            else:
                symbol = code
            
            logger.info(f"Processing file: {filename} -> Symbol: {symbol}")
            
            # Register stock in master table
            register_stock_master(symbol)
            
            # Read and process data
            try:
                df = pd.read_csv(file, sep=',')  # Try comma separator first
            except:
                try:
                    df = pd.read_csv(file, sep='\t')  # Try tab separator
                except:
                    df = pd.read_csv(file)  # Default pandas behavior
            
            if df.empty:
                logger.warning(f"Empty dataframe for {symbol}")
                continue
            
            logger.info(f"Loaded {len(df)} rows for {symbol}")
            
            # Process the data
            processed_count = batch_upsert_historical(df, symbol)
            total_processed += processed_count
            
            logger.info(f"Successfully processed {processed_count} records for {symbol}")
            
        except Exception as e:
            logger.error(f"Error processing file {file}: {e}")
            continue
    
    logger.info(f"Historical data sync completed. Total records processed: {total_processed}")

if __name__ == "__main__":
    main()

