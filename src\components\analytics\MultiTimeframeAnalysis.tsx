
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, Clock, TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface TimeframeData {
  timeframe: string;
  trend: 'bullish' | 'bearish' | 'neutral';
  strength: number;
  signal: string;
  price: number;
  volume: number;
}

interface StockAnalysis {
  symbol: string;
  timeframes: TimeframeData[];
  overallTrend: 'bullish' | 'bearish' | 'neutral';
  consensus: number;
}

const MultiTimeframeAnalysis = () => {
  const [analyses, setAnalyses] = useState<StockAnalysis[]>([]);
  const [selectedStock, setSelectedStock] = useState('COMI');

  useEffect(() => {
    const mockData: StockAnalysis[] = [
      {
        symbol: 'COMI',
        overallTrend: 'bullish',
        consensus: 78,
        timeframes: [
          { timeframe: '5م', trend: 'neutral', strength: 45, signal: 'تذبذب', price: 62.30, volume: 1200000 },
          { timeframe: '15م', trend: 'bullish', strength: 65, signal: 'صعود', price: 62.25, volume: 1180000 },
          { timeframe: '1س', trend: 'bullish', strength: 75, signal: 'صعود قوي', price: 62.10, volume: 1150000 },
          { timeframe: '4س', trend: 'bullish', strength: 85, signal: 'اتجاه صاعد', price: 61.80, volume: 1100000 },
          { timeframe: '1ي', trend: 'bullish', strength: 80, signal: 'ثور قوي', price: 60.50, volume: 1000000 },
          { timeframe: '1أ', trend: 'bullish', strength: 75, signal: 'اتجاه صاعد', price: 58.20, volume: 900000 }
        ]
      },
      {
        symbol: 'TALAAT',
        overallTrend: 'bullish',
        consensus: 72,
        timeframes: [
          { timeframe: '5م', trend: 'bullish', strength: 55, signal: 'صعود خفيف', price: 42.15, volume: 800000 },
          { timeframe: '15م', trend: 'bullish', strength: 70, signal: 'صعود', price: 42.10, volume: 780000 },
          { timeframe: '1س', trend: 'bullish', strength: 75, signal: 'صعود قوي', price: 41.95, volume: 750000 },
          { timeframe: '4س', trend: 'neutral', strength: 50, signal: 'تماسك', price: 41.50, volume: 700000 },
          { timeframe: '1ي', trend: 'bullish', strength: 80, signal: 'ثور قوي', price: 40.80, volume: 650000 },
          { timeframe: '1أ', trend: 'bullish', strength: 85, signal: 'اتجاه صاعد', price: 38.90, volume: 600000 }
        ]
      }
    ];
    setAnalyses(mockData);
  }, []);

  const getTrendIcon = (trend: string, strength: number) => {
    const size = "h-4 w-4";
    if (trend === 'bullish') {
      return <TrendingUp className={`${size} text-green-600`} />;
    } else if (trend === 'bearish') {
      return <TrendingDown className={`${size} text-red-600`} />;
    }
    return <Minus className={`${size} text-gray-600`} />;
  };

  const getTrendColor = (trend: string, strength: number) => {
    if (trend === 'bullish') {
      return strength > 70 ? 'bg-green-500' : 'bg-green-400';
    } else if (trend === 'bearish') {
      return strength > 70 ? 'bg-red-500' : 'bg-red-400';
    }
    return 'bg-gray-400';
  };

  const getStrengthColor = (strength: number) => {
    if (strength >= 75) return 'text-green-600';
    if (strength >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const selectedAnalysis = analyses.find(a => a.symbol === selectedStock);

  return (
    <Card className="border-2 border-indigo-200 bg-gradient-to-br from-indigo-50 to-purple-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-indigo-800">
          <Clock className="h-5 w-5" />
          تحليل الأطر الزمنية المتعددة
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Stock Selector */}
        <div className="flex gap-2">
          {analyses.map(analysis => (
            <button
              key={analysis.symbol}
              onClick={() => setSelectedStock(analysis.symbol)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                selectedStock === analysis.symbol
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white border border-indigo-200 hover:bg-indigo-50'
              }`}
            >
              {analysis.symbol}
            </button>
          ))}
        </div>

        {selectedAnalysis && (
          <div className="space-y-6">
            {/* Overall Consensus */}
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                {getTrendIcon(selectedAnalysis.overallTrend, selectedAnalysis.consensus)}
                <span className="text-2xl font-bold">{selectedAnalysis.consensus}%</span>
              </div>
              <Badge className={`${getTrendColor(selectedAnalysis.overallTrend, selectedAnalysis.consensus)} text-white`}>
                {selectedAnalysis.overallTrend === 'bullish' ? 'إجماع صاعد' :
                 selectedAnalysis.overallTrend === 'bearish' ? 'إجماع هابط' : 'إجماع محايد'}
              </Badge>
            </div>

            {/* Timeframe Analysis */}
            <div className="space-y-3">
              <h4 className="font-medium text-indigo-700">تحليل الأطر الزمنية:</h4>
              
              <div className="grid gap-3">
                {selectedAnalysis.timeframes.map((tf, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/60 rounded-lg border border-indigo-200">
                    <div className="flex items-center gap-3">
                      <div className="w-12 text-center font-medium text-indigo-700">
                        {tf.timeframe}
                      </div>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(tf.trend, tf.strength)}
                        <span className="font-medium">{tf.signal}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-sm text-muted-foreground">
                        القوة: <span className={`font-medium ${getStrengthColor(tf.strength)}`}>
                          {tf.strength}%
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        السعر: <span className="font-medium">{tf.price}</span>
                      </div>
                      <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className={`h-full ${getTrendColor(tf.trend, tf.strength)} transition-all duration-300`}
                          style={{ width: `${tf.strength}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Summary Insights */}
            <div className="p-4 bg-indigo-100/50 rounded-lg">
              <h4 className="font-medium text-indigo-700 mb-2">ملخص التحليل:</h4>
              <div className="space-y-1 text-sm">
                <div>• الأطر الزمنية القصيرة تظهر {selectedAnalysis.timeframes.slice(0,2).every(tf => tf.trend === 'bullish') ? 'صعود' : 'تذبذب'}</div>
                <div>• الأطر الزمنية المتوسطة تؤكد الاتجاه {selectedAnalysis.timeframes.slice(2,4).every(tf => tf.trend === 'bullish') ? 'الصاعد' : 'المختلط'}</div>
                <div>• الأطر الزمنية الطويلة تدعم النظرة {selectedAnalysis.overallTrend === 'bullish' ? 'الإيجابية' : 'المحايدة'}</div>
                <div>• إجماع السوق عند {selectedAnalysis.consensus}% {selectedAnalysis.overallTrend === 'bullish' ? 'للصعود' : 'للثبات'}</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MultiTimeframeAnalysis;
