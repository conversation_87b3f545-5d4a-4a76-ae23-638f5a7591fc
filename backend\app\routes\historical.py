from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from datetime import date, datetime, timedelta

from ..database import get_db
from ..models.historical import StockHistorical
from ..models.stocks import StockMaster
from ..models.schemas import ResponseBase

router = APIRouter()


# Pydantic models for historical data
from pydantic import BaseModel, Field
from decimal import Decimal


class HistoricalData(BaseModel):
    """Historical stock data model"""
    symbol: str
    trade_date: date
    open_price: Optional[Decimal] = None
    high_price: Optional[Decimal] = None
    low_price: Optional[Decimal] = None
    close_price: Optional[Decimal] = None
    volume: Optional[int] = None
    turnover: Optional[Decimal] = None
    trades_count: Optional[int] = None
    change_amount: Optional[Decimal] = None
    change_percent: Optional[Decimal] = None
    adjusted_close: Optional[Decimal] = None
    
    class Config:
        from_attributes = True


class OHLCData(BaseModel):
    """OHLC data for charts"""
    date: str  # ISO format for frontend
    open: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    close: Optional[float] = None
    volume: Optional[int] = None


class HistoricalListResponse(ResponseBase):
    """Historical data list response"""
    data: Optional[List[HistoricalData]] = None
    total: Optional[int] = None
    symbol: Optional[str] = None
    name_ar: Optional[str] = None


class OHLCListResponse(ResponseBase):
    """OHLC data list response"""
    data: Optional[List[OHLCData]] = None
    total: Optional[int] = None
    symbol: Optional[str] = None
    name_ar: Optional[str] = None


@router.get("/{symbol}", response_model=HistoricalListResponse)
async def get_historical_data(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date (YYYY-MM-DD)"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=200, description="Items per page"),
    db: Session = Depends(get_db)
):
    """Get historical data for a specific stock"""
    try:
        # Verify stock exists
        stock = db.query(StockMaster).filter(
            StockMaster.symbol == symbol.upper()
        ).first()
        
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        # Build query
        query = db.query(StockHistorical).filter(
            StockHistorical.symbol == symbol.upper()
        )
        
        # Apply date filters
        if start_date:
            query = query.filter(StockHistorical.trade_date >= start_date)
        if end_date:
            query = query.filter(StockHistorical.trade_date <= end_date)
        else:
            # Default to last 30 days if no end date specified
            if not start_date:
                thirty_days_ago = datetime.now().date() - timedelta(days=30)
                query = query.filter(StockHistorical.trade_date >= thirty_days_ago)
        
        # Get total count
        total = query.count()
        
        # Apply pagination and ordering (most recent first)
        offset = (page - 1) * page_size
        historical_data = query.order_by(
            desc(StockHistorical.trade_date)
        ).offset(offset).limit(page_size).all()
        
        # Convert to response format
        data_list = [
            HistoricalData(
                symbol=data.symbol,
                trade_date=data.trade_date,
                open_price=data.open_price,
                high_price=data.high_price,
                low_price=data.low_price,
                close_price=data.close_price,
                volume=data.volume,
                turnover=data.turnover,
                trades_count=data.trades_count,
                change_amount=data.change_amount,
                change_percent=data.change_percent,
                adjusted_close=data.adjusted_close
            )
            for data in historical_data
        ]
        
        return HistoricalListResponse(
            success=True,
            data=data_list,
            total=total,
            symbol=symbol.upper(),
            name_ar=stock.name_ar
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching historical data: {str(e)}")


@router.get("/{symbol}/ohlc", response_model=OHLCListResponse)
async def get_ohlc_data(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date (YYYY-MM-DD)"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    db: Session = Depends(get_db)
):
    """Get OHLC data for charts"""
    try:
        # Verify stock exists
        stock = db.query(StockMaster).filter(
            StockMaster.symbol == symbol.upper()
        ).first()
        
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        # Build query
        query = db.query(StockHistorical).filter(
            StockHistorical.symbol == symbol.upper()
        )
        
        # Apply date filters
        if start_date:
            query = query.filter(StockHistorical.trade_date >= start_date)
        if end_date:
            query = query.filter(StockHistorical.trade_date <= end_date)
        else:
            # Default to last 90 days if no date range specified
            if not start_date:
                ninety_days_ago = datetime.now().date() - timedelta(days=90)
                query = query.filter(StockHistorical.trade_date >= ninety_days_ago)
        
        # Get data ordered by date (oldest first for charts)
        historical_data = query.order_by(
            asc(StockHistorical.trade_date)
        ).limit(limit).all()
        
        # Convert to OHLC format
        ohlc_data = []
        for data in historical_data:
            ohlc_data.append(OHLCData(
                date=data.trade_date.isoformat(),
                open=float(data.open_price) if data.open_price else None,
                high=float(data.high_price) if data.high_price else None,
                low=float(data.low_price) if data.low_price else None,
                close=float(data.close_price) if data.close_price else None,
                volume=data.volume
            ))
        
        return OHLCListResponse(
            success=True,
            data=ohlc_data,
            total=len(ohlc_data),
            symbol=symbol.upper(),
            name_ar=stock.name_ar
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching OHLC data: {str(e)}")


@router.get("/{symbol}/volume", response_model=HistoricalListResponse)
async def get_volume_data(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date (YYYY-MM-DD)"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of records"),
    db: Session = Depends(get_db)
):
    """Get volume data for a specific stock"""
    try:
        # Verify stock exists
        stock = db.query(StockMaster).filter(
            StockMaster.symbol == symbol.upper()
        ).first()
        
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        # Build query
        query = db.query(StockHistorical).filter(
            StockHistorical.symbol == symbol.upper(),
            StockHistorical.volume.isnot(None)
        )
        
        # Apply date filters
        if start_date:
            query = query.filter(StockHistorical.trade_date >= start_date)
        if end_date:
            query = query.filter(StockHistorical.trade_date <= end_date)
        else:
            # Default to last 60 days
            if not start_date:
                sixty_days_ago = datetime.now().date() - timedelta(days=60)
                query = query.filter(StockHistorical.trade_date >= sixty_days_ago)
        
        # Get data ordered by date (most recent first)
        historical_data = query.order_by(
            desc(StockHistorical.trade_date)
        ).limit(limit).all()
        
        # Convert to response format (focus on volume data)
        data_list = [
            HistoricalData(
                symbol=data.symbol,
                trade_date=data.trade_date,
                close_price=data.close_price,
                volume=data.volume,
                turnover=data.turnover,
                trades_count=data.trades_count
            )
            for data in historical_data
        ]
        
        return HistoricalListResponse(
            success=True,
            data=data_list,
            total=len(data_list),
            symbol=symbol.upper(),
            name_ar=stock.name_ar
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching volume data: {str(e)}")
