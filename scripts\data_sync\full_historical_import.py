#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ Full Historical Data Import - Egyptian Stock Market
Complete import system using PostgreSQL direct approach
"""

import pandas as pd
import subprocess
import sys
from pathlib import Path
import time
from datetime import datetime
import json
import threading
from concurrent.futures import ThreadPoolExecutor
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('full_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PostgresDirectImporter:
    """Direct PostgreSQL importer using sudo commands"""
    
    def __init__(self):
        self.stats = {
            'start_time': None,
            'end_time': None,
            'files_processed': 0,
            'files_successful': 0,
            'files_failed': 0,
            'total_records': 0,
            'failed_files': []
        }
        
        self.meta2_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2")
        
    def run_sql(self, sql_command, database='egx_stock_oracle'):
        """Execute SQL command as postgres user"""
        try:
            cmd = ['sudo', '-u', 'postgres', 'psql', '-d', database, '-c', sql_command]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"SQL Error: {e.stderr}")
            return None
    
    def process_txt_file(self, txt_file_path):
        """Process a single TXT file"""
        file_stats = {
            'file': txt_file_path.name,
            'symbol': '',
            'records_processed': 0,
            'records_inserted': 0,
            'status': 'FAILED',
            'error': None
        }
        
        try:
            logger.info(f"📖 Processing {txt_file_path.name}")
            
            # Read file
            df = pd.read_csv(txt_file_path)
            file_stats['records_processed'] = len(df)
            
            if df.empty:
                file_stats['error'] = "Empty file"
                return file_stats
            
            # Extract symbol
            symbol = txt_file_path.stem
            if symbol.endswith('D'):
                symbol = symbol[:-1]
            file_stats['symbol'] = symbol
            
            # Insert symbol into stocks_master
            sql_master = f"""
            INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
            VALUES ('{symbol}', 'سهم {symbol}', '{symbol}', true)
            ON CONFLICT (symbol) DO NOTHING;
            """
            self.run_sql(sql_master)
            
            # Process historical data in batches
            batch_size = 1000
            records_inserted = 0
            
            for start_idx in range(0, len(df), batch_size):
                end_idx = min(start_idx + batch_size, len(df))
                batch_df = df.iloc[start_idx:end_idx]
                
                # Prepare batch insert
                values_list = []
                for _, row in batch_df.iterrows():
                    try:
                        if len(row) >= 9:  # Ensure minimum columns
                            # Parse date
                            date_str = str(row.iloc[2])
                            if len(date_str) == 8 and date_str.isdigit():
                                formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                                
                                # Extract prices
                                open_price = float(row.iloc[4]) if pd.notna(row.iloc[4]) and row.iloc[4] != '' else 0
                                high_price = float(row.iloc[5]) if pd.notna(row.iloc[5]) and row.iloc[5] != '' else 0
                                low_price = float(row.iloc[6]) if pd.notna(row.iloc[6]) and row.iloc[6] != '' else 0
                                close_price = float(row.iloc[7]) if pd.notna(row.iloc[7]) and row.iloc[7] != '' else 0
                                volume = int(float(row.iloc[8])) if pd.notna(row.iloc[8]) and row.iloc[8] != '' else 0
                                
                                # Only insert valid records
                                if all(p > 0 for p in [open_price, high_price, low_price, close_price]):
                                    values_list.append(f"('{symbol}', '{formatted_date}', 'D', {open_price}, {high_price}, {low_price}, {close_price}, {volume})")
                                    
                    except (ValueError, IndexError) as e:
                        continue  # Skip invalid records
                
                # Batch insert if we have valid records
                if values_list:
                    values_str = ',\n'.join(values_list)
                    sql_insert = f"""
                    INSERT INTO stocks_historical (
                        symbol, trade_date, period, open_price, high_price, 
                        low_price, close_price, volume
                    ) VALUES 
                    {values_str}
                    ON CONFLICT (symbol, trade_date, period) DO NOTHING;
                    """
                    
                    result = self.run_sql(sql_insert)
                    if result is not None:
                        records_inserted += len(values_list)
                    
                # Progress update
                progress = (end_idx / len(df)) * 100
                if progress % 25 == 0:  # Log every 25%
                    logger.info(f"  {symbol}: {progress:.0f}% complete ({end_idx}/{len(df)})")
            
            file_stats['records_inserted'] = records_inserted
            file_stats['status'] = 'SUCCESS' if records_inserted > 0 else 'PARTIAL'
            
            logger.info(f"✅ {symbol}: {records_inserted}/{file_stats['records_processed']} records imported")
            
        except Exception as e:
            file_stats['error'] = str(e)
            logger.error(f"❌ Failed {txt_file_path.name}: {e}")
        
        return file_stats
    
    def run_full_import(self):
        """Run the complete import process"""
        self.stats['start_time'] = datetime.now()
        logger.info("🚀 Starting Full Historical Data Import")
        logger.info("💎 Egyptian Stock Market Data Treasure - 321 Stocks × 8 Years")
        logger.info("=" * 80)
        
        # Get all TXT files
        txt_files = list(self.meta2_path.glob("*.TXT"))
        total_files = len(txt_files)
        
        logger.info(f"📁 Found {total_files} TXT files to process")
        
        if not txt_files:
            logger.error("❌ No TXT files found!")
            return False
        
        # Process files sequentially for stability
        successful_files = []
        failed_files = []
        
        for i, txt_file in enumerate(txt_files, 1):
            logger.info(f"\n▶️ Processing file {i}/{total_files}: {txt_file.name}")
            
            file_result = self.process_txt_file(txt_file)
            
            self.stats['files_processed'] += 1
            self.stats['total_records'] += file_result['records_inserted']
            
            if file_result['status'] in ['SUCCESS', 'PARTIAL']:
                self.stats['files_successful'] += 1
                successful_files.append(file_result)
            else:
                self.stats['files_failed'] += 1
                failed_files.append(file_result)
                self.stats['failed_files'].append(file_result['file'])
            
            # Progress report every 50 files
            if i % 50 == 0:
                progress_pct = (i / total_files) * 100
                logger.info(f"📊 Progress: {progress_pct:.1f}% ({i}/{total_files}) - Success: {self.stats['files_successful']}, Failed: {self.stats['files_failed']}")
        
        self.stats['end_time'] = datetime.now()
        duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        
        # Final report
        self.generate_final_report(duration)
        
        # Save detailed results
        self.save_import_results(successful_files, failed_files)
        
        return self.stats['files_successful'] > 0
    
    def generate_final_report(self, duration):
        """Generate comprehensive final report"""
        logger.info("\n" + "=" * 80)
        logger.info("📋 FINAL IMPORT REPORT - Egyptian Stock Market Data Treasure")
        logger.info("=" * 80)
        
        logger.info(f"🕐 Start Time: {self.stats['start_time']}")
        logger.info(f"🕐 End Time: {self.stats['end_time']}")
        logger.info(f"⏱️ Total Duration: {duration:.1f} seconds ({duration/60:.1f} minutes)")
        
        logger.info(f"\n📁 File Processing:")
        logger.info(f"  📊 Total Files: {self.stats['files_processed']}")
        logger.info(f"  ✅ Successful: {self.stats['files_successful']}")
        logger.info(f"  ❌ Failed: {self.stats['files_failed']}")
        logger.info(f"  📈 Success Rate: {(self.stats['files_successful']/self.stats['files_processed']*100):.1f}%")
        
        logger.info(f"\n📊 Data Import:")
        logger.info(f"  📈 Total Records Imported: {self.stats['total_records']:,}")
        
        if duration > 0:
            speed = self.stats['total_records'] / duration
            logger.info(f"  🚀 Import Speed: {speed:.0f} records/second")
        
        # Database verification
        master_count = self.run_sql("SELECT COUNT(*) FROM stocks_master;")
        historical_count = self.run_sql("SELECT COUNT(*) FROM stocks_historical;")
        
        logger.info(f"\n🗄️ Final Database State:")
        logger.info(f"  📋 Stocks Master: {master_count} stocks")
        logger.info(f"  📈 Historical Records: {historical_count} records")
        
        if self.stats['files_failed'] > 0:
            logger.info(f"\n⚠️ Failed Files:")
            for failed_file in self.stats['failed_files']:
                logger.info(f"  - {failed_file}")
        
        # Success determination
        if self.stats['files_successful'] >= 300:  # At least 300 out of 321
            logger.info(f"\n🎉 IMPORT SUCCESSFUL!")
            logger.info(f"💎 Egyptian Stock Market Database is Ready!")
        elif self.stats['files_successful'] > 200:
            logger.info(f"\n✅ IMPORT MOSTLY SUCCESSFUL!")
            logger.info(f"⚠️ Some files failed but core data is imported")
        else:
            logger.info(f"\n❌ IMPORT NEEDS ATTENTION!")
            logger.info(f"🔧 Review failed files and retry")
        
        logger.info("=" * 80)
    
    def save_import_results(self, successful_files, failed_files):
        """Save detailed import results to files"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Save stats
            with open(f'import_stats_{timestamp}.json', 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2, default=str)
            
            # Save successful files details
            with open(f'successful_files_{timestamp}.json', 'w', encoding='utf-8') as f:
                json.dump(successful_files, f, ensure_ascii=False, indent=2, default=str)
            
            # Save failed files details
            if failed_files:
                with open(f'failed_files_{timestamp}.json', 'w', encoding='utf-8') as f:
                    json.dump(failed_files, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"📄 Import results saved with timestamp {timestamp}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")

def main():
    """Main execution function"""
    print("🏛️ Full Historical Data Import - Egyptian Stock Market")
    print("💎 Processing 321 TXT files with ~642,000 records")
    print("=" * 80)
    
    importer = PostgresDirectImporter()
    success = importer.run_full_import()
    
    if success:
        print("\n🎉 Historical data import completed successfully!")
        print("📊 Database is ready for realtime and financial data import")
    else:
        print("\n❌ Import encountered significant issues")
        print("📋 Check logs for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
