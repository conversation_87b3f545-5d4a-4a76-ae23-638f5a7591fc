from fastapi import <PERSON><PERSON><PERSON>, APIRouter, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from .config import settings
import logging

# Import routes
from .routes import stocks, realtime, historical, financial, analytics, webhooks

# Import WebSocket manager
from .services.websocket_manager import signal_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description=settings.DESCRIPTION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Set up CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Create API router
api_router = APIRouter()

# Include route modules
api_router.include_router(
    stocks.router, 
    prefix="/stocks", 
    tags=["Stocks"]
)
api_router.include_router(
    realtime.router, 
    prefix="/realtime", 
    tags=["Realtime Data"]
)
api_router.include_router(
    historical.router, 
    prefix="/historical", 
    tags=["Historical Data"]
)
api_router.include_router(
    financial.router, 
    prefix="/financials", 
    tags=["Financial Data"]
)
api_router.include_router(
    analytics.router, 
    prefix="/analytics", 
    tags=["Analytics"]
)

# Include webhook routes (PRIORITY FEATURE)
api_router.include_router(
    webhooks.router,
    tags=["Webhooks", "Trading Signals"]
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to EGX Stock Oracle API",
        "version": settings.VERSION,
        "docs": "/docs",
        "api": settings.API_V1_STR
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": settings.VERSION}


@app.websocket("/ws/{channel}")
async def websocket_endpoint(websocket: WebSocket, channel: str):
    """
    WebSocket endpoint for real-time communication
    
    Channels:
    - signals: Trading signals
    - market: Market data updates
    - notifications: User notifications
    - alerts: Price and technical alerts
    """
    try:
        await signal_manager.connect(websocket, [channel])
        
        logger.info(f"WebSocket connected to channel: {channel}")
        
        while True:
            try:
                # Receive message from client
                message = await websocket.receive_text()
                await signal_manager.handle_client_message(websocket, message)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected from channel: {channel}")
                break
            except Exception as e:
                logger.error(f"Error in WebSocket communication: {str(e)}")
                await signal_manager.send_personal_message(websocket, {
                    'type': 'error',
                    'message': 'Communication error occurred'
                })
                
    except Exception as e:
        logger.error(f"Error in WebSocket endpoint: {str(e)}")
    finally:
        signal_manager.disconnect(websocket)


@app.websocket("/ws")
async def websocket_default(websocket: WebSocket):
    """Default WebSocket endpoint (connects to signals channel)"""
    await websocket_endpoint(websocket, 'signals')


@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("🚀 EGX Stock Oracle API starting up...")
    logger.info(f"📊 Version: {settings.VERSION}")
    logger.info(f"🔗 API Base URL: {settings.API_V1_STR}")
    logger.info("🎯 Trading Signals Webhook System: ACTIVE")
    logger.info("📡 WebSocket Manager: ACTIVE")
    
    # Test WebSocket manager
    connection_stats = signal_manager.get_channel_stats()
    logger.info(f"📊 WebSocket Channels: {connection_stats}")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("🛑 EGX Stock Oracle API shutting down...")
    
    # Cleanup connections
    for channel in signal_manager.active_connections:
        for connection in list(signal_manager.active_connections[channel]):
            signal_manager.disconnect(connection)
    
    logger.info("✅ Cleanup completed")
