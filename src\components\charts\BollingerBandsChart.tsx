import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface BollingerBandsChartProps {
  data: Array<{ 
    date: string; 
    price: number; 
    bb_upper: number; 
    bb_middle: number; 
    bb_lower: number 
  }>;
}

const BollingerBandsChart: React.FC<BollingerBandsChartProps> = React.memo(({ data }) => {
  // Memoize formatters to prevent recreation on each render
  const tickFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG', {
      month: 'short',
      day: 'numeric'
    });
  }, []);

  const labelFormatter = React.useCallback((value: string) => {
    return new Date(value).toLocaleDateString('ar-EG');
  }, []);

  const tooltipFormatter = React.useCallback((value: number, name: string) => {
    const labels: Record<string, string> = {
      'bb_upper': 'النطاق العلوي',
      'bb_middle': 'النطاق الأوسط',
      'bb_lower': 'النطاق السفلي',
      'price': 'السعر'
    };
    return [value.toFixed(2), labels[name] || name];
  }, []);

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tick={{ fontSize: 12 }}
          tickFormatter={tickFormatter}
          stroke="#666"
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <Tooltip 
          labelFormatter={labelFormatter}
          formatter={tooltipFormatter}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <Legend />
        
        {/* Upper Bollinger Band */}
        <Line 
          type="monotone" 
          dataKey="bb_upper" 
          stroke="#ff7300" 
          strokeWidth={1}
          dot={false}
          name="النطاق العلوي"
          connectNulls={false}
        />
        
        {/* Middle Band (SMA) */}
        <Line 
          type="monotone" 
          dataKey="bb_middle" 
          stroke="#8884d8" 
          strokeWidth={2}
          dot={false}
          name="النطاق الأوسط"
          connectNulls={false}
        />
        
        {/* Lower Bollinger Band */}
        <Line 
          type="monotone" 
          dataKey="bb_lower" 
          stroke="#82ca9d" 
          strokeWidth={1}
          dot={false}
          name="النطاق السفلي"
          connectNulls={false}
        />
        
        {/* Current Price */}
        <Line 
          type="monotone" 
          dataKey="price" 
          stroke="#000000" 
          strokeWidth={2}
          dot={false}
          name="السعر"
          connectNulls={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

BollingerBandsChart.displayName = 'BollingerBandsChart';

export default BollingerBandsChart;
