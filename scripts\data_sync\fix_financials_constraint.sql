-- Add unique constraint to stocks_financials symbol column
-- Execute this in Supabase SQL editor

-- First, remove any duplicate symbols if they exist
DELETE FROM stocks_financials a USING (
  SELECT MIN(ctid) as ctid, symbol
  FROM stocks_financials 
  GROUP BY symbol 
  HAVING COUNT(*) > 1
) b
WHERE a.symbol = b.symbol 
AND a.ctid <> b.ctid;

-- Add unique constraint on symbol
ALTER TABLE stocks_financials 
ADD CONSTRAINT stocks_financials_symbol_unique 
UNIQUE (symbol);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_stocks_financials_symbol_unique 
ON stocks_financials(symbol);
