
import { useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

export function useEnsureUserInUsersTable(user: { id: string; email: string } | null) {
  useEffect(() => {
    async function ensureUser() {
      if (!user?.id || !user?.email) return;
      // تحقق هل المستخدم موجود فعلاً في جدول users
      const { data, error } = await supabase
        .from("users")
        .select("id")
        .eq("id", user.id)
        .maybeSingle();

      if (!data) {
        // المستخدم غير موجود - أضفه
        await supabase.from("users").insert([
          {
            id: user.id,
            email: user.email,
            // أضف أي قيم افتراضية أخرى هنا حسب جداولك مثلاً country, etc
          },
        ]);
      }
    }
    ensureUser();
  }, [user]);
}
