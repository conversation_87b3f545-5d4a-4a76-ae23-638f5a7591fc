
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { DollarSign, TrendingUp, TrendingDown, Sparkles, Zap } from 'lucide-react';

interface PortfolioSummaryProps {
  totalValue: number;
  totalProfitLoss: number;
  totalProfitLossPercent: number;
  portfolioLength: number;
}

const PortfolioSummary = ({ 
  totalValue, 
  totalProfitLoss, 
  totalProfitLossPercent, 
  portfolioLength 
}: PortfolioSummaryProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50 card-hover interactive-card relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-100/30 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
        <CardContent className="p-6 relative z-10">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign className="h-5 w-5 text-blue-600 animate-pulse" />
            <Sparkles className="h-4 w-4 text-blue-400" />
            <span className="font-semibold text-blue-800">القيمة الإجمالية</span>
          </div>
          <div className="text-2xl font-bold text-blue-900 text-gradient-primary">
            {totalValue.toLocaleString()} ج.م
          </div>
          <div className="w-full h-1 bg-blue-200 rounded-full mt-2 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full animate-shimmer"></div>
          </div>
        </CardContent>
      </Card>

      <Card className={`border-2 card-hover interactive-card relative overflow-hidden ${totalProfitLoss >= 0 ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50' : 'border-red-200 bg-gradient-to-br from-red-50 to-rose-50'}`}>
        <div className={`absolute inset-0 bg-gradient-to-r from-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 ${totalProfitLoss >= 0 ? 'via-green-100/30' : 'via-red-100/30'} to-transparent`}></div>
        <CardContent className="p-6 relative z-10">
          <div className="flex items-center gap-2 mb-2">
            {totalProfitLoss >= 0 ? 
              <TrendingUp className="h-5 w-5 text-green-600 animate-pulse" /> : 
              <TrendingDown className="h-5 w-5 text-red-600 animate-pulse" />
            }
            <Zap className="h-4 w-4 text-yellow-400" />
            <span className={`font-semibold ${totalProfitLoss >= 0 ? 'text-green-800' : 'text-red-800'}`}>
              الربح/الخسارة
            </span>
          </div>
          <div className={`text-2xl font-bold mb-2 ${totalProfitLoss >= 0 ? 'text-green-900 text-gradient-primary' : 'text-red-900'}`}>
            {totalProfitLoss >= 0 ? '+' : ''}{totalProfitLoss.toLocaleString()} ج.م
          </div>
          <div className={`text-sm flex items-center gap-1 ${totalProfitLoss >= 0 ? 'text-green-700' : 'text-red-700'}`}>
            <span>({totalProfitLossPercent >= 0 ? '+' : ''}{totalProfitLossPercent.toFixed(2)}%)</span>
            {Math.abs(totalProfitLossPercent) > 5 && <Sparkles className="h-3 w-3 animate-pulse" />}
          </div>
          <div className="w-full h-1 bg-gray-200 rounded-full mt-2 overflow-hidden">
            <div className={`h-full rounded-full transition-all duration-1000 ${totalProfitLoss >= 0 ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-red-400 to-red-600'}`}
                 style={{ width: `${Math.min(100, Math.abs(totalProfitLossPercent) * 10)}%` }}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-violet-50 card-hover interactive-card relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-100/30 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
        <CardContent className="p-6 relative z-10">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="h-5 w-5 text-purple-600 animate-pulse" />
            <Sparkles className="h-4 w-4 text-purple-400" />
            <span className="font-semibold text-purple-800">عدد الأسهم</span>
          </div>
          <div className="text-2xl font-bold text-purple-900 text-gradient-magical">
            {portfolioLength}
          </div>
          <div className="w-full h-1 bg-purple-200 rounded-full mt-2 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-purple-400 to-purple-600 rounded-full animate-shimmer"></div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PortfolioSummary;
