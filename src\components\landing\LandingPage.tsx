import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ArrowRight,
  BarChart3,
  TrendingUp,
  Shield,
  Zap,
  Brain,
  Smartphone,
  Globe,
  Star,
  Users,
  Award,
  CheckCircle,
  Play,
  Download,
  MessageCircle,
  Phone,
  Mail,
  MapPin,
  Sparkles,
  Target,
  Eye,
  PieChart,
  Activity,
  Lock,
  Wifi,
  Clock,
  DollarSign,
  LineChart,
  Bell,
  Settings,
  ChevronRight,
  ChevronDown,
  Menu,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface LandingPageProps {
  onEnterApp: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onEnterApp }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  // تغيير الميزة النشطة تلقائياً
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % 4); // 4 features
    }, 4000);
    return () => clearInterval(interval);
  }, []);
  // تغيير التوصية تلقائياً
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % 3); // 3 testimonials
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: Activity,
      title: "بيانات فورية",
      description: "تحديثات لحظية لجميع أسهم البورصة المصرية",
      details: "تحديث كل ثانية • أكثر من 200 شركة • بيانات موثوقة 100%"
    },
    {
      icon: Brain,
      title: "ذكاء اصطناعي",
      description: "تحليلات وتوصيات مدعومة بالذكاء الاصطناعي",
      details: "تحليل متقدم • توقعات دقيقة • قرارات ذكية"
    },
    {
      icon: PieChart,
      title: "تحليل شامل",
      description: "أدوات تحليل فني وأساسي متقدمة",
      details: "50+ مؤشر تقني • تحليل أساسي • رسوم بيانية تفاعلية"
    },
    {
      icon: Shield,
      title: "أمان متقدم",
      description: "حماية عالية لبياناتك الاستثمارية",
      details: "تشفير 256-bit • مصادقة ثنائية • نسخ احتياطية آمنة"
    }
  ];
  const statistics = [
    { number: "200+", label: "شركة مدرجة", icon: BarChart3 },
    { number: "50K+", label: "مستثمر نشط", icon: Users },
    { number: "99.9%", label: "وقت التشغيل", icon: Zap },
    { number: "24/7", label: "دعم فني", icon: Clock }
  ];

  const testimonials = [
    {
      name: "أحمد محمد",
      role: "مستثمر محترف",
      content: "التطبيق غير حياتي الاستثمارية! البيانات الفورية والتحليلات الذكية ساعدتني في اتخاذ قرارات أفضل.",
      rating: 5,
      image: "👨‍💼"
    },
    {
      name: "فاطمة علي",
      role: "محللة مالية",
      content: "أدوات التحليل الفني المتقدمة والواجهة السهلة جعلت عملي أكثر فعالية ودقة.",
      rating: 5,
      image: "👩‍💼"
    },
    {
      name: "محمود سعد",
      role: "مدير محفظة",
      content: "التنبيهات الذكية والتحديثات الفورية ساعدتني في إدارة محافظ عملائي بكفاءة عالية.",
      rating: 5,
      image: "👨‍💻"
    }
  ];

  const pricingPlans = [
    {
      name: "المجاني",
      price: "0",
      period: "مجاناً إلى الأبد",
      features: [
        "بيانات أساسية للأسهم",
        "تحديثات كل 15 دقيقة",
        "3 تنبيهات شهرية",
        "تحليل أساسي محدود"
      ],
      highlighted: false
    },
    {
      name: "الاحترافي",
      price: "99",
      period: "شهرياً",
      features: [
        "بيانات فورية لحظية",
        "تحليلات ذكاء اصطناعي",
        "تنبيهات غير محدودة",
        "أدوات تحليل متقدمة",
        "دعم أولوية",
        "تقارير مخصصة"
      ],
      highlighted: true
    },
    {
      name: "المؤسسي",
      price: "299",
      period: "شهرياً",
      features: [
        "جميع ميزات الاحترافي",
        "API مخصص",
        "تدريب شخصي",
        "تكامل مخصص",
        "دعم 24/7",
        "استشارات استثمارية"
      ],
      highlighted: false
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50" dir="rtl">
      {/* Navigation Header */}
      <nav className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled ? "bg-white/95 backdrop-blur-lg shadow-lg" : "bg-transparent"
      )}>
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-egx-gold-500 to-egx-gold-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">EGX Oracle</h1>
                <p className="text-sm text-gray-600">ذكاء البورصة المصرية</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-8">
              <a href="#features" className="text-gray-700 hover:text-egx-gold-600 transition-colors">الميزات</a>
              <a href="#pricing" className="text-gray-700 hover:text-egx-gold-600 transition-colors">الأسعار</a>
              <a href="#testimonials" className="text-gray-700 hover:text-egx-gold-600 transition-colors">التوصيات</a>
              <a href="#contact" className="text-gray-700 hover:text-egx-gold-600 transition-colors">تواصل معنا</a>
            </div>

            {/* CTA Buttons */}
            <div className="hidden md:flex items-center gap-3">
              <Button variant="outline" onClick={onEnterApp}>
                تسجيل الدخول
              </Button>
              <Button 
                onClick={onEnterApp}
                className="bg-gradient-to-r from-egx-gold-500 to-egx-gold-600 hover:from-egx-gold-600 hover:to-egx-gold-700"
              >
                ابدأ مجاناً
                <ArrowRight className="h-4 w-4 mr-2" />
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden mt-4 pb-4 border-t border-gray-200">
              <div className="flex flex-col gap-4 pt-4">
                <a href="#features" className="text-gray-700 hover:text-egx-gold-600">الميزات</a>
                <a href="#pricing" className="text-gray-700 hover:text-egx-gold-600">الأسعار</a>
                <a href="#testimonials" className="text-gray-700 hover:text-egx-gold-600">التوصيات</a>
                <a href="#contact" className="text-gray-700 hover:text-egx-gold-600">تواصل معنا</a>
                <div className="flex flex-col gap-2 pt-2">
                  <Button variant="outline" onClick={onEnterApp} className="w-full">
                    تسجيل الدخول
                  </Button>
                  <Button onClick={onEnterApp} className="w-full bg-gradient-to-r from-egx-gold-500 to-egx-gold-600">
                    ابدأ مجاناً
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-20 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge className="bg-egx-gold-100 text-egx-gold-800 hover:bg-egx-gold-200">
                  <Sparkles className="h-3 w-3 ml-1" />
                  أحدث تقنيات الذكاء الاصطناعي
                </Badge>
                <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  مستقبل 
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-egx-gold-500 to-egx-gold-600"> الاستثمار</span>
                  <br />
                  في البورصة المصرية
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  منصة شاملة مدعومة بالذكاء الاصطناعي تقدم بيانات فورية، تحليلات متقدمة، وتوصيات ذكية 
                  لتحقيق أقصى عائد من استثماراتك في البورصة المصرية.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg"
                  onClick={onEnterApp}
                  className="bg-gradient-to-r from-egx-gold-500 to-egx-gold-600 hover:from-egx-gold-600 hover:to-egx-gold-700 text-lg px-8 py-6"
                >
                  ابدأ الآن مجاناً
                  <ArrowRight className="h-5 w-5 mr-2" />
                </Button>
                <Button 
                  size="lg" 
                  variant="outline"
                  className="text-lg px-8 py-6 border-2"
                >
                  <Play className="h-5 w-5 ml-2" />
                  شاهد العرض التوضيحي
                </Button>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 pt-8">
                {statistics.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl font-bold text-egx-gold-600">{stat.number}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Visual */}
            <div className="relative">
              <div className="relative z-10 bg-white rounded-2xl shadow-2xl p-6 border">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-bold text-lg">لوحة التحكم المباشرة</h3>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm text-green-600">متصل</span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <TrendingUp className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium">EGX30</span>
                        </div>
                        <div className="text-2xl font-bold text-green-600">18,450</div>
                        <div className="text-sm text-green-600">+2.3% (+420.5)</div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <BarChart3 className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium">الحجم</span>
                        </div>
                        <div className="text-2xl font-bold">2.1B</div>
                        <div className="text-sm text-gray-600">ج.م</div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                      <span className="text-sm font-medium">ETEL</span>
                      <span className="text-sm text-green-600">****%</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-red-50 rounded">
                      <span className="text-sm font-medium">CIB</span>
                      <span className="text-sm text-red-600">-1.8%</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                      <span className="text-sm font-medium">HRHO</span>
                      <span className="text-sm text-green-600">****%</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Background Effects */}
              <div className="absolute -top-4 -left-4 w-72 h-72 bg-egx-gold-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
              <div className="absolute -bottom-8 -right-4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              ميزات تقودك لقرارات استثمارية أذكى
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              استكشف مجموعة شاملة من الأدوات والميزات المتطورة التي تضعك في المقدمة
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Features List */}
            <div className="space-y-4">
              {features.map((feature, index) => (
                <Card 
                  key={index}
                  className={cn(
                    "p-6 cursor-pointer transition-all duration-300 border-2",
                    activeFeature === index 
                      ? "border-egx-gold-300 bg-egx-gold-50 shadow-lg" 
                      : "border-gray-200 hover:border-gray-300"
                  )}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className="flex items-start gap-4">
                    <div className={cn(
                      "w-12 h-12 rounded-lg flex items-center justify-center",
                      activeFeature === index 
                        ? "bg-egx-gold-500 text-white" 
                        : "bg-gray-100 text-gray-600"
                    )}>
                      <feature.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h3>
                      <p className="text-gray-600 mb-2">{feature.description}</p>
                      <p className="text-sm text-egx-gold-600 font-medium">{feature.details}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Feature Showcase */}
            <div className="relative">
              <Card className="p-8 bg-gradient-to-br from-gray-900 to-gray-800 text-white">
                <div className="space-y-6">                  <div className="flex items-center gap-3">
                    {React.createElement(features[activeFeature].icon, { 
                      className: "h-8 w-8 text-egx-gold-400" 
                    })}
                    <h3 className="text-2xl font-bold">{features[activeFeature].title}</h3>
                  </div>
                  
                  <p className="text-gray-300 text-lg leading-relaxed">
                    {features[activeFeature].description}
                  </p>

                  {/* Mock Interface based on active feature */}
                  <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                    {activeFeature === 0 && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-green-400">LIVE</span>
                          <span className="text-sm text-gray-400">آخر تحديث: الآن</span>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <div className="text-2xl font-bold text-green-400">+2.5%</div>
                            <div className="text-xs text-gray-400">EGX30</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-red-400">-1.2%</div>
                            <div className="text-xs text-gray-400">EGX70</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-green-400">+0.8%</div>
                            <div className="text-xs text-gray-400">EGX100</div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {activeFeature === 1 && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Brain className="h-5 w-5 text-blue-400" />
                          <span className="text-blue-400">توصية ذكية</span>
                        </div>
                        <div className="bg-green-900/50 p-3 rounded border-r-4 border-green-400">
                          <div className="font-bold text-green-400">شراء قوي - ETEL</div>
                          <div className="text-sm text-gray-300">الهدف: 28.50 ج.م (+15%)</div>
                          <div className="text-xs text-gray-400">دقة التوقعات: 89%</div>
                        </div>
                      </div>
                    )}
                    
                    {activeFeature === 2 && (
                      <div className="space-y-3">
                        <div className="text-center">
                          <div className="text-4xl">📊</div>
                          <div className="text-sm text-gray-400">رسم بياني تفاعلي</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="bg-gray-700 p-2 rounded">RSI: 65</div>
                          <div className="bg-gray-700 p-2 rounded">MACD: صعود</div>
                          <div className="bg-gray-700 p-2 rounded">MA20: دعم</div>
                          <div className="bg-gray-700 p-2 rounded">Vol: عالي</div>
                        </div>
                      </div>
                    )}
                    
                    {activeFeature === 3 && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Shield className="h-5 w-5 text-green-400" />
                          <span className="text-green-400">محمي بالكامل</span>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">تشفير SSL</span>
                            <CheckCircle className="h-4 w-4 text-green-400" />
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">مصادقة ثنائية</span>
                            <CheckCircle className="h-4 w-4 text-green-400" />
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">نسخ احتياطية</span>
                            <CheckCircle className="h-4 w-4 text-green-400" />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              ماذا يقول عملاؤنا
            </h2>
            <p className="text-xl text-gray-600">
              تجارب حقيقية من مستثمرين حققوا نجاحات مع منصتنا
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Card className="p-8 bg-white shadow-xl">
              <div className="text-center space-y-6">
                <div className="text-6xl">{testimonials[currentTestimonial].image}</div>
                
                <div className="flex justify-center gap-1">
                  {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-egx-gold-400 text-egx-gold-400" />
                  ))}
                </div>
                
                <blockquote className="text-xl text-gray-700 leading-relaxed">
                  "{testimonials[currentTestimonial].content}"
                </blockquote>
                
                <div>
                  <div className="font-bold text-lg text-gray-900">
                    {testimonials[currentTestimonial].name}
                  </div>
                  <div className="text-gray-600">
                    {testimonials[currentTestimonial].role}
                  </div>
                </div>
              </div>
            </Card>            {/* Testimonial Navigation */}
            <div className="flex justify-center gap-2 mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={cn(
                    "w-3 h-3 rounded-full transition-colors",
                    currentTestimonial === index ? "bg-egx-gold-500" : "bg-gray-300"
                  )}
                  aria-label={`عرض التوصية ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              خطط تناسب جميع المستثمرين
            </h2>
            <p className="text-xl text-gray-600">
              اختر الخطة التي تناسب احتياجاتك الاستثمارية
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <Card 
                key={index}
                className={cn(
                  "relative p-8 transition-all duration-300",
                  plan.highlighted 
                    ? "border-2 border-egx-gold-300 shadow-xl scale-105" 
                    : "border border-gray-200 hover:shadow-lg"
                )}
              >
                {plan.highlighted && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-egx-gold-500 text-white">
                    الأكثر شعبية
                  </Badge>
                )}
                
                <div className="text-center space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                  <div>
                    <span className="text-4xl font-bold text-egx-gold-600">{plan.price}</span>
                    <span className="text-gray-600 mr-2">ج.م</span>
                  </div>
                  <p className="text-gray-600">{plan.period}</p>
                </div>

                <div className="space-y-4 mt-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <Button 
                  className={cn(
                    "w-full mt-8",
                    plan.highlighted 
                      ? "bg-gradient-to-r from-egx-gold-500 to-egx-gold-600 hover:from-egx-gold-600 hover:to-egx-gold-700" 
                      : "bg-gray-900 hover:bg-gray-800"
                  )}
                  onClick={onEnterApp}
                >
                  {plan.name === "المجاني" ? "ابدأ مجاناً" : "اشترك الآن"}
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-egx-gold-500 to-egx-gold-600">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-4xl lg:text-5xl font-bold text-white">
              ابدأ رحلتك الاستثمارية اليوم
            </h2>
            <p className="text-xl text-egx-gold-100">
              انضم لآلاف المستثمرين الذين يثقون في منصتنا لاتخاذ قرارات استثمارية ذكية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg"
                onClick={onEnterApp}
                className="bg-white text-egx-gold-600 hover:bg-gray-100 text-lg px-8 py-6"
              >
                ادخل للمنصة الآن
                <ArrowRight className="h-5 w-5 mr-2" />
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="border-2 border-white text-white hover:bg-white hover:text-egx-gold-600 text-lg px-8 py-6"
              >
                تحميل التطبيق
                <Download className="h-5 w-5 mr-2" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-egx-gold-500 to-egx-gold-600 rounded-lg flex items-center justify-center">
                  <BarChart3 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">EGX Oracle</h3>
                  <p className="text-gray-400 text-sm">ذكاء البورصة المصرية</p>
                </div>
              </div>
              <p className="text-gray-400 leading-relaxed">
                منصة شاملة مدعومة بالذكاء الاصطناعي للاستثمار الذكي في البورصة المصرية
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-bold text-lg mb-4">روابط سريعة</h4>
              <div className="space-y-2">
                <a href="#features" className="block text-gray-400 hover:text-white transition-colors">الميزات</a>
                <a href="#pricing" className="block text-gray-400 hover:text-white transition-colors">الأسعار</a>
                <a href="#testimonials" className="block text-gray-400 hover:text-white transition-colors">التوصيات</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">الدعم</a>
              </div>
            </div>

            {/* Services */}
            <div>
              <h4 className="font-bold text-lg mb-4">خدماتنا</h4>
              <div className="space-y-2">
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">تحليل الأسهم</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">إدارة المحافظ</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">التنبيهات الذكية</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">الاستشارات</a>
              </div>
            </div>

            {/* Contact */}
            <div>
              <h4 className="font-bold text-lg mb-4">تواصل معنا</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-egx-gold-400" />
                  <span className="text-gray-400"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-egx-gold-400" />
                  <span className="text-gray-400">+20 ************</span>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-egx-gold-400" />
                  <span className="text-gray-400">القاهرة، مصر</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 text-center">
            <p className="text-gray-400">
              جميع الحقوق محفوظة © 2025 EGX Oracle. تم التطوير بـ ❤️ في مصر
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
