"""
Smart Portfolio - Advanced Technical Analysis Functions
وظائف التحليل الفني المتقدم للمحفظة الذكية
"""

import numpy as np
import pandas as pd
from typing import Dict, List
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class AdvancedTechnicalAnalyzer:
    """محلل التحليل الفني المتقدم"""
    
    @staticmethod
    async def perform_advanced_technical_analysis(df: pd.DataFrame) -> Dict:
        """التحليل الفني المتقدم للسهم"""
        try:
            if df.empty or len(df) < 20:
                return {'error': 'Insufficient data for analysis', 'overall_score': 0}
            
            # حساب المؤشرات الفنية المتقدمة
            indicators = {}
            
            # 1. Moving Averages (المتوسطات المتحركة)
            df['MA10'] = df['close'].rolling(window=10).mean()
            df['MA20'] = df['close'].rolling(window=20).mean()
            df['MA50'] = df['close'].rolling(window=min(50, len(df))).mean()
            
            # 2. RSI (مؤشر القوة النسبية)
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # 3. MACD
            df['EMA12'] = df['close'].ewm(span=12).mean()
            df['EMA26'] = df['close'].ewm(span=26).mean()
            df['MACD'] = df['EMA12'] - df['EMA26']
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
            
            # 4. Bollinger Bands
            df['BB_Middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
            
            # 5. Stochastic Oscillator
            low_14 = df['low'].rolling(window=14).min()
            high_14 = df['high'].rolling(window=14).max()
            df['%K'] = 100 * ((df['close'] - low_14) / (high_14 - low_14))
            df['%D'] = df['%K'].rolling(window=3).mean()
            
            # التحليل الحالي
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # تحليل الاتجاه
            trend_signals = []
            if current['close'] > current['MA10'] > current['MA20']:
                trend_signals.append('BULLISH_MA_ALIGNMENT')
            if current['close'] < current['MA10'] < current['MA20']:
                trend_signals.append('BEARISH_MA_ALIGNMENT')
                
            # تحليل RSI
            rsi_signals = []
            if current['RSI'] < 30:
                rsi_signals.append('OVERSOLD')
            elif current['RSI'] > 70:
                rsi_signals.append('OVERBOUGHT')
            elif 40 <= current['RSI'] <= 60:
                rsi_signals.append('NEUTRAL')
                
            # تحليل MACD
            macd_signals = []
            if current['MACD'] > current['MACD_Signal'] and prev['MACD'] <= prev['MACD_Signal']:
                macd_signals.append('BULLISH_CROSSOVER')
            elif current['MACD'] < current['MACD_Signal'] and prev['MACD'] >= prev['MACD_Signal']:
                macd_signals.append('BEARISH_CROSSOVER')
                
            # تحليل Bollinger Bands
            bb_signals = []
            bb_position = (current['close'] - current['BB_Lower']) / (current['BB_Upper'] - current['BB_Lower'])
            if bb_position < 0.2:
                bb_signals.append('NEAR_LOWER_BAND')
            elif bb_position > 0.8:
                bb_signals.append('NEAR_UPPER_BAND')
                
            # حساب قوة الاتجاه
            price_change_10d = (current['close'] - df['close'].iloc[-10]) / df['close'].iloc[-10] * 100
            volume_trend = df['volume'].rolling(window=5).mean().iloc[-1] / df['volume'].rolling(window=20).mean().iloc[-1]
            
            # حساب النتيجة الإجمالية
            bullish_signals = len([s for s in trend_signals + rsi_signals + macd_signals + bb_signals 
                                 if 'BULLISH' in s or s in ['OVERSOLD', 'NEAR_LOWER_BAND']])
            bearish_signals = len([s for s in trend_signals + rsi_signals + macd_signals + bb_signals 
                                 if 'BEARISH' in s or s in ['OVERBOUGHT', 'NEAR_UPPER_BAND']])
            
            total_signals = bullish_signals + bearish_signals
            overall_score = (bullish_signals / max(total_signals, 1)) * 100
            
            # تحديد قوة الاتجاه
            if overall_score >= 75:
                trend_strength = 'STRONG_BULLISH'
            elif overall_score >= 60:
                trend_strength = 'MODERATE_BULLISH'
            elif overall_score >= 40:
                trend_strength = 'NEUTRAL'
            elif overall_score >= 25:
                trend_strength = 'MODERATE_BEARISH'
            else:
                trend_strength = 'STRONG_BEARISH'
            
            return {
                'overall_score': round(overall_score, 2),
                'trend_strength': trend_strength,
                'current_indicators': {
                    'rsi': round(current['RSI'], 2),
                    'macd': round(current['MACD'], 4),
                    'macd_signal': round(current['MACD_Signal'], 4),
                    'bb_position': round(bb_position * 100, 2),
                    'stoch_k': round(current['%K'], 2),
                    'stoch_d': round(current['%D'], 2)
                },
                'signals': {
                    'trend': trend_signals,
                    'rsi': rsi_signals,
                    'macd': macd_signals,
                    'bollinger': bb_signals
                },
                'momentum': {
                    'price_change_10d': round(price_change_10d, 2),
                    'volume_trend': round(volume_trend, 2),
                    'momentum_score': min(100, max(0, 50 + price_change_10d * 2))
                },
                'bullish_factors': bullish_signals,
                'bearish_factors': bearish_signals
            }
            
        except Exception as e:
            logger.error(f"Error in advanced technical analysis: {e}")
            return {'error': str(e), 'overall_score': 0}
    
    @staticmethod
    async def analyze_liquidity_comprehensive(df: pd.DataFrame) -> Dict:
        """تحليل شامل للسيولة وجودة التداول"""
        try:
            if df.empty:
                return {'liquidity_score': 0, 'quality': 'POOR'}
            
            # حساب متوسط الحجم اليومي
            avg_volume_30d = df['volume'].tail(30).mean()
            avg_volume_7d = df['volume'].tail(7).mean()
            
            # حساب متوسط القيمة المتداولة
            df['value_traded'] = df['close'] * df['volume']
            avg_value_30d = df['value_traded'].tail(30).mean()
            
            # تحليل انتظام التداول
            zero_volume_days = (df['volume'] == 0).sum()
            trading_frequency = (len(df) - zero_volume_days) / len(df) * 100
            
            # تحليل تقلبات الحجم
            volume_volatility = df['volume'].std() / df['volume'].mean() if df['volume'].mean() > 0 else 0
            
            # حساب نتيجة السيولة
            liquidity_factors = {
                'volume_adequacy': min(100, (avg_volume_30d / 100000) * 20),  # 100k كحد أدنى جيد
                'value_adequacy': min(100, (avg_value_30d / 5000000) * 25),   # 5M كحد أدنى ممتاز
                'trading_frequency': trading_frequency,
                'volume_consistency': max(0, 100 - (volume_volatility * 20)),
                'recent_activity': min(100, (avg_volume_7d / avg_volume_30d) * 100)
            }
            
            liquidity_score = sum(liquidity_factors.values()) / len(liquidity_factors)
            
            # تصنيف جودة السيولة
            if liquidity_score >= 80:
                quality = 'EXCELLENT'
                risk_level = 'LOW'
            elif liquidity_score >= 65:
                quality = 'GOOD'
                risk_level = 'LOW'
            elif liquidity_score >= 50:
                quality = 'MODERATE'
                risk_level = 'MEDIUM'
            elif liquidity_score >= 35:
                quality = 'POOR'
                risk_level = 'HIGH'
            else:
                quality = 'VERY_POOR'
                risk_level = 'EXTREME'
            
            return {
                'liquidity_score': round(liquidity_score, 2),
                'quality': quality,
                'risk_level': risk_level,
                'metrics': {
                    'avg_volume_30d': int(avg_volume_30d),
                    'avg_volume_7d': int(avg_volume_7d),
                    'avg_value_30d': int(avg_value_30d),
                    'trading_frequency': round(trading_frequency, 2),
                    'volume_volatility': round(volume_volatility, 2),
                    'zero_volume_days': zero_volume_days
                },
                'factors': liquidity_factors,
                'recommendations': {
                    'max_position_size': min(avg_volume_30d * 0.1, 1000000),  # 10% من الحجم اليومي
                    'execution_style': 'AGGRESSIVE' if quality in ['EXCELLENT', 'GOOD'] else 'CONSERVATIVE'
                }
            }
            
        except Exception as e:
            logger.error(f"Error in liquidity analysis: {e}")
            return {'liquidity_score': 0, 'quality': 'POOR', 'error': str(e)}
    
    @staticmethod
    async def analyze_volatility_advanced(df: pd.DataFrame) -> Dict:
        """تحليل متقدم للتقلبات والمخاطر"""
        try:
            if df.empty or len(df) < 20:
                return {'volatility_score': 50, 'risk_level': 'MEDIUM'}
            
            # حساب العوائد اليومية
            df['returns'] = df['close'].pct_change()
            
            # التقلبات التاريخية
            volatility_30d = df['returns'].tail(30).std() * np.sqrt(252) * 100  # السنوية
            volatility_7d = df['returns'].tail(7).std() * np.sqrt(252) * 100
            
            # Range-based volatility (Garman-Klass)
            if 'high' in df.columns and 'low' in df.columns and 'open' in df.columns:
                gk_vol = np.sqrt(
                    0.5 * np.log(df['high'] / df['low'])**2 - 
                    (2 * np.log(2) - 1) * np.log(df['close'] / df['open'])**2
                ).tail(30).mean() * np.sqrt(252) * 100
            else:
                gk_vol = volatility_30d
            
            # Maximum Drawdown
            cumulative_returns = (1 + df['returns']).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min()) * 100
            
            # Value at Risk (VaR)
            var_95 = abs(np.percentile(df['returns'].dropna(), 5)) * 100
            var_99 = abs(np.percentile(df['returns'].dropna(), 1)) * 100
            
            # حساب Downside Deviation
            negative_returns = df['returns'][df['returns'] < 0]
            downside_deviation = negative_returns.std() * np.sqrt(252) * 100 if len(negative_returns) > 0 else 0
            
            # تصنيف مستوى المخاطر
            volatility_percentile = 0
            if volatility_30d <= 15:
                volatility_percentile = 20
                risk_level = 'LOW'
            elif volatility_30d <= 25:
                volatility_percentile = 40
                risk_level = 'MEDIUM'
            elif volatility_30d <= 40:
                volatility_percentile = 60
                risk_level = 'HIGH'
            elif volatility_30d <= 60:
                volatility_percentile = 80
                risk_level = 'VERY_HIGH'
            else:
                volatility_percentile = 95
                risk_level = 'EXTREME'
            
            # حساب نتيجة الاستقرار (أقل تقلبات = نتيجة أعلى)
            stability_score = max(0, 100 - volatility_30d * 2)
            
            return {
                'volatility_30d': round(volatility_30d, 2),
                'volatility_7d': round(volatility_7d, 2),
                'garman_klass_vol': round(gk_vol, 2),
                'max_drawdown': round(max_drawdown, 2),
                'var_95': round(var_95, 2),
                'var_99': round(var_99, 2),
                'downside_deviation': round(downside_deviation, 2),
                'risk_level': risk_level,
                'volatility_percentile': volatility_percentile,
                'stability_score': round(stability_score, 2),
                'risk_metrics': {
                    'sharpe_estimate': round(5 / volatility_30d, 2) if volatility_30d > 0 else 0,
                    'sortino_estimate': round(5 / downside_deviation, 2) if downside_deviation > 0 else 0,
                    'calmar_ratio': round(5 / max_drawdown, 2) if max_drawdown > 0 else 0
                },
                'position_sizing_adjustment': {
                    'volatility_multiplier': max(0.3, min(1.5, 25 / volatility_30d)),
                    'risk_penalty': max(1.0, volatility_30d / 20),
                    'recommended_max_weight': min(15, max(3, 20 - volatility_30d / 2))
                }
            }
            
        except Exception as e:
            logger.error(f"Error in volatility analysis: {e}")
            return {'volatility_score': 50, 'risk_level': 'MEDIUM', 'error': str(e)}
