
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TabsList } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Menu, X, Sparkles } from 'lucide-react';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

interface NavigationTabsProps {
  navItems: NavigationItem[];
  isLoaded: boolean;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const NavigationTabs = ({ 
  navItems, 
  isLoaded, 
  mobileMenuOpen, 
  setMobileMenuOpen, 
  activeTab, 
  setActiveTab 
}: NavigationTabsProps) => {
  return (
    <>
      {/* Enhanced Mobile Menu Button */}
      <div className="md:hidden mb-6">
        <Card className="shadow-magical border-2 border-egx-gold-200 bg-gradient-to-r from-egx-gold-50 to-amber-50">
          <CardContent className="p-4">
            <Button
              variant="outline"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="w-full justify-between h-14 text-lg font-bold arabic border-2 border-egx-gold-300 hover:bg-egx-gold-100 transition-all duration-300"
            >
              <div className="flex items-center gap-3">
                <Sparkles className="h-5 w-5 text-egx-gold-600 animate-pulse" />
                <span>القائمة الرئيسية</span>
              </div>
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Desktop Navigation Grid */}
      <div className="hidden md:block">
        <Card className="shadow-elevated bg-gradient-to-br from-white/90 to-blue-50/90 backdrop-blur-sm border-2 border-white/30 relative overflow-hidden">
          {/* Magical background effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-egx-gold-100/20 to-transparent opacity-50"></div>
          
          <div className="relative z-10 p-6">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {navItems.map((item, index) => (
                <Button
                  key={item.id}
                  variant="ghost"
                  onClick={() => setActiveTab(item.id)}
                  className={`
                    flex flex-col items-center gap-3 p-6 text-center rounded-xl transition-all duration-500 arabic min-h-[120px]
                    ${activeTab === item.id 
                      ? 'bg-gradient-to-br from-egx-blue-600 to-egx-blue-700 text-white shadow-magical scale-105 border-egx-blue-300' 
                      : 'hover:bg-gradient-to-br hover:from-egx-gold-100 hover:to-amber-100 hover:shadow-soft hover:scale-102'
                    }
                    border-2 border-transparent hover:border-egx-gold-200
                    relative overflow-hidden group
                    ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}
                  `}
                  dir="rtl"
                  style={{ 
                    animationDelay: `${index * 80}ms`,
                    animation: isLoaded ? 'slide-up 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards' : 'none'
                  }}
                >
                  {/* Hover effect overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  <div className="relative z-10 flex flex-col items-center gap-2">
                    <div className="p-2 rounded-lg bg-white/20 group-hover:bg-white/30 transition-colors duration-300">
                      <item.icon className="h-6 w-6" />
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-sm leading-tight mb-1">{item.label}</div>
                      <div className="text-xs opacity-80 leading-tight hidden lg:block max-w-[120px]">
                        {item.description}
                      </div>
                    </div>
                  </div>
                  
                  {/* Active indicator */}
                  <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-egx-gold-400 to-amber-400 transition-opacity duration-300 ${activeTab === item.id ? 'opacity-100' : 'opacity-0'}`}></div>
                </Button>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Enhanced Mobile Navigation Dropdown */}
      {mobileMenuOpen && (
        <div className="md:hidden">
          <Card className="shadow-elevated bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-sm border-2 border-egx-gold-200">
            <CardContent className="p-4">
              <div className="grid grid-cols-1 gap-3">
                {navItems.map((item, index) => (
                  <Button
                    key={item.id}
                    variant={activeTab === item.id ? "default" : "ghost"}
                    onClick={() => {
                      setActiveTab(item.id);
                      setMobileMenuOpen(false);
                    }}
                    className={`
                      justify-start gap-4 arabic h-18 text-right transition-all duration-300 relative overflow-hidden group
                      ${activeTab === item.id 
                        ? 'bg-gradient-to-r from-egx-blue-600 to-egx-blue-700 hover:from-egx-blue-700 hover:to-egx-blue-800 text-white shadow-magical' 
                        : 'hover:bg-gradient-to-r hover:from-egx-gold-100 hover:to-amber-100 hover:shadow-soft'
                      }
                      ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}
                      border-2 border-transparent hover:border-egx-gold-200
                    `}
                    dir="rtl"
                    style={{ 
                      animationDelay: `${index * 80}ms`,
                      animation: isLoaded ? 'slide-up 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards' : 'none'
                    }}
                  >
                    {/* Background effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    <div className="relative z-10 flex items-center gap-4 w-full">
                      <div className="p-2 rounded-lg bg-white/20 group-hover:bg-white/30 transition-colors duration-300">
                        <item.icon className="h-5 w-5" />
                      </div>
                      <div className="text-right flex-1">
                        <div className="font-bold text-base">{item.label}</div>
                        <div className="text-sm opacity-80 mt-1 leading-tight">{item.description}</div>
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
};

export default NavigationTabs;
