import { useState, useEffect, useRef, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface EnhancedStockData {
  symbol: string;
  name?: string;
  current_price: number;
  previous_close?: number;
  change_amount?: number;
  change_percent: number;
  volume: number;
  turnover: number;
  high_price?: number;
  low_price?: number;
  updated_at: string;
  
  // حالات التغيير للتأثيرات البصرية
  priceDirection?: 'up' | 'down' | 'neutral';
  volumeChange?: 'up' | 'down' | 'neutral';
  isNewUpdate?: boolean;
  flashColor?: 'green' | 'red' | 'none';
  isHighlighted?: boolean;
}

export interface MarketStats {
  totalStocks: number;
  gainers: number;
  losers: number;
  unchanged: number;
  totalVolume: number;
  totalTurnover: number;
  mostActiveStock?: EnhancedStockData;
  topGainer?: EnhancedStockData;
  topLoser?: EnhancedStockData;
  lastUpdate: string;
}

export const useEnhancedLiveData = () => {
  const [previousData, setPreviousData] = useState<Map<string, EnhancedStockData>>(new Map());
  const [marketStats, setMarketStats] = useState<MarketStats | null>(null);
  const isFirstLoad = useRef(true);
  const flashTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // استعلام البيانات مع تحديث أسرع
  const {
    data: rawStocks,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['enhanced_live_data'],
    queryFn: async () => {
      try {
        console.log('🔄 Fetching enhanced live data...');
        
        // جلب بيانات محسنة
        const { data: stocks, error: stocksError } = await supabase
          .from('stocks_realtime')
          .select(`
            symbol,
            current_price,
            previous_close,
            change_amount,
            change_percent,
            volume,
            turnover,
            high_price,
            low_price,
            updated_at
          `)
          .not('symbol', 'like', '*EGX*') // استبعاد المؤشرات
          .not('symbol', 'like', '*INDEX*')
          .gt('current_price', 0) // فقط الأسهم النشطة
          .order('turnover', { ascending: false })
          .limit(100); // أهم 100 سهم

        if (stocksError) {
          console.error('❌ Error fetching stocks:', stocksError);
          throw stocksError;
        }

        // جلب أسماء الأسهم
        const symbols = stocks?.map(s => s.symbol) || [];
        const { data: masterData } = await supabase
          .from('stocks_master')
          .select('symbol, name, name_ar')
          .in('symbol', symbols);

        // دمج البيانات
        const enrichedStocks = stocks?.map(stock => {
          const master = masterData?.find(m => m.symbol === stock.symbol);
          return {
            ...stock,
            name: master?.name_ar || master?.name || stock.symbol,
            change_amount: stock.change_amount || (stock.current_price - (stock.previous_close || stock.current_price)),
            previous_close: stock.previous_close || stock.current_price,
          };
        }) || [];

        console.log(`✅ Loaded ${enrichedStocks.length} enhanced stocks`);
        return enrichedStocks;
      } catch (error) {
        console.error('❌ Error in enhanced data fetch:', error);
        throw error;
      }
    },
    refetchInterval: 15000, // تحديث كل 15 ثانية
    staleTime: 10000, // البيانات صالحة لمدة 10 ثوانِ
    retry: 3,
    retryDelay: 2000,
  });

  // معالجة البيانات وإضافة التأثيرات البصرية
  const processedStocks = useMemo(() => {
    if (!rawStocks) return [];

    return rawStocks.map(stock => {
      const previous = previousData.get(stock.symbol);
      let priceDirection: 'up' | 'down' | 'neutral' = 'neutral';
      let volumeChange: 'up' | 'down' | 'neutral' = 'neutral';
      let flashColor: 'green' | 'red' | 'none' = 'none';
      let isNewUpdate = false;
      let isHighlighted = false;

      if (previous && !isFirstLoad.current) {
        // مقارنة الأسعار
        if (stock.current_price > previous.current_price) {
          priceDirection = 'up';
          flashColor = 'green';
          isNewUpdate = true;
          isHighlighted = true;
        } else if (stock.current_price < previous.current_price) {
          priceDirection = 'down';
          flashColor = 'red';
          isNewUpdate = true;
          isHighlighted = true;
        }

        // مقارنة الحجم
        if (stock.volume > previous.volume) {
          volumeChange = 'up';
        } else if (stock.volume < previous.volume) {
          volumeChange = 'down';
        }

        // تشغيل مؤقت إزالة التأثير
        if (isNewUpdate) {
          // إزالة المؤقت السابق إن وجد
          const existingTimer = flashTimers.current.get(stock.symbol);
          if (existingTimer) {
            clearTimeout(existingTimer);
          }

          // إنشاء مؤقت جديد لإزالة التأثير بعد 3 ثوان
          const timer = setTimeout(() => {
            flashTimers.current.delete(stock.symbol);
          }, 3000);
          
          flashTimers.current.set(stock.symbol, timer);
        }
      }

      const enhancedStock: EnhancedStockData = {
        ...stock,
        priceDirection,
        volumeChange,
        isNewUpdate,
        flashColor,
        isHighlighted,
      };

      return enhancedStock;
    });
  }, [rawStocks, previousData]);

  // حساب إحصائيات السوق
  const calculatedMarketStats = useMemo(() => {
    if (!processedStocks || processedStocks.length === 0) return null;

    const gainers = processedStocks.filter(s => s.change_percent > 0);
    const losers = processedStocks.filter(s => s.change_percent < 0);
    const unchanged = processedStocks.filter(s => s.change_percent === 0);
    
    const totalVolume = processedStocks.reduce((sum, s) => sum + (s.volume || 0), 0);
    const totalTurnover = processedStocks.reduce((sum, s) => sum + (s.turnover || 0), 0);

    // أهم الأسهم
    const mostActiveStock = processedStocks.reduce((max, stock) => 
      (stock.turnover || 0) > (max.turnover || 0) ? stock : max
    );

    const topGainer = gainers.reduce((max, stock) => 
      stock.change_percent > max.change_percent ? stock : max, 
      gainers[0]
    );

    const topLoser = losers.reduce((min, stock) => 
      stock.change_percent < min.change_percent ? stock : min, 
      losers[0]
    );

    const stats: MarketStats = {
      totalStocks: processedStocks.length,
      gainers: gainers.length,
      losers: losers.length,
      unchanged: unchanged.length,
      totalVolume,
      totalTurnover,
      mostActiveStock,
      topGainer,
      topLoser,
      lastUpdate: new Date().toLocaleTimeString('ar-EG'),
    };

    return stats;
  }, [processedStocks]);

  // تحديث البيانات السابقة بعد المعالجة
  useEffect(() => {
    if (processedStocks && processedStocks.length > 0) {
      const newPreviousData = new Map<string, EnhancedStockData>();
      processedStocks.forEach(stock => {
        newPreviousData.set(stock.symbol, { ...stock });
      });
      setPreviousData(newPreviousData);
      setMarketStats(calculatedMarketStats);
      
      if (isFirstLoad.current) {
        isFirstLoad.current = false;
        console.log('📊 First load completed, visual effects will be active on next update');
      }
    }
  }, [processedStocks, calculatedMarketStats]);
  // تنظيف المؤقتات عند الإغلاق
  useEffect(() => {
    const timers = flashTimers.current;
    return () => {
      timers.forEach(timer => clearTimeout(timer));
      timers.clear();
    };
  }, []);

  return {
    stocks: processedStocks,
    marketStats,
    isLoading,
    error,
    refetch,
    lastUpdate: marketStats?.lastUpdate || null,
  };
};
