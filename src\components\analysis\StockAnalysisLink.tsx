import React from 'react';
import { Button } from '@/components/ui/button';
import { TrendingUp, BarChart3 } from 'lucide-react';

interface StockAnalysisLinkProps {
  symbol: string;
  onAnalyze: (symbol: string) => void;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  showIcon?: boolean;
  showText?: boolean;
  className?: string;
}

const StockAnalysisLink: React.FC<StockAnalysisLinkProps> = ({
  symbol,
  onAnalyze,
  variant = 'outline',
  size = 'sm',
  showIcon = true,
  showText = true,
  className = ''
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAnalyze(symbol);
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      className={`flex items-center gap-1 ${className}`}
      title={`تحليل شامل لسهم ${symbol}`}
    >
      {showIcon && <BarChart3 className="h-3 w-3" />}
      {showText && <span>تحليل</span>}
    </Button>
  );
};

export default StockAnalysisLink;
