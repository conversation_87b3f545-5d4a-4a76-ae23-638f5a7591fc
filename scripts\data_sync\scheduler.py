#!/usr/bin/env python3
"""
EGX Stock AI Oracle - Data Sync Scheduler
Coordinates all data synchronization tasks
"""

import schedule
import time
import logging
import os
import subprocess
import sys
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_sync_scheduler.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_script(script_name):
    """Run a data sync script and log results"""
    try:
        logger.info(f"Starting {script_name}")
        
        # Get the directory of this script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(script_dir, script_name)
        
        if not os.path.exists(script_path):
            logger.error(f"Script not found: {script_path}")
            return False
        
        # Run the script
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True, 
                              timeout=3600)  # 1 hour timeout
        
        if result.returncode == 0:
            logger.info(f"Successfully completed {script_name}")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
            return True
        else:
            logger.error(f"Error in {script_name}: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"Timeout: {script_name} took longer than 1 hour")
        return False
    except Exception as e:
        logger.error(f"Exception running {script_name}: {e}")
        return False

def sync_realtime_data():
    """Sync real-time stock data from Excel"""
    logger.info("=== Starting Real-time Data Sync ===")
    success = run_script("load_realtime.py")
    if success:
        logger.info("Real-time data sync completed successfully")
    else:
        logger.error("Real-time data sync failed")

def sync_historical_data():
    """Sync historical stock data from TXT files"""
    logger.info("=== Starting Historical Data Sync ===")
    success = run_script("load_historical.py")
    if success:
        logger.info("Historical data sync completed successfully")
    else:
        logger.error("Historical data sync failed")

def sync_financial_data():
    """Sync financial data from CSV"""
    logger.info("=== Starting Financial Data Sync ===")
    success = run_script("load_financials.py")
    if success:
        logger.info("Financial data sync completed successfully")
    else:
        logger.error("Financial data sync failed")

def full_data_sync():
    """Run all data sync tasks"""
    logger.info("=== Starting Full Data Sync ===")
    start_time = datetime.now()
    
    # Run syncs in order of dependency
    tasks = [
        ("Historical Data", sync_historical_data),
        ("Real-time Data", sync_realtime_data),
        ("Financial Data", sync_financial_data)
    ]
    
    results = {}
    for task_name, task_func in tasks:
        try:
            task_func()
            results[task_name] = "SUCCESS"
        except Exception as e:
            logger.error(f"Failed to run {task_name}: {e}")
            results[task_name] = f"FAILED: {e}"
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info("=== Full Data Sync Summary ===")
    logger.info(f"Total duration: {duration}")
    for task, status in results.items():
        logger.info(f"{task}: {status}")
    
    # Send notification if needed (could integrate with email/Telegram)
    success_count = sum(1 for status in results.values() if status == "SUCCESS")
    total_count = len(results)
    
    if success_count == total_count:
        logger.info("All data sync tasks completed successfully!")
    else:
        logger.warning(f"Only {success_count}/{total_count} data sync tasks succeeded")

def setup_scheduler():
    """Setup the task scheduler"""
    logger.info("Setting up data sync scheduler")
    
    # Get sync intervals from environment
    realtime_interval = int(os.getenv("REALTIME_SYNC_MINUTES", 5))  # Every 5 minutes
    financial_interval = int(os.getenv("FINANCIAL_SYNC_HOURS", 24))   # Daily
    historical_interval = int(os.getenv("HISTORICAL_SYNC_HOURS", 24)) # Daily or on-demand
    
    # Schedule real-time data sync (frequent)
    schedule.every(realtime_interval).minutes.do(sync_realtime_data)
    logger.info(f"Scheduled real-time sync every {realtime_interval} minutes")
    
    # Schedule financial data sync (daily)
    schedule.every(financial_interval).hours.do(sync_financial_data)
    logger.info(f"Scheduled financial sync every {financial_interval} hours")
    
    # Schedule historical data sync (weekly or on-demand)
    schedule.every(historical_interval).hours.do(sync_historical_data)
    logger.info(f"Scheduled historical sync every {historical_interval} hours")

    # Schedule full sync daily at 3 PM
    schedule.every().day.at("15:00").do(full_data_sync)
    logger.info("Scheduled full sync daily at 3:00 PM")

    # Initial sync on startup
    logger.info("Running initial data sync...")
    sync_realtime_data()

def main():
    """Main scheduler loop"""
    logger.info("EGX Stock AI Oracle - Data Sync Scheduler Started")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    # Check environment variables
    required_vars = ["SUPABASE_URL", "SUPABASE_SERVICE_ROLE_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        logger.error("Please check your .env file")
        sys.exit(1)
    
    # Setup scheduler
    setup_scheduler()
    
    logger.info("Scheduler is running. Press Ctrl+C to stop.")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
            
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user")
    except Exception as e:
        logger.error(f"Scheduler error: {e}")
        raise

if __name__ == "__main__":
    main()
