#!/usr/bin/env python3
"""
EGX Stock AI Oracle - Quick Data Check
التحقق السريع من البيانات
"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_SERVICE_ROLE_KEY:
    print("❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def check_data():
    print("🔍 EGX Stock AI Oracle - فحص البيانات")
    print("=" * 50)
    
    # Check real-time data
    realtime_result = supabase.table("stocks_realtime").select("symbol, current_price, change_percent").limit(10).execute()
    print(f"📊 عينة من البيانات الحقيقية (أول 10 أسهم):")
    for stock in realtime_result.data:
        symbol = stock['symbol']
        price = stock.get('current_price', 0)
        change = stock.get('change_percent', 0)
        print(f"   {symbol}: {price:.2f} EGP ({change:+.2f}%)")
      # Check market indices
    indices_result = supabase.table("market_indices").select("symbol, current_value, change_percent").execute()
    print(f"\n📈 مؤشرات السوق:")
    for index in indices_result.data:
        symbol = index['symbol']
        value = index.get('current_value') or 0
        change = index.get('change_percent') or 0
        print(f"   {symbol}: {value:.2f} ({change:+.2f}%)")
    
    # Check counts
    stocks_count = len(supabase.table("stocks_realtime").select("symbol").execute().data)
    master_count = len(supabase.table("stocks_master").select("symbol").execute().data)
    indices_count = len(indices_result.data)
    
    print(f"\n📊 إحصائيات قاعدة البيانات:")
    print(f"   🏢 أسهم في stocks_master: {master_count}")
    print(f"   ⚡ أسهم في stocks_realtime: {stocks_count}")
    print(f"   📈 مؤشرات السوق: {indices_count}")
    
    print("\n✅ البيانات جاهزة للعرض على الموقع!")
    print("🌐 يمكنك الآن تشغيل الموقع ورؤية البيانات الحقيقية")

if __name__ == "__main__":
    check_data()
