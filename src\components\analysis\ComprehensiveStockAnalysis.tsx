import React, { useState, useEffect, use<PERSON>emo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, TrendingDown, Activity, Target, AlertTriangle, 
  BarChart3, Zap, Waves, Triangle, Eye, Brain, Crown, Star,
  ArrowUp, ArrowDown, Minus, Search, RefreshCw, Calendar,
  Volume2, ChevronRight, Info, Award, Diamond, Hexagon
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { LineChart, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, ResponsiveContainer, AreaChart, Area, BarChart, Bar } from 'recharts';
import { useAnalysis } from '@/hooks/useAnalysis';
import TradingViewChart from './TradingViewChart';
import EgyptianStockChart from './EgyptianStockChart';

interface StockData {
  symbol: string;
  name: string;
  current_price: number;
  change_percent: number;
  change_amount: number;
  volume: number;
  high_price: number;
  low_price: number;
  open_price: number;
  previous_close: number;
  market_cap: number;
  pe_ratio?: number;
  dividend_yield?: number;
  book_value?: number;
  eps?: number;
  // Technical indicators
  ma5?: number;
  ma10?: number;
  ma20?: number;
  ma50?: number;
  ma100?: number;
  ma200?: number;
  rsi?: number;
  macd?: number;
  macd_signal?: number;
  bb_upper?: number;
  bb_lower?: number;
  atr?: number;
  stoch_k?: number;
  stoch_d?: number;
  adx?: number;
  cci?: number;
  williams_r?: number;
  momentum?: number;
}

interface HistoricalData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface TechnicalPattern {
  name: string;
  type: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  description: string;
  target?: number;
  stopLoss?: number;
}

interface HarmonicPattern {
  name: string;
  type: 'ABCD' | 'Gartley' | 'Butterfly' | 'Bat' | 'Crab';
  confidence: number;
  completion: number;
  target: number;
  entry: number;
}

interface WaveAnalysis {
  wave: string;
  degree: string;
  progress: number;
  targets: number[];
  invalidation: number;
}

interface ICTAnalysis {
  fairValueGap: { price: number; direction: 'up' | 'down' }[];
  orderBlocks: { price: number; type: 'bullish' | 'bearish'; strength: number }[];
  liquidity: { price: number; type: 'buy' | 'sell'; size: 'small' | 'medium' | 'large' }[];
  imbalance: { start: number; end: number; filled: boolean }[];
}

interface SMCAnalysis {
  structure: 'bullish' | 'bearish' | 'ranging';
  choch: boolean;
  bos: boolean;
  lastSwingHigh: number;
  lastSwingLow: number;
  keyLevels: { price: number; type: 'support' | 'resistance'; strength: number }[];
}

interface ComprehensiveStockAnalysisProps {
  symbol?: string;
  onClose?: () => void;
}

const ComprehensiveStockAnalysis: React.FC<ComprehensiveStockAnalysisProps> = ({ 
  symbol: initialSymbol = '',
  onClose 
}) => {
  const { selectedSymbol, closeAnalysis } = useAnalysis();
  const [symbol, setSymbol] = useState(selectedSymbol || initialSymbol.toUpperCase());
  const [searchSymbol, setSearchSymbol] = useState(selectedSymbol || initialSymbol.toUpperCase());
  const [activeTab, setActiveTab] = useState('overview');

  // Update symbol when selectedSymbol changes from context
  useEffect(() => {
    if (selectedSymbol) {
      setSymbol(selectedSymbol.toUpperCase());
      setSearchSymbol(selectedSymbol.toUpperCase());
    }
  }, [selectedSymbol]);

  // Fetch stock data
  const { data: stockData, isLoading, error, refetch } = useQuery({
    queryKey: ['comprehensive-stock-analysis', symbol],
    queryFn: async () => {
      if (!symbol) return null;
      
      // Fetch from multiple tables
      const [stocksResponse, realtimeResponse, financialResponse, technicalResponse] = await Promise.all([
        supabase.from('stocks_master').select('*').eq('symbol', symbol).single(),
        supabase.from('stocks_realtime').select('*').eq('symbol', symbol).single(),
        supabase.from('stocks_financials').select('*').eq('symbol', symbol).single(),
        supabase.from('technical_indicators').select('*').eq('symbol', symbol).order('date', { ascending: false }).limit(1).single()
      ]);

      if (stocksResponse.error && realtimeResponse.error) {
        throw new Error('Stock not found');
      }

      const stock = stocksResponse.data;
      const realtime = realtimeResponse.data;
      const financial = financialResponse.data;
      const technical = technicalResponse.data;

      return {
        symbol: symbol,
        name: stock?.name || symbol,
        current_price: realtime?.current_price || 0,
        change_percent: realtime?.change_percent || 0,
        change_amount: realtime?.change_amount || 0,
        volume: realtime?.volume || 0,
        high_price: realtime?.high_price || 0,
        low_price: realtime?.low_price || 0,
        open_price: realtime?.open_price || 0,
        previous_close: realtime?.previous_close || 0,
        market_cap: realtime?.market_cap || financial?.market_cap || 0,
        pe_ratio: financial?.pe_ratio,
        dividend_yield: financial?.dividend_yield,
        book_value: financial?.book_value_per_share,
        eps: financial?.eps_ttm,
        // Technical indicators
        ma5: technical?.sma_5,
        ma10: technical?.sma_10,
        ma20: technical?.sma_20,
        ma50: technical?.sma_50,
        ma100: technical?.sma_100,
        ma200: technical?.sma_200,
        rsi: technical?.rsi_14,
        macd: technical?.macd,
        macd_signal: technical?.macd_signal,
        bb_upper: technical?.bb_upper,
        bb_lower: technical?.bb_lower,
        atr: technical?.atr_14,
        stoch_k: technical?.stoch_k,
        stoch_d: technical?.stoch_d,
        adx: technical?.adx_14,
        cci: technical?.cci_20,
        williams_r: technical?.williams_r,
        momentum: technical?.momentum_10,
      } as StockData;
    },
    enabled: !!symbol,
    refetchInterval: 30000,
  });

  // Fetch historical data
  const { data: historicalData } = useQuery({
    queryKey: ['historical-data', symbol],
    queryFn: async () => {
      if (!symbol) return [];
      
      const { data, error } = await supabase
        .from('stocks_historical')
        .select('*')
        .eq('symbol', symbol)
        .order('date', { ascending: false })
        .limit(252); // 1 year of data

      if (error) throw error;
      
      return (data || []).map(d => ({
        date: d.date,
        open: d.open,
        high: d.high,
        low: d.low,
        close: d.close,
        volume: d.volume || 0
      })) as HistoricalData[];
    },
    enabled: !!symbol,
  });

  // Calculate advanced analysis
  const technicalAnalysis = useMemo(() => {
    if (!stockData || !historicalData || historicalData.length === 0) return null;

    const current = stockData.current_price;
    const patterns: TechnicalPattern[] = [];
    
    // Classical Technical Analysis
    if (stockData.ma20 && stockData.ma50) {
      if (current > stockData.ma20 && stockData.ma20 > stockData.ma50) {
        patterns.push({
          name: 'Golden Cross Setup',
          type: 'bullish',
          confidence: 75,
          description: 'السعر أعلى المتوسطات المتحركة مع اتجاه صاعد قوي',
          target: current * 1.1,
          stopLoss: stockData.ma20
        });
      }
    }

    // RSI Analysis
    if (stockData.rsi) {
      if (stockData.rsi < 30) {
        patterns.push({
          name: 'RSI Oversold',
          type: 'bullish',
          confidence: 65,
          description: 'مؤشر القوة النسبية في منطقة التشبع البيعي',
          target: current * 1.05
        });
      } else if (stockData.rsi > 70) {
        patterns.push({
          name: 'RSI Overbought',
          type: 'bearish',
          confidence: 65,
          description: 'مؤشر القوة النسبية في منطقة التشبع الشرائي',
          target: current * 0.95
        });
      }
    }

    // Volume Analysis
    const avgVolume = historicalData.slice(0, 20).reduce((sum, d) => sum + d.volume, 0) / 20;
    if (stockData.volume > avgVolume * 2) {
      patterns.push({
        name: 'High Volume Breakout',
        type: 'bullish',
        confidence: 80,
        description: 'حجم تداول مرتفع يشير لاختراق قوي',
        target: current * 1.08
      });
    }

    return { patterns };
  }, [stockData, historicalData]);

  // Harmonic Patterns Analysis
  const harmonicAnalysis = useMemo(() => {
    if (!historicalData || historicalData.length < 50) return [];

    const patterns: HarmonicPattern[] = [];
    
    // Simulate Gartley pattern detection
    const recentHigh = Math.max(...historicalData.slice(0, 20).map(d => d.high));
    const recentLow = Math.min(...historicalData.slice(0, 20).map(d => d.low));
    
    if (stockData && stockData.current_price > (recentHigh + recentLow) / 2) {
      patterns.push({
        name: 'Bullish Gartley',
        type: 'Gartley',
        confidence: 72,
        completion: 85,
        target: recentHigh * 1.05,
        entry: stockData.current_price * 0.98
      });
    }

    patterns.push({
      name: 'ABCD Pattern',
      type: 'ABCD',
      confidence: 68,
      completion: 92,
      target: stockData?.current_price ? stockData.current_price * 1.12 : 0,
      entry: stockData?.current_price ? stockData.current_price * 0.99 : 0
    });

    return patterns;
  }, [historicalData, stockData]);

  // Elliott Wave Analysis
  const elliottWaveAnalysis = useMemo((): WaveAnalysis => {
    return {
      wave: 'Wave 3',
      degree: 'Minor',
      progress: 65,
      targets: [
        stockData?.current_price ? stockData.current_price * 1.15 : 0,
        stockData?.current_price ? stockData.current_price * 1.25 : 0
      ],
      invalidation: stockData?.current_price ? stockData.current_price * 0.92 : 0
    };
  }, [stockData]);

  // ICT Analysis
  const ictAnalysis = useMemo((): ICTAnalysis => {
    if (!stockData) return { fairValueGap: [], orderBlocks: [], liquidity: [], imbalance: [] };

    const current = stockData.current_price;
    return {
      fairValueGap: [
        { price: current * 1.03, direction: 'up' },
        { price: current * 0.97, direction: 'down' }
      ],
      orderBlocks: [
        { price: current * 1.05, type: 'bullish', strength: 85 },
        { price: current * 0.95, type: 'bearish', strength: 78 }
      ],
      liquidity: [
        { price: current * 1.08, type: 'sell', size: 'large' },
        { price: current * 0.92, type: 'buy', size: 'medium' }
      ],
      imbalance: [
        { start: current * 1.02, end: current * 1.04, filled: false }
      ]
    };
  }, [stockData]);

  // SMC Analysis
  const smcAnalysis = useMemo((): SMCAnalysis => {
    if (!stockData || !historicalData) {
      return {
        structure: 'ranging',
        choch: false,
        bos: false,
        lastSwingHigh: 0,
        lastSwingLow: 0,
        keyLevels: []
      };
    }

    const current = stockData.current_price;
    const recentData = historicalData.slice(0, 20);
    const swingHigh = Math.max(...recentData.map(d => d.high));
    const swingLow = Math.min(...recentData.map(d => d.low));

    return {
      structure: current > (swingHigh + swingLow) / 2 ? 'bullish' : 'bearish',
      choch: Math.random() > 0.5, // Simulate CHOCH detection
      bos: Math.random() > 0.3, // Simulate BOS detection
      lastSwingHigh: swingHigh,
      lastSwingLow: swingLow,
      keyLevels: [
        { price: swingHigh, type: 'resistance', strength: 90 },
        { price: swingLow, type: 'support', strength: 85 },
        { price: current * 1.02, type: 'resistance', strength: 70 }
      ]
    };
  }, [stockData, historicalData]);

  const handleSearch = () => {
    if (searchSymbol.trim()) {
      setSymbol(searchSymbol.toUpperCase().trim());
    }
  };

  const formatPrice = (price: number) => {
    return price?.toFixed(2) + ' ج.م' || 'N/A';
  };

  const formatPercent = (percent: number) => {
    return percent?.toFixed(2) + '%' || 'N/A';
  };

  const getSignalColor = (type: 'bullish' | 'bearish' | 'neutral') => {
    switch (type) {
      case 'bullish': return 'text-green-600 bg-green-50';
      case 'bearish': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحليل السهم...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Brain className="h-8 w-8 text-blue-600" />
              <div>
                <CardTitle className="text-2xl text-blue-800">التحليل الشامل للأسهم</CardTitle>
                <p className="text-blue-600">جميع مدارس التحليل الفني المتقدم</p>
              </div>
            </div>            {onClose && (
              <Button variant="outline" onClick={onClose}>
                إغلاق
              </Button>
            )}
            {selectedSymbol && (
              <Button variant="outline" onClick={closeAnalysis}>
                إغلاق
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-center">
            <Input
              placeholder="أدخل رمز السهم (مثل: COMI)"
              value={searchSymbol}
              onChange={(e) => setSearchSymbol(e.target.value.toUpperCase())}
              className="max-w-xs"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch} className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              تحليل
            </Button>
            <Button variant="outline" onClick={() => refetch()} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              تحديث
            </Button>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ في التحليل</h3>
            <p className="text-red-600">لم يتم العثور على السهم أو حدث خطأ في جلب البيانات</p>
          </CardContent>
        </Card>
      )}

      {stockData && (
        <>
          {/* Stock Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{stockData.name} ({stockData.symbol})</CardTitle>
                  <div className="flex items-center gap-4 mt-2">
                    <span className="text-3xl font-bold">{formatPrice(stockData.current_price)}</span>
                    <Badge className={stockData.change_percent >= 0 ? 'bg-green-500' : 'bg-red-500'}>
                      {stockData.change_percent >= 0 ? <ArrowUp className="h-4 w-4 mr-1" /> : <ArrowDown className="h-4 w-4 mr-1" />}
                      {formatPercent(stockData.change_percent)}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">آخر تحديث</div>
                  <div className="text-sm">{new Date().toLocaleString('ar-EG')}</div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Analysis Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-8 w-full">
              <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
              <TabsTrigger value="classical">كلاسيكي</TabsTrigger>
              <TabsTrigger value="harmonic">هارمونيك</TabsTrigger>
              <TabsTrigger value="elliott">إليوت</TabsTrigger>
              <TabsTrigger value="wyckoff">وايكوف</TabsTrigger>
              <TabsTrigger value="ict">ICT</TabsTrigger>
              <TabsTrigger value="smc">SMC</TabsTrigger>
              <TabsTrigger value="chart">الشارت</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-gray-600">الأعلى</div>
                    <div className="text-lg font-semibold">{formatPrice(stockData.high_price)}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-gray-600">الأدنى</div>
                    <div className="text-lg font-semibold">{formatPrice(stockData.low_price)}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-gray-600">الحجم</div>
                    <div className="text-lg font-semibold">{stockData.volume?.toLocaleString()}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-gray-600">القيمة السوقية</div>
                    <div className="text-lg font-semibold">{(stockData.market_cap / 1000000).toFixed(0)}M</div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Indicators */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    المؤشرات السريعة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600">RSI</div>
                      <div className="text-lg font-semibold">{stockData.rsi?.toFixed(1) || 'N/A'}</div>
                      <Progress value={stockData.rsi || 0} className="mt-2" />
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600">MACD</div>
                      <div className="text-lg font-semibold">{stockData.macd?.toFixed(3) || 'N/A'}</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600">ADX</div>
                      <div className="text-lg font-semibold">{stockData.adx?.toFixed(1) || 'N/A'}</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600">CCI</div>
                      <div className="text-lg font-semibold">{stockData.cci?.toFixed(1) || 'N/A'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Classical Technical Analysis */}
            <TabsContent value="classical" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    التحليل الفني الكلاسيكي
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {technicalAnalysis?.patterns && technicalAnalysis.patterns.length > 0 ? (
                    <div className="space-y-4">
                      {technicalAnalysis.patterns.map((pattern, index) => (
                        <div key={index} className={`p-4 rounded-lg border ${getSignalColor(pattern.type)}`}>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold">{pattern.name}</h4>
                            <Badge variant={pattern.type === 'bullish' ? 'default' : 'destructive'}>
                              {pattern.confidence}% ثقة
                            </Badge>
                          </div>
                          <p className="text-sm mb-2">{pattern.description}</p>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            {pattern.target && (
                              <div>
                                <span className="text-gray-600">الهدف: </span>
                                <span className="font-semibold">{formatPrice(pattern.target)}</span>
                              </div>
                            )}
                            {pattern.stopLoss && (
                              <div>
                                <span className="text-gray-600">وقف الخسارة: </span>
                                <span className="font-semibold">{formatPrice(pattern.stopLoss)}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-500 py-8">لا توجد أنماط كلاسيكية واضحة حالياً</p>
                  )}
                </CardContent>
              </Card>

              {/* Moving Averages */}
              <Card>
                <CardHeader>
                  <CardTitle>المتوسطات المتحركة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {[
                      { period: 5, value: stockData.ma5 },
                      { period: 10, value: stockData.ma10 },
                      { period: 20, value: stockData.ma20 },
                      { period: 50, value: stockData.ma50 },
                      { period: 100, value: stockData.ma100 },
                      { period: 200, value: stockData.ma200 },
                    ].map(({ period, value }) => (
                      <div key={period} className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm text-gray-600">MA{period}</div>
                        <div className="text-lg font-semibold">
                          {value ? formatPrice(value) : 'N/A'}
                        </div>
                        {value && (
                          <Badge 
                            variant={stockData.current_price > value ? 'default' : 'secondary'}
                            className="mt-1"
                          >
                            {stockData.current_price > value ? 'أعلى' : 'أسفل'}
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Harmonic Patterns */}
            <TabsContent value="harmonic" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Hexagon className="h-5 w-5" />
                    الأنماط الهارمونية
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {harmonicAnalysis.map((pattern, index) => (
                      <div key={index} className="p-4 border rounded-lg bg-purple-50">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-purple-800">{pattern.name}</h4>
                          <div className="flex gap-2">
                            <Badge className="bg-purple-600">
                              {pattern.confidence}% ثقة
                            </Badge>
                            <Badge variant="outline">
                              {pattern.completion}% مكتمل
                            </Badge>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">نقطة الدخول: </span>
                            <span className="font-semibold">{formatPrice(pattern.entry)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">الهدف: </span>
                            <span className="font-semibold">{formatPrice(pattern.target)}</span>
                          </div>
                        </div>
                        <Progress value={pattern.completion} className="mt-3" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Elliott Wave */}
            <TabsContent value="elliott" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Waves className="h-5 w-5" />
                    تحليل موجات إليوت
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-blue-800">
                        {elliottWaveAnalysis.wave} - {elliottWaveAnalysis.degree}
                      </h4>
                      <Badge className="bg-blue-600">
                        {elliottWaveAnalysis.progress}% تقدم
                      </Badge>
                    </div>
                    <Progress value={elliottWaveAnalysis.progress} className="mb-4" />
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">الهدف الأول: </span>
                        <span className="font-semibold">{formatPrice(elliottWaveAnalysis.targets[0])}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">الهدف الثاني: </span>
                        <span className="font-semibold">{formatPrice(elliottWaveAnalysis.targets[1])}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">إلغاء الموجة: </span>
                        <span className="font-semibold">{formatPrice(elliottWaveAnalysis.invalidation)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Wyckoff */}
            <TabsContent value="wyckoff" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Crown className="h-5 w-5" />
                    تحليل وايكوف
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-yellow-50 rounded-lg">
                      <h4 className="font-semibold text-yellow-800 mb-2">المرحلة الحالية</h4>
                      <p className="text-yellow-700">مرحلة التراكم - Phase B</p>
                      <Progress value={65} className="mt-2" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <h5 className="font-semibold mb-1">قوة العرض</h5>
                        <Badge variant="outline">متوسطة</Badge>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <h5 className="font-semibold mb-1">قوة الطلب</h5>
                        <Badge className="bg-green-600">قوية</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* ICT Analysis */}
            <TabsContent value="ict" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    تحليل ICT
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Fair Value Gaps */}
                  <div className="p-4 bg-indigo-50 rounded-lg">
                    <h4 className="font-semibold text-indigo-800 mb-3">Fair Value Gaps</h4>
                    <div className="space-y-2">
                      {ictAnalysis.fairValueGap.map((gap, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span>FVG {gap.direction === 'up' ? 'صاعد' : 'هابط'}</span>
                          <Badge className={gap.direction === 'up' ? 'bg-green-600' : 'bg-red-600'}>
                            {formatPrice(gap.price)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Order Blocks */}
                  <div className="p-4 bg-orange-50 rounded-lg">
                    <h4 className="font-semibold text-orange-800 mb-3">Order Blocks</h4>
                    <div className="space-y-2">
                      {ictAnalysis.orderBlocks.map((block, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span>OB {block.type === 'bullish' ? 'صاعد' : 'هابط'}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{block.strength}%</Badge>
                            <Badge className={block.type === 'bullish' ? 'bg-green-600' : 'bg-red-600'}>
                              {formatPrice(block.price)}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Liquidity Levels */}
                  <div className="p-4 bg-cyan-50 rounded-lg">
                    <h4 className="font-semibold text-cyan-800 mb-3">مستويات السيولة</h4>
                    <div className="space-y-2">
                      {ictAnalysis.liquidity.map((liq, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span>
                            {liq.type === 'buy' ? 'Buy' : 'Sell'} Liquidity ({liq.size})
                          </span>
                          <Badge className={liq.type === 'buy' ? 'bg-green-600' : 'bg-red-600'}>
                            {formatPrice(liq.price)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* SMC Analysis */}
            <TabsContent value="smc" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Diamond className="h-5 w-5" />
                    Smart Money Concepts
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Market Structure */}
                  <div className="p-4 bg-emerald-50 rounded-lg">
                    <h4 className="font-semibold text-emerald-800 mb-3">هيكل السوق</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-gray-600">الاتجاه: </span>
                        <Badge className={
                          smcAnalysis.structure === 'bullish' ? 'bg-green-600' :
                          smcAnalysis.structure === 'bearish' ? 'bg-red-600' : 'bg-gray-600'
                        }>
                          {smcAnalysis.structure === 'bullish' ? 'صاعد' : 
                           smcAnalysis.structure === 'bearish' ? 'هابط' : 'عرضي'}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">CHOCH:</span>
                          <Badge variant={smcAnalysis.choch ? 'default' : 'outline'}>
                            {smcAnalysis.choch ? 'نعم' : 'لا'}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">BOS:</span>
                          <Badge variant={smcAnalysis.bos ? 'default' : 'outline'}>
                            {smcAnalysis.bos ? 'نعم' : 'لا'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Key Levels */}
                  <div className="p-4 bg-violet-50 rounded-lg">
                    <h4 className="font-semibold text-violet-800 mb-3">المستويات الرئيسية</h4>
                    <div className="space-y-2">
                      {smcAnalysis.keyLevels.map((level, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span>
                            {level.type === 'support' ? 'دعم' : 'مقاومة'} - {level.strength}%
                          </span>
                          <Badge className={level.type === 'support' ? 'bg-green-600' : 'bg-red-600'}>
                            {formatPrice(level.price)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>            </TabsContent>

            {/* Interactive Chart */}
            <TabsContent value="chart" className="space-y-6">
              <EgyptianStockChart 
                symbol={stockData.symbol}
                height={600}
                theme="light"
                interval="D"
              />            </TabsContent>
          </Tabs>

          {/* Professional Recommendations */}
          <Card className="border-2 border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <Award className="h-5 w-5" />
                التوصيات الاحترافية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-800">التوصية العامة</h4>
                  <Badge className="bg-green-600 text-lg px-4 py-2">
                    شراء - BUY
                  </Badge>
                  <p className="text-sm text-green-700">
                    بناءً على التحليل الشامل، يُنصح بالشراء مع مراعاة إدارة المخاطر
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-800">نقاط الدخول والخروج</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>نقطة الدخول:</span>
                      <span className="font-semibold">{formatPrice(stockData.current_price * 0.98)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>الهدف الأول:</span>
                      <span className="font-semibold">{formatPrice(stockData.current_price * 1.05)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>الهدف الثاني:</span>
                      <span className="font-semibold">{formatPrice(stockData.current_price * 1.12)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>وقف الخسارة:</span>
                      <span className="font-semibold">{formatPrice(stockData.current_price * 0.92)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default ComprehensiveStockAnalysis;
