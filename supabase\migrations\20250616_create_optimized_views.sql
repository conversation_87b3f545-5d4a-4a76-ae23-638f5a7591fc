-- Migration: Create Optimized Views for Market Data
-- Date: 2025-06-16
-- Purpose: Create computed views to reduce frontend calculations

-- 1. Market Summary View
CREATE OR REPLACE VIEW market_summary_view AS
SELECT 
    -- Basic statistics
    COUNT(*) as total_stocks,
    COUNT(CASE WHEN volume > 0 THEN 1 END) as active_stocks,
    COUNT(CASE WHEN volume > 100000 THEN 1 END) as high_volume_stocks,
    
    -- Change distribution
    COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
    COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
    COUNT(CASE WHEN change_percent = 0 THEN 1 END) as unchanged,
    
    -- Averages
    ROUND(AVG(change_percent), 2) as avg_change,
    ROUND(AVG(volume), 0) as avg_volume,
    
    -- Totals
    SUM(volume) as total_volume,
    SUM(turnover) as total_turnover,
    COUNT(CASE WHEN dividend_yield > 0 THEN 1 END) as dividend_stocks,
    COUNT(CASE WHEN speculation_opportunity = true THEN 1 END) as speculation_opportunities,
    
    -- Last update
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL;

-- 2. Technical Analysis View
CREATE OR REPLACE VIEW technical_analysis_view AS
SELECT 
    -- Moving average trends
    COUNT(CASE WHEN ma5 > ma20 THEN 1 END) as bullish_stocks,
    COUNT(CASE WHEN ma5 < ma20 THEN 1 END) as bearish_stocks,
    COUNT(CASE WHEN ma5 > ma20 AND ma20 > ma50 THEN 1 END) as strong_uptrend,
    COUNT(CASE WHEN ma5 < ma20 AND ma20 < ma50 THEN 1 END) as strong_downtrend,
    
    -- Level breaks
    COUNT(CASE WHEN current_price > ma20 THEN 1 END) as above_ma20,
    COUNT(CASE WHEN ma5 > ma20 AND (ma5 - ma20) / NULLIF(ma20, 0) > 0.02 THEN 1 END) as bullish_crossover,
    COUNT(CASE WHEN ma5 < ma20 AND (ma20 - ma5) / NULLIF(ma20, 0) > 0.02 THEN 1 END) as bearish_crossover,
    
    -- Targets and stop loss
    COUNT(CASE WHEN target_1 IS NOT NULL AND current_price >= target_1 * 0.95 THEN 1 END) as near_targets,
    COUNT(CASE WHEN stop_loss IS NOT NULL AND current_price <= stop_loss * 1.05 THEN 1 END) as near_stop_loss,
    
    -- Liquidity and momentum
    COUNT(CASE WHEN liquidity_ratio > 1 THEN 1 END) as liquidity_inflow,
    COUNT(CASE WHEN liquidity_ratio < 1 THEN 1 END) as liquidity_outflow,
    COUNT(CASE WHEN change_percent > 5 AND volume > 1000000 THEN 1 END) as high_momentum,
    
    -- Last update
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL;

-- 3. Advanced Metrics View
CREATE OR REPLACE VIEW advanced_metrics_view AS
SELECT 
    -- Financial averages
    ROUND(AVG(CASE WHEN pe_ratio > 0 AND pe_ratio < 100 THEN pe_ratio END), 2) as average_pe,
    ROUND(AVG(CASE WHEN dividend_yield > 0 THEN dividend_yield END), 2) as average_dividend_yield,
    
    -- Market indicators
    ROUND(
        COUNT(CASE WHEN ma20 IS NOT NULL AND current_price > ma20 THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN ma20 IS NOT NULL THEN 1 END), 0), 2
    ) as market_breadth,
    
    -- Quality index (complete data)
    ROUND(
        COUNT(CASE 
            WHEN pe_ratio IS NOT NULL 
            AND dividend_yield IS NOT NULL 
            AND ma20 IS NOT NULL 
            THEN 1 END) * 100.0 / COUNT(*), 2
    ) as quality_score,
    
    -- Speculation index
    ROUND(
        COUNT(CASE WHEN speculation_opportunity = true OR change_percent > 10 THEN 1 END) * 100.0 / 
        COUNT(*), 2
    ) as speculation_index,
    
    -- Volatility index (average absolute change)
    ROUND(AVG(ABS(change_percent)), 2) as volatility_index,
    
    -- Momentum points
    ROUND(
        SUM(CASE 
            WHEN change_percent > 5 THEN 3
            WHEN change_percent > 2 THEN 2
            WHEN change_percent > 0 THEN 1
            WHEN change_percent < -5 THEN -3
            WHEN change_percent < -2 THEN -2
            WHEN change_percent < 0 THEN -1
            ELSE 0
        END) * 1.0 / COUNT(*), 2
    ) as momentum_score,
    
    -- Last update
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL;

-- 4. Top Performers View
CREATE OR REPLACE VIEW top_performers_view AS
SELECT 
    'gainers' as category,
    symbol,
    name,
    current_price,
    change_percent,
    volume,
    ROW_NUMBER() OVER (ORDER BY change_percent DESC) as rank
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL
  AND change_percent > 0
  AND volume > 0
UNION ALL
SELECT 
    'losers' as category,
    symbol,
    name,
    current_price,
    change_percent,
    volume,
    ROW_NUMBER() OVER (ORDER BY change_percent ASC) as rank
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL
  AND change_percent < 0
  AND volume > 0
UNION ALL
SELECT 
    'most_active' as category,
    symbol,
    name,
    current_price,
    change_percent,
    volume,
    ROW_NUMBER() OVER (ORDER BY volume DESC) as rank
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL
  AND volume > 0;

-- 5. Sector Performance View
CREATE OR REPLACE VIEW sector_performance_view AS
SELECT 
    sector,
    COUNT(*) as total_stocks,
    COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
    COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
    ROUND(AVG(change_percent), 2) as avg_change,
    SUM(volume) as total_volume,
    SUM(turnover) as total_turnover,
    ROUND(AVG(pe_ratio), 2) as avg_pe,
    ROUND(AVG(dividend_yield), 2) as avg_dividend_yield,
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL
  AND sector IS NOT NULL
GROUP BY sector
ORDER BY avg_change DESC;

-- 6. Market Liquidity View
CREATE OR REPLACE VIEW market_liquidity_view AS
SELECT 
    -- Liquidity distribution
    COUNT(CASE WHEN liquidity_ratio > 2 THEN 1 END) as high_liquidity,
    COUNT(CASE WHEN liquidity_ratio BETWEEN 1 AND 2 THEN 1 END) as medium_liquidity,
    COUNT(CASE WHEN liquidity_ratio < 1 THEN 1 END) as low_liquidity,
    
    -- Flow analysis
    SUM(liquidity_inflow) as total_inflow,
    SUM(liquidity_outflow) as total_outflow,
    SUM(net_liquidity) as total_net_liquidity,
    
    -- Volume analysis
    SUM(volume_inflow) as total_volume_inflow,
    SUM(volume_outflow) as total_volume_outflow,
    
    -- Averages
    ROUND(AVG(liquidity_ratio), 2) as avg_liquidity_ratio,
    ROUND(AVG(liquidity_flow), 2) as avg_liquidity_flow,
    
    -- Last update
    MAX(updated_at) as last_updated
FROM stocks_realtime 
WHERE symbol NOT LIKE '%EGX%' 
  AND symbol NOT LIKE '%ETF%'
  AND name IS NOT NULL
  AND liquidity_ratio IS NOT NULL;

-- Grant permissions for the views
GRANT SELECT ON market_summary_view TO anon, authenticated;
GRANT SELECT ON technical_analysis_view TO anon, authenticated;
GRANT SELECT ON advanced_metrics_view TO anon, authenticated;
GRANT SELECT ON top_performers_view TO anon, authenticated;
GRANT SELECT ON sector_performance_view TO anon, authenticated;
GRANT SELECT ON market_liquidity_view TO anon, authenticated;

-- Add comments for documentation
COMMENT ON VIEW market_summary_view IS 'Precomputed market summary statistics';
COMMENT ON VIEW technical_analysis_view IS 'Technical analysis indicators and trends';
COMMENT ON VIEW advanced_metrics_view IS 'Advanced market metrics and quality scores';
COMMENT ON VIEW top_performers_view IS 'Top gaining, losing, and most active stocks';
COMMENT ON VIEW sector_performance_view IS 'Performance metrics grouped by sector';
COMMENT ON VIEW market_liquidity_view IS 'Market liquidity analysis and flow metrics';
