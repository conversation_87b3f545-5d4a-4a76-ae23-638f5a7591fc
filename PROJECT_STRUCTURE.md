# هيكل المشروع - EGX Stock AI Oracle 📊

## 📁 هيكل المجلدات الرئيسية

```
egx-stock-ai-oracle/
├── 📄 README.md                    # ملف التعريف الرئيسي
├── 📄 package.json                 # إعدادات Node.js والتبعيات
├── 📄 vite.config.ts              # إعدادات Vite
├── 📄 tailwind.config.ts          # إعدادات Tailwind CSS
├── 📄 tsconfig.json               # إعدادات TypeScript
│
├── 📂 src/                        # الكود المصدري الرئيسي
│   ├── 📂 components/             # مكونات React
│   ├── 📂 hooks/                  # React Hooks
│   ├── 📂 pages/                  # صفحات التطبيق
│   ├── 📂 lib/                    # مكتبات مساعدة
│   ├── 📂 contexts/               # React Contexts
│   └── 📂 integrations/           # تكاملات خارجية
│
├── 📂 docs/                       # 📚 التوثيق والأدلة
│   ├── 📂 guides/                 # أدلة الاستخدام
│   ├── 📂 schemas/                # مخططات قاعدة البيانات
│   ├── 📂 completion-reports/     # تقارير إكمال المراحل
│   ├── 📄 ADVANCED_FEATURES.md    # الميزات المتقدمة
│   └── 📄 DEEP_ANALYSIS_AND_RECOMMENDATIONS.md
│
├── 📂 tests/                      # 🧪 ملفات الاختبار
│   ├── 📄 final_system_test.py    # اختبار النظام النهائي
│   ├── 📄 test_frontend_api.js    # اختبار واجهة برمجة التطبيقات
│   ├── 📄 test_realtime_updates.py # اختبار التحديثات اللحظية
│   └── ...                       # المزيد من ملفات الاختبار
│
├── 📂 scripts/                    # 🔧 سكريبتات الأتمتة
│   ├── 📂 data_sync/              # سكريبتات مزامنة البيانات
│   ├── 📄 setup_rls_policies.py   # إعداد سياسات الأمان
│   └── 📄 update_market_distribution.py
│
├── 📂 supabase/                   # ⚡ إعدادات Supabase
│   ├── 📂 migrations/             # هجرات قاعدة البيانات
│   └── 📂 functions/              # دوال Supabase Edge
│
└── 📂 public/                     # 🌐 الملفات العامة
    ├── 📄 favicon.ico
    ├── 📄 manifest.json
    └── 📄 sw.js                   # Service Worker
```

## 📚 مجلد التوثيق (docs/)

### 📖 الأدلة (guides/)
- `DATA_INTEGRATION_COMPLETE.md` - دليل تكامل البيانات
- `DATA_UPDATE_SYSTEM_GUIDE.md` - دليل نظام تحديث البيانات
- `LIVE_TRADING_SCREEN_GUIDE.md` - دليل الشاشة اللحظية
- `TRADINGVIEW_CHART_GUIDE.md` - دليل رسوم TradingView

### 🗄️ مخططات قاعدة البيانات (schemas/)
- `CURRENT_DATABASE_SCHEMA.md` - المخطط الحالي لقاعدة البيانات
- `DATABASE_README.md` - دليل قاعدة البيانات
- `DATABASE_SCHEMA_PLAN.md` - خطة مخطط قاعدة البيانات
- `database_schema_reference.json` - مرجع JSON للمخطط

### 📋 تقارير الإكمال (completion-reports/)
- `EGX30_COMPLETION_SUMMARY.md` - ملخص إكمال مؤشر EGX30
- `LIVE_TRADING_COMPLETION_SUMMARY.md` - ملخص إكمال الشاشة اللحظية
- `PHASE_3_COMPLETION_SUMMARY.md` - ملخص إكمال المرحلة الثالثة

## 🧪 مجلد الاختبارات (tests/)

### 🔍 اختبارات النظام
- `final_system_test.py` - الاختبار النهائي للنظام
- `final_stocks_test.py` - اختبار البيانات النهائية
- `final_success_test.py` - اختبار النجاح النهائي

### 🌐 اختبارات الواجهة الأمامية
- `test_frontend_api.js` - اختبار API الواجهة الأمامية
- `simple_frontend_test.mjs` - اختبار بسيط للواجهة

### 📈 اختبارات البيانات اللحظية
- `test_realtime_updates.py` - اختبار التحديثات اللحظية
- `test_active_stocks.py` - اختبار الأسهم النشطة
- `test_change_percent_fix.py` - اختبار إصلاح نسب التغيير

## 🔧 مجلد السكريبتات (scripts/)

### 📊 مزامنة البيانات (data_sync/)
- `load_realtime.py` - تحميل البيانات اللحظية
- `scheduler.py` - جدولة التحديثات التلقائية
- `add_missing_columns.py` - إضافة الأعمدة المفقودة

### 🔒 إدارة الأمان
- `setup_rls_policies.py` - إعداد سياسات الأمان
- `fix_rls_policies.py` - إصلاح سياسات الأمان

## 🚀 البدء السريع

### 1️⃣ تثبيت التبعيات
```bash
npm install
```

### 2️⃣ تشغيل التطبيق للتطوير
```bash
npm run dev
```

### 3️⃣ تشغيل الاختبارات
```bash
# اختبار JavaScript
npm test

# اختبار Python
cd tests
python final_system_test.py
```

### 4️⃣ بناء للإنتاج
```bash
npm run build
```

## 📝 ملاحظات مهمة

- ✅ جميع ملفات التوثيق منظمة في `docs/`
- ✅ جميع ملفات الاختبار منظمة في `tests/`
- ✅ السكريبتات المساعدة في `scripts/`
- ✅ الكود المصدري منظم في `src/`

## 🔄 معدل التحديث اللحظي
- **البيانات اللحظية**: كل 10 ثوانِ في الواجهة الأمامية
- **تحديث البيانات**: كل 5-10 دقائق من مصدر Excel
- **مزامنة قاعدة البيانات**: تلقائية عبر سكريبتات Python

---

📞 **للدعم والاستفسارات**: راجع ملفات التوثيق في مجلد `docs/`
