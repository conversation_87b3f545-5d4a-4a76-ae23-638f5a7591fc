
-- 1. Core master data for stocks
CREATE TABLE IF NOT EXISTS stocks_master (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(200) NOT NULL,
  name_ar VARCHAR(200),
  sector VARCHAR(100),
  sector_ar VARCHAR(100),
  industry VARCHAR(100),
  market VARCHAR(50) DEFAULT 'EGX',
  is_active BOOLEAN DEFAULT true,
  listing_date DATE,
  isin VARCHAR(20),
  free_float_shares BIGINT,
  total_shares BIGINT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Daily historical OHLCV prices
CREATE TABLE IF NOT EXISTS stocks_historical (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(20) NOT NULL,
  date DATE NOT NULL,
  open DECIMAL(12,4) NOT NULL,
  high DECIMAL(12,4) NOT NULL,
  low DECIMAL(12,4) NOT NULL,
  close DECIMAL(12,4) NOT NULL,
  volume BIGINT DEFAULT 0,
  open_interest BIGINT DEFAULT 0,
  adjusted_close DECIMAL(12,4),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(symbol, date)
);

-- 3. Real-time market snapshot (updates frequently)
CREATE TABLE IF NOT EXISTS stocks_realtime (
  symbol VARCHAR(20) PRIMARY KEY,
  current_price DECIMAL(12,4),
  open_price DECIMAL(12,4),
  high_price DECIMAL(12,4),
  low_price DECIMAL(12,4),
  previous_close DECIMAL(12,4),
  change_amount DECIMAL(12,4),
  change_percent DECIMAL(8,4),
  volume BIGINT,
  turnover DECIMAL(15,2),
  trades_count INTEGER,
  bid_price DECIMAL(12,4),
  ask_price DECIMAL(12,4),
  bid_volume BIGINT,
  ask_volume BIGINT,
  last_trade_time TIMESTAMPTZ,
  market_cap DECIMAL(15,2),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Financials table: ratios, yearly/quarterly data
CREATE TABLE IF NOT EXISTS stocks_financials (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(20) NOT NULL,
  fiscal_year INTEGER,
  fiscal_quarter INTEGER,
  market_cap DECIMAL(15,2),
  pe_ratio DECIMAL(8,4),
  eps_ttm DECIMAL(8,4),
  eps_growth_yoy DECIMAL(8,4),
  dividend_yield DECIMAL(8,4),
  book_value_per_share DECIMAL(8,4),
  price_to_book DECIMAL(8,4),
  revenue_ttm DECIMAL(15,2),
  net_income_ttm DECIMAL(15,2),
  total_assets DECIMAL(15,2),
  total_debt DECIMAL(15,2),
  free_cash_flow DECIMAL(15,2),
  roe DECIMAL(8,4),
  roa DECIMAL(8,4),
  debt_to_equity DECIMAL(8,4),
  current_ratio DECIMAL(8,4),
  gross_margin DECIMAL(8,4),
  operating_margin DECIMAL(8,4),
  net_margin DECIMAL(8,4),
  beta DECIMAL(8,4),
  analyst_rating VARCHAR(20),
  target_price DECIMAL(12,4),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Technical indicator snapshot per day/timeframe
CREATE TABLE IF NOT EXISTS technical_indicators (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(20) NOT NULL,
  date DATE NOT NULL,
  timeframe VARCHAR(10) DEFAULT 'D',
  sma_5 DECIMAL(12,4),
  sma_10 DECIMAL(12,4),
  sma_20 DECIMAL(12,4),
  sma_50 DECIMAL(12,4),
  sma_100 DECIMAL(12,4),
  sma_200 DECIMAL(12,4),
  ema_12 DECIMAL(12,4),
  ema_26 DECIMAL(12,4),
  rsi_14 DECIMAL(8,4),
  macd DECIMAL(8,4),
  macd_signal DECIMAL(8,4),
  macd_histogram DECIMAL(8,4),
  bb_upper DECIMAL(12,4),
  bb_middle DECIMAL(12,4),
  bb_lower DECIMAL(12,4),
  stoch_k DECIMAL(8,4),
  stoch_d DECIMAL(8,4),
  atr_14 DECIMAL(8,4),
  adx_14 DECIMAL(8,4),
  cci_20 DECIMAL(8,4),
  williams_r DECIMAL(8,4),
  momentum_10 DECIMAL(8,4),
  roc_10 DECIMAL(8,4),
  volume_sma_20 DECIMAL(15,2),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(symbol, date, timeframe)
);

-- 6. Users (for advanced authenticated features)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  full_name VARCHAR(200),
  subscription_tier VARCHAR(50) DEFAULT 'free',
  api_key VARCHAR(100) UNIQUE,
  api_calls_used INTEGER DEFAULT 0,
  api_calls_limit INTEGER DEFAULT 100,
  api_calls_reset_date DATE DEFAULT CURRENT_DATE,
  phone VARCHAR(20),
  country VARCHAR(100) DEFAULT 'Egypt',
  preferred_language VARCHAR(10) DEFAULT 'ar',
  is_active BOOLEAN DEFAULT true,
  email_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. User portfolios (each user can have many)
CREATE TABLE IF NOT EXISTS user_portfolios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  is_default BOOLEAN DEFAULT false,
  total_value DECIMAL(15,2) DEFAULT 0,
  total_cost DECIMAL(15,2) DEFAULT 0,
  total_profit_loss DECIMAL(15,2) DEFAULT 0,
  currency VARCHAR(10) DEFAULT 'EGP',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. Portfolio holdings (stock-by-stock position per portfolio)
CREATE TABLE IF NOT EXISTS portfolio_holdings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  portfolio_id UUID REFERENCES user_portfolios(id) ON DELETE CASCADE,
  symbol VARCHAR(20) NOT NULL,
  quantity DECIMAL(15,4) NOT NULL,
  average_buy_price DECIMAL(12,4) NOT NULL,
  current_price DECIMAL(12,4),
  total_cost DECIMAL(15,2),
  current_value DECIMAL(15,2),
  profit_loss DECIMAL(15,2),
  profit_loss_percent DECIMAL(8,4),
  purchase_date DATE,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 9. User price alerts
CREATE TABLE IF NOT EXISTS price_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  symbol VARCHAR(20) NOT NULL,
  alert_type VARCHAR(20) NOT NULL,
  target_value DECIMAL(12,4) NOT NULL,
  current_value DECIMAL(12,4),
  condition_met BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  notification_methods JSON,
  message_template TEXT,
  triggered_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 10. Notification history
CREATE TABLE IF NOT EXISTS user_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(50),
  priority VARCHAR(20) DEFAULT 'medium',
  is_read BOOLEAN DEFAULT false,
  related_symbol VARCHAR(20),
  action_url TEXT,
  metadata JSON,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 11. API usage logs
CREATE TABLE IF NOT EXISTS api_usage_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  api_key VARCHAR(100),
  endpoint VARCHAR(200) NOT NULL,
  method VARCHAR(10) NOT NULL,
  query_params JSON,
  response_status INTEGER,
  response_time_ms INTEGER,
  data_points_returned INTEGER,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 12. Market news
CREATE TABLE IF NOT EXISTS market_news (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(500) NOT NULL,
  title_ar VARCHAR(500),
  content TEXT,
  content_ar TEXT,
  summary TEXT,
  summary_ar TEXT,
  source VARCHAR(200),
  author VARCHAR(200),
  published_at TIMESTAMPTZ,
  sentiment_score DECIMAL(4,2),
  impact_score DECIMAL(4,2),
  related_symbols VARCHAR(500)[],
  categories VARCHAR(100)[],
  url TEXT,
  image_url TEXT,
  view_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 13. Paper trading accounts (virtual money)
CREATE TABLE IF NOT EXISTS paper_trading_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  account_name VARCHAR(200) NOT NULL,
  initial_balance DECIMAL(15,2) DEFAULT 100000,
  current_balance DECIMAL(15,2),
  total_profit_loss DECIMAL(15,2) DEFAULT 0,
  total_trades INTEGER DEFAULT 0,
  winning_trades INTEGER DEFAULT 0,
  losing_trades INTEGER DEFAULT 0,
  win_rate DECIMAL(6,4),
  sharpe_ratio DECIMAL(8,4),
  max_drawdown DECIMAL(8,4),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 14. Paper trades (virtual trades)
CREATE TABLE IF NOT EXISTS paper_trades (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES paper_trading_accounts(id) ON DELETE CASCADE,
  symbol VARCHAR(20) NOT NULL,
  trade_type VARCHAR(10) NOT NULL,
  quantity DECIMAL(15,4) NOT NULL,
  entry_price DECIMAL(12,4) NOT NULL,
  exit_price DECIMAL(12,4),
  stop_loss DECIMAL(12,4),
  take_profit DECIMAL(12,4),
  profit_loss DECIMAL(15,2),
  commission DECIMAL(8,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'open',
  entry_time TIMESTAMPTZ DEFAULT NOW(),
  exit_time TIMESTAMPTZ,
  notes TEXT,
  strategy_name VARCHAR(200)
);

-- 15. Market indices for dashboards
CREATE TABLE IF NOT EXISTS market_indices (
  symbol VARCHAR(20) PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  name_ar VARCHAR(200),
  current_value DECIMAL(12,4),
  open_value DECIMAL(12,4),
  high_value DECIMAL(12,4),
  low_value DECIMAL(12,4),
  previous_close DECIMAL(12,4),
  change_amount DECIMAL(12,4),
  change_percent DECIMAL(8,4),
  market_cap DECIMAL(18,2),
  volume BIGINT,
  turnover DECIMAL(15,2),
  constituents_count INTEGER,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============== Indexes for queries & analytics ==============
CREATE INDEX IF NOT EXISTS idx_stocks_historical_symbol_date ON stocks_historical(symbol, date DESC);
CREATE INDEX IF NOT EXISTS idx_stocks_historical_date ON stocks_historical(date DESC);

CREATE INDEX IF NOT EXISTS idx_technical_indicators_symbol_date ON technical_indicators(symbol, date DESC, timeframe);

CREATE INDEX IF NOT EXISTS idx_stocks_realtime_updated ON stocks_realtime(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_portfolios_user_id ON user_portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_holdings_portfolio ON portfolio_holdings(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_price_alerts_user_active ON price_alerts(user_id, is_active);

CREATE INDEX IF NOT EXISTS idx_api_usage_user_date ON api_usage_logs(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint ON api_usage_logs(endpoint, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_market_news_published ON market_news(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_market_news_symbols ON market_news USING GIN(related_symbols);
