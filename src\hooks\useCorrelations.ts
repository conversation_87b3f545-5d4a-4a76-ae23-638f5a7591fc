
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface CorrelationResult {
  id: string;
  stock1: string;
  stock2: string;
  correlation: number;
  strength: string;
  analyzed_at: string;
}

export function useCorrelations() {
  return useQuery({
    queryKey: ['analytics_correlations'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('analytics_correlations')
        .select('*')
        .order('analyzed_at', { ascending: false });
      if (error) throw error;
      return data ?? [];
    }
  });
}
