# EGX Stock AI Oracle - Database Schema & Data Integration Plan

## Date: June 15, 2025

## Database Schema Design

### 1. Core Tables

#### `stocks_master`
```sql
CREATE TABLE stocks_master (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_ar VARCHAR(255),
    sector VARCHAR(100),
    industry VARCHAR(100),
    isin VARCHAR(20),
    market_index VARCHAR(50), -- EGX30, EGX70, etc.
    shares_outstanding BIGINT,
    free_float_percent DECIMAL(5,2),
    currency VARCHAR(3) DEFAULT 'EGP',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### `stocks_historical` (For daily OHLCV data)
```sql
CREATE TABLE stocks_historical (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    time_period VARCHAR(5) DEFAULT 'D', -- D for daily
    open_price DECIMAL(15,4),
    high_price DECIMAL(15,4),
    low_price DECIMAL(15,4),
    close_price DECIMAL(15,4),
    volume BIGINT,
    open_interest BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, date, time_period)
);

CREATE INDEX idx_stocks_historical_symbol_date ON stocks_historical(symbol, date DESC);
```

#### `stocks_realtime` (Current market data)
```sql
CREATE TABLE stocks_realtime (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) UNIQUE NOT NULL,
    current_price DECIMAL(15,4),
    open_price DECIMAL(15,4),
    high_price DECIMAL(15,4),
    low_price DECIMAL(15,4),
    previous_close DECIMAL(15,4),
    change_amount DECIMAL(15,4),
    change_percent DECIMAL(8,4),
    volume BIGINT,
    turnover DECIMAL(20,2),
    trades_count INTEGER,
    liquidity_ratio DECIMAL(8,4),
    net_liquidity DECIMAL(20,2),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    trading_date DATE,
    FOREIGN KEY (symbol) REFERENCES stocks_master(symbol)
);
```

#### `stocks_technical` (Technical indicators)
```sql
CREATE TABLE stocks_technical (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    ma5 DECIMAL(15,4),
    ma10 DECIMAL(15,4),
    ma20 DECIMAL(15,4),
    ma50 DECIMAL(15,4),
    ma100 DECIMAL(15,4),
    ma200 DECIMAL(15,4),
    rsi DECIMAL(8,4),
    macd DECIMAL(15,8),
    macd_signal DECIMAL(15,8),
    macd_histogram DECIMAL(15,8),
    bollinger_upper DECIMAL(15,4),
    bollinger_middle DECIMAL(15,4),
    bollinger_lower DECIMAL(15,4),
    tk_indicator DECIMAL(15,4),
    kj_indicator DECIMAL(15,4),
    target_1 DECIMAL(15,4),
    target_2 DECIMAL(15,4),
    target_3 DECIMAL(15,4),
    stop_loss DECIMAL(15,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, date),
    FOREIGN KEY (symbol) REFERENCES stocks_master(symbol)
);
```

#### `stocks_financial` (Financial metrics)
```sql
CREATE TABLE stocks_financial (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    report_date DATE NOT NULL,
    market_cap DECIMAL(20,2),
    pe_ratio DECIMAL(10,4),
    eps_ttm DECIMAL(15,4),
    eps_growth_yoy DECIMAL(8,4),
    dividend_yield DECIMAL(8,4),
    book_value DECIMAL(15,4),
    pb_ratio DECIMAL(10,4),
    revenue_ttm DECIMAL(20,2),
    net_income_ttm DECIMAL(20,2),
    ebitda_ttm DECIMAL(20,2),
    operating_income_ttm DECIMAL(20,2),
    free_cash_flow_ttm DECIMAL(20,2),
    total_assets DECIMAL(20,2),
    total_debt DECIMAL(20,2),
    total_equity DECIMAL(20,2),
    roe DECIMAL(8,4),
    roa DECIMAL(8,4),
    debt_to_equity DECIMAL(10,4),
    current_ratio DECIMAL(10,4),
    revenue_growth_yoy DECIMAL(8,4),
    net_income_growth_yoy DECIMAL(8,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, report_date),
    FOREIGN KEY (symbol) REFERENCES stocks_master(symbol)
);
```

#### `analyst_ratings` (Analyst recommendations and targets)
```sql
CREATE TABLE analyst_ratings (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    rating VARCHAR(20), -- Strong Buy, Buy, Hold, Sell, Strong Sell
    target_price DECIMAL(15,4),
    target_timeframe VARCHAR(20), -- 1 year, 6 months, etc.
    analyst_firm VARCHAR(100),
    report_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (symbol) REFERENCES stocks_master(symbol)
);
```

## Data Integration Strategy

### 1. Historical Data Import (One-time)
```python
# Script: scripts/import_historical_data.py
import pandas as pd
import os
from datetime import datetime

def import_historical_data(data_folder="/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2"):
    """Import all historical data files from meta2 folder"""
    for filename in os.listdir(data_folder):
        if filename.endswith('D.TXT'):
            symbol = filename.replace('D.TXT', '')
            file_path = os.path.join(data_folder, filename)
            
            # Read the file
            df = pd.read_csv(file_path)
            df.columns = ['ticker', 'period', 'date', 'time', 'open', 'high', 'low', 'close', 'volume', 'openint']
            
            # Process and insert into database
            process_historical_file(symbol, df)

def process_historical_file(symbol, df):
    """Process and insert historical data for a symbol"""
    for _, row in df.iterrows():
        date_str = str(row['date'])
        date_obj = datetime.strptime(date_str, '%Y%m%d').date()
        
        # Insert into stocks_historical table
        insert_historical_record(
            symbol=symbol,
            date=date_obj,
            open_price=row['open'],
            high_price=row['high'],
            low_price=row['low'],
            close_price=row['close'],
            volume=row['volume'],
            open_interest=row['openint']
        )
```

### 2. Real-time Data Sync (Daily)
```python
# Script: scripts/sync_realtime_data.py
import pandas as pd

def sync_realtime_data(excel_path="/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx"):
    """Sync real-time data from Excel file"""
    df = pd.read_excel(excel_path)
    
    # Map Arabic column names to English
    column_mapping = {
        'الرمز': 'symbol',
        'الاسم': 'name',
        'إفتتاح': 'open',
        'أعلى': 'high',
        'أدنى': 'low',
        'الاغلاق': 'close',
        'التغير %': 'change_percent',
        'نسبة السيولة %': 'liquidity_ratio',
        'صافي السيولة': 'net_liquidity',
        'الحجم': 'volume',
        'القيمة': 'turnover',
        'الصفقات': 'trades',
        'ma5 (يوم)': 'ma5',
        'ma10 (يوم)': 'ma10',
        'ma20 (يوم)': 'ma20',
        'ma50 (يوم)': 'ma50',
        'ma100 (يوم)': 'ma100',
        'ma200 (يوم)': 'ma200',
        'TK (يوم)': 'tk',
        'KJ (يوم)': 'kj',
        'هدف 1 (يوم)': 'target_1',
        'هدف 2 (يوم)': 'target_2',
        'هدف 3 (يوم)': 'target_3',
        'وقف خسارة (يوم)': 'stop_loss'
    }
    
    df_renamed = df.rename(columns=column_mapping)
    
    # Update stocks_realtime and stocks_technical tables
    for _, row in df_renamed.iterrows():
        update_realtime_data(row)
        update_technical_data(row)
```

### 3. Financial Data Import
```python
# Script: scripts/import_financial_data.py
def import_financial_data(csv_path="/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv"):
    """Import comprehensive financial data"""
    df = pd.read_csv(csv_path)
    
    for _, row in df.iterrows():
        symbol = row['Symbol']
        
        # Extract and insert financial metrics
        insert_financial_data(
            symbol=symbol,
            market_cap=row.get('Market capitalization'),
            pe_ratio=row.get('Price to earnings ratio'),
            eps_ttm=row.get('Earnings per share diluted, Trailing 12 months'),
            eps_growth_yoy=row.get('Earnings per share diluted growth %, TTM YoY'),
            dividend_yield=row.get('Dividend yield %, Trailing 12 months'),
            revenue_ttm=row.get('Total revenue, Annual'),
            net_income_ttm=row.get('Net income, Annual'),
            ebitda_ttm=row.get('EBITDA, Trailing 12 months'),
            # ... other financial metrics
        )
```

## Automation & Scheduling

### Daily Data Update Pipeline
```bash
#!/bin/bash
# scripts/daily_update.sh

# 1. Sync real-time data from Excel
python scripts/sync_realtime_data.py

# 2. Update technical indicators
python scripts/calculate_technical_indicators.py

# 3. Import any new financial data
python scripts/import_financial_data.py

# 4. Update derived metrics and alerts
python scripts/update_alerts.py

# 5. Generate daily reports
python scripts/generate_daily_reports.py
```

### Cron Job Setup
```bash
# Add to crontab for daily execution at 8 PM
0 20 * * * /path/to/egx-stock-ai-oracle/scripts/daily_update.sh
```

## API Endpoints for Frontend

### Stock Data Endpoints
```typescript
// Get real-time stock data
GET /api/stocks/realtime
GET /api/stocks/realtime/{symbol}

// Get historical data
GET /api/stocks/historical/{symbol}?period=1M&interval=1d

// Get technical indicators
GET /api/stocks/technical/{symbol}

// Get financial metrics
GET /api/stocks/financial/{symbol}

// Get analyst ratings
GET /api/stocks/ratings/{symbol}
```

## Implementation Priority

### Phase 1 (Immediate)
1. ✅ Set up core database tables
2. ✅ Import historical data from TXT files
3. ✅ Create daily sync for Excel real-time data
4. ✅ Build basic API endpoints

### Phase 2 (Week 2)
1. Import and process financial data CSV
2. Calculate technical indicators
3. Set up automated daily updates
4. Add data validation and error handling

### Phase 3 (Week 3)
1. Advanced analytics and ML predictions
2. Real-time alerts system
3. Performance optimization
4. Data backup and recovery

## Benefits of This Approach

1. **Comprehensive Coverage**: All three data sources integrated
2. **Scalable**: Can handle historical data going back to 1998
3. **Real-time Ready**: Daily updates keep data current
4. **Rich Analytics**: Technical and fundamental analysis supported
5. **API-First**: Clean separation between data and presentation
6. **Automated**: Minimal manual intervention required

This schema provides a solid foundation for the EGX Stock AI Oracle webapp with robust data management and scalability for future enhancements.
