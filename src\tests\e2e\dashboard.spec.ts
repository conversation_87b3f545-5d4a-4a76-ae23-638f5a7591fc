import { test, expect, Page } from '@playwright/test';

test.describe('Dashboard E2E Tests', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    await page.goto('/');
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('loads the main dashboard successfully', async () => {
    // Check if the main dashboard elements are present
    await expect(page.locator('h1')).toContainText(/EGX Stock AI Oracle|Dashboard/i);
    
    // Check for key components
    await expect(page.locator('[data-testid="market-overview"]')).toBeVisible();
    await expect(page.locator('[data-testid="stock-ticker"]')).toBeVisible();
  });

  test('displays market data correctly', async () => {
    // Wait for market data to load
    await page.waitForSelector('[data-testid="market-indices"]', { timeout: 10000 });
    
    // Check if EGX30 index is displayed
    const egx30Element = page.locator('text=EGX30');
    await expect(egx30Element).toBeVisible();
    
    // Check if price and change are displayed
    await expect(page.locator('[data-testid="index-price"]').first()).toBeVisible();
    await expect(page.locator('[data-testid="index-change"]').first()).toBeVisible();
  });

  test('stock search functionality works', async () => {
    // Find and interact with search input
    const searchInput = page.locator('input[placeholder*="search" i], input[type="search"]');
    await searchInput.fill('ATQA');
    
    // Wait for search results
    await page.waitForTimeout(1000);
    
    // Check if search results appear
    const searchResults = page.locator('[data-testid="search-results"]');
    if (await searchResults.isVisible()) {
      await expect(searchResults.locator('text=ATQA')).toBeVisible();
    }
  });

  test('portfolio section displays correctly', async () => {
    // Check if portfolio section exists
    const portfolioSection = page.locator('[data-testid="portfolio-tracker"], [data-testid="portfolio-section"]');
    
    if (await portfolioSection.isVisible()) {
      // Check portfolio value display
      await expect(portfolioSection.locator('[data-testid="portfolio-value"]')).toBeVisible();
      
      // Check holdings list
      const holdingsSection = portfolioSection.locator('[data-testid="holdings-list"]');
      if (await holdingsSection.isVisible()) {
        await expect(holdingsSection).toContainText(/ATQA|CLHO|Holdings/);
      }
    }
  });

  test('AI insights section loads', async () => {
    // Look for AI insights section
    const aiSection = page.locator('[data-testid="ai-insights"], text=AI');
    
    if (await aiSection.isVisible()) {
      await expect(aiSection).toContainText(/AI|Prediction|Insight/i);
      
      // Check for prediction data
      const predictionElements = page.locator('[data-testid="prediction-item"]');
      if (await predictionElements.count() > 0) {
        await expect(predictionElements.first()).toBeVisible();
      }
    }
  });

  test('charts render correctly', async () => {
    // Wait for charts to load
    await page.waitForTimeout(2000);
    
    // Check for chart containers
    const chartElements = page.locator('canvas, svg, [data-testid*="chart"]');
    const chartCount = await chartElements.count();
    
    if (chartCount > 0) {
      // At least one chart should be visible
      await expect(chartElements.first()).toBeVisible();
    }
  });

  test('responsive design works on mobile', async () => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if mobile navigation works
    const mobileMenu = page.locator('[data-testid="mobile-menu"], button[aria-label*="menu" i]');
    
    if (await mobileMenu.isVisible()) {
      await mobileMenu.click();
      
      // Check if navigation menu appears
      const navMenu = page.locator('[data-testid="nav-menu"], nav');
      await expect(navMenu).toBeVisible();
    }
    
    // Check if content is still accessible on mobile
    await expect(page.locator('h1')).toBeVisible();
  });

  test('real-time updates work', async () => {
    // Get initial price value
    const priceElement = page.locator('[data-testid="stock-price"]').first();
    
    if (await priceElement.isVisible()) {
      const initialPrice = await priceElement.textContent();
      
      // Wait for potential updates
      await page.waitForTimeout(5000);
      
      // Check if WebSocket connection indicator is present
      const connectionStatus = page.locator('[data-testid="connection-status"], [data-testid="websocket-status"]');
      if (await connectionStatus.isVisible()) {
        await expect(connectionStatus).toContainText(/connected/i);
      }
    }
  });

  test('error states are handled gracefully', async () => {
    // Test by simulating network issues
    await page.route('**/api/**', route => route.abort());
    
    // Reload page to trigger errors
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Check if error messages are displayed appropriately
    const errorMessages = page.locator('text=/error|failed|unable/i');
    if (await errorMessages.count() > 0) {
      // Errors should not break the entire page
      await expect(page.locator('body')).toBeVisible();
    }
  });

  test('navigation between sections works', async () => {
    // Test navigation if tabs or links exist
    const navLinks = page.locator('[data-testid="nav-link"], a[href*="#"], button[data-tab]');
    const linkCount = await navLinks.count();
    
    if (linkCount > 0) {
      // Click on the first navigation item
      await navLinks.first().click();
      await page.waitForTimeout(1000);
      
      // Verify URL or content change
      const currentUrl = page.url();
      expect(currentUrl).toBeTruthy();
    }
  });

  test('accessibility standards are met', async () => {
    // Check for basic accessibility features
    const headings = page.locator('h1, h2, h3, h4, h5, h6');
    await expect(headings.first()).toBeVisible();
    
    // Check for alt text on images
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < Math.min(imageCount, 5); i++) {
      const img = images.nth(i);
      if (await img.isVisible()) {
        const altText = await img.getAttribute('alt');
        expect(altText).toBeTruthy();
      }
    }
    
    // Check for proper form labels
    const inputs = page.locator('input[type="text"], input[type="email"], input[type="search"]');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < Math.min(inputCount, 3); i++) {
      const input = inputs.nth(i);
      if (await input.isVisible()) {
        const label = await input.getAttribute('aria-label') || await input.getAttribute('placeholder');
        expect(label).toBeTruthy();
      }
    }
  });
});
