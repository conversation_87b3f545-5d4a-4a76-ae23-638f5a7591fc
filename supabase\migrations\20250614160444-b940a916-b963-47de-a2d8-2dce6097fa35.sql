
-- تفعيل RLS على جدول users إن لم يكن مفعلًا
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- السماح لأي مستخدم مسجل بإدراج نفسه بجدول users بشرط أن يكون ID الخاص به هو نفس الذي من الـ auth
CREATE POLICY "يمكن لكل مستخدم إدراج نفسه" 
    ON users
    FOR INSERT
    WITH CHECK (auth.uid() = id);

-- السماح لكل مستخدم بقراءة بياناته فقط من جدول users
CREATE POLICY "يمكن لكل مستخدم قراءة صفه فقط"
    ON users
    FOR SELECT
    USING (auth.uid() = id);

-- تفعيل RLS على جدول paper_trading_accounts
ALTER TABLE paper_trading_accounts ENABLE ROW LEVEL SECURITY;

-- السماح لكل مستخدم بإنشاء حساب تداول له فقط
CREATE POLICY "يمكن لكل مستخدم إدراج حساباته"
    ON paper_trading_accounts
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- السماح لكل مستخدم بقراءة حساباته فقط
CREATE POLICY "يمكن لكل مستخدم قراءة حساباته"
    ON paper_trading_accounts
    FOR SELECT
    USING (auth.uid() = user_id);

-- كذلك سياسة تحديث وحذف الحسابات لنفس المستخدم
CREATE POLICY "يمكن لكل مستخدم تحديث حساباته"
    ON paper_trading_accounts
    FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "يمكن لكل مستخدم حذف حساباته"
    ON paper_trading_accounts
    FOR DELETE
    USING (auth.uid() = user_id);
