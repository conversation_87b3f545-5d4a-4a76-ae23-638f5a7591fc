#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_SERVICE_ROLE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
    print("❌ Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env file")
    exit(1)

def check_rls_policies():
    """Check current RLS policies for our tables"""
    print("🔍 Checking RLS policies...")
    
    # Query to get RLS policies
    query = """
    SELECT 
        schemaname,
        tablename,
        policyname,
        permissive,
        roles,
        cmd,
        qual,
        with_check
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename IN ('market_indices', 'stocks_realtime', 'stocks_master')
    ORDER BY tablename, policyname;
    """
    
    # Execute query using service role
    response = requests.post(
        f"{SUPABASE_URL}/rest/v1/rpc/query_policies",
        headers={
            "apikey": SUPABASE_SERVICE_ROLE_KEY,
            "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
            "Content-Type": "application/json"
        },
        json={"query": query}
    )
    
    if response.status_code == 200:
        policies = response.json()
        print(f"✅ Found {len(policies)} RLS policies")
        for policy in policies:
            print(f"  📋 {policy['tablename']}: {policy['policyname']}")
    else:
        print(f"❌ Error checking policies: {response.status_code}")
        print(response.text)

def check_table_accessibility():
    """Test direct table access using different keys"""
    print("\n🔍 Testing table accessibility...")
    
    tables_to_test = ['market_indices', 'stocks_realtime', 'stocks_master']
    
    # Test with service role key
    print("\n🔑 Testing with SERVICE ROLE key:")
    for table in tables_to_test:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/{table}?select=*&limit=1",
            headers={
                "apikey": SUPABASE_SERVICE_ROLE_KEY,
                "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}"
            }
        )
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ {table}: {len(data)} records accessible")
        else:
            print(f"  ❌ {table}: Error {response.status_code}")
    
    # Test with anon key
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    print("\n🌐 Testing with ANON key:")
    for table in tables_to_test:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/{table}?select=*&limit=1",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ {table}: {len(data)} records accessible")
        else:
            print(f"  ❌ {table}: Error {response.status_code} - {response.text[:100]}")

def fix_rls_policies():
    """Create or update RLS policies to allow public read access"""
    print("\n🔧 Fixing RLS policies...")
    
    # SQL to create/recreate public read policies
    sql_commands = [
        "DROP POLICY IF EXISTS \"Allow public read access to market_indices\" ON market_indices;",
        "CREATE POLICY \"Allow public read access to market_indices\" ON market_indices FOR SELECT USING (true);",
        
        "DROP POLICY IF EXISTS \"Allow public read access to stocks_realtime\" ON stocks_realtime;", 
        "CREATE POLICY \"Allow public read access to stocks_realtime\" ON stocks_realtime FOR SELECT USING (true);",
        
        "DROP POLICY IF EXISTS \"Allow public read access to stocks_master\" ON stocks_master;",
        "CREATE POLICY \"Allow public read access to stocks_master\" ON stocks_master FOR SELECT USING (true);",
        
        "DROP POLICY IF EXISTS \"Allow public read access to stocks_historical\" ON stocks_historical;",
        "CREATE POLICY \"Allow public read access to stocks_historical\" ON stocks_historical FOR SELECT USING (true);",
    ]
    
    for i, sql in enumerate(sql_commands, 1):
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
            headers={
                "apikey": SUPABASE_SERVICE_ROLE_KEY,
                "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
                "Content-Type": "application/json"
            },
            json={"sql": sql}
        )
        
        if response.status_code in [200, 201, 204]:
            print(f"  ✅ Command {i}/{len(sql_commands)} executed successfully")
        else:
            print(f"  ❌ Command {i}/{len(sql_commands)} failed: {response.status_code}")
            print(f"     SQL: {sql}")
            print(f"     Error: {response.text}")

if __name__ == "__main__":
    print("🔍 EGX Stock AI Oracle - RLS Diagnostics")
    print("=" * 50)
    
    check_table_accessibility()
    # check_rls_policies()  # This might not work with the RPC approach
    # fix_rls_policies()     # This might also not work with RPC
    
    print("\n" + "=" * 50)
    print("🎯 Diagnostics completed!")
