#!/usr/bin/env python3
"""
EGX Stock Oracle - Backend Server Runner
Start the FastAPI application with optimized settings
"""

import uvicorn
import os
from pathlib import Path

def main():
    """Run the FastAPI application"""
    
    # Get the current directory
    current_dir = Path(__file__).parent
    
    # Set environment variables if not already set
    if not os.getenv('PYTHONPATH'):
        os.environ['PYTHONPATH'] = str(current_dir)
    
    # Configuration
    config = {
        'app': 'app.main:app',
        'host': '0.0.0.0',
        'port': 9000,
        'reload': True,
        'reload_dirs': [str(current_dir / 'app')],
        'log_level': 'info',
        'access_log': True,
        'use_colors': True,
    }
    
    print("🚀 Starting EGX Stock Oracle API Server...")
    print(f"📡 Server will be available at: http://localhost:{config['port']}")
    print(f"📊 API Documentation: http://localhost:{config['port']}/docs")
    print(f"🔄 WebSocket endpoint: ws://localhost:{config['port']}/ws/signals")
    print(f"🎯 Webhook endpoint: http://localhost:{config['port']}/api/v1/webhooks/trading-signals")
    print("-" * 60)
    
    # Start the server
    uvicorn.run(**config)

if __name__ == '__main__':
    main()
