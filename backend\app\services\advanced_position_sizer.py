"""
Advanced Position Sizing Calculator - حاسبة حجم المراكز المتقدمة
يستخدم Kelly Criterion المحسن مع تحليل المخاطر المتقدم
"""

import logging
import numpy as np
from typing import Dict, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

class AdvancedPositionSizer:
    """حاسبة حجم المراكز المتقدمة"""
    
    def __init__(self, db: Session, initial_capital: float = 1000000.0):
        self.db = db
        self.initial_capital = initial_capital
        self.max_position_risk = 0.02  # 2% كحد أقصى للمخاطرة لكل صفقة
        self.max_portfolio_risk = 0.06  # 6% كحد أقصى لإجمالي مخاطر المحفظة
    
    async def calculate_optimal_position_size(self, signal_data: Dict, stock_analysis: Dict, signal_quality: Dict) -> Dict:
        """حساب حجم المركز الأمثل باستخدام Kelly Criterion المحسن"""
        try:
            # الحصول على رأس المال المتاح
            available_capital = await self._get_available_capital()
            
            # استخراج البيانات المطلوبة
            current_price = stock_analysis.get('current_price', 0)
            signal_confidence = signal_quality.get('overall_score', 50) / 100
            volatility_data = stock_analysis.get('volatility_analysis', {})
            liquidity_data = stock_analysis.get('liquidity_analysis', {})
            
            if current_price <= 0:
                return {'error': 'Invalid current price', 'shares': 0}
            
            # 1. Kelly Criterion المحسن
            kelly_result = await self._calculate_enhanced_kelly(signal_data, signal_confidence)
            
            # 2. تعديلات المخاطر المتقدمة
            risk_adjustments = await self._calculate_risk_adjustments(
                stock_analysis, signal_quality, signal_data['stock_code']
            )
            
            # 3. حساب الحجم الأساسي
            base_allocation = available_capital * self.max_position_risk
            
            # 4. تطبيق Kelly Criterion
            kelly_allocation = base_allocation * kelly_result['kelly_fraction']
            
            # 5. تطبيق تعديلات المخاطر
            final_allocation = kelly_allocation
            for adjustment_name, adjustment_value in risk_adjustments.items():
                final_allocation *= adjustment_value
            
            # 6. التأكد من عدم تجاوز الحدود
            max_position_value = min(
                final_allocation,
                available_capital * 0.15,  # حد أقصى 15% من رأس المال
                liquidity_data.get('recommendations', {}).get('max_position_size', 1000000),
                self._calculate_liquidity_limit(liquidity_data, current_price)
            )
            
            # 7. حساب عدد الأسهم
            shares = int(max_position_value / current_price)
            actual_value = shares * current_price
            
            # 8. حساب مستويات إدارة المخاطر
            risk_levels = await self._calculate_risk_levels(current_price, volatility_data)
            
            # 9. حساب المخاطرة الفعلية
            risk_amount = shares * (current_price - risk_levels['stop_loss_price'])
            risk_percentage = (risk_amount / available_capital) * 100
            
            # 10. تقييم جودة الصفقة
            trade_quality = await self._evaluate_trade_quality(
                actual_value, risk_amount, signal_quality, stock_analysis
            )
            
            return {
                'shares': shares,
                'total_value': actual_value,
                'entry_price': current_price,
                'risk_amount': risk_amount,
                'risk_percentage': round(risk_percentage, 2),
                'stop_loss_price': risk_levels['stop_loss_price'],
                'target_prices': risk_levels['target_prices'],
                'kelly_analysis': kelly_result,
                'risk_adjustments': risk_adjustments,
                'capital_allocation': {
                    'available_capital': available_capital,
                    'base_allocation': base_allocation,
                    'kelly_allocation': kelly_allocation,
                    'final_allocation': actual_value,
                    'allocation_percentage': round((actual_value / available_capital) * 100, 2)
                },
                'trade_quality': trade_quality,
                'position_limits': {
                    'max_individual_risk': self.max_position_risk * 100,
                    'max_portfolio_risk': self.max_portfolio_risk * 100,
                    'liquidity_limit': self._calculate_liquidity_limit(liquidity_data, current_price)
                },
                'confidence_metrics': {
                    'overall_confidence': round(np.mean([
                        signal_confidence * 100,
                        kelly_result['confidence_score'],
                        trade_quality['quality_score']
                    ]), 2),
                    'risk_reward_ratio': round(
                        (risk_levels['target_prices']['target_2'] - current_price) / 
                        (current_price - risk_levels['stop_loss_price']), 2
                    )
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {'error': str(e), 'shares': 0}
    
    async def _calculate_enhanced_kelly(self, signal_data: Dict, signal_confidence: float) -> Dict:
        """حساب Kelly Criterion المحسن"""
        try:
            # تحليل الأداء التاريخي للإشارات المشابهة
            historical_performance = await self._analyze_historical_signal_performance(signal_data)
            
            # حساب معدل الربح المتوقع بناءً على البيانات التاريخية
            win_rate = historical_performance.get('win_rate', 0.55)
            avg_win = historical_performance.get('avg_win', 0.08)
            avg_loss = historical_performance.get('avg_loss', 0.04)
            
            # تعديل المعدلات حسب جودة الإشارة
            adjusted_win_rate = win_rate + (signal_confidence - 0.5) * 0.2
            adjusted_win_rate = max(0.3, min(0.8, adjusted_win_rate))
            
            # حساب Kelly Fraction الأساسي
            basic_kelly = (adjusted_win_rate * avg_win - (1 - adjusted_win_rate) * avg_loss) / avg_win
            
            # تطبيق تحسينات Kelly
            # 1. Half Kelly للحماية من التقلبات
            conservative_kelly = basic_kelly * 0.5
            
            # 2. Dynamic Kelly حسب ظروف السوق
            market_conditions = await self._assess_market_conditions()
            market_adjusted_kelly = conservative_kelly * market_conditions
            
            # 3. حد أقصى للأمان
            final_kelly = max(0.05, min(0.25, market_adjusted_kelly))
            
            return {
                'kelly_fraction': final_kelly,
                'basic_kelly': basic_kelly,
                'conservative_kelly': conservative_kelly,
                'market_adjusted_kelly': market_adjusted_kelly,
                'win_rate': adjusted_win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'confidence_score': round(signal_confidence * 100, 2),
                'market_conditions': market_conditions,
                'historical_data': historical_performance
            }
            
        except Exception as e:
            logger.error(f"Error calculating enhanced Kelly: {e}")
            return {'kelly_fraction': 0.1, 'error': str(e)}
    
    async def _calculate_risk_adjustments(self, stock_analysis: Dict, signal_quality: Dict, stock_code: str) -> Dict:
        """حساب تعديلات المخاطر المتقدمة"""
        try:
            adjustments = {}
            
            # 1. تعديل التقلبات
            volatility_data = stock_analysis.get('volatility_analysis', {})
            vol_multiplier = volatility_data.get('position_sizing_adjustment', {}).get('volatility_multiplier', 1.0)
            adjustments['volatility_adjustment'] = vol_multiplier
            
            # 2. تعديل السيولة
            liquidity_data = stock_analysis.get('liquidity_analysis', {})
            liquidity_score = liquidity_data.get('liquidity_score', 70)
            adjustments['liquidity_adjustment'] = min(1.0, liquidity_score / 70)
            
            # 3. تعديل جودة الإشارة
            signal_score = signal_quality.get('overall_score', 50)
            adjustments['signal_quality_adjustment'] = signal_score / 100
            
            # 4. تعديل تركز المحفظة
            concentration_data = await self._check_portfolio_concentration(stock_code)
            concentration_adj = 1.0 - (concentration_data.get('current_concentration', 0) / 100)
            adjustments['concentration_adjustment'] = max(0.3, concentration_adj)
            
            # 5. تعديل ظروف السوق
            market_conditions = await self._assess_market_conditions()
            adjustments['market_adjustment'] = max(0.5, market_conditions)
            
            # 6. تعديل التحليل الفني
            technical_score = stock_analysis.get('technical_analysis', {}).get('overall_score', 50)
            adjustments['technical_adjustment'] = max(0.7, technical_score / 100)
            
            # 7. تعديل حسب القطاع
            sector_adjustment = await self._calculate_sector_adjustment(stock_code)
            adjustments['sector_adjustment'] = sector_adjustment
            
            return adjustments
            
        except Exception as e:
            logger.error(f"Error calculating risk adjustments: {e}")
            return {'volatility_adjustment': 0.8, 'liquidity_adjustment': 0.8}
    
    async def _calculate_risk_levels(self, current_price: float, volatility_data: Dict) -> Dict:
        """حساب مستويات إدارة المخاطر المتقدمة"""
        try:
            volatility_30d = volatility_data.get('volatility_30d', 25)
            
            # Stop Loss ديناميكي حسب التقلبات
            if volatility_30d <= 20:
                stop_loss_percentage = 0.04  # 4% للأسهم منخفضة التقلب
            elif volatility_30d <= 35:
                stop_loss_percentage = 0.06  # 6% للأسهم متوسطة التقلب
            else:
                stop_loss_percentage = 0.08  # 8% للأسهم عالية التقلب
            
            stop_loss_price = current_price * (1 - stop_loss_percentage)
            
            # أهداف الربح المتدرجة
            target_multipliers = {
                'target_1': 1 + (stop_loss_percentage * 0.75),  # ربح أقل من المخاطرة قليلاً
                'target_2': 1 + (stop_loss_percentage * 1.5),   # ربح 1.5 ضعف المخاطرة
                'target_3': 1 + (stop_loss_percentage * 2.5)    # ربح 2.5 ضعف المخاطرة
            }
            
            target_prices = {
                target: round(current_price * multiplier, 2)
                for target, multiplier in target_multipliers.items()
            }
            
            # Trailing Stop ديناميكي
            trailing_stop_distance = stop_loss_percentage * 0.5  # نصف المسافة الأولية
            
            return {
                'stop_loss_price': round(stop_loss_price, 2),
                'stop_loss_percentage': stop_loss_percentage * 100,
                'target_prices': target_prices,
                'trailing_stop_distance': trailing_stop_distance,
                'risk_reward_ratios': {
                    f'{target}': round((price - current_price) / (current_price - stop_loss_price), 2)
                    for target, price in target_prices.items()
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating risk levels: {e}")
            return {
                'stop_loss_price': current_price * 0.94,
                'target_prices': {
                    'target_1': current_price * 1.03,
                    'target_2': current_price * 1.06,
                    'target_3': current_price * 1.10
                }
            }
    
    async def _evaluate_trade_quality(self, position_value: float, risk_amount: float, 
                                    signal_quality: Dict, stock_analysis: Dict) -> Dict:
        """تقييم جودة الصفقة الإجمالية"""
        try:
            quality_factors = {}
            
            # 1. نسبة المخاطرة إلى رأس المال
            available_capital = await self._get_available_capital()
            risk_to_capital = (risk_amount / available_capital) * 100
            quality_factors['risk_management'] = max(0, 100 - (risk_to_capital * 10))
            
            # 2. جودة الإشارة
            quality_factors['signal_strength'] = signal_quality.get('overall_score', 50)
            
            # 3. جودة السهم
            stock_quality = stock_analysis.get('quality_score', 50)
            quality_factors['stock_quality'] = stock_quality
            
            # 4. توقيت السوق
            market_timing = await self._assess_market_timing()
            quality_factors['market_timing'] = market_timing
            
            # 5. تنويع المحفظة
            diversification = await self._assess_portfolio_diversification()
            quality_factors['diversification'] = diversification
            
            # حساب النتيجة الإجمالية
            weights = {
                'risk_management': 0.25,
                'signal_strength': 0.25,
                'stock_quality': 0.20,
                'market_timing': 0.15,
                'diversification': 0.15
            }
            
            quality_score = sum(
                quality_factors[factor] * weights[factor]
                for factor in quality_factors
            )
            
            # تصنيف الجودة
            if quality_score >= 85:
                quality_grade = 'EXCELLENT'
                recommendation = 'STRONG_EXECUTE'
            elif quality_score >= 70:
                quality_grade = 'GOOD'
                recommendation = 'EXECUTE'
            elif quality_score >= 55:
                quality_grade = 'FAIR'
                recommendation = 'CONDITIONAL_EXECUTE'
            elif quality_score >= 40:
                quality_grade = 'POOR'
                recommendation = 'RECONSIDER'
            else:
                quality_grade = 'VERY_POOR'
                recommendation = 'AVOID'
            
            return {
                'quality_score': round(quality_score, 2),
                'quality_grade': quality_grade,
                'recommendation': recommendation,
                'quality_factors': quality_factors,
                'weighted_factors': {
                    factor: round(score * weights[factor], 2)
                    for factor, score in quality_factors.items()
                },
                'risk_assessment': {
                    'risk_to_capital_ratio': round(risk_to_capital, 2),
                    'position_size_appropriateness': 'APPROPRIATE' if risk_to_capital <= 2 else 'TOO_HIGH'
                }
            }
            
        except Exception as e:
            logger.error(f"Error evaluating trade quality: {e}")
            return {'quality_score': 50, 'quality_grade': 'FAIR'}
    
    # =============== وظائف مساعدة ===============
    
    async def _get_available_capital(self) -> float:
        """حساب رأس المال المتاح"""
        try:
            from ..models.smart_portfolio import SmartPortfolioPosition
            from sqlalchemy import func
            
            # حساب رأس المال المستثمر
            active_positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.status == 'ACTIVE').all()
            
            invested_capital = sum(
                pos.remaining_shares * pos.entry_price 
                for pos in active_positions
            )
            
            # حساب الأرباح المحققة
            total_realized_pnl = self.db.query(func.sum(SmartPortfolioPosition.realized_pnl))\
                .scalar() or 0
            
            available = self.initial_capital - invested_capital + total_realized_pnl
            return max(0, available)
            
        except Exception as e:
            logger.error(f"Error getting available capital: {e}")
            return self.initial_capital * 0.7
    
    async def _assess_market_conditions(self) -> float:
        """تقييم ظروف السوق العامة (0-1)"""
        try:
            # تحليل مبسط لظروف السوق
            # في التطبيق الحقيقي، يمكن ربط هذا بمؤشرات السوق الفعلية
            return 0.75  # قيمة افتراضية جيدة
        except:
            return 0.6
    
    async def _check_portfolio_concentration(self, stock_code: str) -> Dict:
        """فحص تركز المحفظة"""
        try:
            from ..models.smart_portfolio import SmartPortfolioPosition
            
            # حساب التركز الحالي
            active_positions = self.db.query(SmartPortfolioPosition)\
                .filter(SmartPortfolioPosition.status == 'ACTIVE').all()
            
            total_value = sum(pos.remaining_shares * pos.entry_price for pos in active_positions)
            stock_value = sum(
                pos.remaining_shares * pos.entry_price 
                for pos in active_positions 
                if pos.stock_code == stock_code
            )
            
            current_concentration = (stock_value / total_value * 100) if total_value > 0 else 0
            
            return {
                'current_concentration': current_concentration,
                'would_exceed_limit': current_concentration > 15  # حد أقصى 15%
            }
            
        except Exception as e:
            logger.error(f"Error checking concentration: {e}")
            return {'current_concentration': 0, 'would_exceed_limit': False}
    
    def _calculate_liquidity_limit(self, liquidity_data: Dict, current_price: float) -> float:
        """حساب حد السيولة للمركز"""
        try:
            avg_volume = liquidity_data.get('metrics', {}).get('avg_volume_30d', 100000)
            max_shares = int(avg_volume * 0.05)  # 5% من متوسط الحجم اليومي
            return max_shares * current_price
        except:
            return 500000  # حد افتراضي
    
    async def _analyze_historical_signal_performance(self, signal_data: Dict) -> Dict:
        """تحليل الأداء التاريخي للإشارات المشابهة"""
        try:
            # في التطبيق الحقيقي، سيتم تحليل البيانات التاريخية الفعلية
            return {
                'win_rate': 0.58,
                'avg_win': 0.085,
                'avg_loss': 0.042,
                'sample_size': 150,
                'confidence_level': 0.85
            }
        except:
            return {'win_rate': 0.55, 'avg_win': 0.08, 'avg_loss': 0.04}
    
    async def _calculate_sector_adjustment(self, stock_code: str) -> float:
        """حساب تعديل القطاع"""
        try:
            # تحليل مبسط للقطاع
            return 0.95  # تعديل طفيف للحذر
        except:
            return 0.9
    
    async def _assess_market_timing(self) -> float:
        """تقييم توقيت السوق"""
        try:
            return 75.0  # قيمة افتراضية جيدة
        except:
            return 60.0
    
    async def _assess_portfolio_diversification(self) -> float:
        """تقييم تنويع المحفظة"""
        try:
            return 80.0  # قيمة افتراضية جيدة
        except:
            return 70.0
