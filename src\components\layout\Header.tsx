
import React from "react";
import { Button } from "@/components/ui/button";
import { LogIn, LogOut } from "lucide-react";
import { useAuthUser } from "@/hooks/useAuthUser";
import { supabase } from "@/integrations/supabase/client";

interface HeaderProps {
  onLoginClick: () => void;
  logoutLoading: boolean;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({ onLoginClick, logoutLoading, onLogout }) => {
  const { user, loading: authLoading } = useAuthUser();

  return (
    <header className="w-full flex items-center justify-between py-3 px-4 md:px-8 bg-white/70 dark:bg-gray-900/70 shadow-sm sticky top-0 z-40">
      <div className="flex items-center gap-4">
        <span className="font-extrabold text-blue-800 text-2xl select-none tracking-wide">
          البورصة المصرية الذكية
        </span>
      </div>
      <div className="flex items-center gap-4">
        {!authLoading && user && (
          <>
            <span className="text-gray-800 dark:text-gray-100 text-sm font-semibold hidden sm:block" dir="ltr">
              {user.email}
            </span>
            <Button onClick={onLogout} variant="outline" size="sm" disabled={logoutLoading}>
              <LogOut className="ml-2" /> {logoutLoading ? "..." : "تسجيل الخروج"}
            </Button>
          </>
        )}
        {!authLoading && !user && (
          <Button onClick={onLoginClick} variant="outline" size="sm">
            <LogIn className="ml-2" /> تسجيل الدخول / إنشاء حساب
          </Button>
        )}
      </div>
    </header>
  );
};

export default Header;

