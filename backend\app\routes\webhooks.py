# Trading Signals Webhook Routes
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
import json
import logging

from ..database import get_db
from ..models.trading_signals import TradingSignal, LiveRecommendations, SignalPerformance
from ..services.signal_processor import SignalProcessor
from ..services.notification_service import NotificationService
from ..services.telegram_service import TelegramService
from ..services.websocket_manager import signal_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/webhook", tags=["webhooks"])

# Pydantic models for signal validation
class BuySignal(BaseModel):
    stock_code: str = Field(..., description="Stock symbol")
    report: str = Field(..., description="Signal type (buy)")
    buy_price: str = Field(..., description="Entry price")
    tp1: str = Field(..., description="First target price")
    tp2: str = Field(..., description="Second target price")
    tp3: str = Field(..., description="Third target price")
    sl: str = Field(..., description="Stop loss price")

class SellSignal(BaseModel):
    stock_code: str = Field(..., description="Stock symbol")
    report: str = Field(..., description="Signal type (sell)")
    sell_price: str = Field(..., description="Exit price")

class TPSignal(BaseModel):
    stock_code: str = Field(..., description="Stock symbol")
    report: str = Field(..., description="Signal type (tp1done, tp2done, tp3done)")
    tp1: Optional[str] = Field(None, description="Target price hit")
    tp2: Optional[str] = Field(None, description="Target price hit")
    tp3: Optional[str] = Field(None, description="Target price hit")
    sl: str = Field(..., description="New stop loss")

class StopLossSignal(BaseModel):
    stock_code: str = Field(..., description="Stock symbol")
    report: str = Field(..., description="Signal type (tsl)")
    sl: str = Field(..., description="Stop loss price")

class TradingSignalPayload(BaseModel):
    stock_code: str
    report: str
    buy_price: Optional[str] = None
    tp1: Optional[str] = None
    tp2: Optional[str] = None
    tp3: Optional[str] = None
    sl: Optional[str] = None
    sell_price: Optional[str] = None

# Price Update Model
class PriceUpdate(BaseModel):
    stock_code: str = Field(..., description="Stock symbol")
    current_price: float = Field(..., description="Current market price")
    timestamp: Optional[str] = Field(None, description="Price timestamp")

@router.post("/trading-signals", 
             summary="Receive Trading Signals",
             description="Webhook endpoint to receive trading signals from external alerting system")
async def receive_trading_signal(
    signal: TradingSignalPayload,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    **PRIORITY ENDPOINT** - Receive and process trading signals
    
    This is the main webhook endpoint for receiving trading signals from external systems.
    Processes buy, sell, take profit, and stop loss signals in real-time.
    
    Signal Types:
    - buy: New buy signal with TP/SL levels
    - sell: Sell signal to close position
    - tp1done, tp2done, tp3done: Take profit levels hit
    - tsl: Trailing stop loss update
    """
    try:
        # Log incoming signal
        logger.info(f"Received trading signal: {signal.dict()}")
        
        # Validate signal data
        if not signal.stock_code or not signal.report:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing required fields: stock_code and report"
            )
        
        # Convert string prices to float
        signal_data = signal.dict()
        float_fields = ['buy_price', 'tp1', 'tp2', 'tp3', 'sl', 'sell_price']
        for field in float_fields:
            if signal_data.get(field):
                try:
                    signal_data[field] = float(signal_data[field])
                except ValueError:
                    logger.warning(f"Invalid {field} value: {signal_data[field]}")
                    signal_data[field] = None
        
        # Create trading signal record
        db_signal = TradingSignal(
            stock_code=signal.stock_code.upper(),
            signal_type=signal.report,
            buy_price=signal_data.get('buy_price'),
            tp1=signal_data.get('tp1'),
            tp2=signal_data.get('tp2'),
            tp3=signal_data.get('tp3'),
            stop_loss=signal_data.get('sl'),
            sell_price=signal_data.get('sell_price'),
            status='active',
            raw_data=signal_data
        )
        
        db.add(db_signal)
        db.commit()
        db.refresh(db_signal)
        
        # Process signal in background
        background_tasks.add_task(
            process_signal_background,
            signal_data,
            db_signal.id
        )
        
        # Send immediate response
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": "Signal received and queued for processing",
                "signal_id": db_signal.id,
                "stock_code": signal.stock_code,
                "signal_type": signal.report,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing trading signal: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

async def process_signal_background(signal_data: Dict[str, Any], signal_id: int):
    """Background task to process trading signal"""
    try:
        processor = SignalProcessor()
        
        # Process based on signal type
        signal_type = signal_data['report']
        stock_code = signal_data['stock_code'].upper()
        
        if signal_type == 'buy':
            await processor.process_buy_signal(signal_data, signal_id)
        elif signal_type == 'sell':
            await processor.process_sell_signal(signal_data, signal_id)
        elif signal_type in ['tp1done', 'tp2done', 'tp3done']:
            await processor.process_tp_signal(signal_data, signal_id)
        elif signal_type == 'tsl':
            await processor.process_stop_loss_signal(signal_data, signal_id)
        
        # Send WebSocket notification
        await signal_manager.broadcast_signal({
            'signal_id': signal_id,
            'stock_code': stock_code,
            'signal_type': signal_type,
            'timestamp': datetime.utcnow().isoformat(),
            **signal_data
        })
        
        logger.info(f"Successfully processed signal {signal_id} for {stock_code}")
        
    except Exception as e:
        logger.error(f"Error in background signal processing: {str(e)}")

@router.get("/signals/recent",
            summary="Get Recent Signals", 
            description="Get recent trading signals for monitoring")
async def get_recent_signals(
    limit: int = 20,
    signal_type: Optional[str] = None,
    stock_code: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get recent trading signals with optional filtering"""
    try:
        query = db.query(TradingSignal).order_by(TradingSignal.created_at.desc())
        
        if signal_type:
            query = query.filter(TradingSignal.signal_type == signal_type)
        
        if stock_code:
            query = query.filter(TradingSignal.stock_code == stock_code.upper())
        
        signals = query.limit(limit).all()
        
        return {
            "status": "success",
            "count": len(signals),
            "signals": [
                {
                    "id": signal.id,
                    "stock_code": signal.stock_code,
                    "signal_type": signal.signal_type,
                    "buy_price": signal.buy_price,
                    "tp1": signal.tp1,
                    "tp2": signal.tp2,
                    "tp3": signal.tp3,
                    "stop_loss": signal.stop_loss,
                    "sell_price": signal.sell_price,
                    "status": signal.status,
                    "created_at": signal.created_at.isoformat() if signal.created_at else None
                }
                for signal in signals
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting recent signals: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving signals"
        )

@router.get("/recommendations/live",
            summary="Get Live Recommendations",
            description="Get current live trading recommendations (التوصيات اللحظية)")
async def get_live_recommendations(
    status: Optional[str] = 'active',
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get live trading recommendations for frontend display"""
    try:
        query = db.query(LiveRecommendations).order_by(LiveRecommendations.created_at.desc())
        
        if status:
            query = query.filter(LiveRecommendations.status == status)
        
        recommendations = query.limit(limit).all()
        
        return {
            "status": "success",
            "count": len(recommendations),
            "recommendations": [
                {
                    "id": rec.id,
                    "stock_code": rec.stock_code,
                    "action": rec.action,
                    "entry_price": rec.entry_price,
                    "current_price": rec.current_price,
                    "tp1": rec.tp1,
                    "tp2": rec.tp2,
                    "tp3": rec.tp3,
                    "tp1_hit": rec.tp1_hit,
                    "tp2_hit": rec.tp2_hit,
                    "tp3_hit": rec.tp3_hit,
                    "stop_loss": rec.stop_loss,
                    "current_pnl": rec.current_pnl,
                    "max_pnl": rec.max_pnl,
                    "risk_reward_ratio": rec.risk_reward_ratio,
                    "status": rec.status,
                    "created_at": rec.created_at.isoformat() if rec.created_at else None,
                    "updated_at": rec.updated_at.isoformat() if rec.updated_at else None
                }
                for rec in recommendations
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting live recommendations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving live recommendations"
        )

@router.get("/signals/performance",
            summary="Signal Performance Metrics",
            description="Get performance metrics for trading signals")
async def get_signal_performance(
    days: int = 30,
    db: Session = Depends(get_db)
):
    """Get trading signals performance metrics"""
    try:
        # Calculate performance metrics
        from datetime import datetime, timedelta
        from sqlalchemy import func
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Get closed recommendations for performance calculation
        closed_recs = db.query(LiveRecommendations).filter(
            LiveRecommendations.status == 'closed',
            LiveRecommendations.created_at >= cutoff_date
        ).all()
        
        if not closed_recs:
            return {
                "status": "success",
                "message": "No closed positions in the specified period",
                "metrics": {
                    "total_signals": 0,
                    "win_rate": 0,
                    "avg_return": 0,
                    "total_return": 0
                }
            }
        
        # Calculate metrics
        total_signals = len(closed_recs)
        profitable_signals = len([r for r in closed_recs if r.current_pnl and r.current_pnl > 0])
        win_rate = (profitable_signals / total_signals) * 100 if total_signals > 0 else 0
        
        total_return = sum(r.current_pnl for r in closed_recs if r.current_pnl)
        avg_return = total_return / total_signals if total_signals > 0 else 0
        
        # Target achievement stats
        tp1_hits = len([r for r in closed_recs if r.tp1_hit])
        tp2_hits = len([r for r in closed_recs if r.tp2_hit])
        tp3_hits = len([r for r in closed_recs if r.tp3_hit])
        
        return {
            "status": "success",
            "period_days": days,
            "metrics": {
                "total_signals": total_signals,
                "profitable_signals": profitable_signals,
                "win_rate": round(win_rate, 2),
                "avg_return": round(avg_return, 2),
                "total_return": round(total_return, 2),
                "tp1_achievement_rate": round((tp1_hits / total_signals) * 100, 2) if total_signals > 0 else 0,
                "tp2_achievement_rate": round((tp2_hits / total_signals) * 100, 2) if total_signals > 0 else 0,
                "tp3_achievement_rate": round((tp3_hits / total_signals) * 100, 2) if total_signals > 0 else 0
            }
        }
        
    except Exception as e:
        logger.error(f"Error calculating signal performance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error calculating performance metrics"
        )

@router.post("/test-signal",
             summary="Test Signal Endpoint",
             description="Test endpoint for signal processing (development only)")
async def test_signal_endpoint(test_data: Dict[str, Any]):
    """Test endpoint for signal processing - development use only"""
    try:
        # Create test signal
        test_signal = TradingSignalPayload(**test_data)
        
        return {
            "status": "success",
            "message": "Test signal validated successfully",
            "parsed_data": test_signal.dict(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Test signal validation failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }

@router.post("/price-update", status_code=status.HTTP_200_OK)
async def update_stock_price(
    price_data: PriceUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Update current price for active recommendations to track highest price
    This endpoint helps maintain accurate trailing stop calculations
    """
    try:
        stock_code = price_data.stock_code.upper()
        current_price = price_data.current_price
        
        # Find active recommendations for this stock
        active_recs = db.query(LiveRecommendations).filter(
            LiveRecommendations.stock_code == stock_code,
            LiveRecommendations.status == 'active'
        ).all()
        
        if not active_recs:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"message": f"No active recommendations found for {stock_code}"}
            )
        
        updated_count = 0
        for rec in active_recs:
            # Update current price
            rec.current_price = current_price
            
            # Update highest price if current price is higher
            if not rec.highest_price or current_price > rec.highest_price:
                rec.highest_price = current_price
                logger.info(f"New highest price for {stock_code}: {current_price}")
            
            # Calculate and update current P&L
            if rec.entry_price:
                current_pnl = rec.calculate_pnl(current_price)
                rec.current_pnl = current_pnl
                
                # Update max P&L if this is better
                if not rec.max_pnl or current_pnl > rec.max_pnl:
                    rec.max_pnl = current_pnl
            
            rec.updated_at = datetime.utcnow()
            updated_count += 1
        
        db.commit()
        
        # Send WebSocket update
        background_tasks.add_task(
            signal_manager.broadcast_recommendation_update,
            {
                "type": "price_update",
                "stock_code": stock_code,
                "current_price": current_price,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
        logger.info(f"Updated {updated_count} active recommendations for {stock_code} at price {current_price}")
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "message": f"Price updated for {updated_count} active recommendations",
                "stock_code": stock_code,
                "current_price": current_price,
                "updated_recommendations": updated_count
            }
        )
        
    except Exception as e:
        logger.error(f"Error updating price: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update price: {str(e)}"
        )
