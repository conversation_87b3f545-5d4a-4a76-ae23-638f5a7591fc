import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface SimpleStockData {
  symbol: string;
  name?: string;
  current_price: number;
  change_percent: number;
  volume: number;
  turnover: number;
  updated_at: string;
}

export const useSimpleLiveData = () => {
  return useQuery<SimpleStockData[]>({
    queryKey: ['simple_live_data'],
    queryFn: async () => {
      try {
        console.log('🔄 Fetching simple live data...');
        
        // استعلام بسيط جداً
        const { data, error } = await supabase
          .from('stocks_realtime')
          .select(`
            symbol,
            current_price,
            change_percent,
            volume,
            turnover,
            updated_at
          `)
          .gt('current_price', 0)
          .limit(50);

        if (error) {
          console.error('❌ Database error:', error);
          throw new Error(`Database error: ${error.message}`);
        }

        if (!data || data.length === 0) {
          console.warn('⚠️ No data returned from database');
          return [];
        }

        console.log(`✅ Fetched ${data.length} stocks`);
        
        // إضافة اسم بسيط
        const enriched = data.map(stock => ({
          ...stock,
          name: stock.symbol // اسم مؤقت
        }));

        return enriched;
      } catch (error) {
        console.error('❌ Error fetching data:', error);
        throw error;
      }
    },
    refetchInterval: 30000, // كل 30 ثانية
    retry: 2,
    retryDelay: 1000,
  });
};
