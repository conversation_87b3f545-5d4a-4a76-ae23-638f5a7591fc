#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎉 EGX Stock AI Oracle - تأكيد النجاح النهائي
Final Success Confirmation Test
"""

import requests
import json
from datetime import datetime

SUPABASE_URL = "https://tbzbrujqjwpatbzffmwq.supabase.co"
ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"

def test_api_endpoint(endpoint, description, limit=5):
    """Test a specific API endpoint"""
    if "?" in endpoint:
        url = f"{SUPABASE_URL}/rest/v1/{endpoint}&limit={limit}"
    else:
        url = f"{SUPABASE_URL}/rest/v1/{endpoint}?limit={limit}"
    headers = {
        "apikey": ANON_KEY,
        "Authorization": f"Bearer {ANON_KEY}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ {description}: {len(data)} records")
            return True, data
        else:
            print(f"  ❌ {description}: Error {response.status_code}")
            return False, None
    except Exception as e:
        print(f"  ❌ {description}: Exception {str(e)}")
        return False, None

def test_market_overview_query():
    """Test the exact query used by useMarketOverview hook"""
    url = f"{SUPABASE_URL}/rest/v1/market_indices?select=*&symbol=in.(EGX30,EGX70)"
    headers = {
        "apikey": ANON_KEY,
        "Authorization": f"Bearer {ANON_KEY}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Market Overview Query: {len(data)} indices")
            
            # Process like the frontend hook
            egx30 = next((idx for idx in data if idx['symbol'] == 'EGX30'), None)
            egx70 = next((idx for idx in data if idx['symbol'] == 'EGX70'), None)
            
            print(f"    📊 EGX30: {egx30['current_value'] if egx30 else 'Not found'}")
            print(f"    📊 EGX70: {egx70['current_value'] if egx70 else 'Not found'}")
            
            return True, data
        else:
            print(f"  ❌ Market Overview Query: Error {response.status_code}")
            return False, None
    except Exception as e:
        print(f"  ❌ Market Overview Query: Exception {str(e)}")
        return False, None

def main():
    print("🎉 EGX Stock AI Oracle - Final Success Test")
    print("=" * 60)
    print(f"🕒 Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test all main data sources
    tests = [
        ("market_indices?select=symbol,current_value,change_percent", "Market Indices", 10),
        ("stocks_realtime?select=symbol,current_price,change_percent", "Real-time Stocks", 10),
        ("stocks_master?select=symbol,name", "Stocks Master", 10),
        ("stocks_historical?select=symbol,date,close_price", "Historical Data", 5),
    ]
    
    success_count = 0
    total_tests = len(tests) + 1  # +1 for market overview query
    
    print("🔍 Testing API Endpoints:")
    for endpoint, description, limit in tests:
        success, data = test_api_endpoint(endpoint, description, limit)
        if success:
            success_count += 1
    
    print("\n🎯 Testing Frontend Integration:")
    success, data = test_market_overview_query()
    if success:
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 SUCCESS! All tests passed!")
        print("✅ Frontend can now access real market data")
        print("🌐 The EGX Stock AI Oracle is ready to use!")
        print()
        print("🚀 Next Steps:")
        print("   1. Visit http://localhost:5173 to see the live dashboard")
        print("   2. Check that market indices show real values")
        print("   3. Verify that stock tickers display live prices")
        print("   4. Test portfolio and trading features")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
