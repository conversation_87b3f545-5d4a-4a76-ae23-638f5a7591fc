// Optimized supabase client wrapper for better code splitting
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://tbzbrujqjwpatbzffmwq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw";

let supabaseClient: ReturnType<typeof createClient<Database>> | null = null;

// Create a singleton instance to prevent multiple client instances
export const getSupabaseClient = () => {
  if (!supabaseClient) {
    supabaseClient = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
  }
  return supabaseClient;
};

// Export the client for backward compatibility
export const supabase = getSupabaseClient();
