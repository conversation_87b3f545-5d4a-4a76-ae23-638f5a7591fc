import React, { createContext, useState } from 'react';

interface AnalysisContextType {
  selectedSymbol: string | null;
  isAnalysisOpen: boolean;
  openAnalysis: (symbol: string) => void;
  closeAnalysis: () => void;
  setActiveTab: (tab: string) => void;
}

const AnalysisContext = createContext<AnalysisContextType | undefined>(undefined);

interface AnalysisProviderProps {
  children: React.ReactNode;
  onTabChange?: (tab: string) => void;
}

export const AnalysisProvider: React.FC<AnalysisProviderProps> = ({ 
  children, 
  onTabChange 
}) => {
  const [selectedSymbol, setSelectedSymbol] = useState<string | null>(null);
  const [isAnalysisOpen, setIsAnalysisOpen] = useState(false);

  const openAnalysis = (symbol: string) => {
    setSelectedSymbol(symbol);
    setIsAnalysisOpen(true);
    if (onTabChange) {
      onTabChange('comprehensive-analysis');
    }
  };

  const closeAnalysis = () => {
    setIsAnalysisOpen(false);
    setSelectedSymbol(null);
  };

  const setActiveTab = (tab: string) => {
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  return (
    <AnalysisContext.Provider
      value={{
        selectedSymbol,
        isAnalysisOpen,
        openAnalysis,
        closeAnalysis,
        setActiveTab,
      }}
    >
      {children}
    </AnalysisContext.Provider>
  );
};

export default AnalysisContext;
