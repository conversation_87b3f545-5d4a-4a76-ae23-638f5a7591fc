#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ مستورد البيانات التاريخية - كنز الأسهم المصرية
Historical Data Importer - Egyptian Stock Market Data Treasure

📅 Created: 2025-06-16
🎯 Purpose: استيراد 642,000+ سجل تاريخي من 321 ملف TXT
📊 Source: /mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2/*.TXT
"""

import pandas as pd
import psycopg2
from psycopg2.extras import execute_batch
import logging
from pathlib import Path
from datetime import datetime, timedelta
import re
import sys
from typing import List, Dict, Tuple
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('historical_data_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EGXHistoricalDataImporter:
    """مستورد البيانات التاريخية للأسهم المصرية"""
    
    def __init__(self, db_config: Dict[str, str]):
        """تهيئة المستورد مع إعدادات قاعدة البيانات"""
        self.db_config = db_config
        self.connection = None
        self.meta2_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2")
        
        # إحصائيات العملية
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'total_records': 0,
            'inserted_records': 0,
            'failed_records': 0,
            'skipped_files': 0,
            'start_time': None,
            'end_time': None
        }
        
        # خريطة تطابق أعمدة TXT مع قاعدة البيانات
        self.column_mapping = {
            'TICKER': 'symbol',
            'DATE': 'trade_date', 
            'OPEN': 'open_price',
            'HIGH': 'high_price',
            'LOW': 'low_price',
            'CLOSE': 'close_price',
            'VOL': 'volume',
            'OPENINT': 'open_interest'
        }
        
        # قائمة الأسهم المعروفة (لتنظيف البيانات)
        self.known_symbols = set()
        
    def connect_db(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.connection.autocommit = False
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close_db(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            logger.info("🔒 تم إغلاق الاتصال بقاعدة البيانات")
    
    def get_existing_symbols(self) -> set:
        """الحصول على الرموز الموجودة في قاعدة البيانات"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT symbol FROM stocks_master WHERE is_active = true")
            symbols = {row[0] for row in cursor.fetchall()}
            cursor.close()
            logger.info(f"📊 تم العثور على {len(symbols)} رمز موجود في قاعدة البيانات")
            return symbols
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الرموز الموجودة: {e}")
            return set()
    
    def validate_txt_file(self, file_path: Path) -> Tuple[bool, str]:
        """التحقق من صحة ملف TXT"""
        try:
            # التحقق من وجود الملف
            if not file_path.exists():
                return False, "الملف غير موجود"
            
            # التحقق من الحجم
            if file_path.stat().st_size == 0:
                return False, "الملف فارغ"
            
            # قراءة أول سطر للتحقق من التنسيق
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                if not first_line or ',' not in first_line:
                    return False, "تنسيق غير صحيح"
            
            return True, "صحيح"
            
        except Exception as e:
            return False, f"خطأ في التحقق: {str(e)}"
    
    def extract_symbol_from_filename(self, filename: str) -> str:
        """استخراج رمز السهم من اسم الملف"""
        # إزالة امتداد الملف
        base_name = filename.replace('.TXT', '').replace('D.TXT', '')
        
        # إزالة الحرف D في النهاية إذا وجد
        if base_name.endswith('D'):
            base_name = base_name[:-1]
        
        # تنظيف الرمز
        symbol = re.sub(r'[^A-Z0-9]', '', base_name.upper())
        
        return symbol
    
    def parse_txt_file(self, file_path: Path) -> pd.DataFrame:
        """قراءة وتحليل ملف TXT"""
        try:
            # استخراج رمز السهم
            symbol = self.extract_symbol_from_filename(file_path.name)
            
            logger.info(f"📖 قراءة ملف {file_path.name} للسهم {symbol}")
            
            # قراءة الملف مع معالجة التنسيقات المختلفة
            try:
                # تجربة قراءة CSV عادية
                df = pd.read_csv(file_path, encoding='utf-8')
            except:
                try:
                    # تجربة قراءة بـ encoding مختلف
                    df = pd.read_csv(file_path, encoding='latin-1')
                except:
                    # تجربة قراءة بفاصل مختلف
                    df = pd.read_csv(file_path, sep=',', encoding='utf-8')
            
            # التحقق من وجود الأعمدة المطلوبة
            expected_columns = ['TICKER', 'PER', 'DATE', 'TIME', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL', 'OPENINT']
            
            if not all(col in df.columns for col in expected_columns[:6]):  # الأعمدة الأساسية
                logger.warning(f"⚠️ أعمدة مفقودة في ملف {file_path.name}: {df.columns.tolist()}")
                # محاولة تخمين الأعمدة
                if len(df.columns) >= 6:
                    df.columns = expected_columns[:len(df.columns)]
            
            # تنظيف البيانات
            df['symbol'] = symbol
            
            # تحويل التاريخ
            if 'DATE' in df.columns:
                df['trade_date'] = pd.to_datetime(df['DATE'], format='%Y%m%d', errors='coerce')
            
            # تحويل الأسعار إلى numeric
            price_columns = ['OPEN', 'HIGH', 'LOW', 'CLOSE']
            for col in price_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # تحويل الحجم
            if 'VOL' in df.columns:
                df['VOL'] = pd.to_numeric(df['VOL'], errors='coerce').fillna(0).astype('int64')
            
            # إزالة الصفوف التي تحتوي على قيم null في التاريخ أو الأسعار
            df = df.dropna(subset=['trade_date', 'OPEN', 'HIGH', 'LOW', 'CLOSE'])
            
            # فلترة البيانات المعقولة (أسعار > 0)
            df = df[(df['OPEN'] > 0) & (df['HIGH'] > 0) & (df['LOW'] > 0) & (df['CLOSE'] > 0)]
            
            # حساب التغير في السعر
            df = df.sort_values('trade_date')
            df['price_change'] = df['CLOSE'] - df['CLOSE'].shift(1)
            df['price_change_pct'] = (df['price_change'] / df['CLOSE'].shift(1) * 100).round(4)
            
            # إضافة period
            df['period'] = 'D'  # Daily
            
            logger.info(f"✅ تم تحليل {len(df)} سجل من ملف {file_path.name}")
            
            return df[df.columns.intersection(['symbol', 'trade_date', 'period', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL', 'OPENINT', 'price_change', 'price_change_pct'])]
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل ملف {file_path.name}: {e}")
            return pd.DataFrame()
    
    def insert_stock_master_if_needed(self, symbol: str) -> bool:
        """إدراج السهم في جدول stocks_master إذا لم يكن موجوداً"""
        try:
            cursor = self.connection.cursor()
            
            # التحقق من وجود السهم
            cursor.execute("SELECT COUNT(*) FROM stocks_master WHERE symbol = %s", (symbol,))
            exists = cursor.fetchone()[0] > 0
            
            if not exists:
                # إدراج السهم بمعلومات أساسية
                cursor.execute("""
                    INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (symbol) DO NOTHING
                """, (symbol, f"سهم {symbol}", symbol, True))
                
                logger.info(f"➕ تم إضافة السهم {symbol} إلى stocks_master")
            
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إدراج السهم {symbol}: {e}")
            return False
    
    def insert_historical_data(self, df: pd.DataFrame, symbol: str) -> Tuple[int, int]:
        """إدراج البيانات التاريخية في قاعدة البيانات"""
        if df.empty:
            return 0, 0
        
        try:
            cursor = self.connection.cursor()
            
            # تحضير البيانات للإدراج
            insert_data = []
            for _, row in df.iterrows():
                insert_data.append((
                    symbol,
                    row.get('trade_date'),
                    'D',  # period
                    float(row.get('OPEN', 0)) if pd.notna(row.get('OPEN')) else None,
                    float(row.get('HIGH', 0)) if pd.notna(row.get('HIGH')) else None,
                    float(row.get('LOW', 0)) if pd.notna(row.get('LOW')) else None,
                    float(row.get('CLOSE', 0)) if pd.notna(row.get('CLOSE')) else None,
                    int(row.get('VOL', 0)) if pd.notna(row.get('VOL')) else 0,
                    None,  # turnover
                    None,  # trades_count
                    float(row.get('price_change', 0)) if pd.notna(row.get('price_change')) else None,
                    float(row.get('price_change_pct', 0)) if pd.notna(row.get('price_change_pct')) else None,
                    int(row.get('OPENINT', 0)) if pd.notna(row.get('OPENINT')) else 0
                ))
            
            # إدراج البيانات بمجموعات
            insert_query = """
                INSERT INTO stocks_historical (
                    symbol, trade_date, period, open_price, high_price, low_price, 
                    close_price, volume, turnover, trades_count, price_change, 
                    price_change_pct, open_interest
                ) VALUES %s
                ON CONFLICT (symbol, trade_date, period) DO NOTHING
            """
            
            # تقسيم البيانات إلى مجموعات صغيرة لتحسين الأداء
            batch_size = 1000
            inserted_count = 0
            failed_count = 0
            
            for i in range(0, len(insert_data), batch_size):
                batch = insert_data[i:i + batch_size]
                try:
                    execute_batch(cursor, insert_query, batch, page_size=batch_size)
                    inserted_count += len(batch)
                except Exception as e:
                    logger.error(f"❌ خطأ في إدراج مجموعة بيانات للسهم {symbol}: {e}")
                    failed_count += len(batch)
            
            cursor.close()
            
            logger.info(f"💾 تم إدراج {inserted_count} سجل للسهم {symbol}")
            
            return inserted_count, failed_count
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في إدراج بيانات السهم {symbol}: {e}")
            return 0, len(df)
    
    def log_sync_operation(self, sync_type: str, source_file: str, 
                          processed: int, inserted: int, failed: int, 
                          duration: int, status: str, error_msg: str = None):
        """تسجيل عملية المزامنة"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO data_sync_log (
                    sync_type, source_file, records_processed, records_inserted,
                    records_failed, sync_status, error_message, sync_duration_seconds,
                    started_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                sync_type, source_file, processed, inserted, failed, 
                status, error_msg, duration, 
                datetime.now() - timedelta(seconds=duration)
            ))
            cursor.close()
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل عملية المزامنة: {e}")
    
    def process_single_file(self, file_path: Path) -> Dict:
        """معالجة ملف TXT واحد"""
        start_time = time.time()
        result = {
            'file': file_path.name,
            'symbol': '',
            'processed': 0,
            'inserted': 0,
            'failed': 0,
            'status': 'FAILED',
            'error': None,
            'duration': 0
        }
        
        try:
            # التحقق من صحة الملف
            is_valid, validation_msg = self.validate_txt_file(file_path)
            if not is_valid:
                result['error'] = f"ملف غير صحيح: {validation_msg}"
                return result
            
            # تحليل الملف
            df = self.parse_txt_file(file_path)
            if df.empty:
                result['error'] = "لا توجد بيانات صحيحة في الملف"
                return result
            
            symbol = self.extract_symbol_from_filename(file_path.name)
            result['symbol'] = symbol
            result['processed'] = len(df)
            
            # التأكد من وجود السهم في stocks_master
            if not self.insert_stock_master_if_needed(symbol):
                result['error'] = f"فشل في إضافة السهم {symbol} إلى stocks_master"
                return result
            
            # إدراج البيانات التاريخية
            inserted, failed = self.insert_historical_data(df, symbol)
            result['inserted'] = inserted
            result['failed'] = failed
            
            if inserted > 0:
                result['status'] = 'SUCCESS' if failed == 0 else 'PARTIAL'
                self.connection.commit()
            else:
                result['status'] = 'FAILED'
                result['error'] = "لم يتم إدراج أي سجل"
                self.connection.rollback()
                
        except Exception as e:
            result['error'] = str(e)
            self.connection.rollback()
            logger.error(f"❌ خطأ في معالجة الملف {file_path.name}: {e}")
        
        finally:
            result['duration'] = int(time.time() - start_time)
            
            # تسجيل العملية
            self.log_sync_operation(
                'historical', file_path.name, result['processed'], 
                result['inserted'], result['failed'], result['duration'],
                result['status'], result['error']
            )
        
        return result
    
    def import_all_historical_data(self, max_workers: int = 4) -> Dict:
        """استيراد جميع البيانات التاريخية"""
        self.stats['start_time'] = datetime.now()
        logger.info("🚀 بدء استيراد البيانات التاريخية للأسهم المصرية")
        
        if not self.connect_db():
            return self.stats
        
        try:
            # البحث عن ملفات TXT
            txt_files = list(self.meta2_path.glob("*.TXT"))
            self.stats['total_files'] = len(txt_files)
            
            if not txt_files:
                logger.error("❌ لم يتم العثور على ملفات TXT في المجلد المحدد")
                return self.stats
            
            logger.info(f"📁 تم العثور على {len(txt_files)} ملف TXT")
            
            # الحصول على الرموز الموجودة
            self.known_symbols = self.get_existing_symbols()
            
            # معالجة الملفات
            successful_files = []
            failed_files = []
            
            # معالجة تسلسلية لتجنب مشاكل قاعدة البيانات
            for file_path in txt_files:
                result = self.process_single_file(file_path)
                
                self.stats['processed_files'] += 1
                self.stats['total_records'] += result['processed']
                self.stats['inserted_records'] += result['inserted']
                self.stats['failed_records'] += result['failed']
                
                if result['status'] in ['SUCCESS', 'PARTIAL']:
                    successful_files.append(result)
                else:
                    failed_files.append(result)
                    self.stats['skipped_files'] += 1
                
                # تقرير التقدم
                progress = (self.stats['processed_files'] / self.stats['total_files']) * 100
                logger.info(f"📊 التقدم: {progress:.1f}% ({self.stats['processed_files']}/{self.stats['total_files']})")
            
            self.stats['end_time'] = datetime.now()
            
            # تقرير نهائي
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            logger.info("=" * 80)
            logger.info("📋 تقرير استيراد البيانات التاريخية")
            logger.info("=" * 80)
            logger.info(f"📁 إجمالي الملفات: {self.stats['total_files']}")
            logger.info(f"✅ ملفات ناجحة: {len(successful_files)}")
            logger.info(f"❌ ملفات فاشلة: {len(failed_files)}")
            logger.info(f"📊 إجمالي السجلات: {self.stats['total_records']:,}")
            logger.info(f"💾 سجلات مدرجة: {self.stats['inserted_records']:,}")
            logger.info(f"⚠️ سجلات فاشلة: {self.stats['failed_records']:,}")
            logger.info(f"⏱️ المدة الإجمالية: {duration:.1f} ثانية")
            logger.info(f"🚀 متوسط السرعة: {self.stats['inserted_records']/duration:.0f} سجل/ثانية")
            logger.info("=" * 80)
            
            # طباعة تفاصيل الملفات الفاشلة
            if failed_files:
                logger.warning("⚠️ الملفات التي فشل استيرادها:")
                for failed in failed_files:
                    logger.warning(f"  - {failed['file']}: {failed['error']}")
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في عملية الاستيراد: {e}")
        
        finally:
            self.close_db()
        
        return self.stats

def main():
    """الدالة الرئيسية"""    # إعدادات قاعدة البيانات
    db_config = {
        'host': 'localhost',
        'database': 'egx_stock_oracle',
        'user': 'postgres',
        'password': '',
        'port': 5432
    }
    
    # إنشاء المستورد
    importer = EGXHistoricalDataImporter(db_config)
    
    # بدء عملية الاستيراد
    stats = importer.import_all_historical_data()
    
    print("\n🎉 اكتملت عملية استيراد البيانات التاريخية!")
    print(f"📊 تم إدراج {stats['inserted_records']:,} سجل تاريخي")

if __name__ == "__main__":
    main()
