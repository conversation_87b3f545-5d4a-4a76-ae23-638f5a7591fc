#!/usr/bin/env python3
"""
Quick data check for improved criteria
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key)

print("🔍 Checking sample stock data...")

# Get sample active stocks
response = supabase.table('stocks_realtime').select('symbol, volume, market_cap, change_percent, turnover, current_price').neq('symbol', 'EGX30').neq('symbol', 'EGX70EWI').neq('symbol', 'EGX100EWI').gt('volume', 100000).order('volume', {'ascending': False}).limit(10).execute()

stocks = response.data
print(f"\nSample active stocks ({len(stocks)}):")
for stock in stocks:
    market_cap = stock.get('market_cap') or 0
    turnover = stock.get('turnover') or 0
    price = stock.get('current_price') or 0
    print(f"  • {stock['symbol']}: Vol={stock['volume']:,}, Cap={market_cap:,.0f}, Change={stock['change_percent']}%, Price={price}")

# Get volume ranges
response = supabase.table('stocks_realtime').select('volume').neq('symbol', 'EGX30').neq('symbol', 'EGX70EWI').neq('symbol', 'EGX100EWI').gt('volume', 0).execute()
volumes = [s['volume'] for s in response.data if s.get('volume')]

if volumes:
    volumes.sort(reverse=True)
    print(f"\n📊 Volume ranges:")
    print(f"  • Highest: {volumes[0]:,}")
    print(f"  • Top 10%: {volumes[len(volumes)//10]:,}")
    print(f"  • Median: {volumes[len(volumes)//2]:,}")
    print(f"  • Bottom 10%: {volumes[len(volumes)*9//10]:,}")
    print(f"  • Lowest: {volumes[-1]:,}")
