#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 محسن استيراد البيانات الحية والمالية - معالجة متقدمة للأخطاء
Enhanced Realtime & Financial Data Importer - Advanced Error Handling

📅 Created: 2025-06-16
🎯 Purpose: استيراد محسن مع معالجة قيود قاعدة البيانات والبيانات المفقودة
"""

import pandas as pd
import subprocess
import logging
import sys
from pathlib import Path
from datetime import datetime
import numpy as np
import re

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EnhancedEGXDataImporter:
    """مستورد محسن للبيانات مع معالجة الأخطاء المتقدمة"""
    
    def __init__(self):
        self.excel_file_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx")
        self.csv_file_path = Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv")
        
        # إحصائيات
        self.stats = {
            'realtime': {'processed': 0, 'inserted': 0, 'updated': 0, 'failed': 0},
            'financial': {'processed': 0, 'inserted': 0, 'updated': 0, 'failed': 0},
            'symbols_created': 0
        }
        
        # خريطة الأعمدة المحسنة
        self.realtime_column_mapping = {
            'الرمز': 'symbol',
            'إفتتاح': 'open_price',
            'أعلى': 'high_price', 
            'أدنى': 'low_price',
            'الاغلاق': 'current_price',
            'التغير %': 'change_percent',
            'نسبة السيولة %': 'liquidity_ratio',
            'فرص مضاربة ساعة': 'speculation_opportunity',
            'هدف 1 (يوم)': 'target_1',
            'هدف 2 (يوم)': 'target_2', 
            'هدف 3 (يوم)': 'target_3',
            'وقف خسارة (يوم)': 'stop_loss',
            'حاله السهم (يوم)': 'stock_status',
            'TK (يوم)': 'tk_indicator',
            'KJ (يوم)': 'kj_indicator',
            'ma5 (يوم)': 'ma5',
            'ma10 (يوم)': 'ma10',
            'ma20 (يوم)': 'ma20',
            'ma50 (يوم)': 'ma50',
            'ma100 (يوم)': 'ma100',
            'ma200 (يوم)': 'ma200',
            'صافي السيولة': 'net_liquidity',
            'الحجم': 'volume',
            'القيمة': 'turnover',
            'الصفقات': 'trades_count',
            'تدفق السيولة': 'liquidity_flow',
            'قيمة السيولة الداخلة': 'liquidity_inflow',
            'قيمة السيولة الخارجة': 'liquidity_outflow',
            'حجم السيولة الداخلة': 'volume_inflow',
            'حجم السيولة الخارجة': 'volume_outflow',
            'متوسط صافي الحجم 3 يوم': 'avg_net_volume_3d',
            'متوسط صافي الحجم 5 يوم': 'avg_net_volume_5d',
            'ربح السهم (سنوي)': 'eps_annual',
            'القيمة الدفترية': 'book_value',
            'مكرر الأرباح': 'pe_ratio',
            'العائد الربحي (٪)': 'dividend_yield'
        }
    
    def execute_sql(self, sql_command: str, description: str = ""):
        """تنفيذ أمر SQL باستخدام sudo postgres"""
        try:
            cmd = ['sudo', '-u', 'postgres', 'psql', '-d', 'egx_stock_oracle', '-c', sql_command]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            if description:
                logger.info(f"✅ {description}")
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ SQL Error in {description}: {e.stderr}")
            return False, e.stderr
    
    def clean_symbol(self, symbol: str) -> str:
        """تنظيف رمز السهم"""
        if pd.isna(symbol):
            return None
        
        symbol = str(symbol).strip().upper()
        
        # إزالة الأحرف غير المرغوبة
        symbol = re.sub(r'[^\w]', '', symbol)
        
        # اقتطاع إلى 15 حرف كحد أقصى
        if len(symbol) > 15:
            symbol = symbol[:15]
            
        return symbol if symbol and symbol != 'NAN' else None
    
    def validate_numeric(self, value, min_val=None, max_val=None, default=None):
        """التحقق من صحة القيم الرقمية مع حدود"""
        if pd.isna(value) or value in ['', 'NULL', 'null']:
            return default
            
        try:
            num_val = float(value)
            
            if min_val is not None and num_val < min_val:
                return default
                
            if max_val is not None and num_val > max_val:                logger.warning(f"Value {num_val} exceeds maximum {max_val}, capping it")
                return max_val
                
            return num_val
        except (ValueError, TypeError):
            return default
    
    def ensure_symbol_exists(self, symbol: str, arabic_name: str = None) -> bool:
        """التأكد من وجود الرمز في stocks_master أو إنشاؤه مع الاسم العربي الصحيح"""
        if not symbol:
            return False
            
        # فحص وجود الرمز
        check_sql = f"SELECT COUNT(*) FROM stocks_master WHERE symbol = '{symbol}'"
        success, result = self.execute_sql(check_sql)
        
        if success and '0' in result:
            # استخدام الاسم العربي المُمرر أو اسم مؤقت
            display_name = arabic_name if arabic_name and not arabic_name.startswith('مؤشر') else f'سهم {symbol}'
            
            # إنشاء الرمز الجديد
            insert_sql = f"""
                INSERT INTO stocks_master (symbol, name_ar, name_en, is_active)
                VALUES ('{symbol}', '{display_name}', '{symbol}', true)
                ON CONFLICT (symbol) DO NOTHING
            """
            success, _ = self.execute_sql(insert_sql, f"إضافة رمز جديد: {symbol}")
            if success:
                self.stats['symbols_created'] += 1
            return success
        
        return True
    
    def load_and_clean_realtime_data(self) -> pd.DataFrame:
        """تحميل وتنظيف البيانات الحية"""
        try:
            logger.info("📖 تحميل البيانات الحية من Excel...")
            df = pd.read_excel(self.excel_file_path)
            
            logger.info(f"📊 تم تحميل {len(df)} سجل من Excel")
            
            # إعادة تسمية الأعمدة
            df = df.rename(columns=self.realtime_column_mapping)
            
            # تنظيف رموز الأسهم
            df['symbol'] = df['symbol'].apply(self.clean_symbol)
            df = df.dropna(subset=['symbol'])
            df = df[df['symbol'] != '']
            
            # تنظيف البيانات الرقمية
            price_columns = ['open_price', 'high_price', 'low_price', 'current_price', 
                           'target_1', 'target_2', 'target_3', 'stop_loss']
            
            for col in price_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: self.validate_numeric(x, min_val=0, max_val=999999))
            
            # معالجة المتوسطات المتحركة
            ma_columns = ['ma5', 'ma10', 'ma20', 'ma50', 'ma100', 'ma200']
            for col in ma_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: self.validate_numeric(x, min_val=0, max_val=999999))
            
            # معالجة النسب المئوية
            percent_columns = ['change_percent', 'liquidity_ratio', 'dividend_yield']
            for col in percent_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: self.validate_numeric(x, min_val=-999, max_val=999))
            
            # معالجة الحجم والقيمة
            if 'volume' in df.columns:
                df['volume'] = df['volume'].apply(lambda x: self.validate_numeric(x, min_val=0, default=0))
                df['volume'] = df['volume'].astype('int64')
            
            if 'turnover' in df.columns:
                df['turnover'] = df['turnover'].apply(lambda x: self.validate_numeric(x, min_val=0, default=0))
            
            # معالجة المؤشرات الخاصة
            if 'stock_status' in df.columns:
                df['stock_status'] = df['stock_status'].apply(lambda x: self.validate_numeric(x, min_val=1, max_val=5, default=3))
                df['stock_status'] = df['stock_status'].astype('int')
            
            if 'speculation_opportunity' in df.columns:
                df['speculation_opportunity'] = df['speculation_opportunity'].apply(
                    lambda x: True if str(x).lower() in ['true', '1', 'yes', 'نعم'] else False
                )
            
            # إضافة تواريخ
            df['last_trade_date'] = datetime.now().date()
            df['updated_at'] = datetime.now()
            
            logger.info(f"✅ تم تنظيف البيانات - المتبقي: {len(df)} سجل")
            return df
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل البيانات الحية: {e}")
            return pd.DataFrame()
    
    def import_realtime_data(self) -> bool:
        """استيراد البيانات الحية"""
        try:
            df = self.load_and_clean_realtime_data()
            if df.empty:
                return False
            
            self.stats['realtime']['processed'] = len(df)
            
            # التأكد من وجود جميع الرموز
            logger.info("🔍 التأكد من وجود رموز الأسهم...")
            for symbol in df['symbol'].unique():
                self.ensure_symbol_exists(symbol)
            
            # إعداد الأعمدة للإدراج
            realtime_columns = [
                'symbol', 'open_price', 'high_price', 'low_price', 'current_price',
                'change_percent', 'volume', 'turnover', 'trades_count',
                'liquidity_ratio', 'net_liquidity', 'liquidity_inflow', 'liquidity_outflow',
                'volume_inflow', 'volume_outflow', 'liquidity_flow',
                'ma5', 'ma10', 'ma20', 'ma50', 'ma100', 'ma200',
                'tk_indicator', 'kj_indicator', 'target_1', 'target_2', 'target_3',
                'stop_loss', 'stock_status', 'speculation_opportunity',
                'avg_net_volume_3d', 'avg_net_volume_5d',
                'eps_annual', 'book_value', 'pe_ratio', 'dividend_yield',
                'last_trade_date', 'updated_at'
            ]
            
            # تصفية الأعمدة الموجودة فقط
            available_columns = [col for col in realtime_columns if col in df.columns]
            df_insert = df[available_columns].copy()
              # معالجة كل سجل
            for index, row in df_insert.iterrows():
                try:
                    # تحضير القيم
                    values = []
                    for col in available_columns:
                        value = row[col]
                        if pd.isna(value):
                            values.append('NULL')
                        elif isinstance(value, str):
                            escaped_value = value.replace("'", "''")
                            values.append(f"'{escaped_value}'")
                        elif isinstance(value, bool):
                            values.append('true' if value else 'false')
                        elif isinstance(value, datetime):
                            values.append(f"'{value}'")
                        else:
                            values.append(str(value))
                    
                    # إنشاء استعلام UPSERT
                    columns_str = ', '.join(available_columns)
                    values_str = ', '.join(values)
                    update_assignments = ', '.join([f"{col} = EXCLUDED.{col}" for col in available_columns if col != 'symbol'])
                    
                    upsert_sql = f"""
                        INSERT INTO stocks_realtime ({columns_str})
                        VALUES ({values_str})
                        ON CONFLICT (symbol) DO UPDATE SET {update_assignments}
                    """
                    
                    success, result = self.execute_sql(upsert_sql)
                    if success:
                        if 'INSERT 0 1' in result:
                            self.stats['realtime']['inserted'] += 1
                        else:
                            self.stats['realtime']['updated'] += 1
                    else:
                        self.stats['realtime']['failed'] += 1
                        logger.warning(f"⚠️ فشل في معالجة السهم {row.get('symbol', 'غير معروف')}")
                        
                except Exception as e:
                    self.stats['realtime']['failed'] += 1
                    logger.warning(f"⚠️ خطأ في معالجة السجل {index}: {e}")
            
            logger.info(f"✅ البيانات الحية - إدراج: {self.stats['realtime']['inserted']}, تحديث: {self.stats['realtime']['updated']}, فشل: {self.stats['realtime']['failed']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في استيراد البيانات الحية: {e}")
            return False
    
    def import_financial_data(self) -> bool:
        """استيراد البيانات المالية (مبسط)"""
        try:
            logger.info("📊 تحميل البيانات المالية من CSV...")
            df = pd.read_csv(self.csv_file_path)
            
            logger.info(f"📊 تم تحميل {len(df)} سجل من CSV")
            
            # البحث عن عمود الرمز
            symbol_col = None
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['symbol', 'رمز', 'ticker']):
                    symbol_col = col
                    break
            
            if not symbol_col:
                logger.error("❌ لم يتم العثور على عمود الرمز في البيانات المالية")
                return False
            
            # تنظيف الرموز
            df['clean_symbol'] = df[symbol_col].apply(self.clean_symbol)
            df = df.dropna(subset=['clean_symbol'])
            
            self.stats['financial']['processed'] = len(df)
            
            # معالجة مبسطة - إدراج البيانات الأساسية فقط
            for index, row in df.iterrows():
                try:
                    symbol = row['clean_symbol']
                    if not self.ensure_symbol_exists(symbol):
                        continue
                    
                    # البيانات المالية الأساسية
                    pe_ratio = self.validate_numeric(row.get('P/E', row.get('PE', None)), min_val=0, max_val=999)
                    pb_ratio = self.validate_numeric(row.get('P/B', row.get('PB', None)), min_val=0, max_val=999)
                    roe = self.validate_numeric(row.get('ROE', None), min_val=-999, max_val=999)
                    
                    financial_sql = f"""
                        INSERT INTO stocks_financials (
                            symbol, fiscal_year, fiscal_quarter, pe_ratio, pb_ratio, roe, created_at, updated_at
                        ) VALUES (
                            '{symbol}', {datetime.now().year}, 4, 
                            {pe_ratio if pe_ratio is not None else 'NULL'},
                            {pb_ratio if pb_ratio is not None else 'NULL'},
                            {roe if roe is not None else 'NULL'},
                            NOW(), NOW()
                        ) ON CONFLICT (symbol, fiscal_year, fiscal_quarter) DO UPDATE SET
                            pe_ratio = EXCLUDED.pe_ratio,
                            pb_ratio = EXCLUDED.pb_ratio,
                            roe = EXCLUDED.roe,
                            updated_at = NOW()
                    """
                    
                    success, _ = self.execute_sql(financial_sql)
                    if success:
                        self.stats['financial']['inserted'] += 1
                    else:
                        self.stats['financial']['failed'] += 1
                        
                except Exception as e:
                    self.stats['financial']['failed'] += 1
                    logger.warning(f"⚠️ خطأ في معالجة البيانات المالية للسجل {index}: {e}")
            
            logger.info(f"✅ البيانات المالية - إدراج: {self.stats['financial']['inserted']}, فشل: {self.stats['financial']['failed']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في استيراد البيانات المالية: {e}")
            return False
    
    def run_complete_import(self) -> bool:
        """تشغيل الاستيراد الكامل"""
        logger.info("🚀 بدء الاستيراد المحسن للبيانات الحية والمالية")
        logger.info("=" * 80)
        
        # استيراد البيانات الحية
        logger.info("📊 استيراد البيانات الحية...")
        realtime_success = self.import_realtime_data()
        
        # استيراد البيانات المالية
        logger.info("💰 استيراد البيانات المالية...")
        financial_success = self.import_financial_data()
        
        # تقرير نهائي
        logger.info("=" * 80)
        logger.info("📋 تقرير الاستيراد المحسن")
        logger.info("=" * 80)
        logger.info(f"📊 البيانات الحية: معالجة {self.stats['realtime']['processed']}, إدراج {self.stats['realtime']['inserted']}, تحديث {self.stats['realtime']['updated']}")
        logger.info(f"💰 البيانات المالية: معالجة {self.stats['financial']['processed']}, إدراج {self.stats['financial']['inserted']}")
        logger.info(f"➕ رموز جديدة: {self.stats['symbols_created']}")
        logger.info("=" * 80)
        
        return realtime_success and financial_success

def main():
    """الدالة الرئيسية"""
    importer = EnhancedEGXDataImporter()
    success = importer.run_complete_import()
    
    if success:
        print("\n🎉 اكتمل الاستيراد المحسن بنجاح!")
    else:
        print("\n⚠️ اكتمل الاستيراد مع بعض المشاكل")
    
    return success

if __name__ == "__main__":
    main()
