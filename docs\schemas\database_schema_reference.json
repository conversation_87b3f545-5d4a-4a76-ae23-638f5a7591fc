{"generated_at": "2025-06-15T21:40:59.874186", "database_url": "https://tbzbrujqjwpatbzffmwq.supabase.co", "inspection_summary": {"total_tables_inspected": 5, "total_records_across_main_tables": 442737, "total_columns_across_main_tables": 157, "main_tables_list": ["stocks_realtime", "stocks_financials", "stocks_historical"], "additional_tables_list": ["user_portfolios", "paper_trades"]}, "main_tables": {"stocks_realtime": {"table_name": "stocks_realtime", "total_records": 225, "total_columns": 51, "latest_update": "2025-06-15T18:14:21.384212+00:00", "columns": {"symbol": {"name": "symbol", "sample_value": "EGX30", "data_type": "str", "is_null": false, "length": 5}, "current_price": {"name": "current_price", "sample_value": 31016.0, "data_type": "float", "is_null": false, "length": 7}, "open_price": {"name": "open_price", "sample_value": 32511.68, "data_type": "float", "is_null": false, "length": 8}, "high_price": {"name": "high_price", "sample_value": 32511.68, "data_type": "float", "is_null": false, "length": 8}, "low_price": {"name": "low_price", "sample_value": 30002.46, "data_type": "float", "is_null": false, "length": 8}, "previous_close": {"name": "previous_close", "sample_value": 31016.0, "data_type": "float", "is_null": false, "length": 7}, "change_amount": {"name": "change_amount", "sample_value": 0.0, "data_type": "float", "is_null": false, "length": 3}, "change_percent": {"name": "change_percent", "sample_value": 0.0, "data_type": "float", "is_null": false, "length": 3}, "volume": {"name": "volume", "sample_value": 273272187, "data_type": "int", "is_null": false, "length": 9}, "turnover": {"name": "turnover", "sample_value": 2475209404.0, "data_type": "float", "is_null": false, "length": 12}, "trades_count": {"name": "trades_count", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "bid_price": {"name": "bid_price", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ask_price": {"name": "ask_price", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "bid_volume": {"name": "bid_volume", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ask_volume": {"name": "ask_volume", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "last_trade_time": {"name": "last_trade_time", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "market_cap": {"name": "market_cap", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "updated_at": {"name": "updated_at", "sample_value": "2025-06-15T15:39:37.678742+00:00", "data_type": "str", "is_null": false, "length": 32}, "ma5": {"name": "ma5", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ma10": {"name": "ma10", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ma20": {"name": "ma20", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ma50": {"name": "ma50", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ma100": {"name": "ma100", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ma200": {"name": "ma200", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "tk_indicator": {"name": "tk_indicator", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "kj_indicator": {"name": "kj_indicator", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "target_1": {"name": "target_1", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "target_2": {"name": "target_2", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "target_3": {"name": "target_3", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "stop_loss": {"name": "stop_loss", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "stock_status": {"name": "stock_status", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "speculation_opportunity": {"name": "speculation_opportunity", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "liquidity_ratio": {"name": "liquidity_ratio", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "net_liquidity": {"name": "net_liquidity", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "liquidity_inflow": {"name": "liquidity_inflow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "liquidity_outflow": {"name": "liquidity_outflow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "volume_inflow": {"name": "volume_inflow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "volume_outflow": {"name": "volume_outflow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "liquidity_flow": {"name": "liquidity_flow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "free_shares": {"name": "free_shares", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "eps_annual": {"name": "eps_annual", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "book_value": {"name": "book_value", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "pe_ratio": {"name": "pe_ratio", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "dividend_yield": {"name": "dividend_yield", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "sector": {"name": "sector", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "price_range": {"name": "price_range", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "avg_net_volume_3d": {"name": "avg_net_volume_3d", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "avg_net_volume_5d": {"name": "avg_net_volume_5d", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "opening_value": {"name": "opening_value", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "name": {"name": "name", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "last_trade_date": {"name": "last_trade_date", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}}, "column_names": ["ask_price", "ask_volume", "avg_net_volume_3d", "avg_net_volume_5d", "bid_price", "bid_volume", "book_value", "change_amount", "change_percent", "current_price", "dividend_yield", "eps_annual", "free_shares", "high_price", "kj_indicator", "last_trade_date", "last_trade_time", "liquidity_flow", "liquidity_inflow", "liquidity_outflow", "liquidity_ratio", "low_price", "ma10", "ma100", "ma20", "ma200", "ma5", "ma50", "market_cap", "name", "net_liquidity", "open_price", "opening_value", "pe_ratio", "previous_close", "price_range", "sector", "speculation_opportunity", "stock_status", "stop_loss", "symbol", "target_1", "target_2", "target_3", "tk_indicator", "trades_count", "turnover", "updated_at", "volume", "volume_inflow", "volume_outflow"], "inspection_timestamp": "2025-06-15T21:41:00.539852"}, "stocks_financials": {"table_name": "stocks_financials", "total_records": 252, "total_columns": 95, "latest_update": "2025-06-15T18:37:21.28981+00:00", "columns": {"id": {"name": "id", "sample_value": 7, "data_type": "int", "is_null": false, "length": 1}, "symbol": {"name": "symbol", "sample_value": "EGAL", "data_type": "str", "is_null": false, "length": 4}, "fiscal_year": {"name": "fiscal_year", "sample_value": 2025, "data_type": "int", "is_null": false, "length": 4}, "fiscal_quarter": {"name": "fiscal_quarter", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "market_cap": {"name": "market_cap", "sample_value": 67212751007.0, "data_type": "float", "is_null": false, "length": 13}, "pe_ratio": {"name": "pe_ratio", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "eps_ttm": {"name": "eps_ttm", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "eps_growth_yoy": {"name": "eps_growth_yoy", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "dividend_yield": {"name": "dividend_yield", "sample_value": 4.2961, "data_type": "float", "is_null": false, "length": 6}, "book_value_per_share": {"name": "book_value_per_share", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "price_to_book": {"name": "price_to_book", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "revenue_ttm": {"name": "revenue_ttm", "sample_value": 32815674548.0, "data_type": "float", "is_null": false, "length": 13}, "net_income_ttm": {"name": "net_income_ttm", "sample_value": 9324234387.0, "data_type": "float", "is_null": false, "length": 12}, "total_assets": {"name": "total_assets", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "total_debt": {"name": "total_debt", "sample_value": 5759164.0, "data_type": "float", "is_null": false, "length": 9}, "free_cash_flow": {"name": "free_cash_flow", "sample_value": 11384757680.0, "data_type": "float", "is_null": false, "length": 13}, "roe": {"name": "roe", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "roa": {"name": "roa", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "debt_to_equity": {"name": "debt_to_equity", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "current_ratio": {"name": "current_ratio", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "gross_margin": {"name": "gross_margin", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "operating_margin": {"name": "operating_margin", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "net_margin": {"name": "net_margin", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "beta": {"name": "beta", "sample_value": 1.5277, "data_type": "float", "is_null": false, "length": 6}, "analyst_rating": {"name": "analyst_rating", "sample_value": "Strong buy", "data_type": "str", "is_null": false, "length": 10}, "target_price": {"name": "target_price", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "created_at": {"name": "created_at", "sample_value": "2025-06-15T15:23:22.421637+00:00", "data_type": "str", "is_null": false, "length": 32}, "updated_at": {"name": "updated_at", "sample_value": "2025-06-15T18:36:08.382104+00:00", "data_type": "str", "is_null": false, "length": 32}, "description": {"name": "description", "sample_value": "EGYPT ALUMINUM", "data_type": "str", "is_null": false, "length": 14}, "industry": {"name": "industry", "sample_value": "Aluminum", "data_type": "str", "is_null": false, "length": 8}, "isin": {"name": "isin", "sample_value": "EGS3E181C010", "data_type": "str", "is_null": false, "length": 12}, "index_membership": {"name": "index_membership", "sample_value": "EGX 30", "data_type": "str", "is_null": false, "length": 6}, "market_cap_currency": {"name": "market_cap_currency", "sample_value": "EGP", "data_type": "str", "is_null": false, "length": 3}, "total_shares_outstanding": {"name": "total_shares_outstanding", "sample_value": 412500000, "data_type": "int", "is_null": false, "length": 9}, "float_shares_outstanding": {"name": "float_shares_outstanding", "sample_value": 41921550, "data_type": "int", "is_null": false, "length": 8}, "float_shares_percent": {"name": "float_shares_percent", "sample_value": 10.1628, "data_type": "float", "is_null": false, "length": 7}, "number_of_shareholders": {"name": "number_of_shareholders", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "assets_to_equity": {"name": "assets_to_equity", "sample_value": 1.31645748127211, "data_type": "float", "is_null": false, "length": 16}, "assets_turnover": {"name": "assets_turnover", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "cash_ratio": {"name": "cash_ratio", "sample_value": 0.995843760975687, "data_type": "float", "is_null": false, "length": 17}, "cash_to_debt_ratio": {"name": "cash_to_debt_ratio", "sample_value": 823.1831484569635, "data_type": "float", "is_null": false, "length": 17}, "debt_to_assets": {"name": "debt_to_assets", "sample_value": 0.0002810375269884, "data_type": "float", "is_null": false, "length": 18}, "price_to_cash_flow": {"name": "price_to_cash_flow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "price_to_free_cash_flow": {"name": "price_to_free_cash_flow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "price_to_sales": {"name": "price_to_sales", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "enterprise_value": {"name": "enterprise_value", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ev_to_ebitda": {"name": "ev_to_ebitda", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ev_to_revenue": {"name": "ev_to_revenue", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "total_revenue": {"name": "total_revenue", "sample_value": 32815674548.000004, "data_type": "float", "is_null": false, "length": 18}, "gross_profit": {"name": "gross_profit", "sample_value": 12853618550.0, "data_type": "float", "is_null": false, "length": 13}, "operating_income": {"name": "operating_income", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "net_income": {"name": "net_income", "sample_value": 9324234387.0, "data_type": "float", "is_null": false, "length": 12}, "ebitda": {"name": "ebitda", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "total_equity": {"name": "total_equity", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "total_liabilities": {"name": "total_liabilities", "sample_value": 4926104958.0, "data_type": "float", "is_null": false, "length": 12}, "total_current_assets": {"name": "total_current_assets", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "total_current_liabilities": {"name": "total_current_liabilities", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "cash_and_equivalents": {"name": "cash_and_equivalents", "sample_value": 2048122229.0, "data_type": "float", "is_null": false, "length": 12}, "cash_short_term_investments": {"name": "cash_short_term_investments", "sample_value": 4740846754.0, "data_type": "float", "is_null": false, "length": 12}, "net_debt": {"name": "net_debt", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "goodwill": {"name": "goodwill", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "operating_cash_flow": {"name": "operating_cash_flow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "investing_cash_flow": {"name": "investing_cash_flow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "financing_cash_flow": {"name": "financing_cash_flow", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "capital_expenditures": {"name": "capital_expenditures", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "eps_basic_ttm": {"name": "eps_basic_ttm", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "eps_diluted_ttm": {"name": "eps_diluted_ttm", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "eps_reported_annual": {"name": "eps_reported_annual", "sample_value": 22.6, "data_type": "float", "is_null": false, "length": 4}, "eps_estimate_quarterly": {"name": "eps_estimate_quarterly", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "eps_growth_ttm": {"name": "eps_growth_ttm", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "revenue_growth": {"name": "revenue_growth", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "net_income_growth": {"name": "net_income_growth", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "ebitda_growth": {"name": "ebitda_growth", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "gross_profit_growth": {"name": "gross_profit_growth", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "free_cash_flow_growth": {"name": "free_cash_flow_growth", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "capex_growth": {"name": "capex_growth", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "total_assets_growth": {"name": "total_assets_growth", "sample_value": 15.020225411534712, "data_type": "float", "is_null": false, "length": 18}, "dividend_yield_indicated": {"name": "dividend_yield_indicated", "sample_value": 4.29606, "data_type": "float", "is_null": false, "length": 7}, "dividend_payout_ratio": {"name": "dividend_payout_ratio", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "dividends_per_share": {"name": "dividends_per_share", "sample_value": 7.0, "data_type": "float", "is_null": false, "length": 3}, "dividends_paid_annual": {"name": "dividends_paid_annual", "sample_value": -3051986735.0, "data_type": "float", "is_null": false, "length": 13}, "continuous_dividend_growth": {"name": "continuous_dividend_growth", "sample_value": 2, "data_type": "int", "is_null": false, "length": 1}, "forward_pe": {"name": "forward_pe", "sample_value": 5.0, "data_type": "float", "is_null": false, "length": 3}, "target_price_1y": {"name": "target_price_1y", "sample_value": 315.0, "data_type": "float", "is_null": false, "length": 5}, "target_performance_1y": {"name": "target_performance_1y", "sample_value": 89.75903614457832, "data_type": "float", "is_null": false, "length": 17}, "performance_ytd": {"name": "performance_ytd", "sample_value": 43.77273514637105, "data_type": "float", "is_null": false, "length": 17}, "beta_5_years": {"name": "beta_5_years", "sample_value": 1.527714, "data_type": "float", "is_null": false, "length": 8}, "average_volume_10d": {"name": "average_volume_10d", "sample_value": 282567, "data_type": "int", "is_null": false, "length": 6}, "relative_volume": {"name": "relative_volume", "sample_value": 1.0209318382976489, "data_type": "float", "is_null": false, "length": 18}, "volatility_1_day": {"name": "volatility_1_day", "sample_value": 2.921320731557623, "data_type": "float", "is_null": false, "length": 17}, "volume_change": {"name": "volume_change", "sample_value": 279.95424025018394, "data_type": "float", "is_null": false, "length": 18}, "turnover_1_day": {"name": "turnover_1_day", "sample_value": 54857854.0, "data_type": "float", "is_null": false, "length": 10}, "recent_earnings_date": {"name": "recent_earnings_date", "sample_value": "2025-01-06", "data_type": "str", "is_null": false, "length": 10}, "upcoming_earnings_date": {"name": "upcoming_earnings_date", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "sector": {"name": "sector", "sample_value": "Non-energy minerals", "data_type": "str", "is_null": false, "length": 19}}, "column_names": ["analyst_rating", "assets_to_equity", "assets_turnover", "average_volume_10d", "beta", "beta_5_years", "book_value_per_share", "capex_growth", "capital_expenditures", "cash_and_equivalents", "cash_ratio", "cash_short_term_investments", "cash_to_debt_ratio", "continuous_dividend_growth", "created_at", "current_ratio", "debt_to_assets", "debt_to_equity", "description", "dividend_payout_ratio", "dividend_yield", "dividend_yield_indicated", "dividends_paid_annual", "dividends_per_share", "ebitda", "ebitda_growth", "enterprise_value", "eps_basic_ttm", "eps_diluted_ttm", "eps_estimate_quarterly", "eps_growth_ttm", "eps_growth_yoy", "eps_reported_annual", "eps_ttm", "ev_to_ebitda", "ev_to_revenue", "financing_cash_flow", "fiscal_quarter", "fiscal_year", "float_shares_outstanding", "float_shares_percent", "forward_pe", "free_cash_flow", "free_cash_flow_growth", "goodwill", "gross_margin", "gross_profit", "gross_profit_growth", "id", "index_membership", "industry", "investing_cash_flow", "isin", "market_cap", "market_cap_currency", "net_debt", "net_income", "net_income_growth", "net_income_ttm", "net_margin", "number_of_shareholders", "operating_cash_flow", "operating_income", "operating_margin", "pe_ratio", "performance_ytd", "price_to_book", "price_to_cash_flow", "price_to_free_cash_flow", "price_to_sales", "recent_earnings_date", "relative_volume", "revenue_growth", "revenue_ttm", "roa", "roe", "sector", "symbol", "target_performance_1y", "target_price", "target_price_1y", "total_assets", "total_assets_growth", "total_current_assets", "total_current_liabilities", "total_debt", "total_equity", "total_liabilities", "total_revenue", "total_shares_outstanding", "turnover_1_day", "upcoming_earnings_date", "updated_at", "volatility_1_day", "volume_change"], "inspection_timestamp": "2025-06-15T21:41:01.132524"}, "stocks_historical": {"table_name": "stocks_historical", "total_records": 442260, "total_columns": 11, "latest_update": null, "columns": {"id": {"name": "id", "sample_value": 1, "data_type": "int", "is_null": false, "length": 1}, "symbol": {"name": "symbol", "sample_value": "AALR", "data_type": "str", "is_null": false, "length": 4}, "date": {"name": "date", "sample_value": "2017-03-12", "data_type": "str", "is_null": false, "length": 10}, "open": {"name": "open", "sample_value": 11.43, "data_type": "float", "is_null": false, "length": 5}, "high": {"name": "high", "sample_value": 11.7, "data_type": "float", "is_null": false, "length": 4}, "low": {"name": "low", "sample_value": 11.3, "data_type": "float", "is_null": false, "length": 4}, "close": {"name": "close", "sample_value": 11.4, "data_type": "float", "is_null": false, "length": 4}, "volume": {"name": "volume", "sample_value": 3095, "data_type": "int", "is_null": false, "length": 4}, "open_interest": {"name": "open_interest", "sample_value": 0, "data_type": "int", "is_null": false, "length": 1}, "adjusted_close": {"name": "adjusted_close", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "created_at": {"name": "created_at", "sample_value": "2025-06-15T15:08:14.627507+00:00", "data_type": "str", "is_null": false, "length": 32}}, "column_names": ["adjusted_close", "close", "created_at", "date", "high", "id", "low", "open", "open_interest", "symbol", "volume"], "inspection_timestamp": "2025-06-15T21:41:01.411026"}}, "additional_tables": {"user_portfolios": {"table_name": "user_portfolios", "total_records": 1, "total_columns": 11, "latest_update": "2025-06-14T17:32:18.673346+00:00", "columns": {"id": {"name": "id", "sample_value": "d0cd6cdb-7cb4-4666-9c8d-d99081bffd7d", "data_type": "str", "is_null": false, "length": 36}, "user_id": {"name": "user_id", "sample_value": "27260eb4-29e1-48e1-9154-e65229bdda7b", "data_type": "str", "is_null": false, "length": 36}, "name": {"name": "name", "sample_value": "test3", "data_type": "str", "is_null": false, "length": 5}, "description": {"name": "description", "sample_value": "", "data_type": "str", "is_null": false, "length": 0}, "is_default": {"name": "is_default", "sample_value": false, "data_type": "bool", "is_null": false, "length": 5}, "total_value": {"name": "total_value", "sample_value": 0.0, "data_type": "float", "is_null": false, "length": 3}, "total_cost": {"name": "total_cost", "sample_value": 0.0, "data_type": "float", "is_null": false, "length": 3}, "total_profit_loss": {"name": "total_profit_loss", "sample_value": 0.0, "data_type": "float", "is_null": false, "length": 3}, "currency": {"name": "currency", "sample_value": "EGP", "data_type": "str", "is_null": false, "length": 3}, "created_at": {"name": "created_at", "sample_value": "2025-06-14T17:32:18.673346+00:00", "data_type": "str", "is_null": false, "length": 32}, "updated_at": {"name": "updated_at", "sample_value": "2025-06-14T17:32:18.673346+00:00", "data_type": "str", "is_null": false, "length": 32}}, "column_names": ["created_at", "currency", "description", "id", "is_default", "name", "total_cost", "total_profit_loss", "total_value", "updated_at", "user_id"], "inspection_timestamp": "2025-06-15T21:41:02.001995"}, "paper_trades": {"table_name": "paper_trades", "total_records": 2, "total_columns": 16, "latest_update": null, "columns": {"id": {"name": "id", "sample_value": "9fb9232d-8f57-43bd-9b96-11ae2323b63a", "data_type": "str", "is_null": false, "length": 36}, "account_id": {"name": "account_id", "sample_value": "01119f21-88a4-4e44-b453-0d603221585a", "data_type": "str", "is_null": false, "length": 36}, "symbol": {"name": "symbol", "sample_value": "COMI", "data_type": "str", "is_null": false, "length": 4}, "trade_type": {"name": "trade_type", "sample_value": "buy", "data_type": "str", "is_null": false, "length": 3}, "quantity": {"name": "quantity", "sample_value": 1000.0, "data_type": "float", "is_null": false, "length": 6}, "entry_price": {"name": "entry_price", "sample_value": 62.0, "data_type": "float", "is_null": false, "length": 4}, "exit_price": {"name": "exit_price", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "stop_loss": {"name": "stop_loss", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "take_profit": {"name": "take_profit", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "profit_loss": {"name": "profit_loss", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "commission": {"name": "commission", "sample_value": 0.0, "data_type": "float", "is_null": false, "length": 3}, "status": {"name": "status", "sample_value": "open", "data_type": "str", "is_null": false, "length": 4}, "entry_time": {"name": "entry_time", "sample_value": "2025-06-14T16:13:09.483975+00:00", "data_type": "str", "is_null": false, "length": 32}, "exit_time": {"name": "exit_time", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "notes": {"name": "notes", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}, "strategy_name": {"name": "strategy_name", "sample_value": null, "data_type": "null", "is_null": true, "length": 0}}, "column_names": ["account_id", "commission", "entry_price", "entry_time", "exit_price", "exit_time", "id", "notes", "profit_loss", "quantity", "status", "stop_loss", "strategy_name", "symbol", "take_profit", "trade_type"], "inspection_timestamp": "2025-06-15T21:41:02.312024"}}, "table_relationships": {"stocks_realtime_to_stocks_financials": {"common_symbols": 218, "table1_total": 225, "table2_total": 252, "coverage_percentage": 86.51, "sample_common_symbols": ["EALR", "CNFN", "CCRS", "MTIE", "BIDI", "EDFM", "ORWE", "PHDC", "MFPC", "MPCO"]}, "stocks_realtime_to_stocks_historical": {"common_symbols": 1, "table1_total": 225, "table2_total": 1, "coverage_percentage": 0.44, "sample_common_symbols": ["AALR"]}, "stocks_financials_to_stocks_historical": {"common_symbols": 1, "table1_total": 252, "table2_total": 1, "coverage_percentage": 0.4, "sample_common_symbols": ["AALR"]}}, "frontend_usage_guide": {"common_queries": {"get_active_stocks": {"description": "Get all actively traded stocks", "table": "stocks_realtime", "filter": "volume > 0", "select": "symbol, current_price, volume, change_percent"}, "get_stock_financials": {"description": "Get financial data for a specific stock", "table": "stocks_financials", "filter": "symbol = \"SPECIFIC_SYMBOL\"", "select": "symbol, pe_ratio, market_cap, dividend_yield, total_revenue"}, "get_top_performers": {"description": "Get top performing stocks by volume", "table": "stocks_realtime", "filter": "volume > 100000", "order": "volume DESC", "limit": 50}, "get_stock_history": {"description": "Get historical data for a stock", "table": "stocks_historical", "filter": "symbol = \"SPECIFIC_SYMBOL\"", "order": "date DESC", "limit": 365}}, "column_categories": {"stocks_realtime": {"basic_info": ["name", "sector", "symbol"], "price_data": ["ask_price", "bid_price", "current_price", "high_price", "liquidity_flow", "liquidity_inflow", "liquidity_outflow", "low_price", "open_price", "opening_value", "previous_close", "price_range", "volume_inflow", "volume_outflow"], "volume_data": ["ask_volume", "avg_net_volume_3d", "avg_net_volume_5d", "bid_volume", "trades_count", "turnover", "volume"], "technical_indicators": ["kj_indicator", "ma10", "ma100", "ma20", "ma200", "ma5", "ma50", "market_cap", "tk_indicator"], "trading_signals": ["speculation_opportunity", "stop_loss", "target_1", "target_2", "target_3"], "liquidity_metrics": ["liquidity_ratio", "net_liquidity"], "metadata": ["last_trade_date", "last_trade_time", "updated_at"]}}, "data_types": {}, "recommended_filters": {"active_trading": "volume > 0", "exclude_suspended": "current_price IS NOT NULL", "high_volume": "volume > 100000", "recent_data": "updated_at > NOW() - INTERVAL '1 day'", "exclude_indices": "symbol NOT LIKE '%.%'"}, "join_strategies": {}}}