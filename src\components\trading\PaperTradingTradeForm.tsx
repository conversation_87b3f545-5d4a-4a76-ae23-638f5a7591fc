 

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

type Props = {
  onAdd: (data: {
    symbol: string;
    quantity: number;
    entry_price: number;
    trade_type: string;
  }) => void;
};

const PaperTradingTradeForm: React.FC<Props> = ({ onAdd }) => {
  const [symbol, setSymbol] = useState("");
  const [quantity, setQuantity] = useState("");
  const [entryPrice, setEntryPrice] = useState("");
  const [tradeType, setTradeType] = useState<"buy" | "sell">("buy");
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);

  // Call the new backend edge function
  async function handleAddTrade(e: React.FormEvent) {
    e.preventDefault();
    setErrorMsg(null);
    if (!symbol || !quantity || !entryPrice) return;
    setLoading(true);
    try {
      // Instead of calling onAdd prop directly, we call the edge function.
      const res = await fetch("/functions/v1/paper-trade", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // Only send JWT Auth header if available
          "Authorization": (localStorage.getItem("sb-access-token") ? "Bearer " + localStorage.getItem("sb-access-token") : ""),
        },
        body: JSON.stringify({
          account_id: (window as any).selectedPaperAccountId, // Will set this in the parent component next
          symbol: symbol.toUpperCase(),
          quantity: parseFloat(quantity),
          entry_price: parseFloat(entryPrice),
          trade_type: tradeType,
        }),
      });

      const data = await res.json();
      if (!res.ok) {
        setErrorMsg(data.error || "فشل إضافة الصفقة. حاول مرة أخرى.");
      } else {
        setSymbol("");
        setQuantity("");
        setEntryPrice("");
        setTradeType("buy");
        if (onAdd) onAdd(data.trade); // call prop for parent handling (e.g., to refetch)
      }
    } catch (err) {
      setErrorMsg("حدث خطأ أثناء الاتصال بالخادم.");
    } finally {
      setLoading(false);
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>إضافة صفقة</CardTitle>
      </CardHeader>
      <CardContent>
        <form
          className="flex flex-col md:flex-row gap-2 mb-2"
          onSubmit={handleAddTrade}
        >
          <Input
            placeholder="رمز السهم"
            value={symbol}
            onChange={e => setSymbol(e.target.value)}
            disabled={loading}
          />
          <Input
            placeholder="الكمية"
            type="number"
            min={1}
            value={quantity}
            onChange={e => setQuantity(e.target.value)}
            disabled={loading}
          />
          <Input
            placeholder="سعر الدخول"
            type="number"
            min={0}
            value={entryPrice}
            onChange={e => setEntryPrice(e.target.value)}
            disabled={loading}
          />
          <select
            className="border rounded px-2 py-1"
            value={tradeType}
            onChange={e => setTradeType(e.target.value as "buy" | "sell")}
            disabled={loading}
          >
            <option value="buy">شراء</option>
            <option value="sell">بيع</option>
          </select>
          <Button type="submit" disabled={!symbol || !quantity || !entryPrice || loading}>
            {loading ? "جاري الإضافة..." : "إضافة"}
          </Button>
        </form>
        {errorMsg && <div className="text-red-600 mt-1">{errorMsg}</div>}
      </CardContent>
    </Card>
  );
};

export default PaperTradingTradeForm;

