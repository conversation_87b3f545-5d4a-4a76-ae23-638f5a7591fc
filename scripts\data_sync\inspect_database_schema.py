#!/usr/bin/env python3
"""
Database Schema Inspector for EGX Stock AI Oracle
Creates comprehensive documentation of all database tables and columns
for frontend development reference
"""

import os
import sys
import json
import logging
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('schema_inspection.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    logger.error("Missing required environment variables")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def inspect_table_schema(table_name):
    """Inspect a single table and return detailed schema information"""
    logger.info(f"🔍 Inspecting table: {table_name}")
    
    try:
        # Get sample data to understand column structure
        result = supabase.table(table_name).select("*").limit(1).execute()
        
        if not result.data:
            logger.warning(f"⚠️ Table {table_name} is empty")
            return None
        
        sample_record = result.data[0]
        
        # Get column information
        columns = {}
        for column_name, value in sample_record.items():
            columns[column_name] = {
                'name': column_name,
                'sample_value': value,
                'data_type': type(value).__name__ if value is not None else 'null',
                'is_null': value is None,
                'length': len(str(value)) if value is not None else 0
            }
        
        # Get table statistics
        count_result = supabase.table(table_name).select("*", count="exact").execute()
        total_records = count_result.count if hasattr(count_result, 'count') else len(count_result.data)
        
        # Try to get latest update timestamp
        latest_update = None
        if 'updated_at' in columns:
            try:
                latest_result = supabase.table(table_name).select("updated_at").order("updated_at", desc=True).limit(1).execute()
                if latest_result.data:
                    latest_update = latest_result.data[0]['updated_at']
            except:
                pass
        
        table_info = {
            'table_name': table_name,
            'total_records': total_records,
            'total_columns': len(columns),
            'latest_update': latest_update,
            'columns': columns,
            'column_names': sorted(list(columns.keys())),
            'inspection_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ {table_name}: {total_records} records, {len(columns)} columns")
        return table_info
        
    except Exception as e:
        logger.error(f"❌ Error inspecting {table_name}: {e}")
        return None

def get_table_relationships():
    """Identify relationships between tables based on common columns"""
    logger.info("🔗 Analyzing table relationships...")
    
    relationships = {}
    tables = ['stocks_realtime', 'stocks_financials', 'stocks_historical']
    
    # Get symbols from each table
    table_symbols = {}
    for table in tables:
        try:
            result = supabase.table(table).select("symbol").execute()
            symbols = set([row['symbol'] for row in result.data])
            table_symbols[table] = symbols
            logger.info(f"📊 {table}: {len(symbols)} unique symbols")
        except Exception as e:
            logger.warning(f"⚠️ Could not get symbols from {table}: {e}")
            table_symbols[table] = set()
    
    # Calculate intersections
    for i, table1 in enumerate(tables):
        for table2 in tables[i+1:]:
            common_symbols = table_symbols[table1].intersection(table_symbols[table2])
            relationships[f"{table1}_to_{table2}"] = {
                'common_symbols': len(common_symbols),
                'table1_total': len(table_symbols[table1]),
                'table2_total': len(table_symbols[table2]),
                'coverage_percentage': round(len(common_symbols) / max(len(table_symbols[table1]), len(table_symbols[table2])) * 100, 2) if table_symbols[table1] or table_symbols[table2] else 0,
                'sample_common_symbols': list(common_symbols)[:10]  # First 10 as sample
            }
    
    return relationships

def generate_frontend_reference():
    """Generate comprehensive reference documentation for frontend developers"""
    logger.info("📝 Generating frontend reference documentation...")
    
    # Main stock-related tables
    main_tables = [
        'stocks_realtime',
        'stocks_financials', 
        'stocks_historical'
    ]
    
    # Additional tables that might exist
    additional_tables = [
        'user_profiles',
        'user_portfolios',
        'price_alerts',
        'paper_trades',
        'watchlists'
    ]
    
    schema_doc = {
        'generated_at': datetime.now().isoformat(),
        'database_url': SUPABASE_URL,
        'inspection_summary': {},
        'main_tables': {},
        'additional_tables': {},
        'table_relationships': {},
        'frontend_usage_guide': {}
    }
    
    # Inspect main tables
    logger.info("🔍 Inspecting main stock tables...")
    for table in main_tables:
        table_info = inspect_table_schema(table)
        if table_info:
            schema_doc['main_tables'][table] = table_info
    
    # Try to inspect additional tables
    logger.info("🔍 Checking for additional tables...")
    for table in additional_tables:
        table_info = inspect_table_schema(table)
        if table_info:
            schema_doc['additional_tables'][table] = table_info
    
    # Get relationships
    schema_doc['table_relationships'] = get_table_relationships()
    
    # Generate summary
    total_tables = len(schema_doc['main_tables']) + len(schema_doc['additional_tables'])
    total_records = sum([table['total_records'] for table in schema_doc['main_tables'].values()])
    total_columns = sum([table['total_columns'] for table in schema_doc['main_tables'].values()])
    
    schema_doc['inspection_summary'] = {
        'total_tables_inspected': total_tables,
        'total_records_across_main_tables': total_records,
        'total_columns_across_main_tables': total_columns,
        'main_tables_list': list(schema_doc['main_tables'].keys()),
        'additional_tables_list': list(schema_doc['additional_tables'].keys())
    }
    
    # Generate frontend usage guide
    schema_doc['frontend_usage_guide'] = generate_usage_guide(schema_doc)
    
    return schema_doc

def generate_usage_guide(schema_doc):
    """Generate practical usage guide for frontend developers"""
    guide = {
        'common_queries': {},
        'column_categories': {},
        'data_types': {},
        'recommended_filters': {},
        'join_strategies': {}
    }
    
    # Analyze column categories in stocks_realtime
    if 'stocks_realtime' in schema_doc['main_tables']:
        realtime_columns = schema_doc['main_tables']['stocks_realtime']['column_names']
        
        # Categorize columns for easier frontend usage
        categories = {
            'basic_info': [],
            'price_data': [],
            'volume_data': [],
            'technical_indicators': [],
            'trading_signals': [],
            'liquidity_metrics': [],
            'metadata': []
        }
        
        for col in realtime_columns:
            col_lower = col.lower()
            if any(x in col_lower for x in ['symbol', 'name', 'sector']):
                categories['basic_info'].append(col)
            elif any(x in col_lower for x in ['price', 'open', 'high', 'low', 'close']):
                categories['price_data'].append(col)
            elif any(x in col_lower for x in ['volume', 'turnover', 'trades']):
                categories['volume_data'].append(col)
            elif any(x in col_lower for x in ['ma', 'tk', 'kj']):
                categories['technical_indicators'].append(col)
            elif any(x in col_lower for x in ['target', 'stop_loss', 'speculation']):
                categories['trading_signals'].append(col)
            elif any(x in col_lower for x in ['liquidity', 'inflow', 'outflow']):
                categories['liquidity_metrics'].append(col)
            elif any(x in col_lower for x in ['updated_at', 'created_at', 'last_trade']):
                categories['metadata'].append(col)
        
        guide['column_categories']['stocks_realtime'] = categories
    
    # Generate common query examples
    guide['common_queries'] = {
        'get_active_stocks': {
            'description': 'Get all actively traded stocks',
            'table': 'stocks_realtime',
            'filter': 'volume > 0',
            'select': 'symbol, current_price, volume, change_percent'
        },
        'get_stock_financials': {
            'description': 'Get financial data for a specific stock',
            'table': 'stocks_financials',
            'filter': 'symbol = "SPECIFIC_SYMBOL"',
            'select': 'symbol, pe_ratio, market_cap, dividend_yield, total_revenue'
        },
        'get_top_performers': {
            'description': 'Get top performing stocks by volume',
            'table': 'stocks_realtime',
            'filter': 'volume > 100000',
            'order': 'volume DESC',
            'limit': 50
        },
        'get_stock_history': {
            'description': 'Get historical data for a stock',
            'table': 'stocks_historical',
            'filter': 'symbol = "SPECIFIC_SYMBOL"',
            'order': 'date DESC',
            'limit': 365
        }
    }
    
    # Recommended filters for performance
    guide['recommended_filters'] = {
        'active_trading': 'volume > 0',
        'exclude_suspended': 'current_price IS NOT NULL',
        'high_volume': 'volume > 100000',
        'recent_data': 'updated_at > NOW() - INTERVAL \'1 day\'',
        'exclude_indices': 'symbol NOT LIKE \'%.%\''
    }
    
    return guide

def create_typescript_interfaces(schema_doc):
    """Generate TypeScript interfaces for frontend development"""
    logger.info("🔧 Generating TypeScript interfaces...")
    
    interfaces = []
    
    for table_name, table_info in schema_doc['main_tables'].items():
        interface_name = ''.join([word.capitalize() for word in table_name.split('_')])
        
        interface = f"export interface {interface_name} {{\n"
        
        for col_name, col_info in table_info['columns'].items():
            # Map Python types to TypeScript types
            ts_type = 'string'  # default
            if col_info['data_type'] == 'int' or col_info['data_type'] == 'float':
                ts_type = 'number'
            elif col_info['data_type'] == 'bool':
                ts_type = 'boolean'
            elif col_info['data_type'] == 'NoneType' or col_info['is_null']:
                ts_type = 'string | null'
            
            interface += f"  {col_name}: {ts_type};\n"
        
        interface += "}\n\n"
        interfaces.append(interface)
    
    return '\n'.join(interfaces)

def main():
    """Main function"""
    logger.info("🚀 Starting Database Schema Inspection...")
    logger.info("=" * 60)
    
    try:
        # Generate comprehensive schema documentation
        schema_doc = generate_frontend_reference()
        
        # Save main schema documentation
        with open('database_schema_reference.json', 'w', encoding='utf-8') as f:
            json.dump(schema_doc, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info("💾 Main schema reference saved to: database_schema_reference.json")
        
        # Create simplified reference for quick lookup
        simplified_ref = {
            'tables': {},
            'quick_reference': {}
        }
        
        for table_name, table_info in schema_doc['main_tables'].items():
            simplified_ref['tables'][table_name] = {
                'columns': table_info['column_names'],
                'record_count': table_info['total_records'],
                'last_updated': table_info['latest_update']
            }
        
        simplified_ref['quick_reference'] = {
            'all_realtime_columns': schema_doc['main_tables'].get('stocks_realtime', {}).get('column_names', []),
            'all_financial_columns': schema_doc['main_tables'].get('stocks_financials', {}).get('column_names', []),
            'all_historical_columns': schema_doc['main_tables'].get('stocks_historical', {}).get('column_names', []),
            'common_relationships': schema_doc['table_relationships']
        }
        
        with open('database_quick_reference.json', 'w', encoding='utf-8') as f:
            json.dump(simplified_ref, f, indent=2, ensure_ascii=False)
        
        logger.info("💾 Quick reference saved to: database_quick_reference.json")
        
        # Generate TypeScript interfaces
        ts_interfaces = create_typescript_interfaces(schema_doc)
        with open('database_types.ts', 'w', encoding='utf-8') as f:
            f.write("// Auto-generated TypeScript interfaces for EGX Stock AI Oracle\n")
            f.write(f"// Generated on: {datetime.now().isoformat()}\n\n")
            f.write(ts_interfaces)
        
        logger.info("💾 TypeScript interfaces saved to: database_types.ts")
        
        # Create markdown documentation
        create_markdown_documentation(schema_doc)
        
        # Print summary
        logger.info("=" * 60)
        logger.info("📋 Schema Inspection Summary:")
        logger.info(f"   📊 Tables inspected: {schema_doc['inspection_summary']['total_tables_inspected']}")
        logger.info(f"   📈 Total records: {schema_doc['inspection_summary']['total_records_across_main_tables']:,}")
        logger.info(f"   📋 Total columns: {schema_doc['inspection_summary']['total_columns_across_main_tables']}")
        logger.info("")
        logger.info("📄 Files Generated:")
        logger.info("   - database_schema_reference.json (Complete documentation)")
        logger.info("   - database_quick_reference.json (Quick lookup)")
        logger.info("   - database_types.ts (TypeScript interfaces)")
        logger.info("   - DATABASE_SCHEMA.md (Human-readable docs)")
        logger.info("   - schema_inspection.log (This log)")
        
    except Exception as e:
        logger.error(f"❌ Error during schema inspection: {e}")
        sys.exit(1)

def create_markdown_documentation(schema_doc):
    """Create human-readable markdown documentation"""
    logger.info("📝 Creating markdown documentation...")
    
    md_content = []
    md_content.append("# EGX Stock AI Oracle - Database Schema Reference")
    md_content.append(f"\n**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    md_content.append(f"\n**Database:** {SUPABASE_URL}")
    md_content.append("\n---\n")
    
    # Summary
    md_content.append("## 📊 Summary")
    summary = schema_doc['inspection_summary']
    md_content.append(f"- **Total Tables:** {summary['total_tables_inspected']}")
    md_content.append(f"- **Total Records:** {summary['total_records_across_main_tables']:,}")
    md_content.append(f"- **Total Columns:** {summary['total_columns_across_main_tables']}")
    md_content.append("")
    
    # Main Tables
    md_content.append("## 📋 Main Tables")
    for table_name, table_info in schema_doc['main_tables'].items():
        md_content.append(f"\n### 🗂️ {table_name}")
        md_content.append(f"- **Records:** {table_info['total_records']:,}")
        md_content.append(f"- **Columns:** {table_info['total_columns']}")
        md_content.append(f"- **Last Updated:** {table_info['latest_update']}")
        
        md_content.append("\n**Columns:**")
        for col_name in sorted(table_info['column_names']):
            col_info = table_info['columns'][col_name]
            md_content.append(f"- `{col_name}` ({col_info['data_type']})")
    
    # Relationships
    md_content.append("\n## 🔗 Table Relationships")
    for rel_name, rel_info in schema_doc['table_relationships'].items():
        tables = rel_name.replace('_to_', ' ↔ ')
        md_content.append(f"\n### {tables}")
        md_content.append(f"- **Common Symbols:** {rel_info['common_symbols']}")
        md_content.append(f"- **Coverage:** {rel_info['coverage_percentage']}%")
    
    # Usage Guide
    if 'frontend_usage_guide' in schema_doc:
        md_content.append("\n## 🚀 Frontend Usage Guide")
        
        # Common Queries
        md_content.append("\n### Common Queries")
        for query_name, query_info in schema_doc['frontend_usage_guide']['common_queries'].items():
            md_content.append(f"\n#### {query_name.replace('_', ' ').title()}")
            md_content.append(f"**Description:** {query_info['description']}")
            md_content.append(f"```sql")
            md_content.append(f"SELECT {query_info['select']}")
            md_content.append(f"FROM {query_info['table']}")
            if 'filter' in query_info:
                md_content.append(f"WHERE {query_info['filter']}")
            if 'order' in query_info:
                md_content.append(f"ORDER BY {query_info['order']}")
            if 'limit' in query_info:
                md_content.append(f"LIMIT {query_info['limit']}")
            md_content.append("```")
    
    # Write to file
    with open('DATABASE_SCHEMA.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(md_content))
    
    logger.info("💾 Markdown documentation saved to: DATABASE_SCHEMA.md")

if __name__ == "__main__":
    main()
