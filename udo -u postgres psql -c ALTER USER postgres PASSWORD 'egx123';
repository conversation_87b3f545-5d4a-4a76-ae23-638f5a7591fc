                                                           List of databases
        Name         |  Owner   | Encoding | Locale Provider | Collate |  Ctype  | ICU Locale | ICU Rules |      Access privileges      
---------------------+----------+----------+-----------------+---------+---------+------------+-----------+-----------------------------
 ai_social_pro       | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | =Tc/postgres               +
                     |          |          |                 |         |         |            |           | postgres=CTc/postgres      +
                     |          |          |                 |         |         |            |           | ai_social_user=CTc/postgres
 borsa_dashboard     | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | =Tc/postgres               +
                     |          |          |                 |         |         |            |           | postgres=CTc/postgres      +
                     |          |          |                 |         |         |            |           | dashboard_user=CTc/postgres
 data_api            | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | =Tc/postgres               +
                     |          |          |                 |         |         |            |           | postgres=CTc/postgres      +
                     |          |          |                 |         |         |            |           | app_user=c/postgres
 egx_stock_oracle    | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | =Tc/postgres               +
                     |          |          |                 |         |         |            |           | postgres=CTc/postgres      +
                     |          |          |                 |         |         |            |           | egx_api_user=c/postgres    +
                     |          |          |                 |         |         |            |           | egx_etl_user=c/postgres
 egx_stock_oracle_db | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | 
 postgres            | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | 
 template0           | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | =c/postgres                +
                     |          |          |                 |         |         |            |           | postgres=CTc/postgres
 template1           | postgres | UTF8     | libc            | C.UTF-8 | C.UTF-8 |            |           | =c/postgres                +
                     |          |          |                 |         |         |            |           | postgres=CTc/postgres
(8 rows)

