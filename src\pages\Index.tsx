import React, { useState, useEffect } from "react";
import { useWebSocket } from "@/hooks/useWebSocket";
import AuthDialog from "@/components/AuthDialog";
import { useAuthUser } from "@/hooks/useAuthUser";
import { useMobileGestures } from "@/hooks/useMobileGestures";
import Header from "@/components/layout/Header";
import MainTabs from "@/components/layout/MainTabs";
import LandingPage from "@/components/landing/LandingPage";
import { AnalysisProvider } from "@/contexts/AnalysisContext";

const navItems = [  
  { id: "overview", label: "نظرة عامة", icon: "BarChart3", description: "ملخص السوق والمؤشرات العامة مع أهم الأسهم النشطة" },
  { id: "live-trading", label: "الشاشة اللحظية", icon: "Activity", description: "رصد مباشر لجميع أسهم البورصة مع تحديثات لحظية" },
  { id: "advanced-market", label: "التحليل المتقدم للسوق", icon: "Brain", description: "أدوات احترافية للتحليل المتعمق وإدارة المخاطر والتنبؤات" },
  { id: "comprehensive-analysis", label: "التحليل الشامل للأسهم", icon: "Target", description: "تحليل متقدم يشمل جميع مدارس التحليل الفني" },
  { id: "portfolio", label: "محفظتي", icon: "Briefcase", description: "إدارة وتتبع محفظتك الاستثمارية" },
  { id: "screener", label: "مرشح الأسهم", icon: "Filter", description: "البحث والتصفية المتقدمة للأسهم" },
  { id: "alerts", label: "التنبيهات الذكية", icon: "Bell", description: "مركز شامل لإدارة التنبيهات والإشعارات الذكية" },
  { id: "paper-trading", label: "التداول التجريبي", icon: "TrendingUp", description: "محاكي التداول للتدريب" },
  { id: "analytics", label: "التحليل المتقدم", icon: "Brain", description: "تحليلات الذكاء الاصطناعي المتقدمة" },
  { id: "enhanced-experience", label: "تحسين التجربة", icon: "Sparkles", description: "تحسينات الأداء وتخصيص المظهر" },
  { id: "risk-management", label: "إدارة المخاطر", icon: "Settings", description: "أدوات تحليل وإدارة المخاطر" },
  { id: "dashboard-customizer", label: "تخصيص لوحة التحكم", icon: "Settings", description: "تخصيص وترتيب العناصر" },
  { id: "mobile-features", label: "الميزات المحمولة", icon: "Smartphone", description: "إعدادات التطبيق المحمول" },
  { id: "news", label: "الأخبار", icon: "Newspaper", description: "آخر الأخبار المالية والتحليلات" },
  { id: "calendar", label: "التقويم المالي", icon: "Calendar", description: "الأحداث المهمة والمواعيد القادمة" },
  { id: "ai-insights", label: "توصيات الذكاء الاصطناعي", icon: "Brain", description: "تحليلات وتوصيات مدعومة بالذكاء الاصطناعي" },
  { id: "learning", label: "التعلم", icon: "BookOpen", description: "دروس ونصائح تعليمية" },
  { id: "telegram", label: "تيليجرام", icon: "Bell", description: "ربط البوت والتنبيهات الفورية" },
  { id: "settings", label: "الإعدادات", icon: "Settings", description: "تخصيص التفضيلات والحساب" }
];

const Index = () => {
  const [showLanding, setShowLanding] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const { isConnected, lastMessage } = useWebSocket("wss://mock-websocket");

  // Gesture hook
  const gestureRef = useMobileGestures({
    onSwipeLeft: () => {
      const currentIndex = navItems.findIndex((item) => item.id === activeTab);
      const nextIndex = (currentIndex + 1) % navItems.length;
      setActiveTab(navItems[nextIndex].id);
    },
    onSwipeRight: () => {
      const currentIndex = navItems.findIndex((item) => item.id === activeTab);
      const prevIndex = currentIndex === 0 ? navItems.length - 1 : currentIndex - 1;
      setActiveTab(navItems[prevIndex].id);
    },
    onLongPress: () => {
      setMobileMenuOpen(!mobileMenuOpen);
    }
  });

  useEffect(() => {
    setIsLoaded(true);
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme === "dark") {
      setIsDarkMode(true);
      document.documentElement.classList.add("dark");
    }
  }, []);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    if (!isDarkMode) {
      document.documentElement.classList.add("dark");
      localStorage.setItem("theme", "dark");
    } else {
      document.documentElement.classList.remove("dark");
      localStorage.setItem("theme", "light");
    }
  };

  const handleEnterApp = () => {
    setShowLanding(false);
  };

  const { user, loading: authLoading } = useAuthUser();
  const [authDialogOpen, setAuthDialogOpen] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);

  const handleLogout = async () => {
    setLogoutLoading(true);
    await import("@/integrations/supabase/client").then(({ supabase }) =>
      supabase.auth.signOut()
    );
    setLogoutLoading(false);
  };

  // Convert lastMessage (object) to string as expected by MainTabs
  let lastMessageString: string | null = null;
  if (lastMessage) {
    if (typeof lastMessage === "string") {
      lastMessageString = lastMessage;
    } else if (typeof lastMessage === "object") {
      // You can customize how to show the message
      lastMessageString = lastMessage.type === "price_update"
        ? `${lastMessage.symbol}: ${lastMessage.price?.toFixed(2)} (${lastMessage.change?.toFixed(2)})`
        : JSON.stringify(lastMessage);
    }
  }

  // إذا كانت صفحة الهبوط مفعلة، اعرضها
  if (showLanding) {
    return <LandingPage onEnterApp={handleEnterApp} />;
  }

  // إذا دخل المستخدم للتطبيق، اعرض المحتوى الرئيسي
  return (
    <AnalysisProvider onTabChange={setActiveTab}>
      <Header
        onLoginClick={() => setAuthDialogOpen(true)}
        logoutLoading={logoutLoading}
        onLogout={handleLogout}
      />
      <AuthDialog open={authDialogOpen} onOpenChange={setAuthDialogOpen} />

      <MainTabs
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        mobileMenuOpen={mobileMenuOpen}
        setMobileMenuOpen={setMobileMenuOpen}
        isLoaded={isLoaded}
        isConnected={isConnected}
        lastMessage={lastMessageString}
        isDarkMode={isDarkMode}
        toggleTheme={toggleTheme}
        navItems={navItems}
        gestureRef={gestureRef}
      />
    </AnalysisProvider>
  );
};

export default Index;
