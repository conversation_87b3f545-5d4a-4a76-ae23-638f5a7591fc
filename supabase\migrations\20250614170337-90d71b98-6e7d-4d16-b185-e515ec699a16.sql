
-- 1. Paper Trading policies
ALTER TABLE public.paper_trading_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_trades ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only owner can view their paper trading accounts"
  ON public.paper_trading_accounts
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Only owner can manipulate their paper trading accounts"
  ON public.paper_trading_accounts
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Only owner can view their paper trades"
  ON public.paper_trades
  FOR SELECT USING (
    account_id IN (
      SELECT id FROM public.paper_trading_accounts WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Only owner can manipulate their paper trades"
  ON public.paper_trades
  FOR ALL
  USING (
    account_id IN (
      SELECT id FROM public.paper_trading_accounts WHERE user_id = auth.uid()
    )
  ) WITH CHECK (
    account_id IN (
      SELECT id FROM public.paper_trading_accounts WHERE user_id = auth.uid()
    )
  );

-- 2. Portfolio policies
ALTER TABLE public.user_portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolio_holdings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only owner can view their portfolios"
  ON public.user_portfolios
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Only owner can manipulate their portfolios"
  ON public.user_portfolios
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Only owner can view portfolio holdings"
  ON public.portfolio_holdings
  FOR SELECT USING (
    portfolio_id IN (
      SELECT id FROM public.user_portfolios WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Only owner can manipulate portfolio holdings"
  ON public.portfolio_holdings
  FOR ALL
  USING (
    portfolio_id IN (
      SELECT id FROM public.user_portfolios WHERE user_id = auth.uid()
    )
  ) WITH CHECK (
    portfolio_id IN (
      SELECT id FROM public.user_portfolios WHERE user_id = auth.uid()
    )
  );

-- 3. Price Alerts
ALTER TABLE public.price_alerts ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Only owner can view their price alerts"
  ON public.price_alerts
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Only owner can manipulate their price alerts"
  ON public.price_alerts
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- 4. User notifications
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Only owner can view their notifications"
  ON public.user_notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Only owner can manipulate their notifications"
  ON public.user_notifications
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- 5. Broker Integration (if user_broker_credentials table gets added in the future)
-- You can request this if needed!
