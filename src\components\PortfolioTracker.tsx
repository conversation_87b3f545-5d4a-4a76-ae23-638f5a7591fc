import React, { useState } from 'react';
import { useAuthUser } from '@/hooks/useAuthUser';
import { useUserPortfolios } from "@/hooks/useUserPortfolios";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Plus, Trash2 } from "lucide-react";
import PortfolioSummary from "@/components/portfolio/PortfolioSummary";
import AddStockForm from './portfolio/AddStockForm';
import PortfolioHoldings from './portfolio/PortfolioHoldings';

const PortfolioTracker = () => {
  const { user, loading: userLoading } = useAuthUser();
  const {
    portfolios,
    loading,
    error,
    addPortfolio,
    removePortfolio,
    addHolding,
    removeHolding,
    refresh
  } = useUserPortfolios({ userId: user?.id ?? null });

  const [portfolioName, setPortfolioName] = useState("");
  const [desc, setDesc] = useState("");
  const [selectedPortfolio, setSelectedPortfolio] = useState<string | null>(null);

  // New holding (for AddStockForm only)
  const [newStock, setNewStock] = useState({
    symbol: '',
    quantity: '',
    buyPrice: ''
  });

  if (userLoading || loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        جاري تحميل المحفظة ...
      </div>
    );
  }

  // Add portfolio handler
  const handleAddPortfolio = async () => {
    if (!portfolioName) return;
    await addPortfolio({ name: portfolioName, description: desc });
    setPortfolioName("");
    setDesc("");
  };

  const selected = portfolios.find((p) => p.id === selectedPortfolio) || portfolios[0];

  // Handler for holding form
  const handleAddStock = async () => {
    if (!selected) return;
    if (!newStock.symbol || !newStock.quantity || !newStock.buyPrice) return;
    await addHolding(selected.id, {
      symbol: newStock.symbol.toUpperCase(),
      quantity: parseFloat(newStock.quantity),
      averageBuyPrice: parseFloat(newStock.buyPrice),
    });
    setNewStock({ symbol: '', quantity: '', buyPrice: '' });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>المحافظ الاستثمارية</span>
            <Button size="sm" onClick={refresh}>
              <Loader2 className="h-4 w-4 mr-1" />
              تحديث
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Portfolio creation */}
          <div className="flex gap-2 mb-4 flex-col md:flex-row">
            <Input
              placeholder="اسم المحفظة"
              value={portfolioName}
              onChange={(e) => setPortfolioName(e.target.value)}
            />
            <Input
              placeholder="وصف (اختياري)"
              value={desc}
              onChange={(e) => setDesc(e.target.value)}
            />
            <Button onClick={handleAddPortfolio} disabled={!portfolioName}>
              <Plus className="h-4 w-4 mr-1" /> أضف محفظة
            </Button>
          </div>
          {/* List portfolios */}
          <div className="flex flex-wrap gap-2">
            {portfolios.map((portfolio) => (
              <Button
                key={portfolio.id}
                variant={portfolio.id === selected?.id ? "default" : "outline"}
                onClick={() => setSelectedPortfolio(portfolio.id)}
                className={`rounded ${portfolio.id === selected?.id ? "bg-blue-600 text-white" : ""}`}
              >
                {portfolio.name}
                <span className="text-xs text-gray-400 ml-1">{portfolio.holdings.length} سهم</span>
                <Trash2
                  className="ml-2 h-3 w-3 cursor-pointer text-red-500 hover:text-red-600"
                  onClick={e => {
                    e.stopPropagation();
                    removePortfolio(portfolio.id);
                  }}
                />
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Portfolio summary for selected */}
      {selected && (
        <>
          <PortfolioSummary
            totalValue={selected.totalValue}
            totalProfitLoss={selected.totalProfitLoss}
            totalProfitLossPercent={selected.totalProfitLossPercent}
            portfolioLength={selected.holdings.length}
          />
          {/* Add holding */}
          <AddStockForm newStock={newStock} setNewStock={setNewStock} onAddStock={handleAddStock} />
          {/* Holdings list */}
          <PortfolioHoldings portfolio={selected.holdings.map(h => ({
            id: h.id,
            symbol: h.symbol,
            name: '', // No name in holdings, can enrich via stocks_master if wanted
            quantity: h.quantity,
            buyPrice: h.averageBuyPrice,
            currentPrice: h.currentPrice ?? 0,
            totalValue: h.currentValue ?? 0,
            profitLoss: h.profitLoss ?? 0,
            profitLossPercent: h.profitLossPercent ?? 0,
          }))} onRemoveStock={removeHolding} />
        </>
      )}
      {error && <div className="text-red-500 text-center">{error}</div>}
    </div>
  );
};

export default PortfolioTracker;
