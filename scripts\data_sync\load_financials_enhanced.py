#!/usr/bin/env python3
"""
Enhanced Financial Data Loader for EGX Stock AI Oracle
Processes comprehensive financial data from CSV file with full column mapping
"""

import pandas as pd
from supabase import create_client, Client
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
import sys

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_data_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
CSV_PATH = os.getenv("FINANCIAL_CSV_PATH", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def safe_float_convert(value, default=None):
    """Safely convert value to float"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A' or value == '-':
            return default
        # Handle percentage strings
        if isinstance(value, str) and value.endswith('%'):
            return float(value[:-1])
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int_convert(value, default=None):
    """Safely convert value to int"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A' or value == '-':
            return default
        return int(float(value))
    except (ValueError, TypeError):
        return default

def safe_string_convert(value, default=None):
    """Safely convert value to string"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A' or value == '-':
            return default
        return str(value).strip()
    except:
        return default

def process_financial_data():
    """Process comprehensive financial data from CSV file"""
    try:
        if not os.path.exists(CSV_PATH):
            logger.error(f"CSV file not found: {CSV_PATH}")
            return
        
        logger.info(f"Reading financial data from: {CSV_PATH}")
        
        # Read CSV file
        try:
            df = pd.read_csv(CSV_PATH)
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            return
        
        if df.empty:
            logger.warning("CSV file is empty")
            return
        
        logger.info(f"Loaded {len(df)} rows from CSV file")
        logger.info(f"Available columns: {list(df.columns)}")
        
        # Define comprehensive column mapping
        column_mappings = {
            # Basic Information
            'symbol': ['Symbol', 'symbol', 'SYMBOL'],
            'description': ['Description', 'Company Name', 'Name'],
            'sector': ['Sector', 'sector'],
            'industry': ['Industry', 'industry'],
            'index_membership': ['Index', 'index'],            # Market Data
            'market_cap': ['Market capitalization', 'Market Cap', 'market_cap'],
            'market_cap_currency': ['Market capitalization - Currency'],
            'float_shares_percent': ['Float shares %'],
            'float_shares_outstanding': ['Float shares outstanding'],
            'total_shares_outstanding': ['Total common shares outstanding'],
            
            # Handle existing database columns
            'revenue_ttm': ['Total revenue, Annual', 'Revenue TTM'],
            'net_income_ttm': ['Net income, Annual', 'Net Income TTM'],
            'eps_ttm': ['Earnings per share diluted, Trailing 12 months', 'EPS TTM'],
            'beta': ['Beta 5 years', 'Beta'],
            
            # Valuation Ratios
            'pe_ratio': ['Price to earnings ratio', 'P/E Ratio', 'pe_ratio'],
            'price_to_book': ['Price to book ratio'],
            'price_to_sales': ['Price to sales ratio'],
            'price_to_cash_flow': ['Price to cash flow ratio'],
            'price_to_free_cash_flow': ['Price to free cash flow ratio'],
            'enterprise_value': ['Enterprise value'],
            'ev_to_ebitda': ['Enterprise value to EBITDA ratio, Trailing 12 months'],
            'ev_to_revenue': ['Enterprise value to revenue ratio, Trailing 12 months'],
            
            # Earnings & EPS
            'eps_diluted_ttm': ['Earnings per share diluted, Trailing 12 months'],
            'eps_basic_ttm': ['Earnings per share basic, Trailing 12 months'],
            'eps_growth_ttm': ['Earnings per share diluted growth %, TTM YoY'],
            'eps_estimate_quarterly': ['Earnings per share estimate, Quarterly'],
            'eps_reported_annual': ['Earnings per share reported, Annual'],
            'forward_pe': ['Forward non-GAAP price to earnings, Annual'],
            
            # Dividend Information
            'dividend_yield': ['Dividend yield %, Trailing 12 months'],
            'dividend_yield_indicated': ['Dividend yield % (indicated)'],
            'dividends_per_share': ['Dividends per share, Annual'],
            'dividend_payout_ratio': ['Dividend payout ratio %, Trailing 12 months'],
            'dividends_paid_annual': ['Dividends paid, Annual'],
            'continuous_dividend_growth': ['Continuous dividend growth'],
            
            # Financial Statements - Income
            'total_revenue': ['Total revenue, Annual'],
            'gross_profit': ['Gross profit, Annual'],
            'operating_income': ['Operating income, Trailing 12 months'],
            'net_income': ['Net income, Annual'],
            'ebitda': ['EBITDA, Trailing 12 months'],
            
            # Financial Statements - Balance Sheet
            'total_assets': ['Total assets, Quarterly'],
            'total_current_assets': ['Total current assets, Quarterly'],
            'total_current_liabilities': ['Total current liabilities, Quarterly'],
            'total_debt': ['Total debt, Annual'],
            'total_equity': ['Total equity, Quarterly'],
            'total_liabilities': ['Total liabilities, Annual'],
            'cash_and_equivalents': ['Cash & equivalents, Annual'],
            'cash_short_term_investments': ['Cash and short term investments, Annual'],
            'goodwill': ['Goodwill, net, Quarterly'],
            'net_debt': ['Net debt, Quarterly'],
            
            # Cash Flow
            'free_cash_flow': ['Free cash flow, Annual'],
            'operating_cash_flow': ['Cash from operating activities, Trailing 12 months'],
            'investing_cash_flow': ['Cash from investing activities, Trailing 12 months'],
            'financing_cash_flow': ['Cash from financing activities, Trailing 12 months'],
            'capital_expenditures': ['Capital expenditures, Trailing 12 months'],
            
            # Growth Rates
            'revenue_growth': ['Revenue growth %, TTM YoY'],
            'net_income_growth': ['Net income growth %, TTM YoY'],
            'gross_profit_growth': ['Gross profit growth %, TTM YoY'],
            'ebitda_growth': ['EBITDA growth %, TTM YoY'],
            'free_cash_flow_growth': ['Free cash flow growth %, TTM YoY'],
            'total_assets_growth': ['Total assets growth %, Annual YoY'],
            'capex_growth': ['Capital expenditures growth %, TTM YoY'],
            
            # Financial Ratios
            'current_ratio': ['Current ratio, Quarterly'],
            'debt_to_equity': ['Debt to equity ratio, Quarterly'],
            'debt_to_assets': ['Debt to assets ratio, Annual'],
            'cash_ratio': ['Cash ratio, Annual'],
            'assets_turnover': ['Assets turnover, Trailing 12 months'],
            'assets_to_equity': ['Assets to equity ratio, Annual'],
            'cash_to_debt_ratio': ['Cash to debt ratio, Annual'],
            
            # Performance Metrics
            'beta_5_years': ['Beta 5 years'],
            'performance_ytd': ['Performance % Year to date'],
            'volatility_1_day': ['Volatility 1 day'],
            'relative_volume': ['Relative Volume 1 day'],
            'volume_change': ['Volume Change % 1 day'],
            'average_volume_10d': ['Average Volume 10 days'],
            'turnover_1_day': ['Price * Volume (Turnover) 1 day'],
            
            # Analyst Data
            'analyst_rating': ['Analyst Rating'],
            'target_price_1y': ['Target price 1 year'],
            'target_performance_1y': ['Target price performance % 1 year'],
            
            # Dates
            'recent_earnings_date': ['Recent earnings date'],
            'upcoming_earnings_date': ['Upcoming earnings date'],
            'number_of_shareholders': ['Number of shareholders, Annual'],
            
            # Additional Identifiers
            'isin': ['International Securities Identification Number']
        }
        
        processed_count = 0
        batch_payload = []
        
        for index, row in df.iterrows():
            try:
                # Extract symbol (required)
                symbol = None
                for col in column_mappings['symbol']:
                    if col in df.columns and pd.notna(row[col]):
                        symbol = str(row[col]).strip()
                        break
                
                if not symbol:
                    logger.warning(f"No symbol found in row {index}")
                    continue
                
                # Initialize record
                record_data = {'symbol': symbol}
                
                # Extract all available financial data
                for field, possible_cols in column_mappings.items():
                    if field == 'symbol':  # Already processed
                        continue
                    
                    value = None
                    for col in possible_cols:
                        if col in df.columns and pd.notna(row[col]):
                            value = row[col]
                            break
                    
                    if value is not None:
                        # Convert to appropriate data type based on field
                        if field in ['market_capitalization', 'enterprise_value', 'total_revenue', 'gross_profit',
                                   'operating_income', 'net_income', 'ebitda', 'total_assets', 'total_current_assets',
                                   'total_current_liabilities', 'total_debt', 'total_equity', 'total_liabilities',
                                   'cash_and_equivalents', 'cash_short_term_investments', 'goodwill', 'net_debt',
                                   'free_cash_flow', 'operating_cash_flow', 'investing_cash_flow', 'financing_cash_flow',
                                   'capital_expenditures', 'dividends_paid_annual', 'turnover_1_day', 'target_price_1y']:
                            record_data[field] = safe_float_convert(value)
                        elif field in ['pe_ratio', 'price_to_book', 'price_to_sales', 'price_to_cash_flow',
                                     'price_to_free_cash_flow', 'ev_to_ebitda', 'ev_to_revenue', 'eps_diluted_ttm',
                                     'eps_basic_ttm', 'eps_growth_ttm', 'eps_estimate_quarterly', 'eps_reported_annual',
                                     'forward_pe', 'dividend_yield', 'dividend_yield_indicated', 'dividends_per_share',
                                     'dividend_payout_ratio', 'revenue_growth', 'net_income_growth', 'gross_profit_growth',
                                     'ebitda_growth', 'free_cash_flow_growth', 'total_assets_growth', 'capex_growth',
                                     'current_ratio', 'debt_to_equity', 'debt_to_assets', 'cash_ratio', 'assets_turnover',
                                     'assets_to_equity', 'cash_to_debt_ratio', 'beta_5_years', 'performance_ytd',
                                     'volatility_1_day', 'relative_volume', 'volume_change', 'target_performance_1y',
                                     'float_shares_percent']:
                            record_data[field] = safe_float_convert(value)
                        elif field in ['float_shares_outstanding', 'total_shares_outstanding', 'average_volume_10d',
                                     'number_of_shareholders', 'continuous_dividend_growth']:
                            record_data[field] = safe_int_convert(value)
                        else:
                            record_data[field] = safe_string_convert(value)
                
                # Add metadata
                record_data['updated_at'] = datetime.now(timezone.utc).isoformat()
                
                batch_payload.append(record_data)
                processed_count += 1
                
                # Process in batches
                if len(batch_payload) >= 50:
                    if upsert_financial_batch(batch_payload):
                        logger.info(f"Successfully processed batch of {len(batch_payload)} financial records")
                    batch_payload = []
                    
            except Exception as e:
                logger.error(f"Error processing row {index} for symbol {symbol if 'symbol' in locals() else 'unknown'}: {e}")
                continue
        
        # Process remaining records
        if batch_payload:
            if upsert_financial_batch(batch_payload):
                logger.info(f"Successfully processed final batch of {len(batch_payload)} financial records")
        
        logger.info(f"Financial data sync completed. Processed {processed_count} records")
        
    except Exception as e:
        logger.error(f"Error in process_financial_data: {e}")

def upsert_financial_batch(batch_data):
    """Insert/Update batch data to stocks_financials table"""
    try:
        if not batch_data:
            return True
        
        success_count = 0
        
        # Process each record individually using insert/update approach
        for record in batch_data:
            try:
                symbol = record['symbol']
                
                # Check if record exists
                existing = supabase.table("stocks_financials").select("symbol").eq("symbol", symbol).execute()
                
                if existing.data:
                    # Update existing record
                    supabase.table("stocks_financials").update(record).eq("symbol", symbol).execute()
                    logger.debug(f"Updated financial data for {symbol}")
                else:
                    # Insert new record
                    supabase.table("stocks_financials").insert(record).execute()
                    logger.debug(f"Inserted financial data for {symbol}")
                
                success_count += 1
                
            except Exception as record_error:
                logger.warning(f"Error processing {symbol}: {record_error}")
                continue
        
        logger.info(f"Successfully processed {success_count}/{len(batch_data)} financial records")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"Error processing financial batch data: {e}")
        return False

def create_financial_indices():
    """Create useful indices for financial data queries"""
    try:
        # This would create indices for better query performance
        # Note: This requires direct SQL execution which may need different approach
        indices_sql = [
            "CREATE INDEX IF NOT EXISTS idx_financials_sector ON stocks_financials(sector);",
            "CREATE INDEX IF NOT EXISTS idx_financials_pe_ratio ON stocks_financials(pe_ratio);",
            "CREATE INDEX IF NOT EXISTS idx_financials_market_cap ON stocks_financials(market_capitalization);",
            "CREATE INDEX IF NOT EXISTS idx_financials_dividend_yield ON stocks_financials(dividend_yield);"
        ]
        
        for sql in indices_sql:
            try:
                # Note: Supabase Python client doesn't support raw SQL execution
                # This would need to be done via database admin or SQL editor
                logger.info(f"Index creation SQL: {sql}")
            except Exception as e:
                logger.warning(f"Could not create index: {e}")
                
    except Exception as e:
        logger.error(f"Error creating financial indices: {e}")

def validate_financial_data():
    """Validate financial data integrity"""
    try:
        logger.info("Validating financial data...")
        
        # Count total records
        response = supabase.table("stocks_financials").select("symbol", count="exact").execute()
        total_count = response.count
        logger.info(f"Total financial records: {total_count}")
        
        # Check for records with essential data
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("market_capitalization", "is", "null").execute()
        market_cap_count = response.count
        logger.info(f"Records with market cap data: {market_cap_count}")
        
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("pe_ratio", "is", "null").execute()
        pe_count = response.count
        logger.info(f"Records with P/E ratio data: {pe_count}")
        
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("dividend_yield", "is", "null").execute()
        dividend_count = response.count
        logger.info(f"Records with dividend data: {dividend_count}")
        
        logger.info("Financial data validation completed")
        
    except Exception as e:
        logger.error(f"Error in financial data validation: {e}")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Load financial data')
    parser.add_argument('--validate', action='store_true', help='Validate data after loading')
    parser.add_argument('--create-indices', action='store_true', help='Create database indices')
    
    args = parser.parse_args()
    
    logger.info("Starting enhanced financial data sync...")
    
    try:
        process_financial_data()
        
        if args.validate:
            validate_financial_data()
            
        if args.create_indices:
            create_financial_indices()
            
        logger.info("Financial data sync completed successfully")
        
    except Exception as e:
        logger.error(f"Financial data sync failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
