#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def fix_rls_policies():
    """Fix RLS policies using direct PostgreSQL connection"""
    print("🔧 EGX Stock AI Oracle - RLS Policy Fixer")
    print("=" * 60)
    
    # Extract connection details from SUPABASE_URL
    supabase_url = os.getenv('SUPABASE_URL')
    service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not service_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        return False
    
    # Parse Supabase URL to get connection details
    # Format: https://project-ref.supabase.co
    project_ref = supabase_url.replace('https://', '').replace('.supabase.co', '')
    
    # Supabase connection details
    connection_params = {
        'host': f'aws-0-eu-central-1.pooler.supabase.com',
        'database': 'postgres',
        'user': f'postgres.{project_ref}',
        # We need to find the actual password
        'password': 'InternationalEgyptStock2025!',  # Try this first
        'port': 6543,
        'sslmode': 'require'
    }
    
    print(f"🔗 Connecting to: {connection_params['host']}")
    print(f"📊 Database: {connection_params['database']}")
    print(f"👤 User: {connection_params['user']}")
    
    try:
        # Try connection
        conn = psycopg2.connect(**connection_params)
        cursor = conn.cursor()
        print("✅ Connected to PostgreSQL database")
        
        # SQL commands to fix RLS policies
        sql_commands = [
            # Market indices
            'DROP POLICY IF EXISTS "Allow public read access to market_indices" ON public.market_indices;',
            'CREATE POLICY "Allow public read access to market_indices" ON public.market_indices FOR SELECT TO anon USING (true);',
            
            # Stocks realtime
            'DROP POLICY IF EXISTS "Allow public read access to stocks_realtime" ON public.stocks_realtime;',
            'CREATE POLICY "Allow public read access to stocks_realtime" ON public.stocks_realtime FOR SELECT TO anon USING (true);',
            
            # Stocks master
            'DROP POLICY IF EXISTS "Allow public read access to stocks_master" ON public.stocks_master;',
            'CREATE POLICY "Allow public read access to stocks_master" ON public.stocks_master FOR SELECT TO anon USING (true);',
            
            # Stocks historical
            'DROP POLICY IF EXISTS "Allow public read access to stocks_historical" ON public.stocks_historical;',
            'CREATE POLICY "Allow public read access to stocks_historical" ON public.stocks_historical FOR SELECT TO anon USING (true);',
            
            # Stocks financials
            'DROP POLICY IF EXISTS "Allow public read access to stocks_financials" ON public.stocks_financials;',
            'CREATE POLICY "Allow public read access to stocks_financials" ON public.stocks_financials FOR SELECT TO anon USING (true);',
            
            # Technical indicators
            'DROP POLICY IF EXISTS "Allow public read access to technical_indicators" ON public.technical_indicators;',
            'CREATE POLICY "Allow public read access to technical_indicators" ON public.technical_indicators FOR SELECT TO anon USING (true);',
            
            # Market news
            'DROP POLICY IF EXISTS "Allow public read access to market_news" ON public.market_news;',
            'CREATE POLICY "Allow public read access to market_news" ON public.market_news FOR SELECT TO anon USING (true);',
        ]
        
        success_count = 0
        for i, sql in enumerate(sql_commands, 1):
            try:
                cursor.execute(sql)
                conn.commit()
                print(f"  ✅ Command {i}/{len(sql_commands)}: Success")
                success_count += 1
            except Exception as e:
                print(f"  ❌ Command {i}/{len(sql_commands)}: Error - {str(e)}")
                conn.rollback()
        
        cursor.close()
        conn.close()
        
        print(f"\n📊 Results: {success_count}/{len(sql_commands)} policies updated successfully")
        
        if success_count == len(sql_commands):
            print("✅ All RLS policies updated successfully!")
            return True
        else:
            print("⚠️ Some RLS policies failed to update")
            return False
            
    except psycopg2.OperationalError as e:
        if "Wrong password" in str(e):
            print("❌ Connection failed: Wrong password")
            print("💡 We need the correct database password")
            print("🔍 Please check your Supabase project settings for the database password")
            return False
        else:
            print(f"❌ Connection failed: {str(e)}")
            return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_access_after_fix():
    """Test access with anon key after fixing policies"""
    print("\n🧪 Testing access with anon key after policy fix...")
    
    import requests
    
    supabase_url = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    tables_to_test = ['market_indices', 'stocks_realtime']
    
    for table in tables_to_test:
        response = requests.get(
            f"{supabase_url}/rest/v1/{table}?select=*&limit=1",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        if response.status_code == 200:
            data = response.json()
            if len(data) > 0:
                print(f"  ✅ {table}: {len(data)} records accessible")
            else:
                print(f"  ⚠️ {table}: Accessible but no data returned")
        else:
            print(f"  ❌ {table}: Error {response.status_code}")

if __name__ == "__main__":
    success = fix_rls_policies()
    
    if success:
        test_access_after_fix()
        print("\n🎉 RLS policies setup completed!")
        print("🌐 Frontend should now be able to access market data")
    else:
        print("\n💥 Setup failed!")
        print("🔧 Alternative solution: Disable RLS temporarily")
        print("   You can disable RLS on tables if needed:")
        print("   ALTER TABLE market_indices DISABLE ROW LEVEL SECURITY;")
