#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 مدير الاستيراد الرئيسي - كنز البيانات المصرية
Master Data Import Manager - Egyptian Stock Market Data Treasure

📅 Created: 2025-06-16
🎯 Purpose: تنسيق عمليات الاستيراد الكاملة للبيانات التاريخية والحية والمالية
📊 Data: 642,000+ historical records + realtime + financial data
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from pathlib import Path
import json
from typing import Dict, List
import subprocess
import time

# إضافة مسار scripts إلى PYTHONPATH
sys.path.append(str(Path(__file__).parent))

try:
    from historical_data_importer import EGXHistoricalDataImporter
    from realtime_data_importer import EGXRealtimeDataImporter
    from financial_data_importer import EGXFinancialDataImporter
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    print("تأكد من تثبيت المكتبات المطلوبة: pip install pandas psycopg2-binary openpyxl")

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('master_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EGXMasterDataImporter:
    """مدير الاستيراد الرئيسي للأسهم المصرية"""
    
    def __init__(self, db_config: Dict[str, str]):
        """تهيئة مدير الاستيراد"""
        self.db_config = db_config
        
        # إحصائيات شاملة
        self.master_stats = {
            'start_time': None,
            'end_time': None,
            'total_duration': 0,
            'historical': {
                'status': 'PENDING',
                'records': 0,
                'duration': 0,
                'error': None
            },
            'realtime': {
                'status': 'PENDING',
                'records': 0,
                'duration': 0,
                'error': None
            },
            'financial': {
                'status': 'PENDING',
                'records': 0,
                'duration': 0,
                'error': None
            },
            'overall_status': 'PENDING'
        }
        
        # مسارات البيانات للتحقق
        self.data_paths = {
            'meta2': Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2"),
            'excel': Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx"),
            'csv': Path("/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv")
        }
    
    def check_prerequisites(self) -> bool:
        """فحص المتطلبات المسبقة"""
        logger.info("🔍 فحص المتطلبات المسبقة...")
        
        # فحص وجود ملفات البيانات
        missing_data = []
        
        if not self.data_paths['meta2'].exists():
            missing_data.append("مجلد meta2 (البيانات التاريخية)")
        else:
            txt_files = list(self.data_paths['meta2'].glob("*.TXT"))
            if len(txt_files) == 0:
                missing_data.append("ملفات TXT في مجلد meta2")
            else:
                logger.info(f"✅ تم العثور على {len(txt_files)} ملف TXT")
        
        if not self.data_paths['excel'].exists():
            missing_data.append("ملف stock_synco.xlsx")
        else:
            size_mb = self.data_paths['excel'].stat().st_size / (1024*1024)
            logger.info(f"✅ ملف Excel موجود ({size_mb:.1f} MB)")
        
        if not self.data_paths['csv'].exists():
            missing_data.append("ملف financial_data.csv")
        else:
            size_mb = self.data_paths['csv'].stat().st_size / (1024*1024)
            logger.info(f"✅ ملف CSV موجود ({size_mb:.1f} MB)")
        
        if missing_data:
            logger.error("❌ ملفات البيانات المفقودة:")
            for missing in missing_data:
                logger.error(f"  - {missing}")
            return False
        
        # فحص الاتصال بقاعدة البيانات
        try:
            import psycopg2
            conn = psycopg2.connect(**self.db_config)
            conn.close()
            logger.info("✅ الاتصال بقاعدة البيانات ناجح")
        except Exception as e:
            logger.error(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return False
        
        # فحص المكتبات المطلوبة
        required_packages = ['pandas', 'psycopg2', 'openpyxl', 'numpy']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error("❌ المكتبات المفقودة:")
            for package in missing_packages:
                logger.error(f"  - {package}")
            logger.info("تثبيت المكتبات: pip install " + " ".join(missing_packages))
            return False
        
        logger.info("✅ جميع المتطلبات المسبقة متوفرة")
        return True
    
    def backup_existing_data(self) -> bool:
        """نسخ احتياطي للبيانات الموجودة"""
        try:
            logger.info("💾 إنشاء نسخة احتياطية للبيانات الموجودة...")
            
            backup_dir = Path("backups") / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # نسخ احتياطي لجداول قاعدة البيانات
            backup_tables = [
                'stocks_master', 'stocks_historical', 'stocks_realtime', 
                'stocks_financials', 'data_sync_log'
            ]
            
            for table in backup_tables:
                backup_file = backup_dir / f"{table}_backup.sql"
                cmd = [
                    'pg_dump',
                    f"--host={self.db_config['host']}",
                    f"--port={self.db_config['port']}",
                    f"--username={self.db_config['user']}",
                    f"--dbname={self.db_config['database']}",
                    f"--table={table}",
                    '--data-only',
                    '--file', str(backup_file)
                ]
                
                try:
                    subprocess.run(cmd, check=True, capture_output=True)
                    logger.info(f"✅ نسخ احتياطي لجدول {table}")
                except subprocess.CalledProcessError:
                    logger.warning(f"⚠️ لم يتم إنشاء نسخة احتياطية لجدول {table} (قد يكون فارغاً)")
            
            logger.info(f"💾 تم إنشاء النسخة الاحتياطية في: {backup_dir}")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def import_historical_data(self) -> bool:
        """استيراد البيانات التاريخية"""
        logger.info("📈 بدء استيراد البيانات التاريخية...")
        start_time = datetime.now()
        
        try:
            importer = EGXHistoricalDataImporter(self.db_config)
            stats = importer.import_all_historical_data()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.master_stats['historical'] = {
                'status': 'SUCCESS' if stats['inserted_records'] > 0 else 'FAILED',
                'records': stats['inserted_records'],
                'duration': duration,
                'error': None,
                'details': stats
            }
            
            if stats['inserted_records'] > 0:
                logger.info(f"✅ تم استيراد {stats['inserted_records']:,} سجل تاريخي")
                return True
            else:
                logger.error("❌ فشل في استيراد البيانات التاريخية")
                return False
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ خطأ في استيراد البيانات التاريخية: {error_msg}")
            
            self.master_stats['historical']['status'] = 'FAILED'
            self.master_stats['historical']['error'] = error_msg
            return False
    
    def import_realtime_data(self) -> bool:
        """استيراد البيانات الحية"""
        logger.info("📊 بدء استيراد البيانات الحية...")
        start_time = datetime.now()
        
        try:
            importer = EGXRealtimeDataImporter(self.db_config)
            stats = importer.import_realtime_data()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            processed_records = stats['inserted_records'] + stats['updated_records']
            
            self.master_stats['realtime'] = {
                'status': 'SUCCESS' if processed_records > 0 else 'FAILED',
                'records': processed_records,
                'duration': duration,
                'error': None,
                'details': stats
            }
            
            if processed_records > 0:
                logger.info(f"✅ تم معالجة {processed_records} سجل من البيانات الحية")
                return True
            else:
                logger.error("❌ فشل في استيراد البيانات الحية")
                return False
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ خطأ في استيراد البيانات الحية: {error_msg}")
            
            self.master_stats['realtime']['status'] = 'FAILED'
            self.master_stats['realtime']['error'] = error_msg
            return False
    
    def import_financial_data(self) -> bool:
        """استيراد البيانات المالية"""
        logger.info("💰 بدء استيراد البيانات المالية...")
        start_time = datetime.now()
        
        try:
            importer = EGXFinancialDataImporter(self.db_config)
            stats = importer.import_financial_data()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            processed_records = stats['inserted_records'] + stats['updated_records']
            
            self.master_stats['financial'] = {
                'status': 'SUCCESS' if processed_records > 0 else 'FAILED',
                'records': processed_records,
                'duration': duration,
                'error': None,
                'details': stats
            }
            
            if processed_records > 0:
                logger.info(f"✅ تم معالجة {processed_records} سجل من البيانات المالية")
                return True
            else:
                logger.error("❌ فشل في استيراد البيانات المالية")
                return False
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ خطأ في استيراد البيانات المالية: {error_msg}")
            
            self.master_stats['financial']['status'] = 'FAILED'
            self.master_stats['financial']['error'] = error_msg
            return False
    
    def generate_final_report(self) -> str:
        """إنشاء التقرير النهائي"""
        report = []
        report.append("=" * 100)
        report.append("🎯 تقرير الاستيراد الشامل - كنز البيانات المصرية")
        report.append("=" * 100)
        
        # معلومات عامة
        report.append(f"🕐 وقت البدء: {self.master_stats['start_time']}")
        report.append(f"🕐 وقت الانتهاء: {self.master_stats['end_time']}")
        report.append(f"⏱️ المدة الإجمالية: {self.master_stats['total_duration']:.1f} ثانية")
        report.append("")
        
        # تفاصيل كل نوع بيانات
        sections = [
            ('📈 البيانات التاريخية', 'historical'),
            ('📊 البيانات الحية', 'realtime'),
            ('💰 البيانات المالية', 'financial')
        ]
        
        total_records = 0
        successful_sections = 0
        
        for title, key in sections:
            section_data = self.master_stats[key]
            status_icon = "✅" if section_data['status'] == 'SUCCESS' else "❌"
            
            report.append(f"{title}:")
            report.append(f"  {status_icon} الحالة: {section_data['status']}")
            report.append(f"  📊 السجلات: {section_data['records']:,}")
            report.append(f"  ⏱️ المدة: {section_data['duration']:.1f} ثانية")
            
            if section_data['error']:
                report.append(f"  ❌ الخطأ: {section_data['error']}")
            
            if section_data['status'] == 'SUCCESS':
                successful_sections += 1
                total_records += section_data['records']
            
            report.append("")
        
        # ملخص الإنجازات
        report.append("📋 ملخص الإنجازات:")
        report.append(f"  🎯 الأقسام الناجحة: {successful_sections}/3")
        report.append(f"  📊 إجمالي السجلات المعالجة: {total_records:,}")
        
        if total_records > 0:
            speed = total_records / self.master_stats['total_duration']
            report.append(f"  🚀 متوسط السرعة: {speed:.0f} سجل/ثانية")
        
        # تحديد الحالة العامة
        if successful_sections == 3:
            self.master_stats['overall_status'] = 'SUCCESS'
            report.append("  🎉 حالة المشروع: نجح بالكامل")
        elif successful_sections > 0:
            self.master_stats['overall_status'] = 'PARTIAL'
            report.append("  ⚠️ حالة المشروع: نجح جزئياً")
        else:
            self.master_stats['overall_status'] = 'FAILED'
            report.append("  ❌ حالة المشروع: فشل")
        
        report.append("=" * 100)
        
        return "\n".join(report)
    
    def save_report_to_file(self, report: str):
        """حفظ التقرير في ملف"""
        try:
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = reports_dir / f"import_report_{timestamp}.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            # حفظ الإحصائيات كـ JSON
            stats_file = reports_dir / f"import_stats_{timestamp}.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.master_stats, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"📄 تم حفظ التقرير في: {report_file}")
            logger.info(f"📊 تم حفظ الإحصائيات في: {stats_file}")
            
        except Exception as e:
            logger.error(f"❌ فشل في حفظ التقرير: {e}")
    
    def run_full_import(self) -> bool:
        """تشغيل عملية الاستيراد الكاملة"""
        self.master_stats['start_time'] = datetime.now()
        
        logger.info("🚀 بدء عملية الاستيراد الشاملة للبيانات المصرية")
        logger.info("💎 الهدف: استيراد 642,000+ سجل تاريخي + بيانات حية + بيانات مالية")
        
        # فحص المتطلبات المسبقة
        if not self.check_prerequisites():
            logger.error("❌ فشل فحص المتطلبات المسبقة")
            return False
        
        # نسخ احتياطي (اختياري)
        self.backup_existing_data()
        
        # تنفيذ عمليات الاستيراد بالتسلسل
        operations = [
            ("البيانات التاريخية", self.import_historical_data),
            ("البيانات الحية", self.import_realtime_data),
            ("البيانات المالية", self.import_financial_data)
        ]
        
        for operation_name, operation_func in operations:
            logger.info(f"▶️ بدء معالجة {operation_name}...")
            
            try:
                success = operation_func()
                if success:
                    logger.info(f"✅ اكتملت معالجة {operation_name} بنجاح")
                else:
                    logger.error(f"❌ فشلت معالجة {operation_name}")
            except Exception as e:
                logger.error(f"❌ خطأ في معالجة {operation_name}: {e}")
            
            # فترة راحة قصيرة بين العمليات
            time.sleep(2)
        
        # إنهاء وإنشاء التقرير
        self.master_stats['end_time'] = datetime.now()
        self.master_stats['total_duration'] = (
            self.master_stats['end_time'] - self.master_stats['start_time']
        ).total_seconds()
        
        # إنشاء التقرير النهائي
        final_report = self.generate_final_report()
        print(final_report)
        
        # حفظ التقرير
        self.save_report_to_file(final_report)
        
        return self.master_stats['overall_status'] in ['SUCCESS', 'PARTIAL']

def main():
    """الدالة الرئيسية"""
    print("🎯 مدير الاستيراد الشامل - كنز البيانات المصرية")
    print("=" * 80)    # إعدادات قاعدة البيانات
    db_config = {
        'host': 'localhost',
        'database': 'egx_stock_oracle',
        'user': 'postgres',  # Using postgres user directly
        'password': '',  # No password for local PostgreSQL
        'port': 5432
    }
    
    # إنشاء مدير الاستيراد
    master_importer = EGXMasterDataImporter(db_config)
    
    # تشغيل عملية الاستيراد الكاملة
    success = master_importer.run_full_import()
    
    if success:
        print("\n🎉 اكتملت عملية الاستيراد الشاملة بنجاح!")
        print("💎 تم إنشاء نظام قاعدة البيانات المخصصة للأسهم المصرية")
    else:
        print("\n⚠️ اكتملت عملية الاستيراد مع بعض المشاكل")
        print("📋 راجع التقرير للتفاصيل")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
