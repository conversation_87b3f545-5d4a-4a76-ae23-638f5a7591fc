# 🐍 Python Dependencies - Egyptian Stock Market Data Treasure
# متطلبات Python - كنز البيانات المصرية

# Database Connection
psycopg2-binary==2.9.9
psycopg2==2.9.9

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Excel File Processing
openpyxl>=3.1.0
xlrd>=2.0.0

# Date and Time Utilities
python-dateutil>=2.8.0

# Logging and Configuration
python-dotenv>=1.0.0

# Performance and Monitoring (Optional)
tqdm>=4.65.0
psutil>=5.9.0

# Development and Testing (Optional)
pytest>=7.4.0
jupyter>=1.0.0

# API Development (for future use)
fastapi>=0.100.0
uvicorn>=0.23.0
pydantic>=2.0.0

# Data Validation
jsonschema>=4.17.0

# Async Processing (for future optimizations)
asyncio
asyncpg>=0.28.0

# File System Operations
pathlib2>=2.3.7

# Mathematical Operations
scipy>=1.10.0

# Visualization (for data analysis)
matplotlib>=3.7.0
plotly>=5.15.0

# Progress Bars and CLI
click>=8.1.0
rich>=13.4.0
