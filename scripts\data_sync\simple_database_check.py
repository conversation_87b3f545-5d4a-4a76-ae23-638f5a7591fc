#!/usr/bin/env python3
"""
أداة فحص سريعة لقاعدة البيانات - نسخة مبسطة
"""

import os
import sys
from datetime import datetime
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_SERVICE_ROLE_KEY:
    print("❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def quick_database_check():
    """فحص سريع لحالة قاعدة البيانات"""
    
    print("🔍 **فحص سريع لقاعدة البيانات**")
    print("=" * 50)
    
    try:
        # اختبار الاتصال وجلب إحصائيات أساسية
        stocks_count = supabase.from('stocks_realtime').select('*', count='exact').execute()
        print(f"✅ تم الاتصال بقاعدة البيانات بنجاح")
        print(f"📊 عدد الأسهم في stocks_realtime: {stocks_count.count}")
        
        # جلب عينة من البيانات
        sample = supabase.from('stocks_realtime').select('''
            symbol, current_price, change_percent, volume,
            ma5, ma20, target_1, pe_ratio, dividend_yield,
            liquidity_ratio, market_cap
        ''').limit(3).execute()
        
        if sample.data:
            print(f"\n📋 **عينة من البيانات المتاحة:**")
            for i, stock in enumerate(sample.data, 1):
                print(f"\n{i}. 🏢 السهم: {stock.get('symbol', 'N/A')}")
                print(f"   💰 السعر: {stock.get('current_price', 'N/A')}")
                print(f"   📊 التغير%: {stock.get('change_percent', 'N/A')}")
                print(f"   📈 MA5: {stock.get('ma5', 'N/A')}")
                print(f"   📈 MA20: {stock.get('ma20', 'N/A')}")
                print(f"   🎯 هدف1: {stock.get('target_1', 'N/A')}")
                print(f"   💹 P/E: {stock.get('pe_ratio', 'N/A')}")
                print(f"   💧 سيولة: {stock.get('liquidity_ratio', 'N/A')}")
        
        # فحص البيانات المفقودة
        print(f"\n🕳️ **فحص البيانات المفقودة:**")
        
        # استعلام مبسط للعد
        total_query = supabase.from('stocks_realtime').select('symbol', count='exact').execute()
        total = total_query.count
        
        # فحص كل حقل على حدة
        fields_to_check = [
            ('ma5', 'MA5'),
            ('ma20', 'MA20'), 
            ('target_1', 'الهدف الأول'),
            ('pe_ratio', 'نسبة P/E'),
            ('dividend_yield', 'عائد الأرباح'),
            ('liquidity_ratio', 'نسبة السيولة'),
            ('market_cap', 'القيمة السوقية')
        ]
        
        for field, name in fields_to_check:
            try:
                result = supabase.from('stocks_realtime').select(field, count='exact').not(field, 'is', None).execute()
                count = result.count
                percentage = (count / total * 100) if total > 0 else 0
                print(f"   📈 {name}: {count}/{total} ({percentage:.1f}%)")
            except Exception as e:
                print(f"   ❌ خطأ في فحص {name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def check_computed_data():
    """فحص البيانات المحسوبة المتاحة"""
    
    print(f"\n🔄 **فحص البيانات المحسوبة:**")
    print("=" * 50)
    
    try:
        # فحص عينة صغيرة للتحقق من الحسابات
        stocks = supabase.from('stocks_realtime').select('''
            symbol, current_price, previous_close, change_amount, change_percent
        ''').not('change_percent', 'is', None).limit(5).execute()
        
        if stocks.data:
            print("🧮 **التحقق من دقة الحسابات:**")
            
            for stock in stocks.data:
                symbol = stock.get('symbol', 'N/A')
                current = stock.get('current_price')
                previous = stock.get('previous_close') 
                db_change = stock.get('change_percent')
                
                if current and previous and db_change is not None:
                    # حساب التغير المتوقع
                    calculated_change = ((current - previous) / previous) * 100
                    difference = abs(calculated_change - db_change)
                    
                    status = "✅" if difference < 0.1 else "⚠️"
                    print(f"   {status} {symbol}: DB={db_change:.2f}% | حساب={calculated_change:.2f}% | فرق={difference:.2f}")
        
        # حساب إحصائيات سريعة
        print(f"\n📊 **إحصائيات سريعة:**")
        
        # عد الأسهم الصاعدة والهابطة
        gainers = supabase.from('stocks_realtime').select('*', count='exact').gt('change_percent', 0).execute()
        losers = supabase.from('stocks_realtime').select('*', count='exact').lt('change_percent', 0).execute()
        unchanged = supabase.from('stocks_realtime').select('*', count='exact').eq('change_percent', 0).execute()
        
        print(f"   📈 أسهم صاعدة: {gainers.count}")
        print(f"   📉 أسهم هابطة: {losers.count}")  
        print(f"   ➡️ أسهم ثابتة: {unchanged.count}")
        
        # التحقق من المؤشرات الفنية
        ma_stocks = supabase.from('stocks_realtime').select('*', count='exact').not('ma5', 'is', None).not('ma20', 'is', None).execute()
        print(f"   📊 أسهم لها مؤشرات فنية: {ma_stocks.count}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات المحسوبة: {e}")

def main():
    """الوظيفة الرئيسية"""
    
    print("🔍 **فحص سريع لقاعدة البيانات - EGX Stock AI Oracle**")
    print("=" * 70)
    print(f"⏰ وقت الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # فحص أساسي
    if quick_database_check():
        # فحص البيانات المحسوبة
        check_computed_data()
        
        print(f"\n✅ **اكتمل الفحص بنجاح!**")
        print("📋 **النتائج:**")
        print("   - قاعدة البيانات متاحة ومتصلة")
        print("   - البيانات الأساسية موجودة")
        print("   - تم التحقق من عينة من الحسابات")
        
        print(f"\n🎯 **الخطوة التالية:**")
        print("   - مراجعة النتائج أعلاه")
        print("   - تحديد البيانات المفقودة")
        print("   - تحسين استعلامات التطبيق")
        
    else:
        print(f"\n❌ **فشل الفحص!**")
        print("🔧 تأكد من:")
        print("   - متغيرات البيئة (.env)")
        print("   - حالة اتصال الإنترنت")
        print("   - حالة خادم Supabase")

if __name__ == "__main__":
    main()
