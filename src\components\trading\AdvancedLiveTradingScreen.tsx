import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Search, 
  RefreshCw,
  BarChart3,
  Volume2,
  Loader2,
  Zap,
  AlertTriangle,
  Clock,
  Star
} from 'lucide-react';
import { useEnhancedLiveData, EnhancedStockData } from '@/hooks/useEnhancedLiveData';
import { cn } from '@/lib/utils';

interface AdvancedLiveTradingScreenProps {
  className?: string;
}

const AdvancedLiveTradingScreen: React.FC<AdvancedLiveTradingScreenProps> = ({ className }) => {
  const { stocks, marketStats, isLoading, error, refetch, lastUpdate } = useEnhancedLiveData();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'change_percent' | 'volume' | 'turnover' | 'symbol'>('change_percent');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'all' | 'gainers' | 'losers' | 'active' | 'updated'>('all');
  const [showFlashEffects, setShowFlashEffects] = useState(true);

  // تصفية وترتيب البيانات
  const filteredStocks = useMemo(() => {
    let filtered = stocks || [];

    // تصفية حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(stock => 
        stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (stock.name && stock.name.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // تصفية حسب نوع العرض
    switch (viewMode) {
      case 'gainers':
        filtered = filtered.filter(stock => stock.change_percent > 0);
        break;
      case 'losers':
        filtered = filtered.filter(stock => stock.change_percent < 0);
        break;
      case 'active':
        filtered = filtered.filter(stock => (stock.turnover || 0) > 100000);
        break;
      case 'updated':
        filtered = filtered.filter(stock => stock.isNewUpdate);
        break;
    }

    // ترتيب البيانات
    filtered.sort((a, b) => {
      const multiplier = sortOrder === 'desc' ? -1 : 1;
      
      switch (sortBy) {
        case 'change_percent':
          return (a.change_percent - b.change_percent) * multiplier;
        case 'volume':
          return ((a.volume || 0) - (b.volume || 0)) * multiplier;
        case 'turnover':
          return ((a.turnover || 0) - (b.turnover || 0)) * multiplier;
        case 'symbol':
          return a.symbol.localeCompare(b.symbol) * multiplier;
        default:
          return 0;
      }
    });

    return filtered;
  }, [stocks, searchTerm, sortBy, sortOrder, viewMode]);

  // تنسيق الأرقام
  const formatNumber = (num: number) => {
    if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toLocaleString();
  };

  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  const formatChange = (change: number) => {
    return change >= 0 ? `+${change.toFixed(2)}` : change.toFixed(2);
  };

  // مكون صف الأسهم مع التأثيرات البصرية
  const StockRow: React.FC<{ stock: EnhancedStockData }> = ({ stock }) => {
    const [isFlashing, setIsFlashing] = useState(false);    // تأثير الوميض عند التحديث
    useEffect(() => {
      if (stock.isNewUpdate && stock.flashColor !== 'none' && showFlashEffects) {
        setIsFlashing(true);
        const timer = setTimeout(() => setIsFlashing(false), 2000);
        return () => clearTimeout(timer);
      }
    }, [stock.isNewUpdate, stock.flashColor]); // إزالة showFlashEffects من dependencies

    const changeColor = stock.change_percent > 0 ? 'text-green-600' :
                       stock.change_percent < 0 ? 'text-red-600' : 'text-gray-600';
    
    const changeIcon = stock.change_percent > 0 ? <TrendingUp className="w-4 h-4" /> :
                      stock.change_percent < 0 ? <TrendingDown className="w-4 h-4" /> : null;

    // كلاسات التأثير البصري
    const flashColorClass = isFlashing ? (
      stock.flashColor === 'green' ? 'bg-green-100 border-green-300' :
      stock.flashColor === 'red' ? 'bg-red-100 border-red-300' : ''
    ) : '';

    const highlightClass = stock.isHighlighted ? 'ring-2 ring-blue-200' : '';

    return (
      <div className={cn(
        "grid grid-cols-7 gap-4 p-3 border-b hover:bg-gray-50 transition-all duration-500",
        flashColorClass,
        highlightClass
      )}>
        {/* الرمز والاسم */}
        <div className="flex flex-col">
          <div className="flex items-center gap-1">
            <span className="font-semibold text-gray-900">{stock.symbol}</span>
            {stock.isNewUpdate && (
              <Zap className="w-3 h-3 text-yellow-500 animate-pulse" />
            )}
          </div>
          {stock.name && (
            <span className="text-sm text-gray-500 truncate">{stock.name}</span>
          )}
        </div>

        {/* السعر الحالي */}
        <div className="flex flex-col">
          <span className={cn("font-semibold text-lg", changeColor)}>
            {formatPrice(stock.current_price)} ج.م
          </span>
          {stock.previous_close && (
            <span className="text-xs text-gray-400">
              الإغلاق: {formatPrice(stock.previous_close)}
            </span>
          )}
        </div>

        {/* التغير */}
        <div className={cn("flex flex-col", changeColor)}>
          <div className="flex items-center gap-1">
            {changeIcon}
            <span className="font-medium">
              {stock.change_percent.toFixed(2)}%
            </span>
          </div>
          {stock.change_amount && (
            <span className="text-xs">
              {formatChange(stock.change_amount)} ج.م
            </span>
          )}
        </div>

        {/* الحجم */}
        <div className="flex flex-col">
          <div className="flex items-center gap-1">
            <Volume2 className={cn("w-4 h-4", 
              stock.volumeChange === 'up' ? 'text-green-500' :
              stock.volumeChange === 'down' ? 'text-red-500' : 'text-gray-400'
            )} />
            <span>{formatNumber(stock.volume || 0)}</span>
          </div>
          {stock.volumeChange !== 'neutral' && (
            <span className={cn("text-xs", 
              stock.volumeChange === 'up' ? 'text-green-500' : 'text-red-500'
            )}>
              {stock.volumeChange === 'up' ? '↗ نشط' : '↘ هادئ'}
            </span>
          )}
        </div>

        {/* القيمة المتداولة */}
        <div className="flex items-center gap-1">
          <BarChart3 className="w-4 h-4 text-gray-400" />
          <span>{formatNumber(stock.turnover || 0)}</span>
        </div>

        {/* أعلى وأقل */}
        <div className="flex flex-col text-xs">
          {stock.high_price && (
            <span className="text-green-600">
              ↑ {formatPrice(stock.high_price)}
            </span>
          )}
          {stock.low_price && (
            <span className="text-red-600">
              ↓ {formatPrice(stock.low_price)}
            </span>
          )}
        </div>

        {/* وقت التحديث */}
        <div className="flex flex-col text-sm text-gray-500">
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span>{new Date(stock.updated_at).toLocaleTimeString('ar-EG')}</span>
          </div>
          {stock.isNewUpdate && (
            <Badge variant="secondary" className="mt-1 text-xs">
              <Zap className="w-2 h-2 mr-1" />
              محدث
            </Badge>
          )}
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="w-8 h-8 mx-auto mb-2" />
            خطأ في تحميل البيانات: {error.message}
            <Button onClick={() => refetch()} className="mt-2">
              <RefreshCw className="w-4 h-4 mr-2" />
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* إحصائيات السوق المحسنة */}
      {marketStats && (
        <div className="space-y-4">
          {/* الإحصائيات الرئيسية */}
          <div className="grid grid-cols-2 lg:grid-cols-6 gap-4">
            <Card className="relative overflow-hidden">
              <CardContent className="p-4 text-center">
                <div className="absolute top-0 right-0 w-2 h-full bg-blue-500"></div>
                <div className="text-2xl font-bold text-blue-600">{marketStats.totalStocks}</div>
                <div className="text-sm text-gray-500">إجمالي الأسهم</div>
              </CardContent>
            </Card>
            
            <Card className="relative overflow-hidden">
              <CardContent className="p-4 text-center">
                <div className="absolute top-0 right-0 w-2 h-full bg-green-500"></div>
                <div className="text-2xl font-bold text-green-600">{marketStats.gainers}</div>
                <div className="text-sm text-gray-500">مرتفعة</div>
              </CardContent>
            </Card>
            
            <Card className="relative overflow-hidden">
              <CardContent className="p-4 text-center">
                <div className="absolute top-0 right-0 w-2 h-full bg-red-500"></div>
                <div className="text-2xl font-bold text-red-600">{marketStats.losers}</div>
                <div className="text-sm text-gray-500">منخفضة</div>
              </CardContent>
            </Card>
            
            <Card className="relative overflow-hidden">
              <CardContent className="p-4 text-center">
                <div className="absolute top-0 right-0 w-2 h-full bg-gray-500"></div>
                <div className="text-2xl font-bold text-gray-600">{marketStats.unchanged}</div>
                <div className="text-sm text-gray-500">ثابتة</div>
              </CardContent>
            </Card>
            
            <Card className="relative overflow-hidden">
              <CardContent className="p-4 text-center">
                <div className="absolute top-0 right-0 w-2 h-full bg-purple-500"></div>
                <div className="text-xl font-bold text-purple-600">
                  {formatNumber(marketStats.totalVolume)}
                </div>
                <div className="text-sm text-gray-500">إجمالي الحجم</div>
              </CardContent>
            </Card>
            
            <Card className="relative overflow-hidden">
              <CardContent className="p-4 text-center">
                <div className="absolute top-0 right-0 w-2 h-full bg-orange-500"></div>
                <div className="text-xl font-bold text-orange-600">
                  {formatNumber(marketStats.totalTurnover)}
                </div>
                <div className="text-sm text-gray-500">القيمة المتداولة</div>
              </CardContent>
            </Card>
          </div>

          {/* أبرز الأسهم */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {marketStats.topGainer && (
              <Card className="border-green-200 bg-green-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Star className="w-4 h-4 text-green-600" />
                    <span className="font-medium text-green-800">الأكثر ارتفاعاً</span>
                  </div>
                  <div className="font-bold text-lg">{marketStats.topGainer.symbol}</div>
                  <div className="text-green-600 font-medium">
                    +{marketStats.topGainer.change_percent.toFixed(2)}%
                  </div>
                </CardContent>
              </Card>
            )}

            {marketStats.topLoser && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingDown className="w-4 h-4 text-red-600" />
                    <span className="font-medium text-red-800">الأكثر انخفاضاً</span>
                  </div>
                  <div className="font-bold text-lg">{marketStats.topLoser.symbol}</div>
                  <div className="text-red-600 font-medium">
                    {marketStats.topLoser.change_percent.toFixed(2)}%
                  </div>
                </CardContent>
              </Card>
            )}

            {marketStats.mostActiveStock && (
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Volume2 className="w-4 h-4 text-blue-600" />
                    <span className="font-medium text-blue-800">الأكثر نشاطاً</span>
                  </div>
                  <div className="font-bold text-lg">{marketStats.mostActiveStock.symbol}</div>
                  <div className="text-blue-600 font-medium">
                    {formatNumber(marketStats.mostActiveStock.turnover || 0)} ج.م
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* أدوات التحكم المحسنة */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-600 animate-pulse" />
              الشاشة اللحظية المتقدمة
              {lastUpdate && (
                <Badge variant="outline" className="ml-2">
                  آخر تحديث: {lastUpdate}
                </Badge>
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                onClick={() => setShowFlashEffects(!showFlashEffects)}
                variant={showFlashEffects ? "default" : "outline"}
                size="sm"
              >
                <Zap className="w-4 h-4 mr-1" />
                التأثيرات البصرية
              </Button>
              <Button
                onClick={() => refetch()}
                disabled={isLoading}
                variant="outline"
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                تحديث
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* شريط البحث */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="البحث عن سهم..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* أزرار التصفية المحسنة */}
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'الكل', icon: Activity },
              { key: 'gainers', label: 'المرتفعة', icon: TrendingUp },
              { key: 'losers', label: 'المنخفضة', icon: TrendingDown },
              { key: 'active', label: 'الأكثر نشاطاً', icon: Volume2 },
              { key: 'updated', label: 'المحدثة حديثاً', icon: Zap }
            ].map(mode => {
              const Icon = mode.icon;
              return (
                <Button
                  key={mode.key}
                  variant={viewMode === mode.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode(mode.key as 'all' | 'gainers' | 'losers' | 'active' | 'updated')}
                >
                  <Icon className="w-4 h-4 mr-1" />
                  {mode.label}
                </Button>
              );
            })}
          </div>

          {/* أزرار الترتيب */}
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'change_percent', label: 'نسبة التغير' },
              { key: 'volume', label: 'الحجم' },
              { key: 'turnover', label: 'القيمة المتداولة' },
              { key: 'symbol', label: 'الرمز' }
            ].map(sort => (
              <Button
                key={sort.key}
                variant={sortBy === sort.key ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  if (sortBy === sort.key) {
                    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
                  } else {
                    setSortBy(sort.key as 'change_percent' | 'volume' | 'turnover' | 'symbol');
                    setSortOrder('desc');
                  }
                }}
              >
                {sort.label}
                {sortBy === sort.key && (
                  <span className="ml-1">
                    {sortOrder === 'desc' ? '↓' : '↑'}
                  </span>
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* جدول البيانات المحسن */}
      <Card>
        <CardHeader>
          <div className="grid grid-cols-7 gap-4 text-sm font-medium text-gray-500">
            <div>السهم</div>
            <div>السعر</div>
            <div>التغير</div>
            <div>الحجم</div>
            <div>القيمة المتداولة</div>
            <div>أعلى/أقل</div>
            <div>آخر تحديث</div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                <span className="mr-2">جاري تحميل البيانات...</span>
              </div>
            ) : filteredStocks.length === 0 ? (
              <div className="text-center p-8 text-gray-500">
                لا توجد بيانات متاحة
              </div>
            ) : (
              <div className="space-y-0">
                {filteredStocks.map((stock) => (
                  <StockRow key={stock.symbol} stock={stock} />
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* معلومات إضافية */}
      <div className="flex justify-between items-center text-center text-sm text-gray-500">
        <span>عدد الأسهم المعروضة: {filteredStocks.length} من أصل {stocks?.length || 0}</span>
        {lastUpdate && (
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>آخر تحديث: {lastUpdate}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedLiveTradingScreen;
