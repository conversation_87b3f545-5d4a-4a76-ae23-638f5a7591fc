import React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2, TrendingUp, TrendingDown } from "lucide-react";
import StockAnalysisLink from "@/components/analysis/StockAnalysisLink";
import { useAnalysis } from "@/hooks/useAnalysis";

interface Props {
  alert: {
    id: string;
    symbol: string;
    targetPrice: number;
    currentPrice: number | null;
    condition: "above" | "below";
    isActive: boolean;
    createdAt: string;
  };
  onToggle: (id: string) => void;
  onDelete: (id: string) => void;
}

export const AlertRow: React.FC<Props> = ({ alert, onToggle, onDelete }) => {
  const { openAnalysis } = useAnalysis();

  return (
    <div
      className="flex items-center justify-between p-4 bg-white/80 rounded-lg border"
      key={alert.id}
    >
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          {alert.condition === "above" ? (
            <TrendingUp className="h-4 w-4 text-green-600" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-600" />
          )}
          <span className="font-bold text-lg">{alert.symbol}</span>
        </div>
        <div className="text-sm text-muted-foreground">
          {alert.condition === "above" ? "أعلى من" : "أقل من"} {alert.targetPrice} EGP
        </div>
        <div className="text-sm">
          السعر الحالي:{" "}
          {alert.currentPrice !== null && alert.currentPrice !== undefined
            ? alert.currentPrice.toFixed(2)
            : "-"}{" "}
          EGP
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Badge className={alert.isActive ? "bg-green-500" : "bg-gray-500"}>
          {alert.isActive ? "نشط" : "معطل"}
        </Badge>
        <Button variant="outline" size="sm" onClick={() => onToggle(alert.id)}>
          {alert.isActive ? "إيقاف" : "تفعيل"}
        </Button>
        <Button variant="destructive" size="sm" onClick={() => onDelete(alert.id)}>
          <Trash2 className="h-4 w-4" />
        </Button>
        <StockAnalysisLink 
          symbol={alert.symbol} 
          size="sm" 
          onAnalyze={openAnalysis}
          showText={false}
        />
      </div>
    </div>
  );
};
