import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, Activity, BarChart3, Globe, ExternalLink, Info } from 'lucide-react';

const EGX30Chart = () => {
  const [interval, setInterval] = useState<'1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1D' | '1W' | '1M'>('1D');
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [showAdvanced, setShowAdvanced] = useState(false);

  const intervals = [
    { value: '1D' as const, label: 'يومي' },
    { value: '1W' as const, label: 'أسبوعي' },
    { value: '1M' as const, label: 'شهري' },
    { value: '4h' as const, label: '4 ساعات' },
    { value: '1h' as const, label: 'ساعة' },
    { value: '30m' as const, label: '30 دقيقة' },
  ];

  return (
    <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-blue-800">
            <BarChart3 className="h-5 w-5" />
            مؤشر EGX30 - نظرة عامة على السوق المصرية
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-blue-100 text-blue-800">
              <Globe className="h-3 w-3 mr-1" />
              البورصة المصرية
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open('https://www.egx.com.eg/ar/indices.aspx', '_blank')}
              className="text-blue-600 hover:text-blue-800"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              EGX
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Market Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-gradient-to-r from-blue-100 to-blue-200 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
              <span className="font-bold text-blue-800">حجم التداول</span>
            </div>
            <div className="text-sm text-blue-700 text-center">
              إجمالي قيمة الأسهم المتداولة
            </div>
          </div>

          <div className="p-4 bg-gradient-to-r from-green-100 to-green-200 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Activity className="h-5 w-5 text-green-600 mr-2" />
              <span className="font-bold text-green-800">عدد الصفقات</span>
            </div>
            <div className="text-sm text-green-700 text-center">
              العدد الإجمالي للمعاملات المنفذة
            </div>
          </div>

          <div className="p-4 bg-gradient-to-r from-purple-100 to-purple-200 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-5 w-5 text-purple-600 mr-2" />
              <span className="font-bold text-purple-800">الاتجاه العام</span>
            </div>
            <div className="text-sm text-purple-700 text-center">
              اتجاه السوق العام للجلسة
            </div>
          </div>
        </div>

        {/* Interval Selection */}
        <div className="flex flex-wrap gap-2 justify-center">
          {intervals.map((int) => (
            <Button
              key={int.value}
              variant={interval === int.value ? "default" : "outline"}
              size="sm"
              onClick={() => setInterval(int.value)}
              className="text-xs"
            >
              {int.label}
            </Button>
          ))}
        </div>

        {/* Theme and Advanced Options */}
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            className="text-xs"
          >
            {theme === 'light' ? '🌙 ليلي' : '☀️ نهاري'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-xs"
          >
            <Info className="h-3 w-3 mr-1" />
            {showAdvanced ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
          </Button>  
        </div>        {/* TradingView Chart */}
        <div className="bg-white rounded-lg overflow-hidden shadow-sm">
          <div className="h-[500px]">
            <iframe
              src={`https://www.tradingview.com/embed-widget/advanced-chart/?symbol=EGX30&interval=${interval}&theme=${theme}&style=1&locale=ar&range=1M&timezone=Africa%2FCairo&hide_side_toolbar=false&save_image=true&calendar=true&hide_volume=false&support_host=https%3A%2F%2Fwww.tradingview.com`}
              width="100%"
              height="500"
              frameBorder="0"
              allowTransparency={true}
              scrolling="no"
              className="rounded-lg"
              title="TradingView EGX30 Chart"
            />
          </div>
        </div>

        {/* Advanced Information */}
        {showAdvanced && (
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200">
              <h4 className="font-bold text-indigo-800 mb-3 flex items-center">
                <Info className="h-4 w-4 mr-2" />
                معلومات متقدمة عن مؤشر EGX30
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">تعريف المؤشر:</h5>
                  <p className="text-indigo-600">
                    مؤشر EGX30 هو المؤشر الرئيسي للبورصة المصرية ويضم أكبر 30 شركة من حيث السيولة والنشاط.
                  </p>
                </div>
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">أهمية المؤشر:</h5>
                  <p className="text-indigo-600">
                    يُعتبر مرآة لأداء السوق المصرية ومؤشر رئيسي لاتجاهات الاستثمار.
                  </p>
                </div>
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">ساعات التداول:</h5>
                  <p className="text-indigo-600">
                    من 10:00 ص إلى 2:30 م (بتوقيت القاهرة) من الأحد إلى الخميس.
                  </p>
                </div>
                <div>
                  <h5 className="font-semibold text-indigo-700 mb-2">العملة:</h5>
                  <p className="text-indigo-600">
                    الجنيه المصري (EGP) - يتأثر بسعر صرف الدولار.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
          <h4 className="font-bold text-yellow-800 mb-2">إرشادات الاستخدام:</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• اختر الإطار الزمني المناسب من الأزرار أعلاه</li>
            <li>• استخدم أدوات الرسم والتحليل المدمجة في الشارت</li>
            <li>• يمكنك تغيير الثيم بين النهاري والليلي</li>
            <li>• الشارت يحتوي على مؤشرات فنية مثل RSI و MACD</li>
            <li>• الضغط على الشارت يفتح نافذة مكبرة للتحليل التفصيلي</li>
            <li>• اضغط على "عرض التفاصيل" لمعرفة المزيد عن المؤشر</li>
          </ul>
        </div>

        {/* Fallback Message */}
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="text-sm text-blue-800 text-center">
            <strong>ملاحظة:</strong> إذا لم يظهر الشارت، يمكنك زيارة 
            <a 
              href="https://www.tradingview.com/chart/?symbol=EGX30" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline mx-1"
            >
              TradingView مباشرة
            </a>
            أو 
            <a 
              href="https://www.egx.com.eg/ar/indices.aspx" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline mx-1"
            >
              موقع البورصة المصرية
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EGX30Chart;
