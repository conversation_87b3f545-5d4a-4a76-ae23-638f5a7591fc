import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// أنواع البيانات للـ Views
export interface ViewsMarketData {
  marketSummary: {
    total_stocks: number;
    active_stocks: number;
    gainers: number;
    losers: number;
    unchanged: number;
    avg_change: number;
    total_volume: number;
    total_turnover: number;
    last_updated: string;
  };
  topPerformers: {
    gainers: Array<{
      symbol: string;
      name: string;
      current_price: number;
      change_percent: number;
      volume: number;
    }>;
    losers: Array<{
      symbol: string;
      name: string;
      current_price: number;
      change_percent: number;
      volume: number;
    }>;
    mostActive: Array<{
      symbol: string;
      name: string;
      current_price: number;
      change_percent: number;
      volume: number;
    }>;
  };
  technicalSummary: {
    bullishStocks: number;
    bearishStocks: number;
    strongUptrend: number;
    stocksWithTargets: number;
  };
}

export function useViewsMarketData() {
  return useQuery<ViewsMarketData>({
    queryKey: ['views-market-data'],
    queryFn: async () => {
      console.log('🔍 جاري جلب البيانات من Views المحسوبة...');

      try {
        // 1. ملخص السوق
        const { data: marketSummary, error: marketError } = await supabase
          .rpc('exec_sql', {
            sql_query: 'SELECT * FROM market_summary_view'
          });

        if (marketError) {
          console.warn('⚠️ لا يمكن الوصول لـ Views، سنستخدم البيانات المباشرة');
          // استخدام البيانات المباشرة من stocks_realtime
          return await getFallbackData();
        }

        // 2. أفضل الأسهم
        const { data: topPerformers, error: topError } = await supabase
          .rpc('exec_sql', {
            sql_query: 'SELECT * FROM top_performers_view ORDER BY rank LIMIT 15'
          });

        // 3. التحليل الفني
        const { data: technicalAnalysis, error: techError } = await supabase
          .rpc('exec_sql', {
            sql_query: 'SELECT * FROM technical_analysis_view'
          });

        if (marketError || topError || techError) {
          console.warn('⚠️ بعض Views لا تعمل، سنستخدم البيانات المباشرة');
          return await getFallbackData();
        }

        // تنظيم البيانات
        const marketData = Array.isArray(marketSummary) ? marketSummary[0] : marketSummary;
        const techData = Array.isArray(technicalAnalysis) ? technicalAnalysis[0] : technicalAnalysis;
        const topData = Array.isArray(topPerformers) ? topPerformers : [];

        const result: ViewsMarketData = {
          marketSummary: {
            total_stocks: marketData?.total_stocks || 0,
            active_stocks: marketData?.active_stocks || 0,
            gainers: marketData?.gainers || 0,
            losers: marketData?.losers || 0,
            unchanged: marketData?.unchanged || 0,
            avg_change: marketData?.avg_change || 0,
            total_volume: marketData?.total_volume || 0,
            total_turnover: marketData?.total_turnover || 0,
            last_updated: marketData?.last_updated || new Date().toISOString()
          },
          topPerformers: {
            gainers: topData.filter((s: any) => s.category === 'gainers').slice(0, 5),
            losers: topData.filter((s: any) => s.category === 'losers').slice(0, 5),
            mostActive: topData.filter((s: any) => s.category === 'most_active').slice(0, 5)
          },
          technicalSummary: {
            bullishStocks: techData?.bullish_stocks || 0,
            bearishStocks: techData?.bearish_stocks || 0,
            strongUptrend: techData?.strong_uptrend || 0,
            stocksWithTargets: techData?.stocks_with_targets || 0
          }
        };

        console.log('✅ تم جلب البيانات من Views بنجاح');
        return result;

      } catch (error) {
        console.warn('⚠️ خطأ في الوصول للـ Views، سنستخدم البيانات المباشرة:', error);
        return await getFallbackData();
      }
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// دالة احتياطية لجلب البيانات المباشرة إذا فشلت Views
async function getFallbackData(): Promise<ViewsMarketData> {
  console.log('🔄 استخدام البيانات المباشرة من stocks_realtime...');

  const { data: stocks, error } = await supabase
    .from('stocks_realtime')
    .select(`
      symbol,
      name,
      current_price,
      change_percent,
      volume,
      turnover,
      ma5,
      ma20,
      ma50,
      target_1
    `)
    .not('symbol', 'like', '%EGX%')
    .not('name', 'is', null)
    .not('current_price', 'is', null);

  if (error) {
    throw error;
  }

  const validStocks = stocks.filter(s => 
    s.change_percent !== null && 
    s.current_price !== null &&
    s.volume !== null
  );

  // حساب الإحصائيات
  const gainers = validStocks.filter(s => (s.change_percent || 0) > 0);
  const losers = validStocks.filter(s => (s.change_percent || 0) < 0);
  const unchanged = validStocks.filter(s => (s.change_percent || 0) === 0);
  
  const totalValue = validStocks.reduce((sum, s) => sum + (s.turnover || 0), 0);
  const totalVolume = validStocks.reduce((sum, s) => sum + (s.volume || 0), 0);
  const avgChange = validStocks.length > 0 
    ? validStocks.reduce((sum, s) => sum + (s.change_percent || 0), 0) / validStocks.length
    : 0;

  // أفضل الأسهم
  const topGainers = gainers
    .sort((a, b) => (b.change_percent || 0) - (a.change_percent || 0))
    .slice(0, 5)
    .map(s => ({
      symbol: s.symbol,
      name: s.name || '',
      change_percent: s.change_percent || 0,
      current_price: s.current_price || 0,
      volume: s.volume || 0
    }));

  const topLosers = losers
    .sort((a, b) => (a.change_percent || 0) - (b.change_percent || 0))
    .slice(0, 5)
    .map(s => ({
      symbol: s.symbol,
      name: s.name || '',
      change_percent: s.change_percent || 0,
      current_price: s.current_price || 0,
      volume: s.volume || 0
    }));

  const mostActive = validStocks
    .filter(s => (s.volume || 0) > 0)
    .sort((a, b) => (b.volume || 0) - (a.volume || 0))
    .slice(0, 5)
    .map(s => ({
      symbol: s.symbol,
      name: s.name || '',
      current_price: s.current_price || 0,
      change_percent: s.change_percent || 0,
      volume: s.volume || 0
    }));

  // التحليل الفني
  const bullishStocks = validStocks.filter(s => 
    (s.ma5 || 0) > (s.ma20 || 0) && s.ma5 && s.ma20
  ).length;

  const bearishStocks = validStocks.filter(s => 
    (s.ma5 || 0) < (s.ma20 || 0) && s.ma5 && s.ma20
  ).length;

  const strongUptrend = validStocks.filter(s => 
    (s.ma5 || 0) > (s.ma20 || 0) &&
    (s.ma20 || 0) > (s.ma50 || 0) &&
    s.ma5 && s.ma20 && s.ma50
  ).length;

  const stocksWithTargets = validStocks.filter(s => 
    s.target_1 && s.target_1 > 0
  ).length;

  return {
    marketSummary: {
      total_stocks: validStocks.length,
      active_stocks: validStocks.filter(s => (s.volume || 0) > 0).length,
      gainers: gainers.length,
      losers: losers.length,
      unchanged: unchanged.length,
      avg_change: Math.round(avgChange * 100) / 100,
      total_volume: Math.round(totalVolume),
      total_turnover: Math.round(totalValue),
      last_updated: new Date().toISOString()
    },
    topPerformers: {
      gainers: topGainers,
      losers: topLosers,
      mostActive: mostActive
    },
    technicalSummary: {
      bullishStocks,
      bearishStocks,
      strongUptrend,
      stocksWithTargets
    }
  };
}

// Hook مُبسط للحصول على ملخص سريع فقط
export function useQuickMarketSummary() {
  return useQuery({
    queryKey: ['quick-market-summary'],
    queryFn: async () => {
      console.log('⚡ جاري جلب الملخص السريع...');
      
      try {
        const { data, error } = await supabase
          .rpc('exec_sql', {
            sql_query: 'SELECT * FROM market_summary_view'
          });

        if (error || !data) {
          // استخدام البيانات المباشرة
          const fallback = await getFallbackData();
          return fallback.marketSummary;
        }

        const marketData = Array.isArray(data) ? data[0] : data;
        return {
          total_stocks: marketData?.total_stocks || 0,
          active_stocks: marketData?.active_stocks || 0,
          gainers: marketData?.gainers || 0,
          losers: marketData?.losers || 0,
          avg_change: marketData?.avg_change || 0,
          total_volume: marketData?.total_volume || 0,
          last_updated: marketData?.last_updated || new Date().toISOString()
        };

      } catch (error) {
        console.warn('⚠️ استخدام البيانات الاحتياطية للملخص:', error);
        const fallback = await getFallbackData();
        return fallback.marketSummary;
      }
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // 2 minutes
    retry: 2
  });
}
