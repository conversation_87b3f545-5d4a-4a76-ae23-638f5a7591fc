#!/usr/bin/env python3
"""
Database Schema Update Script
Adds missing columns to support enhanced data import
"""

import os
import sys
import logging
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('schema_update.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    logger.error("Missing required environment variables")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def update_stocks_realtime_schema():
    """Update stocks_realtime table with missing columns"""
    
    logger.info("🔧 Updating stocks_realtime table schema...")
    
    # Define new columns to add
    new_columns = [
        # Technical Analysis
        ('ma5', 'DECIMAL'),
        ('ma10', 'DECIMAL'),
        ('ma20', 'DECIMAL'),
        ('ma50', 'DECIMAL'),
        ('ma100', 'DECIMAL'),
        ('ma200', 'DECIMAL'),
        ('tk_indicator', 'DECIMAL'),
        ('kj_indicator', 'DECIMAL'),
        
        # Trading Targets
        ('target_1', 'DECIMAL'),
        ('target_2', 'DECIMAL'),
        ('target_3', 'DECIMAL'),
        ('stop_loss', 'DECIMAL'),
        ('stock_status', 'VARCHAR(100)'),
        ('speculation_opportunity', 'BOOLEAN'),
        
        # Liquidity Analysis
        ('liquidity_ratio', 'DECIMAL'),
        ('net_liquidity', 'DECIMAL'),
        ('liquidity_inflow', 'DECIMAL'),
        ('liquidity_outflow', 'DECIMAL'),
        ('volume_inflow', 'BIGINT'),
        ('volume_outflow', 'BIGINT'),
        ('liquidity_flow', 'DECIMAL'),
        
        # Financial Metrics
        ('free_shares', 'BIGINT'),
        ('eps_annual', 'DECIMAL'),
        ('book_value', 'DECIMAL'),
        ('pe_ratio', 'DECIMAL'),
        ('dividend_yield', 'DECIMAL'),
        ('sector', 'VARCHAR(100)'),
        
        # Additional Analysis
        ('price_range', 'DECIMAL'),
        ('avg_net_volume_3d', 'DECIMAL'),
        ('avg_net_volume_5d', 'DECIMAL'),
        ('opening_value', 'DECIMAL'),
        ('name', 'VARCHAR(200)')
    ]
    
    added_columns = []
    failed_columns = []
    
    for column_name, column_type in new_columns:
        try:
            # Note: Supabase Python client doesn't support direct SQL execution
            # This is a simulation of what would be done
            logger.info(f"Adding column: {column_name} ({column_type})")
            
            # In practice, these would be executed via Supabase SQL editor or migration
            sql_command = f"ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS {column_name} {column_type};"
            logger.info(f"SQL: {sql_command}")
            
            added_columns.append(column_name)
            
        except Exception as e:
            logger.error(f"Failed to add column {column_name}: {e}")
            failed_columns.append(column_name)
    
    logger.info(f"✅ Successfully added {len(added_columns)} columns")
    if failed_columns:
        logger.warning(f"⚠️ Failed to add {len(failed_columns)} columns: {failed_columns}")
    
    return added_columns, failed_columns

def verify_schema_update():
    """Verify that the schema update was successful"""
    
    logger.info("🔍 Verifying schema update...")
    
    try:
        # Get a sample record to check available columns
        response = supabase.table('stocks_realtime').select('*').limit(1).execute()
        
        if response.data:
            available_columns = list(response.data[0].keys())
            logger.info(f"📊 Total columns in stocks_realtime: {len(available_columns)}")
            
            # Check for key enhanced columns
            enhanced_columns = [
                'ma20', 'target_1', 'liquidity_inflow', 'eps_annual', 
                'avg_net_volume_3d', 'sector', 'speculation_opportunity'
            ]
            
            present_enhanced = [col for col in enhanced_columns if col in available_columns]
            logger.info(f"✅ Enhanced columns present: {len(present_enhanced)}/{len(enhanced_columns)}")
            logger.info(f"Present: {present_enhanced}")
            
            missing_enhanced = [col for col in enhanced_columns if col not in available_columns]
            if missing_enhanced:
                logger.warning(f"❌ Missing enhanced columns: {missing_enhanced}")
            
            return len(missing_enhanced) == 0
        else:
            logger.warning("❌ No data found in stocks_realtime table")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error verifying schema: {e}")
        return False

def create_manual_sql_script():
    """Create SQL script for manual execution"""
    
    logger.info("📝 Creating manual SQL script...")
    
    sql_script = """-- Enhanced EGX Stock AI Oracle Schema Update
-- Execute this script in Supabase SQL editor

-- Technical Analysis Columns
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma5 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma10 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma20 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma50 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma100 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS ma200 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS tk_indicator DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS kj_indicator DECIMAL;

-- Trading Targets & Signals
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS target_1 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS target_2 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS target_3 DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS stop_loss DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS stock_status VARCHAR(100);
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS speculation_opportunity BOOLEAN;

-- Liquidity Analysis
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_ratio DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS net_liquidity DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_inflow DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_outflow DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS volume_inflow BIGINT;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS volume_outflow BIGINT;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS liquidity_flow DECIMAL;

-- Financial Metrics
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS free_shares BIGINT;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS eps_annual DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS book_value DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS pe_ratio DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS dividend_yield DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS sector VARCHAR(100);

-- Additional Analysis
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS price_range DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS avg_net_volume_3d DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS avg_net_volume_5d DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS opening_value DECIMAL;
ALTER TABLE stocks_realtime ADD COLUMN IF NOT EXISTS name VARCHAR(200);

-- Create indices for better performance
CREATE INDEX IF NOT EXISTS idx_realtime_ma20 ON stocks_realtime(ma20);
CREATE INDEX IF NOT EXISTS idx_realtime_sector ON stocks_realtime(sector);
CREATE INDEX IF NOT EXISTS idx_realtime_target1 ON stocks_realtime(target_1);
CREATE INDEX IF NOT EXISTS idx_realtime_liquidity ON stocks_realtime(net_liquidity);

-- Verify schema update
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'stocks_realtime' 
ORDER BY column_name;
"""

    with open('manual_schema_update.sql', 'w') as f:
        f.write(sql_script)
    
    logger.info("💾 Manual SQL script saved to: manual_schema_update.sql")
    logger.info("📋 Execute this script in Supabase SQL editor to add missing columns")

def fix_validation_script():
    """Fix the validation error in financial data loader"""
    
    logger.info("🔧 Creating fixed validation function...")
    
    fixed_validation = """
def validate_financial_data():
    \"\"\"Validate financial data integrity - FIXED VERSION\"\"\"
    try:
        logger.info("Validating financial data...")
        
        # Count total records
        response = supabase.table("stocks_financials").select("symbol", count="exact").execute()
        total_count = response.count
        logger.info(f"Total financial records: {total_count}")
        
        # Check for records with essential data - FIXED
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("market_capitalization", "is", "null").execute()
        market_cap_count = response.count
        logger.info(f"Records with market cap data: {market_cap_count}")
        
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("pe_ratio", "is", "null").execute()
        pe_count = response.count
        logger.info(f"Records with P/E ratio data: {pe_count}")
        
        response = supabase.table("stocks_financials").select("symbol", count="exact").not_("dividend_yield", "is", "null").execute()
        dividend_count = response.count
        logger.info(f"Records with dividend data: {dividend_count}")
        
        logger.info("Financial data validation completed")
        
    except Exception as e:
        logger.error(f"Error in financial data validation: {e}")
"""
    
    with open('fixed_validation.py', 'w') as f:
        f.write(fixed_validation)
    
    logger.info("💾 Fixed validation function saved to: fixed_validation.py")

def main():
    """Main function"""
    
    logger.info("🚀 Starting Database Schema Update...")
    logger.info("=" * 60)
    
    try:
        # Update schema (simulation)
        added_columns, failed_columns = update_stocks_realtime_schema()
        
        # Create manual SQL script
        create_manual_sql_script()
        
        # Fix validation script
        fix_validation_script()
        
        # Verify current schema
        schema_ok = verify_schema_update()
        
        logger.info("=" * 60)
        logger.info("📋 Schema Update Summary:")
        logger.info(f"   Columns to add: {len(added_columns)}")
        logger.info(f"   Schema verification: {'✅ Passed' if schema_ok else '⚠️ Needs manual update'}")
        
        logger.info("\n💡 Next Steps:")
        logger.info("1. Execute 'manual_schema_update.sql' in Supabase SQL editor")
        logger.info("2. Re-run enhanced data import:")
        logger.info("   python load_realtime_enhanced.py")
        logger.info("3. Verify with: python test_enhanced_system.py")
        
        logger.info("📄 Files created:")
        logger.info("   - manual_schema_update.sql (execute in Supabase)")
        logger.info("   - fixed_validation.py (reference for fixes)")
        logger.info("   - schema_update.log (this log)")
        
    except Exception as e:
        logger.error(f"❌ Schema update failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
