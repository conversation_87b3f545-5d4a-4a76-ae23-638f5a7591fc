import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tbzbrujqjwpatbzffmwq.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

console.log("🔍 Testing Market Overview API (simulating frontend)...\n");

try {
    // This mimics the exact query from useMarketOverview hook
    const { data: indices, error: indicesError } = await supabase
        .from('market_indices')
        .select('*')
        .in('symbol', ['EGX30', 'EGX70']);

    if (indicesError) {
        console.error("❌ Error fetching indices:", indicesError);
        process.exit(1);
    }

    console.log("✅ Raw indices data:");
    indices?.forEach(index => {
        console.log(`  ${index.symbol}: ${index.current_value} (${index.change_percent}%)`);
    });

    // Process the data like the hook does
    const findIndexData = (symbol) => (indices || []).find(idx => idx.symbol === symbol) || null;
    
    const egx30 = findIndexData('EGX30');
    const egx70 = findIndexData('EGX70');

    console.log("\n📊 Processed frontend data:");
    if (egx30) {
        console.log(`EGX30: ${Number(egx30.current_value || 0)} (${Number(egx30.change_percent || 0)}%)`);
    } else {
        console.log("EGX30: No data");
    }

    if (egx70) {
        console.log(`EGX70: ${Number(egx70.current_value || 0)} (${Number(egx70.change_percent || 0)}%)`);
    } else {
        console.log("EGX70: No data");
    }

    console.log("\n✅ Frontend API Test Complete!");

} catch (error) {
    console.error("❌ API Test Failed:", error);
}
