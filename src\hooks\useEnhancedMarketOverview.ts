import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../integrations/supabase/client';
import { useToast } from './use-toast';

export interface EnhancedStockData {
  // Basic Info
  symbol: string;
  name?: string;
  sector?: string;
  
  // Price Data
  current_price: number;
  open_price: number;
  high_price: number;
  low_price: number;
  previous_close: number;
  change_amount: number;
  change_percent: number;
  
  // Volume & Trading
  volume: number;
  turnover: number;
  trades_count?: number;
  
  // Technical Indicators
  ma5?: number;
  ma10?: number;
  ma20?: number;
  ma50?: number;
  ma100?: number;
  ma200?: number;
  tk_indicator?: number;
  kj_indicator?: number;
  
  // Trading Signals
  target_1?: number;
  target_2?: number;
  target_3?: number;
  stop_loss?: number;
  stock_status?: string;
  speculation_opportunity?: boolean;
  
  // Liquidity Metrics
  liquidity_ratio?: number;
  net_liquidity?: number;
  liquidity_inflow?: number;
  liquidity_outflow?: number;
  volume_inflow?: number;
  volume_outflow?: number;
  liquidity_flow?: number;
  
  // Financial Metrics
  eps_annual?: number;
  book_value?: number;
  pe_ratio?: number;
  dividend_yield?: number;
  market_cap?: number;
  free_shares?: number;
  
  // Additional Data
  price_range?: number;
  avg_net_volume_3d?: number;
  avg_net_volume_5d?: number;
  opening_value?: number;
  last_trade_date?: string;
  last_trade_time?: string;
  updated_at: string;
}

export interface MarketStats {
  totalStocks: number;
  activeStocks: number;
  totalVolume: number;
  totalTurnover: number;
  topGainers: EnhancedStockData[];
  topLosers: EnhancedStockData[];
  mostActive: EnhancedStockData[];
  speculationOpportunities: EnhancedStockData[];
}

export const useEnhancedMarketOverview = () => {
  const [stocks, setStocks] = useState<EnhancedStockData[]>([]);
  const [marketStats, setMarketStats] = useState<MarketStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchEnhancedStocks = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch all real stocks (exclude indices and suspended stocks)
      const { data, error: fetchError } = await supabase
        .from('stocks_realtime')
        .select(`
          symbol,
          name,
          sector,
          current_price,
          open_price,
          high_price,
          low_price,
          previous_close,
          change_amount,
          change_percent,
          volume,
          turnover,
          trades_count,
          ma5,
          ma10,
          ma20,
          ma50,
          ma100,
          ma200,
          tk_indicator,
          kj_indicator,
          target_1,
          target_2,
          target_3,
          stop_loss,
          stock_status,
          speculation_opportunity,
          liquidity_ratio,
          net_liquidity,
          liquidity_inflow,
          liquidity_outflow,
          volume_inflow,
          volume_outflow,
          liquidity_flow,
          eps_annual,
          book_value,
          pe_ratio,
          dividend_yield,
          market_cap,
          free_shares,
          price_range,
          avg_net_volume_3d,
          avg_net_volume_5d,
          opening_value,
          last_trade_date,
          last_trade_time,
          updated_at
        `)
        .not('symbol', 'like', '%EGX%') // Exclude indices
        .not('symbol', 'like', '%.%')   // Exclude special symbols
        .not('current_price', 'is', null) // Exclude suspended stocks
        .gt('volume', 0) // Only active stocks
        .order('volume', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      const enhancedStocks = data as EnhancedStockData[];
      setStocks(enhancedStocks);

      // Calculate market statistics
      const stats = calculateMarketStats(enhancedStocks);
      setMarketStats(stats);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch market data';
      setError(errorMessage);
      toast({
        title: "خطأ في تحميل البيانات",
        description: errorMessage,
        variant: "destructive",
      });    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const calculateMarketStats = (stocks: EnhancedStockData[]): MarketStats => {
    const activeStocks = stocks.filter(stock => stock.volume > 0);
    
    // Sort stocks for different categories
    const sortedByChange = [...activeStocks].sort((a, b) => b.change_percent - a.change_percent);
    const sortedByVolume = [...activeStocks].sort((a, b) => b.volume - a.volume);
    const speculationOpps = activeStocks.filter(stock => stock.speculation_opportunity === true);

    return {
      totalStocks: stocks.length,
      activeStocks: activeStocks.length,
      totalVolume: activeStocks.reduce((sum, stock) => sum + (stock.volume || 0), 0),
      totalTurnover: activeStocks.reduce((sum, stock) => sum + (stock.turnover || 0), 0),
      topGainers: sortedByChange.slice(0, 10),
      topLosers: sortedByChange.slice(-10).reverse(),
      mostActive: sortedByVolume.slice(0, 10),
      speculationOpportunities: speculationOpps.slice(0, 10)
    };
  };

  const getStocksByFilters = (filters: {
    minVolume?: number;
    maxPE?: number;
    minDividendYield?: number;
    sectors?: string[];
    hasTargets?: boolean;
    hasSpeculationOpportunity?: boolean;
  }) => {
    let filtered = stocks;

    if (filters.minVolume) {
      filtered = filtered.filter(stock => stock.volume >= filters.minVolume!);
    }

    if (filters.maxPE) {
      filtered = filtered.filter(stock => !stock.pe_ratio || stock.pe_ratio <= filters.maxPE!);
    }

    if (filters.minDividendYield) {
      filtered = filtered.filter(stock => stock.dividend_yield && stock.dividend_yield >= filters.minDividendYield!);
    }

    if (filters.sectors && filters.sectors.length > 0) {
      filtered = filtered.filter(stock => stock.sector && filters.sectors!.includes(stock.sector));
    }

    if (filters.hasTargets) {
      filtered = filtered.filter(stock => stock.target_1 || stock.target_2 || stock.target_3);
    }

    if (filters.hasSpeculationOpportunity) {
      filtered = filtered.filter(stock => stock.speculation_opportunity === true);
    }

    return filtered;
  };

  const getStockWithTechnicals = (symbol: string) => {
    const stock = stocks.find(s => s.symbol === symbol);
    if (!stock) return null;

    return {
      ...stock,
      technicals: {
        movingAverages: {
          ma5: stock.ma5,
          ma10: stock.ma10,
          ma20: stock.ma20,
          ma50: stock.ma50,
          ma100: stock.ma100,
          ma200: stock.ma200
        },
        indicators: {
          tk: stock.tk_indicator,
          kj: stock.kj_indicator
        },
        targets: {
          target1: stock.target_1,
          target2: stock.target_2,
          target3: stock.target_3,
          stopLoss: stock.stop_loss
        },
        liquidity: {
          ratio: stock.liquidity_ratio,
          netLiquidity: stock.net_liquidity,
          inflow: stock.liquidity_inflow,
          outflow: stock.liquidity_outflow
        }
      }
    };
  };

  useEffect(() => {
    fetchEnhancedStocks();
    
    // Set up real-time subscription for market data updates
    const subscription = supabase
      .channel('stocks_realtime_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'stocks_realtime' }, 
        () => {
          fetchEnhancedStocks();
        }
      )
      .subscribe();

    // Refresh data every 30 seconds
    const interval = setInterval(fetchEnhancedStocks, 30000);    return () => {
      subscription.unsubscribe();
      clearInterval(interval);
    };
  }, [fetchEnhancedStocks]);

  return {
    stocks,
    marketStats,
    isLoading,
    error,
    refreshData: fetchEnhancedStocks,
    getStocksByFilters,
    getStockWithTechnicals
  };
};
