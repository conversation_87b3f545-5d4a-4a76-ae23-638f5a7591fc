import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

type StockRealtimeData = Database['public']['Tables']['stocks_realtime']['Row'];

export interface SimpleMarketOverview {
  marketSummary: {
    totalValue: number;
    totalVolume: number;
    gainersCount: number;
    losersCount: number;
    unchangedCount: number;
    avgChange: number;
    marketTrend: 'bullish' | 'bearish' | 'neutral';
  };
  topStocks: {
    gainers: Array<{
      symbol: string;
      name: string | null;
      change_percent: number;
      current_price: number;
      volume: number;
    }>;
    losers: Array<{
      symbol: string;
      name: string | null;
      change_percent: number;
      current_price: number;
      volume: number;
    }>;
    mostActive: Array<{
      symbol: string;
      name: string | null;
      volume: number;
      current_price: number;
      change_percent: number;
    }>;
  };
  technicalSummary: {
    bullishStocks: number;
    bearishStocks: number;
    strongUptrend: number;
    stocksWithTargets: number;
  };
}

export function useSimpleMarketOverview() {
  return useQuery<SimpleMarketOverview>({
    queryKey: ['simple-market-overview'],
    queryFn: async () => {
      console.log('🔍 جاري جلب النظرة العامة البسيطة...');

      // استعلام واحد لجلب البيانات الأساسية
      const { data: marketData, error } = await supabase
        .from('stocks_realtime')
        .select(`
          symbol,
          name,
          current_price,
          change_percent,
          volume,
          turnover,
          ma5,
          ma20,
          ma50,
          target_1
        `)
        .not('symbol', 'like', '%EGX%')
        .not('symbol', 'like', '%ETF%')
        .not('name', 'is', null)
        .not('current_price', 'is', null)
        .order('volume', { ascending: false });

      if (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw error;
      }

      if (!marketData || marketData.length === 0) {
        throw new Error('لا توجد بيانات متاحة');
      }

      console.log(`✅ تم جلب ${marketData.length} سهم للنظرة العامة`);

      // تنظيف البيانات
      const validStocks = marketData.filter(s => 
        s.change_percent !== null && 
        s.current_price !== null &&
        s.volume !== null
      ) as Array<StockRealtimeData>;

      // === حساب الملخص العام ===
      const gainers = validStocks.filter(s => (s.change_percent || 0) > 0);
      const losers = validStocks.filter(s => (s.change_percent || 0) < 0);
      const unchanged = validStocks.filter(s => (s.change_percent || 0) === 0);
      
      const totalValue = validStocks.reduce((sum, s) => sum + (s.turnover || 0), 0);
      const totalVolume = validStocks.reduce((sum, s) => sum + (s.volume || 0), 0);
      const avgChange = validStocks.length > 0 
        ? validStocks.reduce((sum, s) => sum + (s.change_percent || 0), 0) / validStocks.length
        : 0;

      // تحديد اتجاه السوق
      let marketTrend: 'bullish' | 'bearish' | 'neutral' = 'neutral';
      if (gainers.length > losers.length * 1.2) {
        marketTrend = 'bullish';
      } else if (losers.length > gainers.length * 1.2) {
        marketTrend = 'bearish';
      }

      // === أفضل الأسهم ===
      const topGainers = gainers
        .sort((a, b) => (b.change_percent || 0) - (a.change_percent || 0))
        .slice(0, 5)
        .map(s => ({
          symbol: s.symbol,
          name: s.name,
          change_percent: s.change_percent || 0,
          current_price: s.current_price || 0,
          volume: s.volume || 0
        }));

      const topLosers = losers
        .sort((a, b) => (a.change_percent || 0) - (b.change_percent || 0))
        .slice(0, 5)
        .map(s => ({
          symbol: s.symbol,
          name: s.name,
          change_percent: s.change_percent || 0,
          current_price: s.current_price || 0,
          volume: s.volume || 0
        }));

      const mostActive = validStocks
        .filter(s => (s.volume || 0) > 0)
        .sort((a, b) => (b.volume || 0) - (a.volume || 0))
        .slice(0, 5)
        .map(s => ({
          symbol: s.symbol,
          name: s.name,
          volume: s.volume || 0,
          current_price: s.current_price || 0,
          change_percent: s.change_percent || 0
        }));

      // === التحليل الفني ===
      const bullishStocks = validStocks.filter(s => 
        (s.ma5 || 0) > (s.ma20 || 0) && s.ma5 && s.ma20
      ).length;

      const bearishStocks = validStocks.filter(s => 
        (s.ma5 || 0) < (s.ma20 || 0) && s.ma5 && s.ma20
      ).length;

      const strongUptrend = validStocks.filter(s => 
        (s.ma5 || 0) > (s.ma20 || 0) &&
        (s.ma20 || 0) > (s.ma50 || 0) &&
        s.ma5 && s.ma20 && s.ma50
      ).length;

      const stocksWithTargets = validStocks.filter(s => 
        s.target_1 && s.target_1 > 0
      ).length;

      const result: SimpleMarketOverview = {
        marketSummary: {
          totalValue: Math.round(totalValue),
          totalVolume: Math.round(totalVolume),
          gainersCount: gainers.length,
          losersCount: losers.length,
          unchangedCount: unchanged.length,
          avgChange: Math.round(avgChange * 100) / 100,
          marketTrend
        },
        topStocks: {
          gainers: topGainers,
          losers: topLosers,
          mostActive: mostActive
        },
        technicalSummary: {
          bullishStocks,
          bearishStocks,
          strongUptrend,
          stocksWithTargets
        }
      };

      console.log('✅ تم حساب النظرة العامة بنجاح');
      console.log(`📊 الإحصائيات: ${gainers.length} رابح، ${losers.length} خاسر، اتجاه: ${marketTrend}`);
      
      return result;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
