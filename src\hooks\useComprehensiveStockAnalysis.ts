import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface EnhancedStockData {
  // Basic info
  symbol: string;
  name: string;
  sector: string;
  
  // Price data
  current_price: number;
  change_percent: number;
  change_amount: number;
  volume: number;
  high_price: number;
  low_price: number;
  open_price: number;
  previous_close: number;
  market_cap: number;
  
  // Financial ratios
  pe_ratio?: number;
  dividend_yield?: number;
  book_value_per_share?: number;
  eps_ttm?: number;
  debt_to_equity?: number;
  roa?: number;
  roe?: number;
  
  // Technical indicators
  ma5?: number;
  ma10?: number;
  ma20?: number;
  ma50?: number;
  ma100?: number;
  ma200?: number;
  rsi_14?: number;
  macd?: number;
  macd_signal?: number;
  macd_histogram?: number;
  bb_upper?: number;
  bb_middle?: number;
  bb_lower?: number;
  atr_14?: number;
  stoch_k?: number;
  stoch_d?: number;
  adx_14?: number;
  cci_20?: number;
  williams_r?: number;
  momentum_10?: number;
  ema_12?: number;
  ema_26?: number;
  roc_10?: number;
  volume_sma_20?: number;
  
  // Advanced calculations
  volatility?: number;
  beta?: number;
  sharpe_ratio?: number;
  correlation_to_market?: number;
}

export interface HistoricalDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjusted_close?: number;
}

export interface TechnicalSignal {
  indicator: string;
  signal: 'BUY' | 'SELL' | 'HOLD' | 'STRONG_BUY' | 'STRONG_SELL';
  strength: number; // 0-100
  description: string;
  last_updated: string;
}

export interface AnalysisResult {
  stock: EnhancedStockData | null;
  historical: HistoricalDataPoint[];
  signals: TechnicalSignal[];
  patterns: PatternAnalysis[];
  recommendations: RecommendationData;
}

export interface PatternAnalysis {
  name: string;
  type: 'chart_pattern' | 'candlestick' | 'harmonic' | 'elliott_wave';
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  description: string;
  target_price?: number;
  stop_loss?: number;
  time_frame: string;
}

export interface RecommendationData {
  overall_rating: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL';
  score: number; // 0-100
  price_target: number;
  stop_loss: number;
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
  time_horizon: 'SHORT' | 'MEDIUM' | 'LONG';
  key_factors: string[];
  analyst_notes: string;
}

export const useComprehensiveStockAnalysis = (symbol: string) => {
  // Fetch basic stock data
  const stockQuery = useQuery({
    queryKey: ['comprehensive-stock', symbol],
    queryFn: async () => {
      if (!symbol) return null;

      // Fetch from multiple tables to get comprehensive data
      const [
        stocksResponse,
        realtimeResponse, 
        financialResponse,
        technicalResponse
      ] = await Promise.all([
        supabase.from('stocks_master').select('*').eq('symbol', symbol).maybeSingle(),
        supabase.from('stocks_realtime').select('*').eq('symbol', symbol).maybeSingle(),
        supabase.from('stocks_financials').select('*').eq('symbol', symbol).maybeSingle(),
        supabase.from('technical_indicators').select('*').eq('symbol', symbol).order('date', { ascending: false }).limit(1).maybeSingle()
      ]);

      if (stocksResponse.error && realtimeResponse.error) {
        throw new Error('Stock not found');
      }

      const stock = stocksResponse.data;
      const realtime = realtimeResponse.data;
      const financial = financialResponse.data;
      const technical = technicalResponse.data;

      return {
        symbol: symbol,
        name: stock?.name || symbol,
        sector: stock?.sector || 'Unknown',
        
        // Price data
        current_price: realtime?.current_price || 0,
        change_percent: realtime?.change_percent || 0,
        change_amount: realtime?.change_amount || 0,
        volume: realtime?.volume || 0,
        high_price: realtime?.high_price || 0,
        low_price: realtime?.low_price || 0,
        open_price: realtime?.open_price || 0,
        previous_close: realtime?.previous_close || 0,
        market_cap: realtime?.market_cap || financial?.market_cap || 0,
        
        // Financial ratios
        pe_ratio: financial?.pe_ratio,
        dividend_yield: financial?.dividend_yield,
        book_value_per_share: financial?.book_value_per_share,
        eps_ttm: financial?.eps_ttm,
        debt_to_equity: financial?.debt_to_equity,
        roa: financial?.roa,
        roe: financial?.roe,
        
        // Technical indicators
        ma5: technical?.sma_5,
        ma10: technical?.sma_10,
        ma20: technical?.sma_20,
        ma50: technical?.sma_50,
        ma100: technical?.sma_100,
        ma200: technical?.sma_200,
        rsi_14: technical?.rsi_14,
        macd: technical?.macd,
        macd_signal: technical?.macd_signal,
        macd_histogram: technical?.macd_histogram,
        bb_upper: technical?.bb_upper,
        bb_middle: technical?.bb_middle,
        bb_lower: technical?.bb_lower,
        atr_14: technical?.atr_14,
        stoch_k: technical?.stoch_k,
        stoch_d: technical?.stoch_d,
        adx_14: technical?.adx_14,
        cci_20: technical?.cci_20,
        williams_r: technical?.williams_r,
        momentum_10: technical?.momentum_10,
        ema_12: technical?.ema_12,
        ema_26: technical?.ema_26,
        roc_10: technical?.roc_10,
        volume_sma_20: technical?.volume_sma_20,
        
        // Advanced calculations
        volatility: calculateVolatility(technical),
        beta: financial?.beta,
        correlation_to_market: 0.75, // This would be calculated from historical data
      } as EnhancedStockData;
    },
    enabled: !!symbol,
    refetchInterval: 30000, // Update every 30 seconds
  });

  // Fetch historical data
  const historicalQuery = useQuery({
    queryKey: ['historical-data', symbol],
    queryFn: async () => {
      if (!symbol) return [];
      
      const { data, error } = await supabase
        .from('stocks_historical')
        .select('*')
        .eq('symbol', symbol)
        .order('date', { ascending: false })
        .limit(252); // 1 year of data

      if (error) throw error;
      
      return (data || []).map(d => ({
        date: d.date,
        open: d.open,
        high: d.high,
        low: d.low,
        close: d.close,
        volume: d.volume || 0,
        adjusted_close: d.adjusted_close
      })) as HistoricalDataPoint[];
    },
    enabled: !!symbol,
  });

  // Generate technical signals
  const signalsQuery = useQuery({
    queryKey: ['technical-signals', symbol],
    queryFn: async () => {
      const stock = stockQuery.data;
      if (!stock) return [];

      const signals: TechnicalSignal[] = [];

      // RSI signals
      if (stock.rsi_14) {
        if (stock.rsi_14 < 30) {
          signals.push({
            indicator: 'RSI',
            signal: 'BUY',
            strength: Math.max(0, 30 - stock.rsi_14) * 3,
            description: 'مؤشر القوة النسبية في منطقة التشبع البيعي',
            last_updated: new Date().toISOString()
          });
        } else if (stock.rsi_14 > 70) {
          signals.push({
            indicator: 'RSI',
            signal: 'SELL',
            strength: Math.max(0, stock.rsi_14 - 70) * 3,
            description: 'مؤشر القوة النسبية في منطقة التشبع الشرائي',
            last_updated: new Date().toISOString()
          });
        }
      }

      // Moving average signals
      if (stock.ma20 && stock.ma50) {
        if (stock.current_price > stock.ma20 && stock.ma20 > stock.ma50) {
          signals.push({
            indicator: 'Moving Averages',
            signal: 'BUY',
            strength: 75,
            description: 'السعر أعلى المتوسطات المتحركة مع اتجاه صاعد',
            last_updated: new Date().toISOString()
          });
        } else if (stock.current_price < stock.ma20 && stock.ma20 < stock.ma50) {
          signals.push({
            indicator: 'Moving Averages',
            signal: 'SELL',
            strength: 70,
            description: 'السعر أسفل المتوسطات المتحركة مع اتجاه هابط',
            last_updated: new Date().toISOString()
          });
        }
      }

      // MACD signals
      if (stock.macd && stock.macd_signal) {
        if (stock.macd > stock.macd_signal) {
          signals.push({
            indicator: 'MACD',
            signal: 'BUY',
            strength: 65,
            description: 'إشارة صاعدة من مؤشر MACD',
            last_updated: new Date().toISOString()
          });
        } else {
          signals.push({
            indicator: 'MACD',
            signal: 'SELL',
            strength: 60,
            description: 'إشارة هابطة من مؤشر MACD',
            last_updated: new Date().toISOString()
          });
        }
      }

      // Volume analysis
      if (stock.volume && stock.volume_sma_20) {
        if (stock.volume > stock.volume_sma_20 * 1.5) {
          signals.push({
            indicator: 'Volume',
            signal: 'BUY',
            strength: 80,
            description: 'حجم تداول مرتفع يشير لاهتمام قوي',
            last_updated: new Date().toISOString()
          });
        }
      }

      return signals;
    },
    enabled: !!stockQuery.data,
  });

  // Generate pattern analysis
  const patternsQuery = useQuery({
    queryKey: ['pattern-analysis', symbol],
    queryFn: async () => {
      const stock = stockQuery.data;
      const historical = historicalQuery.data;
      
      if (!stock || !historical || historical.length < 20) return [];

      const patterns: PatternAnalysis[] = [];

      // Chart patterns
      const recentData = historical.slice(0, 20);
      const highs = recentData.map(d => d.high);
      const lows = recentData.map(d => d.low);
      
      const recentHigh = Math.max(...highs);
      const recentLow = Math.min(...lows);
      
      // Support and resistance levels
      if (stock.current_price > (recentHigh + recentLow) / 2) {
        patterns.push({
          name: 'Ascending Triangle',
          type: 'chart_pattern',
          signal: 'bullish',
          confidence: 72,
          description: 'نمط مثلث صاعد مع مقاومة أفقية ودعم صاعد',
          target_price: recentHigh * 1.05,
          stop_loss: recentLow,
          time_frame: 'Medium-term'
        });
      }

      // Candlestick patterns (simplified)
      if (historical.length >= 3) {
        const last3 = historical.slice(0, 3);
        const [today, yesterday, dayBefore] = last3;
        
        // Bullish engulfing pattern
        if (yesterday.close < yesterday.open && // Yesterday was bearish
            today.close > today.open && // Today is bullish
            today.open < yesterday.close && // Today opened below yesterday's close
            today.close > yesterday.open) { // Today closed above yesterday's open
          patterns.push({
            name: 'Bullish Engulfing',
            type: 'candlestick',
            signal: 'bullish',
            confidence: 85,
            description: 'نمط الابتلاع الصاعد - إشارة انعكاس صاعدة قوية',
            target_price: stock.current_price * 1.03,
            time_frame: 'Short-term'
          });
        }
      }

      return patterns;
    },
    enabled: !!stockQuery.data && !!historicalQuery.data,
  });

  // Generate overall recommendation
  const recommendationQuery = useQuery({
    queryKey: ['recommendation', symbol],
    queryFn: async () => {
      const stock = stockQuery.data;
      const signals = signalsQuery.data || [];
      const patterns = patternsQuery.data || [];
      
      if (!stock) return null;

      // Calculate overall score
      let totalScore = 50; // Neutral starting point
      
      // Technical signals contribution
      signals.forEach(signal => {
        const weight = signal.strength / 100;
        if (signal.signal === 'BUY' || signal.signal === 'STRONG_BUY') {
          totalScore += weight * 20;
        } else if (signal.signal === 'SELL' || signal.signal === 'STRONG_SELL') {
          totalScore -= weight * 20;
        }
      });

      // Pattern analysis contribution
      patterns.forEach(pattern => {
        const weight = pattern.confidence / 100;
        if (pattern.signal === 'bullish') {
          totalScore += weight * 15;
        } else if (pattern.signal === 'bearish') {
          totalScore -= weight * 15;
        }
      });

      // Fundamental analysis contribution
      if (stock.pe_ratio && stock.pe_ratio < 15) totalScore += 5;
      if (stock.pe_ratio && stock.pe_ratio > 25) totalScore -= 5;
      if (stock.dividend_yield && stock.dividend_yield > 3) totalScore += 3;
      if (stock.roe && stock.roe > 15) totalScore += 5;

      // Ensure score is within bounds
      totalScore = Math.max(0, Math.min(100, totalScore));

      // Determine rating
      let rating: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL';
      if (totalScore >= 80) rating = 'STRONG_BUY';
      else if (totalScore >= 65) rating = 'BUY';
      else if (totalScore >= 35) rating = 'HOLD';
      else if (totalScore >= 20) rating = 'SELL';
      else rating = 'STRONG_SELL';

      return {
        overall_rating: rating,
        score: totalScore,
        price_target: stock.current_price * (1 + (totalScore - 50) / 100),
        stop_loss: stock.current_price * 0.92,
        risk_level: stock.volatility && stock.volatility > 0.3 ? 'HIGH' : 
                   stock.volatility && stock.volatility > 0.2 ? 'MEDIUM' : 'LOW',
        time_horizon: 'MEDIUM',
        key_factors: [
          'التحليل الفني',
          'الأنماط السعرية',
          'المؤشرات الفنية',
          'التحليل الأساسي'
        ],
        analyst_notes: `التوصية مبنية على تحليل شامل للسهم باستخدام ${signals.length} مؤشر فني و ${patterns.length} نمط سعري.`
      } as RecommendationData;
    },
    enabled: !!stockQuery.data,
  });

  return {
    stock: stockQuery.data,
    historical: historicalQuery.data || [],
    signals: signalsQuery.data || [],
    patterns: patternsQuery.data || [],
    recommendation: recommendationQuery.data,
    isLoading: stockQuery.isLoading || historicalQuery.isLoading,
    error: stockQuery.error || historicalQuery.error,
    refetch: () => {
      stockQuery.refetch();
      historicalQuery.refetch();
      signalsQuery.refetch();
      patternsQuery.refetch();
      recommendationQuery.refetch();
    }
  };
};

// Utility functions
function calculateVolatility(technical: { atr_14?: number } | null): number | undefined {
  if (!technical?.atr_14) return undefined;
  // Simplified volatility calculation using ATR
  return technical.atr_14 / 100; // Normalize ATR to percentage
}

export default useComprehensiveStockAnalysis;
