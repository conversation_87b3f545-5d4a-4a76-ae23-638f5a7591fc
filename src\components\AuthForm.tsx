
import React, { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, LogIn } from "lucide-react";

const AuthForm = () => {
  const [mode, setMode] = useState<'login' | 'signup'>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [socialLoading, setSocialLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [successMsg, setSuccessMsg] = useState<string | null>(null);

  // Handler for Google login
  const handleGoogleLogin = async () => {
    setSocialLoading(true);
    setErrorMsg(null);
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: window.location.origin, // Redirect back to this app
      },
    });
    if (error) setErrorMsg(error.message);
    setSocialLoading(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrorMsg(null);
    setSuccessMsg(null);
    if (mode === "login") {
      const { error } = await supabase.auth.signInWithPassword({ email, password });
      if (error) setErrorMsg(error.message);
    } else {
      // Always include redirect for email confirmation links
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: window.location.origin,
        },
      });
      if (error) setErrorMsg(error.message);
      else setSuccessMsg("تم التسجيل بنجاح! يرجى التحقق من بريدك الإلكتروني.");
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-gray-100 dark:from-gray-900 dark:to-blue-950">
      <Card className="w-full max-w-md shadow-lg border-2 border-blue-200">
        <CardHeader>
          <CardTitle className="text-center">
            {mode === "login" ? "تسجيل الدخول" : "إنشاء حساب جديد"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Google Login Button */}
            <Button
              type="button"
              variant="outline"
              onClick={handleGoogleLogin}
              className="w-full flex items-center justify-center mb-2"
              disabled={socialLoading || loading}
            >
              {socialLoading ? (
                <>
                  <Loader2 className="animate-spin mr-2" />
                  جاري تسجيل الدخول عبر Google...
                </>
              ) : (
                <>
                  <LogIn className="mr-2" />
                  تسجيل الدخول عبر Google
                </>
              )}
            </Button>

            <div className="flex items-center my-3">
              <div className="flex-1 h-px bg-gray-200"></div>
              <span className="mx-2 text-xs text-gray-400">أو</span>
              <div className="flex-1 h-px bg-gray-200"></div>
            </div>

            <Input
              placeholder="البريد الإلكتروني"
              type="email"
              autoComplete="username"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              dir="rtl"
            />
            <Input
              placeholder="كلمة المرور"
              type="password"
              autoComplete={mode === "login" ? "current-password" : "new-password"}
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              dir="rtl"
            />
            <Button type="submit" disabled={loading || socialLoading} className="w-full">
              {loading ? "جاري..." : mode === "login" ? "تسجيل الدخول" : "تسجيل"}
            </Button>
            <div className="text-center text-sm py-2">
              {mode === "login" ? (
                <span>
                  ليس لديك حساب؟{" "}
                  <button type="button" onClick={() => setMode("signup")} className="text-blue-700 underline">إنشاء حساب</button>
                </span>
              ) : (
                <span>
                  لديك حساب بالفعل؟{" "}
                  <button type="button" onClick={() => setMode("login")} className="text-blue-700 underline">سجّل الدخول</button>
                </span>
              )}
            </div>
            {errorMsg && <div className="text-red-600 text-xs text-center">{errorMsg}</div>}
            {successMsg && <div className="text-green-600 text-xs text-center">{successMsg}</div>}
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
export default AuthForm;

