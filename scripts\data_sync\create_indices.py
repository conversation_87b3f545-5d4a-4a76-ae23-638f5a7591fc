#!/usr/bin/env python3
"""
إنشاء بيانات مؤشرات السوق المصري (EGX30, EGX70)
Create EGX market indices data (EGX30, EGX70)
"""

import os
import sys
import logging
from datetime import datetime, timezone
from dotenv import load_dotenv
from supabase import create_client, Client
import random

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def create_market_indices():
    """إنشاء مؤشرات السوق المصري"""
    try:
        # جلب بيانات الأسهم الحقيقية لحساب المؤشرات
        logger.info("جلب بيانات الأسهم من الوقت الحقيقي...")
        result = supabase.table("stocks_realtime").select("*").execute()
        realtime_data = result.data or []
        
        if not realtime_data:
            logger.error("لا توجد بيانات حقيقية للأسهم")
            return
        
        # ترتيب الأسهم حسب القيمة السوقية (تقديرية)
        stocks_with_values = []
        for stock in realtime_data:
            if stock.get('current_price') and stock.get('volume'):
                market_value = float(stock['current_price']) * int(stock.get('volume', 0))
                stocks_with_values.append({
                    'symbol': stock['symbol'],
                    'price': float(stock['current_price']),
                    'change_percent': float(stock.get('change_percent', 0)),
                    'volume': int(stock.get('volume', 0)),
                    'market_value': market_value
                })
        
        # ترتيب حسب القيمة السوقية
        stocks_with_values.sort(key=lambda x: x['market_value'], reverse=True)
        
        # أفضل 30 سهم للـ EGX30
        top_30_stocks = stocks_with_values[:30]
          # حساب مؤشر EGX30
        if top_30_stocks:
            avg_change_30 = sum(stock['change_percent'] for stock in top_30_stocks) / len(top_30_stocks)
            total_volume_30 = sum(stock['volume'] for stock in top_30_stocks)
            # تقليل القيم لتجنب overflow
            total_value_30 = min(sum(stock['market_value'] for stock in top_30_stocks), 999999999999)  # Cap at 999B
            
            # قيمة افتراضية للمؤشر (يمكن تعديلها لاحقاً)
            egx30_base_value = 25000  # قيمة افتراضية
            egx30_current = egx30_base_value * (1 + avg_change_30 / 100)
            egx30_change = egx30_current - egx30_base_value
            
            egx30_data = {
                "symbol": "EGX30",
                "name": "EGX 30 Index",
                "name_ar": "مؤشر EGX 30",
                "current_value": round(egx30_current, 2),
                "previous_close": egx30_base_value,
                "change_amount": round(egx30_change, 2),
                "change_percent": round(avg_change_30, 2),
                "volume": total_volume_30,
                "turnover": round(total_value_30, 2),
                "constituents_count": 30,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            # إدراج/تحديث مؤشر EGX30
            supabase.table("market_indices").upsert(egx30_data).execute()
            logger.info(f"✅ تم تحديث مؤشر EGX30: {egx30_current:.2f} ({avg_change_30:+.2f}%)")
          # أفضل 70 سهم للـ EGX70
        top_70_stocks = stocks_with_values[:70]
        
        if top_70_stocks:
            avg_change_70 = sum(stock['change_percent'] for stock in top_70_stocks) / len(top_70_stocks)
            total_volume_70 = sum(stock['volume'] for stock in top_70_stocks)
            # تقليل القيم لتجنب overflow
            total_value_70 = min(sum(stock['market_value'] for stock in top_70_stocks), 999999999999)  # Cap at 999B
            
            # قيمة افتراضية للمؤشر
            egx70_base_value = 3500  # قيمة افتراضية
            egx70_current = egx70_base_value * (1 + avg_change_70 / 100)
            egx70_change = egx70_current - egx70_base_value
            
            egx70_data = {
                "symbol": "EGX70",
                "name": "EGX 70 Index",
                "name_ar": "مؤشر EGX 70",
                "current_value": round(egx70_current, 2),
                "previous_close": egx70_base_value,
                "change_amount": round(egx70_change, 2),
                "change_percent": round(avg_change_70, 2),
                "volume": total_volume_70,
                "turnover": round(total_value_70, 2),
                "constituents_count": 70,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            # إدراج/تحديث مؤشر EGX70
            supabase.table("market_indices").upsert(egx70_data).execute()
            logger.info(f"✅ تم تحديث مؤشر EGX70: {egx70_current:.2f} ({avg_change_70:+.2f}%)")
        
        # إحصائيات السوق الإجمالية
        all_stocks_count = len(stocks_with_values)
        gainers = len([s for s in stocks_with_values if s['change_percent'] > 0])
        losers = len([s for s in stocks_with_values if s['change_percent'] < 0])
        unchanged = all_stocks_count - gainers - losers
        
        logger.info(f"📊 إحصائيات السوق:")
        logger.info(f"   📈 ارتفاع: {gainers}")
        logger.info(f"   📉 انخفاض: {losers}")
        logger.info(f"   ➡️  ثبات: {unchanged}")
        logger.info(f"   📊 إجمالي: {all_stocks_count}")
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء مؤشرات السوق: {e}")

def main():
    logger.info("🚀 بدء إنشاء مؤشرات السوق المصري...")
    create_market_indices()
    logger.info("✅ تم إنشاء مؤشرات السوق بنجاح!")

if __name__ == "__main__":
    main()
