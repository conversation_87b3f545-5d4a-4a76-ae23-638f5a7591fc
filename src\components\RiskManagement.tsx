
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Calculator, TrendingDown, Shield } from 'lucide-react';

const RiskManagement = () => {
  const [portfolio, setPortfolio] = useState({
    totalValue: 100000,
    cashPosition: 20000,
    riskTolerance: 'متوسط'
  });

  const [positionSize, setPositionSize] = useState({
    stockPrice: 50,
    maxRisk: 2,
    stopLoss: 45
  });

  const calculatePositionSize = () => {
    const riskAmount = (portfolio.totalValue * positionSize.maxRisk) / 100;
    const riskPerShare = positionSize.stockPrice - positionSize.stopLoss;
    const shares = Math.floor(riskAmount / riskPerShare);
    const positionValue = shares * positionSize.stockPrice;
    
    return { shares, positionValue, riskAmount };
  };

  const position = calculatePositionSize();

  const riskMetrics = [
    { name: 'مخاطر المحفظة', value: 15.2, status: 'متوسط', color: 'bg-yellow-500' },
    { name: 'التنويع', value: 78, status: 'جيد', color: 'bg-green-500' },
    { name: 'التقلبات', value: 23.5, status: 'مرتفع', color: 'bg-red-500' },
    { name: 'نسبة شارب', value: 1.24, status: 'ممتاز', color: 'bg-blue-500' }
  ];

  return (
    <div className="space-y-6">
      {/* Risk Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {riskMetrics.map((metric, index) => (
          <Card key={index} className="hover:shadow-elevated transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-muted-foreground">{metric.name}</span>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="text-2xl font-bold mb-2">{metric.value}%</div>
              <Badge className={`${metric.color} text-white`}>
                {metric.status}
              </Badge>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Position Size Calculator */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Calculator className="h-5 w-5" />
            حاسبة حجم المركز
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium mb-2">سعر السهم</label>
              <Input
                type="number"
                value={positionSize.stockPrice}
                onChange={(e) => setPositionSize({...positionSize, stockPrice: parseFloat(e.target.value)})}
                className="text-right"
                dir="rtl"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">أقصى مخاطرة (%)</label>
              <Input
                type="number"
                value={positionSize.maxRisk}
                onChange={(e) => setPositionSize({...positionSize, maxRisk: parseFloat(e.target.value)})}
                className="text-right"
                dir="rtl"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">نقطة وقف الخسارة</label>
              <Input
                type="number"
                value={positionSize.stopLoss}
                onChange={(e) => setPositionSize({...positionSize, stopLoss: parseFloat(e.target.value)})}
                className="text-right"
                dir="rtl"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white/60 rounded-lg">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">عدد الأسهم المقترح</div>
              <div className="text-2xl font-bold text-blue-600">{position.shares}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground">قيمة المركز</div>
              <div className="text-2xl font-bold text-green-600">{position.positionValue.toLocaleString()} ج.م</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground">المخاطرة المحتملة</div>
              <div className="text-2xl font-bold text-red-600">{position.riskAmount.toLocaleString()} ج.م</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Portfolio Risk Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5" />
            توزيع المخاطر في المحفظة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { sector: 'البنوك', allocation: 35, risk: 'منخفض' },
              { sector: 'التطوير العقاري', allocation: 25, risk: 'متوسط' },
              { sector: 'الاتصالات', allocation: 20, risk: 'منخفض' },
              { sector: 'التكنولوجيا', allocation: 15, risk: 'مرتفع' },
              { sector: 'الطاقة', allocation: 5, risk: 'مرتفع' }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="font-medium">{item.sector}</span>
                  <Badge variant={item.risk === 'مرتفع' ? 'destructive' : item.risk === 'متوسط' ? 'default' : 'secondary'}>
                    {item.risk}
                  </Badge>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-muted-foreground w-16">{item.allocation}%</span>
                  <Progress value={item.allocation} className="w-24" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RiskManagement;
