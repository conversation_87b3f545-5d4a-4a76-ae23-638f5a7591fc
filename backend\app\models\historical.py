from sqlalchemy import Column, String, Numeric, DateTime, Date, ForeignKey, BigInteger
from sqlalchemy.sql import func
from ..database import Base


class StockHistorical(Base):
    """Historical stock price data model"""
    __tablename__ = "stocks_historical"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(15), ForeignKey("stocks_master.symbol"), index=True, nullable=False)
    trade_date = Column(Date, nullable=False, index=True)
    
    # OHLCV data
    open_price = Column(Numeric(15, 4))
    high_price = Column(Numeric(15, 4))
    low_price = Column(Numeric(15, 4))
    close_price = Column(Numeric(15, 4))
    volume = Column(BigInteger)
    turnover = Column(Numeric(20, 4))
    trades_count = Column(BigInteger)
    
    # Calculated fields
    change_amount = Column(Numeric(15, 4))
    change_percent = Column(Numeric(8, 4))
    adjusted_close = Column(Numeric(15, 4))
    
    # Technical indicators (if calculated)
    sma_20 = Column(Numeric(15, 4))
    sma_50 = Column(Numeric(15, 4))
    sma_200 = Column(Numeric(15, 4))
    rsi = Column(Numeric(8, 4))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Composite index for efficient querying
    __table_args__ = (
        {'schema': None}  # Default schema
    )
