import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

type Props = {
  onCreate: (data: { account_name: string; initial_balance?: number }) => void;
};

const PaperTradingAccountForm: React.FC<Props> = ({ onCreate }) => {
  const [accountName, setAccountName] = useState("");
  const [balance, setBalance] = useState("100000");

  return (
    <Card>
      <CardHeader>
        <CardTitle>إضافة حساب تجريبي</CardTitle>
      </CardHeader>
      <CardContent>
        <form
          className="flex flex-col md:flex-row gap-2 mb-2"
          onSubmit={e => {
            e.preventDefault();
            if (!accountName) return;
            onCreate({ account_name: accountName, initial_balance: Number(balance) });
            setAccountName("");
            setBalance("100000");
          }}
        >
          <Input
            placeholder="اسم الحساب"
            value={accountName}
            onChange={e => setAccountName(e.target.value)}
          />
          <Input
            placeholder="رصيد ابتدائي"
            type="number"
            value={balance}
            min={100}
            onChange={e => setBalance(e.target.value)}
          />
          <Button type="submit" disabled={!accountName}>
            إضافة
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default PaperTradingAccountForm;
