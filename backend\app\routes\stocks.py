from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_

from ..database import get_db
from ..models.stocks import StockMaster
from ..models.schemas import (
    StockResponse, StockListResponse, SectorListResponse,
    StockSearchParams, SectorInfo, StockDetail, StockBase, StockList
)

router = APIRouter()


@router.get("/", response_model=StockListResponse)
async def get_stocks(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db)
):
    """Get list of stocks with pagination and filtering"""
    try:
        # Build query
        query = db.query(StockMaster)
        
        # Apply filters
        if sector:
            query = query.filter(StockMaster.sector == sector)
        if is_active is not None:
            query = query.filter(StockMaster.is_active == is_active)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        stocks = query.offset(offset).limit(page_size).all()
        
        # Convert to response format
        stock_list = [
            StockBase(
                symbol=stock.symbol,
                name_ar=stock.name_ar,
                name_en=stock.name_en,
                sector=stock.sector
            )
            for stock in stocks
        ]
        
        # Calculate pagination info
        has_next = offset + page_size < total
        has_prev = page > 1
        
        return StockListResponse(
            success=True,
            data=StockList(
                stocks=stock_list,
                total=total,
                page=page,
                page_size=page_size,
                has_next=has_next,
                has_prev=has_prev
            )
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching stocks: {str(e)}")


@router.get("/{symbol}", response_model=StockResponse)
async def get_stock(symbol: str, db: Session = Depends(get_db)):
    """Get detailed information for a specific stock"""
    try:
        stock = db.query(StockMaster).filter(
            StockMaster.symbol == symbol.upper()
        ).first()
        
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        stock_detail = StockDetail(
            symbol=stock.symbol,
            name_ar=stock.name_ar,
            name_en=stock.name_en,
            sector=stock.sector,
            is_active=stock.is_active,
            is_suspended=stock.is_suspended,
            listing_date=stock.listing_date,
            isin=stock.isin,
            market_cap=stock.market_cap,
            shares_outstanding=stock.shares_outstanding,
            free_float=stock.free_float,
            description=stock.description,
            website=stock.website,
            phone=stock.phone,
            address=stock.address,
            created_at=stock.created_at,
            updated_at=stock.updated_at
        )
        
        return StockResponse(
            success=True,
            data=stock_detail
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching stock: {str(e)}")


@router.get("/search", response_model=StockListResponse)
async def search_stocks(
    q: Optional[str] = Query(None, description="Search query (name or symbol)"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """Search stocks by name or symbol"""
    try:
        # Build query
        query = db.query(StockMaster)
        
        # Apply search filter
        if q:
            search_term = f"%{q.upper()}%"
            query = query.filter(
                or_(
                    StockMaster.symbol.ilike(search_term),
                    StockMaster.name_ar.ilike(f"%{q}%"),
                    StockMaster.name_en.ilike(f"%{q}%")
                )
            )
        
        # Apply other filters
        if sector:
            query = query.filter(StockMaster.sector == sector)
        if is_active is not None:
            query = query.filter(StockMaster.is_active == is_active)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        stocks = query.offset(offset).limit(page_size).all()
        
        # Convert to response format
        stock_list = [
            StockBase(
                symbol=stock.symbol,
                name_ar=stock.name_ar,
                name_en=stock.name_en,
                sector=stock.sector
            )
            for stock in stocks
        ]
        
        # Calculate pagination info
        has_next = offset + page_size < total
        has_prev = page > 1
        
        return StockListResponse(
            success=True,
            data=StockList(
                stocks=stock_list,
                total=total,
                page=page,
                page_size=page_size,
                has_next=has_next,
                has_prev=has_prev
            )
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching stocks: {str(e)}")


@router.get("/sectors", response_model=SectorListResponse)
async def get_sectors(db: Session = Depends(get_db)):
    """Get list of all sectors with stock counts"""
    try:
        sectors = db.query(
            StockMaster.sector,
            func.count(StockMaster.symbol).label('stock_count')
        ).filter(
            StockMaster.sector.isnot(None),
            StockMaster.is_active == True
        ).group_by(
            StockMaster.sector
        ).order_by(
            StockMaster.sector
        ).all()
        
        sector_list = [
            SectorInfo(sector=sector, stock_count=count)
            for sector, count in sectors
        ]
        
        return SectorListResponse(
            success=True,
            data=sector_list
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching sectors: {str(e)}")


@router.get("/sectors/{sector}/stocks", response_model=StockListResponse)
async def get_stocks_by_sector(
    sector: str,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db)
):
    """Get stocks in a specific sector"""
    try:
        # Build query
        query = db.query(StockMaster).filter(StockMaster.sector == sector)
        
        # Apply filters
        if is_active is not None:
            query = query.filter(StockMaster.is_active == is_active)
        
        # Get total count
        total = query.count()
        
        if total == 0:
            raise HTTPException(status_code=404, detail=f"Sector '{sector}' not found or has no stocks")
        
        # Apply pagination
        offset = (page - 1) * page_size
        stocks = query.offset(offset).limit(page_size).all()
        
        # Convert to response format
        stock_list = [
            StockBase(
                symbol=stock.symbol,
                name_ar=stock.name_ar,
                name_en=stock.name_en,
                sector=stock.sector
            )
            for stock in stocks
        ]
        
        # Calculate pagination info
        has_next = offset + page_size < total
        has_prev = page > 1
        
        return StockListResponse(
            success=True,
            data=StockList(
                stocks=stock_list,
                total=total,
                page=page,
                page_size=page_size,
                has_next=has_next,
                has_prev=has_prev
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching stocks by sector: {str(e)}")
