 

/* eslint-disable @typescript-eslint/no-explicit-any */

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface MlPrediction {
  id: string;
  symbol: string;
  prediction: string;
  confidence: number;
  target_price: number;
  timeframe: string;
  factors: string[];
  generated_at: string;
}

export function useMlPredictions() {
  return useQuery({
    queryKey: ['analytics_ml_predictions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('analytics_ml_predictions')
        .select('*')
        .order('generated_at', { ascending: false });
      if (error) throw error;
      // factors column may be null or parsed as array
      return (data ?? []).map((d: any) => ({
        ...d,
        factors: Array.isArray(d.factors) ? d.factors : [],
      })) as MlPrediction[];
    },
  });
}
