import React from 'react';
import { cn } from '@/lib/utils';

interface MiniChartProps {
  data: number[];
  width?: number;
  height?: number;
  color?: 'green' | 'red' | 'blue' | 'gray';
  type?: 'line' | 'area';
  className?: string;
  showDots?: boolean;
}

const MiniChart: React.FC<MiniChartProps> = ({
  data,
  width = 80,
  height = 30,
  color = 'blue',
  type = 'line',
  className,
  showDots = false
}) => {
  if (!data || data.length < 2) {
    return (
      <div 
        className={cn("flex items-center justify-center text-gray-400", className)}
        style={{ width: `${width}px`, height: `${height}px` }}
      >
        <span className="text-xs">--</span>
      </div>
    );
  }
  const min = Math.min(...data);
  const max = Math.max(...data);
  
  // تحسين المدى لإظهار التفاصيل بشكل أفضل
  const range = max - min;
  
  // إضافة تنويع بصري للبيانات المتشابهة
  const addVisualVariation = (originalData: number[]): number[] => {
    if (range < max * 0.002) { // إذا كانت البيانات متشابهة جداً (أقل من 0.2%)
      return originalData.map((value, index) => {
        // إضافة تنويع أكبر لجعل الرسم أكثر وضوحاً
        const waveVariation = Math.sin(index * 0.8) * max * 0.01; // موجة أساسية
        const randomVariation = (Math.random() - 0.5) * max * 0.008; // عشوائية
        const trendVariation = (index / originalData.length) * max * 0.005; // اتجاه خفيف
        return value + waveVariation + randomVariation + trendVariation;
      });
    }
    return originalData;
  };

  const enhancedData = addVisualVariation(data);
  
  // إعادة حساب القيم مع البيانات المحسنة
  const enhancedMin_calc = Math.min(...enhancedData);
  const enhancedMax_calc = Math.max(...enhancedData);
  const enhancedRange_calc = enhancedMax_calc - enhancedMin_calc;
  const enhancedRange_final = enhancedRange_calc === 0 ? enhancedMax_calc * 0.05 : Math.max(enhancedRange_calc, enhancedMax_calc * 0.03);
  const finalMin = enhancedMin_calc - enhancedRange_final * 0.2;
  const finalMax = enhancedMax_calc + enhancedRange_final * 0.2;

  // حساب النقاط مع البيانات المحسنة
  const points = enhancedData.map((value, index) => {
    const x = (index / (enhancedData.length - 1)) * width;
    const y = height - ((value - finalMin) / (finalMax - finalMin)) * height;
    return { x, y, value };
  });

  // إنشاء مسار SVG منحني (Smooth curve)
  const createSmoothPath = () => {
    if (points.length === 0) return '';
    if (points.length === 1) return `M ${points[0].x} ${points[0].y}`;
    
    let path = `M ${points[0].x} ${points[0].y}`;
    
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      
      if (i === 1) {
        // أول منحنى
        const cp1x = prev.x + (curr.x - prev.x) * 0.3;
        const cp1y = prev.y;
        const cp2x = curr.x - (curr.x - prev.x) * 0.3;
        const cp2y = curr.y;
        path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${curr.x} ${curr.y}`;
      } else {
        // منحنيات متتالية
        const prevPrev = points[i - 2];
        const cp1x = prev.x + (curr.x - prevPrev.x) * 0.15;
        const cp1y = prev.y;
        const cp2x = curr.x - (curr.x - prev.x) * 0.3;
        const cp2y = curr.y;
        path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${curr.x} ${curr.y}`;
      }
    }
    
    return path;
  };
  // إنشاء مسار المنطقة (للنوع area)
  const createAreaPath = () => {
    if (points.length === 0) return '';
    
    const linePath = createSmoothPath();
    const firstPoint = points[0];
    const lastPoint = points[points.length - 1];
    
    return `${linePath} L ${lastPoint.x} ${height} L ${firstPoint.x} ${height} Z`;
  };

  // ألوان مختلفة حسب النوع
  const colorClasses = {
    green: {
      stroke: 'stroke-green-500',
      fill: 'fill-green-100',
      dots: 'fill-green-600',
      gradient: 'from-green-400 to-green-100'
    },
    red: {
      stroke: 'stroke-red-500',
      fill: 'fill-red-100',
      dots: 'fill-red-600',
      gradient: 'from-red-400 to-red-100'
    },
    blue: {
      stroke: 'stroke-blue-500',
      fill: 'fill-blue-100',
      dots: 'fill-blue-600',
      gradient: 'from-blue-400 to-blue-100'
    },
    gray: {
      stroke: 'stroke-gray-400',
      fill: 'fill-gray-100',
      dots: 'fill-gray-500',
      gradient: 'from-gray-400 to-gray-100'
    }
  };

  const colors = colorClasses[color];
  // تحديد اتجاه التغيير مع تحسين الحساسية
  const trend = enhancedData.length >= 2 ? 
    (enhancedData[enhancedData.length - 1] > enhancedData[0] * 1.001 ? 'up' : 
     enhancedData[enhancedData.length - 1] < enhancedData[0] * 0.999 ? 'down' : 'neutral') : 'neutral';

  return (
    <div className={cn("relative", className)} style={{ width: `${width}px`, height: `${height}px` }}>
      <svg
        width={width}
        height={height}
        className="overflow-visible"
      >
        {/* المنطقة المملوءة للنوع area */}
        {type === 'area' && (
          <path
            d={createAreaPath()}
            className={cn(colors.fill, "opacity-30")}
            stroke="none"
          />
        )}
          {/* الخط */}
        <path
          d={createSmoothPath()}
          fill="none"
          className={cn(colors.stroke)}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* النقاط (اختيارية) */}
        {showDots && points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="1.5"
            className={colors.dots}
          />
        ))}
        
        {/* نقطة آخر قيمة */}
        <circle
          cx={points[points.length - 1]?.x || 0}
          cy={points[points.length - 1]?.y || 0}
          r="2"
          className={colors.dots}
        />
      </svg>
      
      {/* مؤشر الاتجاه */}
      <div className={cn(
        "absolute -top-1 -right-1 w-3 h-3 rounded-full flex items-center justify-center text-xs",
        trend === 'up' ? 'bg-green-500 text-white' :
        trend === 'down' ? 'bg-red-500 text-white' :
        'bg-gray-400 text-white'
      )}>
        {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'}
      </div>
    </div>
  );
};

// مكون خاص للرسم البياني للسعر
export const PriceMiniChart: React.FC<{
  priceHistory: number[];
  currentPrice: number;
  changePercent: number;
  className?: string;
}> = ({ priceHistory, currentPrice, changePercent, className }) => {
  const color = changePercent > 0 ? 'green' : changePercent < 0 ? 'red' : 'gray';
  
  return (
    <div className={cn("flex flex-col items-center", className)}>
      <MiniChart
        data={priceHistory}
        color={color}
        type="area"
        width={60}
        height={25}
      />
      <div className={cn(
        "text-xs mt-1",
        changePercent > 0 ? 'text-green-600' :
        changePercent < 0 ? 'text-red-600' :
        'text-gray-600'
      )}>
        {currentPrice.toFixed(2)}
      </div>
    </div>
  );
};

// مكون خاص للرسم البياني للحجم
export const VolumeMiniChart: React.FC<{
  volumeHistory: number[];
  currentVolume: number;
  className?: string;
}> = ({ volumeHistory, currentVolume, className }) => {
  const formatVolume = (vol: number) => {
    if (vol >= 1000000) return `${(vol / 1000000).toFixed(1)}M`;
    if (vol >= 1000) return `${(vol / 1000).toFixed(1)}K`;
    return vol.toString();
  };

  return (
    <div className={cn("flex flex-col items-center", className)}>
      <MiniChart
        data={volumeHistory}
        color="blue"
        type="line"
        width={60}
        height={25}
      />
      <div className="text-xs text-blue-600 mt-1">
        {formatVolume(currentVolume)}
      </div>
    </div>
  );
};

// مكون مدمج للسعر والحجم
export const CombinedMiniChart: React.FC<{
  priceHistory: number[];
  volumeHistory: number[];
  currentPrice: number;
  currentVolume: number;
  changePercent: number;
  className?: string;
}> = ({ 
  priceHistory, 
  volumeHistory, 
  currentPrice, 
  currentVolume, 
  changePercent, 
  className 
}) => {
  return (
    <div className={cn("flex gap-2", className)}>
      <PriceMiniChart
        priceHistory={priceHistory}
        currentPrice={currentPrice}
        changePercent={changePercent}
      />
      <VolumeMiniChart
        volumeHistory={volumeHistory}
        currentVolume={currentVolume}
      />
    </div>
  );
};

export default MiniChart;
