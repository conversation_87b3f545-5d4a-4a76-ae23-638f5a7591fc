
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Zap } from 'lucide-react';
import { useMlPredictions } from '@/hooks/useMlPredictions';

const MlPredictionsSection = () => {
  const { data: mlPredictionsData, isLoading, error } = useMlPredictions();

  return (
    <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-800">
          <Zap className="h-5 w-5" />
          توقعات التعلم الآلي
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center text-green-800">جاري التحميل...</div>
        ) : error ? (
          <div className="text-center text-red-600">حدث خطأ في التحميل</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(mlPredictionsData ?? []).map((prediction, index) => (
              <div key={prediction.id ?? index} className="p-4 bg-white/60 rounded-lg border border-green-200">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-bold text-lg">{prediction.symbol}</span>
                  <Badge className={`${
                    prediction.prediction.includes('صعود') ? 'bg-green-500' : 
                    prediction.prediction === 'محايد' ? 'bg-yellow-500' : 'bg-red-500'
                  } text-white`}>
                    {prediction.prediction}
                  </Badge>
                </div>
                <div className="space-y-2 mb-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">الثقة</span>
                    <span className="font-medium">{prediction.confidence}%</span>
                  </div>
                  <Progress value={prediction.confidence} className="h-2" />
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">السعر المستهدف</span>
                    <span className="font-medium">{prediction.target_price} ج.م</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">الإطار الزمني</span>
                    <span className="font-medium">{prediction.timeframe}</span>
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium mb-2">العوامل المؤثرة:</div>
                  <div className="space-y-1">
                    {(prediction.factors ?? []).map((factor: string, factorIndex: number) => (
                      <div key={factorIndex} className="text-xs bg-white/80 p-2 rounded">
                        • {factor}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MlPredictionsSection;
