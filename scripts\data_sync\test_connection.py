#!/usr/bin/env python3
"""
Test Supabase Connection
Simple script to test database connectivity
"""

import os
import sys
import requests
import time
from urllib.parse import urlparse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

def test_network_connectivity():
    """Test basic network connectivity"""
    print("🌐 Testing network connectivity...")
    
    if not SUPABASE_URL:
        print("❌ SUPABASE_URL not found in environment variables")
        return False
    
    try:
        # Parse URL to get hostname
        parsed_url = urlparse(SUPABASE_URL)
        hostname = parsed_url.hostname
        print(f"📡 Testing connection to: {hostname}")
        
        # Test HTTP connection
        response = requests.get(f"{SUPABASE_URL}/rest/v1/", 
                              headers={"apikey": SUPABASE_SERVICE_ROLE_KEY},
                              timeout=10)
        
        print(f"✅ HTTP connection successful! Status: {response.status_code}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Network connectivity test failed: {e}")
        return False

def test_supabase_connection():
    """Test Supabase client connection"""
    print("\n🔌 Testing Supabase client connection...")
    
    try:
        from supabase import create_client, Client
        
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
        
        # Test a simple query
        result = supabase.table('stocks_realtime').select('symbol').limit(1).execute()
        
        print(f"✅ Supabase connection successful! Found {len(result.data)} record(s)")
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Database Connection Test")
    print("=" * 50)
    
    # Test basic network connectivity first
    if not test_network_connectivity():
        print("\n💡 Network connectivity issues detected.")
        print("   Try the following:")
        print("   1. Check your internet connection")
        print("   2. Try running from a different network")
        print("   3. Check if you're behind a corporate firewall")
        return
    
    # Test Supabase connection
    if not test_supabase_connection():
        print("\n💡 Supabase connection issues detected.")
        print("   This might be due to:")
        print("   1. Incorrect credentials")
        print("   2. Network/firewall restrictions")
        print("   3. Supabase service issues")
        return
    
    print("\n🎉 All connection tests passed!")
    print("   Your database connection is working properly.")

if __name__ == "__main__":
    main()
