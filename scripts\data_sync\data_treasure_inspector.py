#!/usr/bin/env python3
"""
أداة فحص كنز البيانات المصري
Egyptian Stock Market Data Treasure Inspector
"""

import os
import pandas as pd
from pathlib import Path
from datetime import datetime
import glob

def analyze_historical_data():
    """تحليل البيانات التاريخية في مجلد meta2"""
    print("🔍 فحص البيانات التاريخية...")
    
    meta2_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2"
    
    if not os.path.exists(meta2_path):
        print(f"❌ المجلد غير موجود: {meta2_path}")
        return
    
    # عد ملفات .TXT
    txt_files = glob.glob(os.path.join(meta2_path, "*.TXT"))
    print(f"📊 عدد ملفات البيانات التاريخية: {len(txt_files)}")
    
    # تحليل عينة من الملفات
    total_records = 0
    sample_files = txt_files[:5]  # أول 5 ملفات كعينة
    
    for txt_file in sample_files:
        try:
            # عد السطور
            with open(txt_file, 'r') as f:
                lines = len(f.readlines()) - 1  # -1 للـ header
            
            symbol = os.path.basename(txt_file).replace('D.TXT', '')
            print(f"   📈 {symbol}: {lines:,} سجل")
            total_records += lines
            
        except Exception as e:
            print(f"   ❌ خطأ في قراءة {txt_file}: {e}")
    
    # تقدير إجمالي البيانات
    avg_records_per_file = total_records / len(sample_files) if sample_files else 0
    estimated_total = int(avg_records_per_file * len(txt_files))
    
    print(f"📊 متوسط السجلات لكل ملف: {avg_records_per_file:,.0f}")
    print(f"📊 إجمالي السجلات المقدر: {estimated_total:,}")
    
    return {
        'files_count': len(txt_files),
        'estimated_records': estimated_total,
        'avg_records_per_file': avg_records_per_file
    }

def analyze_realtime_data():
    """تحليل ملف البيانات الحية Excel"""
    print("\n🔍 فحص البيانات الحية...")
    
    excel_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx"
    
    if not os.path.exists(excel_path):
        print(f"❌ ملف Excel غير موجود: {excel_path}")
        return
    
    try:
        # معلومات الملف
        file_stats = os.stat(excel_path)
        file_size = file_stats.st_size / 1024  # KB
        mod_time = datetime.fromtimestamp(file_stats.st_mtime)
        
        print(f"📊 حجم الملف: {file_size:.1f} KB")
        print(f"📅 آخر تحديث: {mod_time}")
        
        # قراءة Excel (أول 5 صفوف فقط للفحص)
        df = pd.read_excel(excel_path, nrows=5)
        
        print(f"📊 عدد الأعمدة: {len(df.columns)}")
        print(f"📊 عينة من الأعمدة:")
        for i, col in enumerate(df.columns[:10]):
            print(f"   {i+1:2d}. {col}")
        if len(df.columns) > 10:
            print(f"   ... و {len(df.columns)-10} عمود آخر")
        
        return {
            'file_size_kb': file_size,
            'columns_count': len(df.columns),
            'last_modified': mod_time
        }
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف Excel: {e}")

def analyze_financial_data():
    """تحليل ملف البيانات المالية CSV"""
    print("\n🔍 فحص البيانات المالية...")
    
    csv_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv"
    
    if not os.path.exists(csv_path):
        print(f"❌ ملف CSV غير موجود: {csv_path}")
        return
    
    try:
        # معلومات الملف
        file_stats = os.stat(csv_path)
        file_size = file_stats.st_size / 1024  # KB
        
        print(f"📊 حجم الملف: {file_size:.1f} KB")
        
        # قراءة CSV (أول 5 صفوف فقط للفحص)
        df = pd.read_csv(csv_path, nrows=5)
        
        print(f"📊 عدد الأعمدة: {len(df.columns)}")
        print(f"📊 عينة من الأعمدة:")
        for i, col in enumerate(df.columns[:10]):
            print(f"   {i+1:2d}. {col}")
        if len(df.columns) > 10:
            print(f"   ... و {len(df.columns)-10} عمود آخر")
        
        return {
            'file_size_kb': file_size,
            'columns_count': len(df.columns)
        }
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف CSV: {e}")

def calculate_database_requirements():
    """حساب متطلبات قاعدة البيانات"""
    print("\n💾 حساب متطلبات قاعدة البيانات...")
    
    # تقديرات تقريبية
    historical_records = 642000  # من التحليل السابق
    record_size_bytes = 50  # تقدير متوسط حجم السجل
    
    historical_data_size = historical_records * record_size_bytes / (1024 * 1024)  # MB
    
    # تقدير حجم الفهارس (عادة 20-30% من حجم البيانات)
    indexes_size = historical_data_size * 0.25
    
    # مساحة إضافية للنمو المستقبلي
    growth_space = historical_data_size * 0.5
    
    total_size = historical_data_size + indexes_size + growth_space
    
    print(f"📊 البيانات التاريخية: {historical_data_size:.1f} MB")
    print(f"📊 الفهارس: {indexes_size:.1f} MB") 
    print(f"📊 مساحة النمو: {growth_space:.1f} MB")
    print(f"📊 إجمالي المساحة المطلوبة: {total_size:.1f} MB ({total_size/1024:.1f} GB)")
    
    # تقدير RAM المطلوب
    recommended_ram = max(4, total_size / 1024 * 2)  # على الأقل 4GB أو ضعف حجم البيانات
    print(f"💾 RAM مُوصى به: {recommended_ram:.0f} GB")

def main():
    """الدالة الرئيسية"""
    print("🏛️ مفتش كنز البيانات المصري")
    print("=" * 50)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تحليل مصادر البيانات
    historical_stats = analyze_historical_data()
    realtime_stats = analyze_realtime_data() 
    financial_stats = analyze_financial_data()
    
    # حساب متطلبات قاعدة البيانات
    calculate_database_requirements()
    
    # ملخص نهائي
    print("\n" + "=" * 50)
    print("📋 ملخص كنز البيانات:")
    print("=" * 50)
    
    if historical_stats:
        print(f"📈 البيانات التاريخية: {historical_stats['files_count']} سهم")
        print(f"📊 إجمالي السجلات: {historical_stats['estimated_records']:,}")
    
    if realtime_stats:
        print(f"📡 البيانات الحية: {realtime_stats['columns_count']} مؤشر")
        
    if financial_stats:
        print(f"💰 البيانات المالية: {financial_stats['columns_count']} مؤشر مالي")
    
    print("\n🎯 هذا كنز حقيقي من البيانات المالية!")
    print("💡 يمكن بناء منصة تحليل مالي عملاقة من هذه البيانات!")

if __name__ == "__main__":
    main()
