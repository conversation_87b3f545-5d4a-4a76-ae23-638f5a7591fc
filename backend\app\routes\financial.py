from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func
from datetime import datetime, date
from decimal import Decimal

from ..database import get_db
from ..models.financial import StockFinancial
from ..models.stocks import StockMaster
from ..models.schemas import ResponseBase
from pydantic import BaseModel, Field

# Pydantic models for financial data
class FinancialBase(BaseModel):
    """Base financial data model"""
    symbol: str = Field(..., description="Stock symbol")
    report_date: date = Field(..., description="Report date")
    report_period: Optional[str] = Field(None, description="Report period (Q1, Q2, Q3, Q4, Annual)")
    report_year: int = Field(..., description="Report year")
    currency: str = Field(default="EGP", description="Currency")
    
    class Config:
        from_attributes = True


class FinancialDetail(FinancialBase):
    """Detailed financial information"""
    # Income Statement
    revenue: Optional[Decimal] = Field(None, description="Revenue")
    gross_profit: Optional[Decimal] = Field(None, description="Gross profit")
    operating_profit: Optional[Decimal] = Field(None, description="Operating profit")
    net_profit: Optional[Decimal] = Field(None, description="Net profit")
    ebitda: Optional[Decimal] = Field(None, description="EBITDA")
    
    # Balance Sheet
    total_assets: Optional[Decimal] = Field(None, description="Total assets")
    total_liabilities: Optional[Decimal] = Field(None, description="Total liabilities")
    shareholders_equity: Optional[Decimal] = Field(None, description="Shareholders equity")
    cash_and_equivalents: Optional[Decimal] = Field(None, description="Cash and equivalents")
    total_debt: Optional[Decimal] = Field(None, description="Total debt")
    
    # Cash Flow
    operating_cash_flow: Optional[Decimal] = Field(None, description="Operating cash flow")
    investing_cash_flow: Optional[Decimal] = Field(None, description="Investing cash flow")
    financing_cash_flow: Optional[Decimal] = Field(None, description="Financing cash flow")
    free_cash_flow: Optional[Decimal] = Field(None, description="Free cash flow")
    
    # Per Share Data
    earnings_per_share: Optional[Decimal] = Field(None, description="Earnings per share")
    book_value_per_share: Optional[Decimal] = Field(None, description="Book value per share")
    dividends_per_share: Optional[Decimal] = Field(None, description="Dividends per share")
    shares_outstanding: Optional[Decimal] = Field(None, description="Shares outstanding")
    
    # Financial Ratios
    pe_ratio: Optional[Decimal] = Field(None, description="P/E ratio")
    pb_ratio: Optional[Decimal] = Field(None, description="P/B ratio")
    roe: Optional[Decimal] = Field(None, description="Return on Equity")
    roa: Optional[Decimal] = Field(None, description="Return on Assets")
    debt_to_equity: Optional[Decimal] = Field(None, description="Debt to Equity ratio")
    current_ratio: Optional[Decimal] = Field(None, description="Current ratio")
    quick_ratio: Optional[Decimal] = Field(None, description="Quick ratio")
    gross_margin: Optional[Decimal] = Field(None, description="Gross margin")
    operating_margin: Optional[Decimal] = Field(None, description="Operating margin")
    net_margin: Optional[Decimal] = Field(None, description="Net margin")
    
    # Additional metrics
    market_cap: Optional[Decimal] = Field(None, description="Market capitalization")
    enterprise_value: Optional[Decimal] = Field(None, description="Enterprise value")
    dividend_yield: Optional[Decimal] = Field(None, description="Dividend yield")
    payout_ratio: Optional[Decimal] = Field(None, description="Payout ratio")
    
    notes: Optional[str] = Field(None, description="Additional notes")
    created_at: Optional[datetime] = Field(None, description="Record creation time")
    updated_at: Optional[datetime] = Field(None, description="Record update time")


class FinancialListResponse(ResponseBase):
    """Financial data list response"""
    data: Optional[List[FinancialDetail]] = None
    total: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None


class FinancialResponse(ResponseBase):
    """Single financial data response"""
    data: Optional[FinancialDetail] = None


class FinancialSummary(BaseModel):
    """Financial summary for a stock"""
    symbol: str
    latest_report_date: Optional[date] = None
    latest_revenue: Optional[Decimal] = None
    latest_net_profit: Optional[Decimal] = None
    latest_pe_ratio: Optional[Decimal] = None
    latest_roe: Optional[Decimal] = None
    reports_count: int = 0
    
    class Config:
        from_attributes = True


# Create router
router = APIRouter()


@router.get("/", response_model=FinancialListResponse)
async def get_financial_data(
    symbol: Optional[str] = Query(None, description="Filter by stock symbol"),
    report_period: Optional[str] = Query(None, description="Filter by report period"),
    year: Optional[int] = Query(None, description="Filter by year"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """Get financial data with optional filters and pagination"""
    try:
        # Build query
        query = db.query(StockFinancial)
        
        # Apply filters
        if symbol:
            query = query.filter(StockFinancial.symbol == symbol.upper())
        if report_period:
            query = query.filter(StockFinancial.report_period == report_period)
        if year:
            query = query.filter(StockFinancial.report_year == year)
        
        # Get total count
        total = query.count()
        
        # Apply pagination and ordering
        financials = query.order_by(
            desc(StockFinancial.report_date)
        ).offset((page - 1) * page_size).limit(page_size).all()
        
        return FinancialListResponse(
            success=True,
            message=f"Retrieved {len(financials)} financial records",
            data=financials,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving financial data: {str(e)}")


@router.get("/{symbol}", response_model=FinancialListResponse)
async def get_stock_financials(
    symbol: str,
    report_period: Optional[str] = Query(None, description="Filter by report period"),
    year: Optional[int] = Query(None, description="Filter by year"),
    limit: int = Query(10, ge=1, le=50, description="Maximum records to return"),
    db: Session = Depends(get_db)
):
    """Get financial data for a specific stock"""
    try:
        # Verify stock exists
        stock = db.query(StockMaster).filter(StockMaster.symbol == symbol.upper()).first()
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        # Build query
        query = db.query(StockFinancial).filter(StockFinancial.symbol == symbol.upper())
        
        # Apply filters
        if report_period:
            query = query.filter(StockFinancial.report_period == report_period)
        if year:
            query = query.filter(StockFinancial.report_year == year)
        
        # Get financial data
        financials = query.order_by(
            desc(StockFinancial.report_date)
        ).limit(limit).all()
        
        return FinancialListResponse(
            success=True,
            message=f"Retrieved {len(financials)} financial records for {symbol}",
            data=financials,
            total=len(financials),
            page=1,
            page_size=limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving financial data for {symbol}: {str(e)}")


@router.get("/{symbol}/latest", response_model=FinancialResponse)
async def get_latest_financials(
    symbol: str,
    report_period: Optional[str] = Query(None, description="Filter by report period"),
    db: Session = Depends(get_db)
):
    """Get the latest financial data for a specific stock"""
    try:
        # Verify stock exists
        stock = db.query(StockMaster).filter(StockMaster.symbol == symbol.upper()).first()
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        # Build query
        query = db.query(StockFinancial).filter(StockFinancial.symbol == symbol.upper())
        
        # Apply period filter if specified
        if report_period:
            query = query.filter(StockFinancial.report_period == report_period)
        
        # Get latest financial data
        financial = query.order_by(desc(StockFinancial.report_date)).first()
        
        if not financial:
            raise HTTPException(status_code=404, detail=f"No financial data found for {symbol}")
        
        return FinancialResponse(
            success=True,
            message=f"Retrieved latest financial data for {symbol}",
            data=financial
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving latest financial data for {symbol}: {str(e)}")


@router.get("/{symbol}/summary", response_model=ResponseBase)
async def get_financial_summary(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Get financial summary for a specific stock"""
    try:
        # Verify stock exists
        stock = db.query(StockMaster).filter(StockMaster.symbol == symbol.upper()).first()
        if not stock:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        
        # Get financial summary
        query = db.query(StockFinancial).filter(StockFinancial.symbol == symbol.upper())
        
        # Get latest financial data
        latest = query.order_by(desc(StockFinancial.report_date)).first()
        
        # Get count of reports
        reports_count = query.count()
        
        summary = FinancialSummary(
            symbol=symbol.upper(),
            latest_report_date=latest.report_date if latest else None,
            latest_revenue=latest.revenue if latest else None,
            latest_net_profit=latest.net_profit if latest else None,
            latest_pe_ratio=latest.pe_ratio if latest else None,
            latest_roe=latest.roe if latest else None,
            reports_count=reports_count
        )
        
        return ResponseBase(
            success=True,
            message=f"Retrieved financial summary for {symbol}",
            data=summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving financial summary for {symbol}: {str(e)}")


@router.get("/periods/available", response_model=ResponseBase)
async def get_available_periods(
    symbol: Optional[str] = Query(None, description="Filter by stock symbol"),
    db: Session = Depends(get_db)
):
    """Get available report periods"""
    try:
        query = db.query(StockFinancial.report_period, StockFinancial.report_year)
        
        if symbol:
            query = query.filter(StockFinancial.symbol == symbol.upper())
        
        periods = query.distinct().order_by(
            desc(StockFinancial.report_year),
            StockFinancial.report_period
        ).all()
        
        period_list = [{"period": p.report_period, "year": p.report_year} for p in periods]
        
        return ResponseBase(
            success=True,
            message=f"Retrieved {len(period_list)} available periods",
            data=period_list
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving available periods: {str(e)}")


@router.get("/ratios/comparison", response_model=ResponseBase)
async def get_financial_ratios_comparison(
    symbols: str = Query(..., description="Comma-separated list of stock symbols"),
    metric: str = Query("pe_ratio", description="Financial metric to compare"),
    period: Optional[str] = Query(None, description="Report period filter"),
    db: Session = Depends(get_db)
):
    """Compare financial ratios across multiple stocks"""
    try:
        symbol_list = [s.strip().upper() for s in symbols.split(",")]
        
        if len(symbol_list) > 10:
            raise HTTPException(status_code=400, detail="Maximum 10 stocks allowed for comparison")
        
        # Validate metric
        valid_metrics = [
            'pe_ratio', 'pb_ratio', 'roe', 'roa', 'debt_to_equity',
            'current_ratio', 'quick_ratio', 'gross_margin', 'operating_margin',
            'net_margin', 'dividend_yield', 'payout_ratio'
        ]
        
        if metric not in valid_metrics:
            raise HTTPException(status_code=400, detail=f"Invalid metric. Valid options: {', '.join(valid_metrics)}")
        
        # Build query
        query = db.query(StockFinancial).filter(StockFinancial.symbol.in_(symbol_list))
        
        if period:
            query = query.filter(StockFinancial.report_period == period)
        
        # Get latest data for each stock
        results = []
        for symbol in symbol_list:
            latest = query.filter(StockFinancial.symbol == symbol).order_by(
                desc(StockFinancial.report_date)
            ).first()
            
            if latest:
                value = getattr(latest, metric, None)
                results.append({
                    "symbol": symbol,
                    "metric": metric,
                    "value": float(value) if value is not None else None,
                    "report_date": latest.report_date.isoformat() if latest.report_date else None,
                    "report_period": latest.report_period
                })
        
        return ResponseBase(
            success=True,
            message=f"Retrieved {metric} comparison for {len(results)} stocks",
            data=results
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving financial ratios comparison: {str(e)}")
