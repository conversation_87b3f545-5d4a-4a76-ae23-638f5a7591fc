
import pandas as pd
from supabase import create_client, Client
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
import sys

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_data_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
CSV_PATH = os.getenv("FINANCIAL_CSV_PATH", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def safe_float_convert(value, default=None):
    """Safely convert value to float"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A':
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int_convert(value, default=None):
    """Safely convert value to int"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A':
            return default
        return int(float(value))
    except (ValueError, TypeError):
        return default

def process_financial_data():
    """Process and sync financial data from CSV file"""
    try:
        if not os.path.exists(CSV_PATH):
            logger.error(f"CSV file not found: {CSV_PATH}")
            return
        
        logger.info(f"Reading CSV file: {CSV_PATH}")
        
        # Read CSV with error handling
        try:
            df = pd.read_csv(CSV_PATH, encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv(CSV_PATH, encoding='latin-1')
            except:
                df = pd.read_csv(CSV_PATH, encoding='cp1256')  # Arabic encoding
        
        if df.empty:
            logger.warning("CSV file is empty")
            return
        
        logger.info(f"Loaded {len(df)} rows from CSV file")
        logger.info(f"Available columns: {list(df.columns)}")
        
        processed_count = 0
        batch_payload = []
        
        for index, row in df.iterrows():
            try:
                # Get symbol with various possible column names
                symbol_cols = ["Symbol", "symbol", "Stock", "Code", "الرمز", "رمز"]
                symbol = None
                for col in symbol_cols:
                    if col in row.index and pd.notna(row[col]):
                        symbol = str(row[col]).strip()
                        break
                
                if not symbol:
                    logger.warning(f"No symbol found in row {index}")
                    continue
                
                # Map CSV columns to database fields with multiple possible names
                column_mappings = {
                    'market_cap': [
                        "Market capitalization", "Market Cap", "market_cap", "Market_Cap",
                        "القيمة السوقية", "رأس المال السوقي"
                    ],
                    'pe_ratio': [
                        "Price to earnings ratio", "P/E Ratio", "pe_ratio", "PE_Ratio",
                        "نسبة السعر للأرباح", "مضاعف الربحية"
                    ],
                    'eps_ttm': [
                        "Earnings per share diluted, Trailing 12 months", "EPS TTM", "eps_ttm",
                        "Earnings per share", "EPS", "ربحية السهم"
                    ],
                    'dividend_yield': [
                        "Dividend yield %, Trailing 12 months", "Dividend Yield", "dividend_yield",
                        "عائد التوزيعات", "نسبة التوزيعات"
                    ],
                    'book_value_per_share': [
                        "Book value per share", "Book Value", "book_value", "BVPS",
                        "القيمة الدفترية للسهم"
                    ],
                    'price_to_book': [
                        "Price to book ratio", "P/B Ratio", "price_to_book", "PB_Ratio",
                        "نسبة السعر للقيمة الدفترية"
                    ],
                    'revenue_ttm': [
                        "Revenue TTM", "Total Revenue", "revenue", "Sales",
                        "الإيرادات", "المبيعات"
                    ],
                    'net_income_ttm': [
                        "Net income TTM", "Net Income", "net_income", "Profit",
                        "صافي الربح", "الأرباح الصافية"
                    ],
                    'total_assets': [
                        "Total assets", "Assets", "total_assets", "إجمالي الأصول"
                    ],
                    'total_debt': [
                        "Total debt", "Debt", "total_debt", "إجمالي الديون"
                    ],
                    'free_cash_flow': [
                        "Free cash flow", "FCF", "free_cash_flow", "التدفق النقدي الحر"
                    ],
                    'roe': [
                        "Return on equity", "ROE", "roe", "العائد على حقوق الملكية"
                    ],
                    'roa': [
                        "Return on assets", "ROA", "roa", "العائد على الأصول"
                    ],
                    'debt_to_equity': [
                        "Debt to equity", "D/E", "debt_to_equity", "نسبة الديون لحقوق الملكية"
                    ],
                    'current_ratio': [
                        "Current ratio", "current_ratio", "النسبة الحالية"
                    ],
                    'gross_margin': [
                        "Gross margin", "gross_margin", "هامش الربح الإجمالي"
                    ],
                    'operating_margin': [
                        "Operating margin", "operating_margin", "هامش التشغيل"
                    ],
                    'net_margin': [
                        "Net margin", "net_margin", "هامش الربح الصافي"
                    ],
                    'beta': [
                        "Beta", "beta", "بيتا"
                    ],
                    'target_price': [
                        "Target price", "target_price", "Price Target", "سعر الهدف"
                    ]
                }
                
                # Build payload with mapped values
                payload = {
                    "symbol": symbol,
                    "fiscal_year": datetime.now().year,  # Default to current year
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                # Map all financial metrics
                for db_field, possible_cols in column_mappings.items():
                    value = None
                    for col in possible_cols:
                        if col in row.index and pd.notna(row[col]):
                            if 'ratio' in db_field or 'margin' in db_field or 'yield' in db_field:
                                # Convert percentages
                                value = safe_float_convert(row[col])
                                if value and value > 1:  # Convert from percentage to decimal
                                    value = value / 100
                            else:
                                value = safe_float_convert(row[col])
                            break
                    
                    payload[db_field] = value
                
                # Special handling for analyst rating
                rating_cols = ["Analyst Rating", "Rating", "analyst_rating", "التقييم"]
                for col in rating_cols:
                    if col in row.index and pd.notna(row[col]):
                        payload["analyst_rating"] = str(row[col]).strip()
                        break
                
                batch_payload.append(payload)
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Error processing row {index} for symbol {symbol}: {e}")
                continue
        
        # Batch insert/upsert
        if batch_payload:
            try:
                batch_size = 50
                for i in range(0, len(batch_payload), batch_size):
                    batch = batch_payload[i:i+batch_size]
                    result = supabase.table("stocks_financials").upsert(batch).execute()
                    logger.info(f"Upserted batch of {len(batch)} financial records")
                
                logger.info(f"Successfully processed {processed_count} financial records")
                
            except Exception as e:
                logger.error(f"Error upserting financial data: {e}")
        else:
            logger.warning("No valid financial data to process")
            
    except Exception as e:
        logger.error(f"Error in process_financial_data: {e}")

def update_calculated_metrics():
    """Update calculated financial metrics"""
    try:
        # Get all financial data
        result = supabase.table("stocks_financials").select("*").execute()
        
        if not result.data:
            logger.info("No financial data to update")
            return
        
        updates = []
        
        for record in result.data:
            try:
                symbol = record.get('symbol')
                updated_record = {"id": record["id"]}
                needs_update = False
                
                # Calculate Price-to-Book ratio if missing
                if not record.get('price_to_book') and record.get('market_cap') and record.get('book_value_per_share'):
                    # Get current price from realtime data
                    realtime_result = supabase.table("stocks_realtime").select("current_price").eq("symbol", symbol).execute()
                    if realtime_result.data:
                        current_price = realtime_result.data[0].get('current_price')
                        book_value = record.get('book_value_per_share')
                        if current_price and book_value:
                            updated_record['price_to_book'] = current_price / book_value
                            needs_update = True
                
                # Calculate debt-to-equity ratio if missing
                if not record.get('debt_to_equity') and record.get('total_debt') and record.get('market_cap'):
                    # Estimate equity from market cap (simplified)
                    debt = record.get('total_debt')
                    equity = record.get('market_cap')  # Simplified assumption
                    if debt and equity:
                        updated_record['debt_to_equity'] = debt / equity
                        needs_update = True
                
                if needs_update:
                    updates.append(updated_record)
                    
            except Exception as e:
                logger.error(f"Error calculating metrics for {record.get('symbol', 'unknown')}: {e}")
                continue
        
        # Batch update calculated metrics
        if updates:
            try:
                batch_size = 20
                for i in range(0, len(updates), batch_size):
                    batch = updates[i:i+batch_size]
                    supabase.table("stocks_financials").upsert(batch).execute()
                    logger.info(f"Updated calculated metrics for {len(batch)} records")
                
                logger.info(f"Updated calculated metrics for {len(updates)} financial records")
                
            except Exception as e:
                logger.error(f"Error updating calculated metrics: {e}")
        
    except Exception as e:
        logger.error(f"Error in update_calculated_metrics: {e}")

def main():
    logger.info("Starting financial data sync")
    
    # Process financial data from CSV
    process_financial_data()
    
    # Update calculated metrics
    update_calculated_metrics()
    
    logger.info("Financial data sync completed")

if __name__ == "__main__":
    main()

