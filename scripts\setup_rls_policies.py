#!/usr/bin/env python3
"""
Setup RLS policies for public data access
This script adds RLS policies to allow anonymous users to read public market data
"""

import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables from data_sync folder
load_dotenv('/mnt/c/Users/<USER>/Desktop/egx-stock-ai-oracle/scripts/data_sync/.env')

def setup_rls_policies():
    """Setup RLS policies for public read access to market data"""
    
    # Initialize Supabase client with service role key
    url = os.getenv("SUPABASE_URL")
    service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not url or not service_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env file")
        return False
    
    try:
        supabase: Client = create_client(url, service_key)
        print("✅ Connected to Supabase")
        
        # SQL commands to create RLS policies for public read access
        rls_policies = [
            # Market indices - public read access
            """
            CREATE POLICY "market_indices_public_read" ON "public"."market_indices"
            AS PERMISSIVE FOR SELECT
            TO public
            USING (true);
            """,
            
            # Real-time stocks - public read access
            """
            CREATE POLICY "stocks_realtime_public_read" ON "public"."stocks_realtime"
            AS PERMISSIVE FOR SELECT
            TO public
            USING (true);
            """,
            
            # Historical stocks - public read access
            """
            CREATE POLICY "stocks_historical_public_read" ON "public"."stocks_historical"
            AS PERMISSIVE FOR SELECT
            TO public
            USING (true);
            """,
            
            # Stocks master - public read access
            """
            CREATE POLICY "stocks_master_public_read" ON "public"."stocks_master"
            AS PERMISSIVE FOR SELECT
            TO public
            USING (true);
            """,
            
            # Financial data - public read access
            """
            CREATE POLICY "financial_data_public_read" ON "public"."financial_data"
            AS PERMISSIVE FOR SELECT
            TO public
            USING (true);
            """
        ]
        
        print("🔧 Creating RLS policies for public read access...")
        
        for i, policy_sql in enumerate(rls_policies, 1):
            try:
                result = supabase.rpc('execute_sql', {'sql': policy_sql.strip()})
                print(f"✅ Policy {i}/5 created successfully")
            except Exception as e:
                error_msg = str(e)
                if "already exists" in error_msg.lower():
                    print(f"ℹ️ Policy {i}/5 already exists - skipping")
                else:
                    print(f"⚠️ Policy {i}/5 failed: {error_msg}")
        
        print("\n🎯 Testing data access with anonymous key...")
        
        # Test with anonymous key
        anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
        anon_client = create_client(url, anon_key)
        
        # Test market indices access
        try:
            result = anon_client.table('market_indices').select('symbol, name, current_value').limit(3).execute()
            if result.data:
                print(f"✅ Anonymous access to market_indices: {len(result.data)} records")
                for record in result.data:
                    print(f"   - {record.get('symbol', 'N/A')}: {record.get('current_value', 'N/A')}")
            else:
                print("⚠️ No data returned from market_indices")
        except Exception as e:
            print(f"❌ Failed to access market_indices: {e}")
        
        # Test real-time stocks access
        try:
            result = anon_client.table('stocks_realtime').select('symbol, current_price').limit(3).execute()
            if result.data:
                print(f"✅ Anonymous access to stocks_realtime: {len(result.data)} records")
                for record in result.data:
                    print(f"   - {record.get('symbol', 'N/A')}: {record.get('current_price', 'N/A')}")
            else:
                print("⚠️ No data returned from stocks_realtime")
        except Exception as e:
            print(f"❌ Failed to access stocks_realtime: {e}")
        
        print("\n✅ RLS policies setup completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up RLS policies: {e}")
        return False

if __name__ == "__main__":
    print("🔐 Setting up RLS policies for public data access...")
    success = setup_rls_policies()
    
    if success:
        print("\n🎉 Setup completed successfully!")
        print("🌐 Frontend should now be able to access market data")
    else:
        print("\n💥 Setup failed!")
        sys.exit(1)
