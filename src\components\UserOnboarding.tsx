
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  TrendingUp, 
  BarChart3, 
  Brain, 
  Bell, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Play,
  Lightbulb,
  Target,
  Award
} from 'lucide-react';

interface Tutorial {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  content: string[];
  completed: boolean;
  category: 'basic' | 'intermediate' | 'advanced';
}

const UserOnboarding = () => {
  const [currentTutorial, setCurrentTutorial] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedTutorials, setCompletedTutorials] = useState<string[]>([]);

  const tutorials: Tutorial[] = [
    {
      id: 'platform-basics',
      title: 'أساسيات المنصة',
      description: 'تعرف على كيفية استخدام منصة تحليل البورصة المصرية',
      icon: <BookOpen className="h-5 w-5" />,
      content: [
        'مرحباً بك في منصة تحليل البورصة المصرية! هذه المنصة تساعدك في اتخاذ قرارات استثمارية مدروسة.',
        'يمكنك العثور على التبويبات الرئيسية في أعلى الشاشة: نظرة عامة، توصيات الذكاء الاصطناعي، تيليجرام، والإعدادات.',
        'شريط الأسهم المتحرك يعرض أحدث أسعار الأسهم وحركة السوق في الوقت الفعلي.',
        'استخدم القوائم الجانبية للوصول السريع لأدوات التحليل المختلفة.'
      ],
      completed: false,
      category: 'basic'
    },
    {
      id: 'reading-charts',
      title: 'قراءة الرسوم البيانية',
      description: 'تعلم كيفية تفسير الرسوم البيانية والمؤشرات الفنية',
      icon: <BarChart3 className="h-5 w-5" />,
      content: [
        'الرسوم البيانية هي أداة أساسية لفهم حركة أسعار الأسهم عبر الزمن.',
        'الخط الأخضر يشير إلى ارتفاع السعر، بينما الخط الأحمر يشير إلى انخفاض السعر.',
        'حجم التداول يظهر في أسفل الرسم البياني ويدل على قوة الحركة السعرية.',
        'المتوسطات المتحركة تساعد في تحديد الاتجاه العام للسهم.'
      ],
      completed: false,
      category: 'intermediate'
    },
    {
      id: 'technical-indicators',
      title: 'المؤشرات الفنية',
      description: 'فهم المؤشرات الفنية الأساسية وكيفية استخدامها',
      icon: <TrendingUp className="h-5 w-5" />,
      content: [
        'مؤشر القوة النسبية (RSI) يقيس سرعة وتغير حركات الأسعار.',
        'قيم RSI أعلى من 70 تشير إلى أن السهم قد يكون مبالغ في شراؤه.',
        'قيم RSI أقل من 30 تشير إلى أن السهم قد يكون مبالغ في بيعه.',
        'استخدم هذه المؤشرات مع أدوات التحليل الأخرى لاتخاذ قرارات أفضل.'
      ],
      completed: false,
      category: 'intermediate'
    },
    {
      id: 'ai-insights',
      title: 'توصيات الذكاء الاصطناعي',
      description: 'كيفية الاستفادة من تحليلات الذكاء الاصطناعي',
      icon: <Brain className="h-5 w-5" />,
      content: [
        'نظام الذكاء الاصطناعي يحلل آلاف نقاط البيانات لتقديم توصيات استثمارية.',
        'التوصيات مصنفة حسب قوة الإشارة: قوية، متوسطة، أو ضعيفة.',
        'راجع دائماً التفسير المرفق مع كل توصية لفهم السبب.',
        'استخدم التوصيات كدليل إرشادي وليس كقرار استثماري نهائي.'
      ],
      completed: false,
      category: 'advanced'
    },
    {
      id: 'portfolio-management',
      title: 'إدارة المحفظة',
      description: 'أساسيات إدارة المحفظة الاستثمارية',
      icon: <Target className="h-5 w-5" />,
      content: [
        'التنويع هو مفتاح إدارة المخاطر في المحفظة الاستثمارية.',
        'لا تضع كل أموالك في سهم واحد أو قطاع واحد.',
        'راقب أداء محفظتك بانتظام وقم بإعادة التوازن عند الحاجة.',
        'حدد أهدافك الاستثمارية ومدى تحملك للمخاطر قبل البدء.'
      ],
      completed: false,
      category: 'basic'
    },
    {
      id: 'notifications',
      title: 'إعداد التنبيهات',
      description: 'كيفية إعداد التنبيهات عبر تيليجرام',
      icon: <Bell className="h-5 w-5" />,
      content: [
        'يمكنك ربط حسابك بتطبيق تيليجرام لتلقي تنبيهات فورية.',
        'اذهب إلى تبويب "تيليجرام" وتابع التعليمات لربط الحساب.',
        'يمكنك تخصيص أنواع التنبيهات التي تريد تلقيها.',
        'ستصلك إشعارات فورية عند تغير أسعار الأسهم في محفظتك.'
      ],
      completed: false,
      category: 'basic'
    }
  ];

  const startTutorial = (tutorialId: string) => {
    setCurrentTutorial(tutorialId);
    setCurrentStep(0);
  };

  const nextStep = () => {
    const tutorial = tutorials.find(t => t.id === currentTutorial);
    if (tutorial && currentStep < tutorial.content.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeTutorial = () => {
    if (currentTutorial && !completedTutorials.includes(currentTutorial)) {
      setCompletedTutorials([...completedTutorials, currentTutorial]);
    }
    setCurrentTutorial(null);
    setCurrentStep(0);
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'bg-green-100 text-green-800 border-green-200';
      case 'intermediate': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'advanced': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'basic': return 'مبتدئ';
      case 'intermediate': return 'متوسط';
      case 'advanced': return 'متقدم';
      default: return category;
    }
  };

  const currentTutorialData = tutorials.find(t => t.id === currentTutorial);
  const completionPercentage = (completedTutorials.length / tutorials.length) * 100;

  if (currentTutorial && currentTutorialData) {
    const progress = ((currentStep + 1) / currentTutorialData.content.length) * 100;
    
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 arabic" dir="rtl">
              {currentTutorialData.icon}
              {currentTutorialData.title}
            </CardTitle>
            <Badge className={`${getCategoryColor(currentTutorialData.category)} border`}>
              {getCategoryLabel(currentTutorialData.category)}
            </Badge>
          </div>
          <Progress value={progress} className="mt-4" />
          <p className="text-sm text-muted-foreground text-center">
            خطوة {currentStep + 1} من {currentTutorialData.content.length}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <p className="text-lg leading-relaxed arabic text-right" dir="rtl">
              {currentTutorialData.content[currentStep]}
            </p>
          </div>
          
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              السابق
            </Button>
            
            <div className="flex gap-2">
              {currentTutorialData.content.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index <= currentStep ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            
            {currentStep === currentTutorialData.content.length - 1 ? (
              <Button onClick={completeTutorial} className="arabic">
                <CheckCircle className="h-4 w-4 ml-2" />
                إنهاء الدرس
              </Button>
            ) : (
              <Button onClick={nextStep}>
                التالي
                <ArrowLeft className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Progress Overview */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800 arabic" dir="rtl">
            <Award className="h-5 w-5" />
            تقدمك في التعلم
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium arabic">إنجاز الدروس</span>
              <span className="text-sm text-muted-foreground">
                {completedTutorials.length} من {tutorials.length}
              </span>
            </div>
            <Progress value={completionPercentage} className="h-3" />
            <p className="text-sm text-muted-foreground text-center arabic">
              {completionPercentage === 100 
                ? 'مبارك! لقد أكملت جميع الدروس' 
                : `${Math.round(completionPercentage)}% مكتمل`}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Tutorial Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {['basic', 'intermediate', 'advanced'].map(category => (
          <Card key={category} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="text-center arabic">
                {getCategoryLabel(category)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {tutorials
                  .filter(tutorial => tutorial.category === category)
                  .map(tutorial => (
                    <div
                      key={tutorial.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        {completedTutorials.includes(tutorial.id) ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <div className="p-1 bg-gray-100 rounded">
                            {tutorial.icon}
                          </div>
                        )}
                        <div>
                          <h4 className="font-semibold text-sm arabic">{tutorial.title}</h4>
                          <p className="text-xs text-muted-foreground arabic">
                            {tutorial.description}
                          </p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant={completedTutorials.includes(tutorial.id) ? "outline" : "default"}
                        onClick={() => startTutorial(tutorial.id)}
                      >
                        {completedTutorials.includes(tutorial.id) ? (
                          <Lightbulb className="h-3 w-3" />
                        ) : (
                          <Play className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 arabic" dir="rtl">
            <Lightbulb className="h-5 w-5" />
            نصائح سريعة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2 arabic">نصيحة للمبتدئين</h4>
              <p className="text-sm text-green-700 arabic">
                ابدأ بمبالغ صغيرة وتعلم من تجاربك. لا تستثمر أموالاً لا يمكنك تحمل خسارتها.
              </p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2 arabic">نصيحة متقدمة</h4>
              <p className="text-sm text-blue-700 arabic">
                استخدم التحليل الفني والأساسي معاً لاتخاذ قرارات استثمارية أكثر دقة.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserOnboarding;
