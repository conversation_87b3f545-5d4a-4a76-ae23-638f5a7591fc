#!/usr/bin/env python3
"""
أداة استيراد البيانات التاريخية المصرية
Egyptian Historical Data Import Tool

يستورد البيانات من مجلد meta2 (321 ملف) إلى قاعدة البيانات الجديدة
"""

import os
import sys
import pandas as pd
import psycopg2
from psycopg2.extras import execute_batch
from datetime import datetime, timedelta
import logging
from pathlib import Path
import glob

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EgyptianStockDataImporter:
    def __init__(self, db_config):
        """إعداد المستورد"""
        self.db_config = db_config
        self.connection = None
        self.historical_data_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2/"
        self.realtime_data_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx"
        self.financial_data_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv"
        
        # إحصائيات
        self.stats = {
            'historical_records': 0,
            'realtime_records': 0,
            'financial_records': 0,
            'stocks_processed': 0,
            'errors': 0
        }

    def connect_db(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.connection.autocommit = False
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        tables_sql = """
        -- إنشاء جدول الأسهم الرئيسي
        CREATE TABLE IF NOT EXISTS stocks_master (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(10) UNIQUE NOT NULL,
            name_ar VARCHAR(200),
            name_en VARCHAR(200),
            sector VARCHAR(100),
            industry VARCHAR(150),
            isin VARCHAR(20),
            total_shares BIGINT,
            free_shares BIGINT,
            listing_date DATE,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- إنشاء جدول البيانات التاريخية
        CREATE TABLE IF NOT EXISTS stocks_historical (
            id BIGSERIAL PRIMARY KEY,
            symbol VARCHAR(10) NOT NULL,
            trade_date DATE NOT NULL,
            open_price DECIMAL(12,4),
            high_price DECIMAL(12,4),
            low_price DECIMAL(12,4),
            close_price DECIMAL(12,4),
            volume BIGINT,
            turnover DECIMAL(15,2),
            trades_count INTEGER,
            price_change DECIMAL(12,4),
            price_change_pct DECIMAL(8,4),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, trade_date)
        );

        -- إنشاء جدول البيانات الحية
        CREATE TABLE IF NOT EXISTS stocks_realtime (
            symbol VARCHAR(10) PRIMARY KEY,
            name_ar VARCHAR(200),
            open_price DECIMAL(12,4),
            high_price DECIMAL(12,4),
            low_price DECIMAL(12,4),
            current_price DECIMAL(12,4),
            previous_close DECIMAL(12,4),
            change_amount DECIMAL(12,4),
            change_percent DECIMAL(8,4),
            volume BIGINT,
            turnover DECIMAL(15,2),
            trades_count INTEGER,
            liquidity_ratio DECIMAL(8,4),
            net_liquidity DECIMAL(15,2),
            liquidity_inflow DECIMAL(15,2),
            liquidity_outflow DECIMAL(15,2),
            volume_inflow BIGINT,
            volume_outflow BIGINT,
            liquidity_flow DECIMAL(8,4),
            ma5 DECIMAL(12,4),
            ma10 DECIMAL(12,4),
            ma20 DECIMAL(12,4),
            ma50 DECIMAL(12,4),
            ma100 DECIMAL(12,4),
            ma200 DECIMAL(12,4),
            tk_indicator DECIMAL(12,4),
            kj_indicator DECIMAL(12,4),
            target_1 DECIMAL(12,4),
            target_2 DECIMAL(12,4),
            target_3 DECIMAL(12,4),
            stop_loss DECIMAL(12,4),
            stock_status INTEGER,
            speculation_opportunity BOOLEAN,
            price_range DECIMAL(8,4),
            avg_net_volume_3d DECIMAL(15,2),
            avg_net_volume_5d DECIMAL(15,2),
            eps_annual DECIMAL(8,4),
            book_value DECIMAL(8,4),
            pe_ratio DECIMAL(8,4),
            dividend_yield DECIMAL(8,4),
            sector VARCHAR(100),
            last_trade_date DATE,
            free_shares BIGINT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- إنشاء جدول البيانات المالية
        CREATE TABLE IF NOT EXISTS stocks_financials (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(10) NOT NULL,
            fiscal_year INTEGER,
            fiscal_quarter INTEGER,
            market_cap DECIMAL(15,2),
            enterprise_value DECIMAL(15,2),
            pe_ratio DECIMAL(10,4),
            pb_ratio DECIMAL(10,4),
            ps_ratio DECIMAL(10,4),
            eps_basic DECIMAL(10,4),
            eps_diluted DECIMAL(10,4),
            eps_growth_yoy DECIMAL(8,4),
            dividend_yield DECIMAL(8,4),
            dividend_payout_ratio DECIMAL(8,4),
            total_revenue DECIMAL(15,2),
            gross_profit DECIMAL(15,2),
            operating_income DECIMAL(15,2),
            net_income DECIMAL(15,2),
            ebitda DECIMAL(15,2),
            total_assets DECIMAL(15,2),
            total_liabilities DECIMAL(15,2),
            total_equity DECIMAL(15,2),
            total_debt DECIMAL(15,2),
            cash_and_equivalents DECIMAL(15,2),
            operating_cash_flow DECIMAL(15,2),
            free_cash_flow DECIMAL(15,2),
            current_ratio DECIMAL(8,4),
            debt_to_equity DECIMAL(8,4),
            roe DECIMAL(8,4),
            roa DECIMAL(8,4),
            gross_margin DECIMAL(8,4),
            operating_margin DECIMAL(8,4),
            net_margin DECIMAL(8,4),
            beta DECIMAL(8,4),
            analyst_rating VARCHAR(50),
            target_price DECIMAL(12,4),
            sector VARCHAR(100),
            industry VARCHAR(150),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, fiscal_year, fiscal_quarter)
        );

        -- إنشاء فهارس محسنة
        CREATE INDEX IF NOT EXISTS idx_historical_symbol_date ON stocks_historical(symbol, trade_date DESC);
        CREATE INDEX IF NOT EXISTS idx_historical_date ON stocks_historical(trade_date DESC);
        CREATE INDEX IF NOT EXISTS idx_historical_volume ON stocks_historical(volume DESC) WHERE volume > 0;
        CREATE INDEX IF NOT EXISTS idx_realtime_sector ON stocks_realtime(sector);
        CREATE INDEX IF NOT EXISTS idx_realtime_change ON stocks_realtime(change_percent DESC);
        CREATE INDEX IF NOT EXISTS idx_financials_symbol ON stocks_financials(symbol);
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(tables_sql)
            self.connection.commit()
            logger.info("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            self.connection.rollback()
            return False

    def import_historical_data(self):
        """استيراد البيانات التاريخية من ملفات TXT"""
        logger.info("🚀 بدء استيراد البيانات التاريخية...")
        
        # العثور على جميع ملفات TXT
        txt_files = glob.glob(os.path.join(self.historical_data_path, "*.TXT"))
        logger.info(f"📊 وُجد {len(txt_files)} ملف للاستيراد")
        
        cursor = self.connection.cursor()
        
        for i, file_path in enumerate(txt_files, 1):
            try:
                # استخراج رمز السهم من اسم الملف
                filename = os.path.basename(file_path)
                symbol = filename.replace('D.TXT', '').replace('.TXT', '')
                
                logger.info(f"📈 [{i}/{len(txt_files)}] معالجة {symbol}...")
                
                # قراءة الملف
                df = pd.read_csv(file_path)
                
                if df.empty:
                    logger.warning(f"⚠️ ملف {symbol} فارغ")
                    continue
                
                # تنظيف البيانات
                df.columns = df.columns.str.strip('<>')
                df = df.dropna(subset=['DTYYYYMMDD', 'CLOSE'])
                
                # تحويل التاريخ
                df['trade_date'] = pd.to_datetime(df['DTYYYYMMDD'], format='%Y%m%d', errors='coerce')
                df = df.dropna(subset=['trade_date'])
                
                # حساب التغيير
                df = df.sort_values('trade_date')
                df['price_change'] = df['CLOSE'].diff()
                df['price_change_pct'] = df['CLOSE'].pct_change() * 100
                
                # إدراج في stocks_master
                cursor.execute("""
                    INSERT INTO stocks_master (symbol, is_active) 
                    VALUES (%s, true) 
                    ON CONFLICT (symbol) DO NOTHING
                """, (symbol,))
                
                # تحضير البيانات للإدراج
                records = []
                for _, row in df.iterrows():
                    if pd.notna(row['trade_date']):
                        records.append((
                            symbol,
                            row['trade_date'].date(),
                            float(row['OPEN']) if pd.notna(row['OPEN']) else None,
                            float(row['HIGH']) if pd.notna(row['HIGH']) else None,
                            float(row['LOW']) if pd.notna(row['LOW']) else None,
                            float(row['CLOSE']) if pd.notna(row['CLOSE']) else None,
                            int(row['VOL']) if pd.notna(row['VOL']) else None,
                            None,  # turnover - سيُحسب لاحقاً
                            None,  # trades_count
                            float(row['price_change']) if pd.notna(row['price_change']) else None,
                            float(row['price_change_pct']) if pd.notna(row['price_change_pct']) else None
                        ))
                
                # إدراج البيانات على دفعات
                if records:
                    execute_batch(cursor, """
                        INSERT INTO stocks_historical 
                        (symbol, trade_date, open_price, high_price, low_price, close_price, 
                         volume, turnover, trades_count, price_change, price_change_pct)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (symbol, trade_date) DO UPDATE SET
                        close_price = EXCLUDED.close_price,
                        volume = EXCLUDED.volume,
                        updated_at = CURRENT_TIMESTAMP
                    """, records, page_size=1000)
                    
                    self.stats['historical_records'] += len(records)
                    logger.info(f"✅ {symbol}: {len(records)} سجل")
                
                self.stats['stocks_processed'] += 1
                
                # حفظ كل 10 أسهم
                if i % 10 == 0:
                    self.connection.commit()
                    logger.info(f"💾 تم حفظ التقدم: {i} سهم")
                    
            except Exception as e:
                logger.error(f"❌ خطأ في معالجة {file_path}: {e}")
                self.stats['errors'] += 1
                continue
        
        self.connection.commit()
        logger.info(f"🎉 اكتمل استيراد البيانات التاريخية: {self.stats['historical_records']} سجل")

    def import_realtime_data(self):
        """استيراد البيانات الحية من Excel"""
        logger.info("🚀 بدء استيراد البيانات الحية...")
        
        try:
            # قراءة ملف Excel
            df = pd.read_excel(self.realtime_data_path)
            logger.info(f"📊 وُجد {len(df)} سهم في البيانات الحية")
            
            cursor = self.connection.cursor()
            
            # تنظيف أسماء الأعمدة
            column_mapping = {
                'الرمز': 'symbol',
                'الاسم': 'name_ar',
                'إفتتاح': 'open_price',
                'أعلى': 'high_price',
                'أدنى': 'low_price',
                'الاغلاق': 'current_price',
                'التغير %': 'change_percent',
                'نسبة السيولة %': 'liquidity_ratio',
                'فرص مضاربة ساعة': 'speculation_opportunity',
                'هدف 1 (يوم)': 'target_1',
                'هدف 2 (يوم)': 'target_2', 
                'هدف 3 (يوم)': 'target_3',
                'وقف خسارة (يوم)': 'stop_loss',
                'حاله السهم (يوم)': 'stock_status',
                'ma5 (يوم)': 'ma5',
                'ma10 (يوم)': 'ma10',
                'ma20 (يوم)': 'ma20',
                'ma50 (يوم)': 'ma50',
                'ma100 (يوم)': 'ma100',
                'ma200 (يوم)': 'ma200',
                'TK (يوم)': 'tk_indicator',
                'KJ (يوم)': 'kj_indicator',
                'صافي السيولة': 'net_liquidity',
                'الحجم': 'volume',
                'القطاع': 'sector',
                'القيمة': 'turnover',
                'الاسهم الحرة': 'free_shares',
                'ربح السهم (سنوي)': 'eps_annual',
                'القيمة الدفترية': 'book_value',
                'مكرر الأرباح': 'pe_ratio',
                'العائد الربحي (٪)': 'dividend_yield'
            }
            
            # إعادة تسمية الأعمدة
            df_clean = df.rename(columns=column_mapping)
            
            # تنظيف البيانات
            df_clean = df_clean.dropna(subset=['symbol'])
            df_clean = df_clean[df_clean['symbol'] != 'الرمز']  # إزالة صفوف الهيدر المكررة
            
            records = []
            for _, row in df_clean.iterrows():
                try:
                    symbol = str(row['symbol']).strip()
                    if not symbol or symbol == 'nan':
                        continue
                        
                    record = {
                        'symbol': symbol,
                        'name_ar': str(row.get('name_ar', '')).strip() if pd.notna(row.get('name_ar')) else None,
                        'open_price': float(row['open_price']) if pd.notna(row.get('open_price')) else None,
                        'high_price': float(row['high_price']) if pd.notna(row.get('high_price')) else None,
                        'low_price': float(row['low_price']) if pd.notna(row.get('low_price')) else None,
                        'current_price': float(row['current_price']) if pd.notna(row.get('current_price')) else None,
                        'change_percent': float(row['change_percent']) if pd.notna(row.get('change_percent')) else None,
                        'volume': int(row['volume']) if pd.notna(row.get('volume')) else None,
                        'turnover': float(row['turnover']) if pd.notna(row.get('turnover')) else None,
                        'liquidity_ratio': float(row['liquidity_ratio']) if pd.notna(row.get('liquidity_ratio')) else None,
                        'speculation_opportunity': bool(row.get('speculation_opportunity')) if pd.notna(row.get('speculation_opportunity')) else False,
                        'target_1': float(row['target_1']) if pd.notna(row.get('target_1')) else None,
                        'target_2': float(row['target_2']) if pd.notna(row.get('target_2')) else None,
                        'target_3': float(row['target_3']) if pd.notna(row.get('target_3')) else None,
                        'stop_loss': float(row['stop_loss']) if pd.notna(row.get('stop_loss')) else None,
                        'ma5': float(row['ma5']) if pd.notna(row.get('ma5')) else None,
                        'ma10': float(row['ma10']) if pd.notna(row.get('ma10')) else None,
                        'ma20': float(row['ma20']) if pd.notna(row.get('ma20')) else None,
                        'ma50': float(row['ma50']) if pd.notna(row.get('ma50')) else None,
                        'ma100': float(row['ma100']) if pd.notna(row.get('ma100')) else None,
                        'ma200': float(row['ma200']) if pd.notna(row.get('ma200')) else None,
                        'tk_indicator': float(row['tk_indicator']) if pd.notna(row.get('tk_indicator')) else None,
                        'kj_indicator': float(row['kj_indicator']) if pd.notna(row.get('kj_indicator')) else None,
                        'net_liquidity': float(row['net_liquidity']) if pd.notna(row.get('net_liquidity')) else None,
                        'sector': str(row.get('sector', '')).strip() if pd.notna(row.get('sector')) else None,
                        'free_shares': int(row['free_shares']) if pd.notna(row.get('free_shares')) else None,
                        'eps_annual': float(row['eps_annual']) if pd.notna(row.get('eps_annual')) else None,
                        'book_value': float(row['book_value']) if pd.notna(row.get('book_value')) else None,
                        'pe_ratio': float(row['pe_ratio']) if pd.notna(row.get('pe_ratio')) else None,
                        'dividend_yield': float(row['dividend_yield']) if pd.notna(row.get('dividend_yield')) else None,
                    }
                    
                    records.append(record)
                    
                except Exception as e:
                    logger.warning(f"⚠️ خطأ في معالجة صف: {e}")
                    continue
            
            # إدراج البيانات
            for record in records:
                try:
                    # إدراج/تحديث في stocks_master
                    cursor.execute("""
                        INSERT INTO stocks_master (symbol, name_ar, sector, is_active) 
                        VALUES (%(symbol)s, %(name_ar)s, %(sector)s, true) 
                        ON CONFLICT (symbol) DO UPDATE SET
                        name_ar = EXCLUDED.name_ar,
                        sector = EXCLUDED.sector,
                        updated_at = CURRENT_TIMESTAMP
                    """, record)
                    
                    # إدراج/تحديث في stocks_realtime
                    cursor.execute("""
                        INSERT INTO stocks_realtime (
                            symbol, name_ar, open_price, high_price, low_price, current_price,
                            change_percent, volume, turnover, liquidity_ratio, speculation_opportunity,
                            target_1, target_2, target_3, stop_loss, ma5, ma10, ma20, ma50, ma100, ma200,
                            tk_indicator, kj_indicator, net_liquidity, sector, free_shares,
                            eps_annual, book_value, pe_ratio, dividend_yield, last_trade_date
                        ) VALUES (
                            %(symbol)s, %(name_ar)s, %(open_price)s, %(high_price)s, %(low_price)s, %(current_price)s,
                            %(change_percent)s, %(volume)s, %(turnover)s, %(liquidity_ratio)s, %(speculation_opportunity)s,
                            %(target_1)s, %(target_2)s, %(target_3)s, %(stop_loss)s, %(ma5)s, %(ma10)s, %(ma20)s, 
                            %(ma50)s, %(ma100)s, %(ma200)s, %(tk_indicator)s, %(kj_indicator)s, %(net_liquidity)s, 
                            %(sector)s, %(free_shares)s, %(eps_annual)s, %(book_value)s, %(pe_ratio)s, %(dividend_yield)s,
                            CURRENT_DATE
                        ) ON CONFLICT (symbol) DO UPDATE SET
                            name_ar = EXCLUDED.name_ar,
                            open_price = EXCLUDED.open_price,
                            high_price = EXCLUDED.high_price,
                            low_price = EXCLUDED.low_price,
                            current_price = EXCLUDED.current_price,
                            change_percent = EXCLUDED.change_percent,
                            volume = EXCLUDED.volume,
                            turnover = EXCLUDED.turnover,
                            liquidity_ratio = EXCLUDED.liquidity_ratio,
                            speculation_opportunity = EXCLUDED.speculation_opportunity,
                            target_1 = EXCLUDED.target_1,
                            target_2 = EXCLUDED.target_2,
                            target_3 = EXCLUDED.target_3,
                            stop_loss = EXCLUDED.stop_loss,
                            ma5 = EXCLUDED.ma5,
                            ma10 = EXCLUDED.ma10,
                            ma20 = EXCLUDED.ma20,
                            ma50 = EXCLUDED.ma50,
                            ma100 = EXCLUDED.ma100,
                            ma200 = EXCLUDED.ma200,
                            tk_indicator = EXCLUDED.tk_indicator,
                            kj_indicator = EXCLUDED.kj_indicator,
                            net_liquidity = EXCLUDED.net_liquidity,
                            sector = EXCLUDED.sector,
                            free_shares = EXCLUDED.free_shares,
                            eps_annual = EXCLUDED.eps_annual,
                            book_value = EXCLUDED.book_value,
                            pe_ratio = EXCLUDED.pe_ratio,
                            dividend_yield = EXCLUDED.dividend_yield,
                            updated_at = CURRENT_TIMESTAMP
                    """, record)
                    
                    self.stats['realtime_records'] += 1
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في إدراج {record.get('symbol', 'N/A')}: {e}")
                    self.stats['errors'] += 1
                    continue
            
            self.connection.commit()
            logger.info(f"🎉 اكتمل استيراد البيانات الحية: {self.stats['realtime_records']} سجل")
            
        except Exception as e:
            logger.error(f"❌ خطأ في استيراد البيانات الحية: {e}")
            self.connection.rollback()

    def import_financial_data(self):
        """استيراد البيانات المالية من CSV"""
        logger.info("🚀 بدء استيراد البيانات المالية...")
        
        try:
            # قراءة ملف CSV
            df = pd.read_csv(self.financial_data_path)
            logger.info(f"📊 وُجد {len(df)} سجل في البيانات المالية")
            
            cursor = self.connection.cursor()
            
            for _, row in df.iterrows():
                try:
                    symbol = str(row['Symbol']).strip()
                    if not symbol or symbol == 'Symbol':
                        continue
                    
                    # تحضير البيانات
                    financial_data = {
                        'symbol': symbol,
                        'fiscal_year': datetime.now().year,
                        'fiscal_quarter': None,
                        'market_cap': float(row['Market capitalization']) if pd.notna(row.get('Market capitalization')) else None,
                        'pe_ratio': float(row['Price to earnings ratio']) if pd.notna(row.get('Price to earnings ratio')) else None,
                        'eps_diluted': float(row['Earnings per share diluted, Trailing 12 months']) if pd.notna(row.get('Earnings per share diluted, Trailing 12 months')) else None,
                        'eps_growth_yoy': float(row['Earnings per share diluted growth %, TTM YoY']) if pd.notna(row.get('Earnings per share diluted growth %, TTM YoY')) else None,
                        'dividend_yield': float(row['Dividend yield %, Trailing 12 months']) if pd.notna(row.get('Dividend yield %, Trailing 12 months')) else None,
                        'sector': str(row.get('Sector', '')).strip() if pd.notna(row.get('Sector')) else None,
                        'industry': str(row.get('Industry', '')).strip() if pd.notna(row.get('Industry')) else None,
                        'total_revenue': float(row['Total revenue, Annual']) if pd.notna(row.get('Total revenue, Annual')) else None,
                        'net_income': float(row['Net income, Annual']) if pd.notna(row.get('Net income, Annual')) else None,
                        'total_assets': float(row['Total assets, Quarterly']) if pd.notna(row.get('Total assets, Quarterly')) else None,
                        'total_equity': float(row['Total equity, Quarterly']) if pd.notna(row.get('Total equity, Quarterly')) else None,
                        'total_debt': float(row['Total debt, Annual']) if pd.notna(row.get('Total debt, Annual')) else None,
                        'free_cash_flow': float(row['Free cash flow, Annual']) if pd.notna(row.get('Free cash flow, Annual')) else None,
                        'current_ratio': float(row['Current ratio, Quarterly']) if pd.notna(row.get('Current ratio, Quarterly')) else None,
                        'debt_to_equity': float(row['Debt to equity ratio, Quarterly']) if pd.notna(row.get('Debt to equity ratio, Quarterly')) else None,
                        'roe': float(row.get('Return on equity, Annual')) if pd.notna(row.get('Return on equity, Annual')) else None,
                        'beta': float(row['Beta 5 years']) if pd.notna(row.get('Beta 5 years')) else None,
                        'target_price': float(row['Target price 1 year']) if pd.notna(row.get('Target price 1 year')) else None,
                    }
                    
                    # إدراج البيانات
                    cursor.execute("""
                        INSERT INTO stocks_financials (
                            symbol, fiscal_year, fiscal_quarter, market_cap, pe_ratio, eps_diluted,
                            eps_growth_yoy, dividend_yield, sector, industry, total_revenue, net_income,
                            total_assets, total_equity, total_debt, free_cash_flow, current_ratio,
                            debt_to_equity, roe, beta, target_price
                        ) VALUES (
                            %(symbol)s, %(fiscal_year)s, %(fiscal_quarter)s, %(market_cap)s, %(pe_ratio)s, %(eps_diluted)s,
                            %(eps_growth_yoy)s, %(dividend_yield)s, %(sector)s, %(industry)s, %(total_revenue)s, %(net_income)s,
                            %(total_assets)s, %(total_equity)s, %(total_debt)s, %(free_cash_flow)s, %(current_ratio)s,
                            %(debt_to_equity)s, %(roe)s, %(beta)s, %(target_price)s
                        ) ON CONFLICT (symbol, fiscal_year, fiscal_quarter) DO UPDATE SET
                            market_cap = EXCLUDED.market_cap,
                            pe_ratio = EXCLUDED.pe_ratio,
                            eps_diluted = EXCLUDED.eps_diluted,
                            eps_growth_yoy = EXCLUDED.eps_growth_yoy,
                            dividend_yield = EXCLUDED.dividend_yield,
                            sector = EXCLUDED.sector,
                            industry = EXCLUDED.industry,
                            total_revenue = EXCLUDED.total_revenue,
                            net_income = EXCLUDED.net_income,
                            total_assets = EXCLUDED.total_assets,
                            total_equity = EXCLUDED.total_equity,
                            total_debt = EXCLUDED.total_debt,
                            free_cash_flow = EXCLUDED.free_cash_flow,
                            current_ratio = EXCLUDED.current_ratio,
                            debt_to_equity = EXCLUDED.debt_to_equity,
                            roe = EXCLUDED.roe,
                            beta = EXCLUDED.beta,
                            target_price = EXCLUDED.target_price,
                            updated_at = CURRENT_TIMESTAMP
                    """, financial_data)
                    
                    self.stats['financial_records'] += 1
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة البيانات المالية لـ {symbol}: {e}")
                    self.stats['errors'] += 1
                    continue
            
            self.connection.commit()
            logger.info(f"🎉 اكتمل استيراد البيانات المالية: {self.stats['financial_records']} سجل")
            
        except Exception as e:
            logger.error(f"❌ خطأ في استيراد البيانات المالية: {e}")
            self.connection.rollback()

    def create_views(self):
        """إنشاء Views محسوبة"""
        views_sql = """
        -- ملخص السوق المتقدم
        CREATE MATERIALIZED VIEW IF NOT EXISTS market_summary_advanced AS
        SELECT 
            COUNT(*) as total_stocks,
            COUNT(CASE WHEN volume > 0 THEN 1 END) as active_stocks,
            COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
            COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
            ROUND(AVG(change_percent), 3) as avg_change,
            SUM(volume) as total_volume,
            SUM(turnover) as total_turnover,
            COUNT(CASE WHEN ma5 > ma20 AND ma20 > ma50 THEN 1 END) as strong_uptrend_stocks,
            AVG(liquidity_ratio) as avg_liquidity_ratio,
            MAX(updated_at) as last_updated
        FROM stocks_realtime
        WHERE current_price > 0;

        -- أداء القطاعات
        CREATE MATERIALIZED VIEW IF NOT EXISTS sector_performance_advanced AS
        SELECT 
            sector,
            COUNT(*) as total_stocks,
            AVG(change_percent) as avg_change,
            SUM(volume) as total_volume,
            SUM(turnover) as total_turnover,
            COUNT(CASE WHEN ma5 > ma20 THEN 1 END) as bullish_count,
            MAX(updated_at) as last_updated
        FROM stocks_realtime
        WHERE sector IS NOT NULL AND current_price > 0
        GROUP BY sector
        ORDER BY avg_change DESC;

        -- أفضل الفرص الاستثمارية
        CREATE MATERIALIZED VIEW IF NOT EXISTS investment_opportunities AS
        SELECT 
            sr.symbol,
            sr.name_ar,
            sr.current_price,
            sr.change_percent,
            ROUND((sr.target_1 - sr.current_price) / sr.current_price * 100, 2) as potential_gain_pct,
            sf.pe_ratio,
            sf.dividend_yield,
            sr.volume,
            sr.liquidity_ratio,
            CASE 
                WHEN sr.current_price < sr.target_1 * 0.8 THEN 'فرصة ممتازة'
                WHEN sr.current_price < sr.target_1 * 0.9 THEN 'فرصة جيدة'
                WHEN sr.current_price < sr.target_1 * 0.95 THEN 'فرصة متوسطة'
                ELSE 'قريب من الهدف'
            END as opportunity_level,
            sr.updated_at
        FROM stocks_realtime sr
        LEFT JOIN stocks_financials sf ON sr.symbol = sf.symbol
        WHERE sr.target_1 IS NOT NULL AND sr.current_price > 0
        ORDER BY potential_gain_pct DESC;

        -- منح الصلاحيات
        GRANT SELECT ON market_summary_advanced TO PUBLIC;
        GRANT SELECT ON sector_performance_advanced TO PUBLIC;
        GRANT SELECT ON investment_opportunities TO PUBLIC;
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(views_sql)
            self.connection.commit()
            logger.info("✅ تم إنشاء Views محسوبة بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء Views: {e}")
            self.connection.rollback()

    def print_summary(self):
        """طباعة ملخص العملية"""
        logger.info("\n" + "="*60)
        logger.info("📊 ملخص عملية الاستيراد")
        logger.info("="*60)
        logger.info(f"📈 البيانات التاريخية: {self.stats['historical_records']:,} سجل")
        logger.info(f"🔴 البيانات الحية: {self.stats['realtime_records']:,} سجل") 
        logger.info(f"💰 البيانات المالية: {self.stats['financial_records']:,} سجل")
        logger.info(f"📊 الأسهم المعالجة: {self.stats['stocks_processed']:,} سهم")
        logger.info(f"❌ الأخطاء: {self.stats['errors']} خطأ")
        logger.info("="*60)
        logger.info("🎉 اكتمل استيراد جميع البيانات بنجاح!")

    def run_full_import(self):
        """تشغيل عملية الاستيراد الكاملة"""
        logger.info("🚀 بدء عملية الاستيراد الشاملة للبيانات المصرية...")
        
        if not self.connect_db():
            return False
        
        try:
            # إنشاء الجداول
            if not self.create_tables():
                return False
            
            # استيراد البيانات بالتسلسل
            self.import_historical_data()
            self.import_realtime_data() 
            self.import_financial_data()
            
            # إنشاء Views
            self.create_views()
            
            # طباعة الملخص
            self.print_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في عملية الاستيراد: {e}")
            return False
        finally:
            if self.connection:
                self.connection.close()
                logger.info("🔒 تم إغلاق الاتصال بقاعدة البيانات")

def main():
    """الدالة الرئيسية"""
    # إعدادات قاعدة البيانات
    db_config = {
        'host': 'localhost',
        'database': 'egx_stock_oracle',
        'user': 'egx_admin',
        'password': 'secure_password',
        'port': 5432
    }
    
    # تشغيل المستورد
    importer = EgyptianStockDataImporter(db_config)
    success = importer.run_full_import()
    
    if success:
        logger.info("✅ تم إنجاز المهمة بنجاح!")
        sys.exit(0)
    else:
        logger.error("❌ فشل في إنجاز المهمة!")
        sys.exit(1)

if __name__ == "__main__":
    main()
