import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Filter, 
  Search, 
  TrendingUp, 
  TrendingDown, 
  Target,
  DollarSign,
  BarChart3,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Star
} from 'lucide-react';
import { useEnhancedMarketData, EnhancedStock } from '@/hooks/useEnhancedMarketData';
import StockAnalysisLink from '@/components/analysis/StockAnalysisLink';
import { useAnalysis } from '@/hooks/useAnalysis';

interface FilterCriteria {
  sector: string;
  minPrice: number;
  maxPrice: number;
  minVolume: number;
  maxVolume: number;
  minChange: number;
  maxChange: number;
  minPE: number;
  maxPE: number;
  minDividendYield: number;
  maxDividendYield: number;
  technicalSignal: string;
  searchTerm: string;
}

const StockScreener = () => {
  const { openAnalysis } = useAnalysis();
  const { data: marketData, isLoading } = useEnhancedMarketData();
  const [activeTab, setActiveTab] = useState('screener');
  
  const [filters, setFilters] = useState<FilterCriteria>({
    sector: 'all',
    minPrice: 0,
    maxPrice: 1000,
    minVolume: 0,
    maxVolume: 10000000,
    minChange: -20,
    maxChange: 20,
    minPE: 0,
    maxPE: 50,
    minDividendYield: 0,
    maxDividendYield: 20,
    technicalSignal: 'all',
    searchTerm: ''
  });

  // Filter stocks based on criteria
  const filteredStocks = useMemo(() => {
    if (!marketData?.stocks) return [];

    return marketData.stocks.filter(stock => {
      // Basic filters
      if (filters.searchTerm && 
          !stock.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase()) &&
          !stock.name?.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
        return false;
      }

      if (filters.sector !== 'all' && stock.sector !== filters.sector) return false;
      
      const price = stock.current_price || 0;
      if (price < filters.minPrice || price > filters.maxPrice) return false;
      
      const volume = stock.volume || 0;
      if (volume < filters.minVolume || volume > filters.maxVolume) return false;
      
      const change = stock.change_percent || 0;
      if (change < filters.minChange || change > filters.maxChange) return false;
      
      const pe = stock.pe_ratio || 0;
      if (pe > 0 && (pe < filters.minPE || pe > filters.maxPE)) return false;
      
      const dividendYield = stock.dividend_yield || 0;
      if (dividendYield > 0 && (dividendYield < filters.minDividendYield || dividendYield > filters.maxDividendYield)) return false;

      // Technical signal filters
      if (filters.technicalSignal !== 'all') {
        const ma5 = stock.ma5 || 0;
        const ma20 = stock.ma20 || 0;
        const ma50 = stock.ma50 || 0;
        
        switch (filters.technicalSignal) {
          case 'bullish':
            if (!(ma5 > ma20 && ma20 > ma50)) return false;
            break;
          case 'bearish':
            if (!(ma5 < ma20 && ma20 < ma50)) return false;
            break;
          case 'breakout':
            if (!(price > ma20)) return false;
            break;
          case 'oversold':
            if (!(price < ma5 * 0.95)) return false;
            break;
          case 'overbought':
            if (!(price > ma5 * 1.05)) return false;
            break;
        }
      }

      return true;
    }).sort((a, b) => (b.volume || 0) - (a.volume || 0));
  }, [marketData?.stocks, filters]);

  // Get unique sectors
  const sectors = useMemo(() => {
    if (!marketData?.sectors) return [];
    return marketData.sectors.map(s => s.sector);
  }, [marketData?.sectors]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const formatCurrency = (num: number) => {
    return new Intl.NumberFormat('ar-EG', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  const getTechnicalSignal = (stock: EnhancedStock) => {
    const price = stock.current_price || 0;
    const ma5 = stock.ma5 || 0;
    const ma20 = stock.ma20 || 0;
    const ma50 = stock.ma50 || 0;

    if (ma5 > ma20 && ma20 > ma50 && price > ma5) {
      return { signal: 'قوي صاعد', color: 'bg-green-600', icon: TrendingUp };
    } else if (ma5 < ma20 && ma20 < ma50 && price < ma5) {
      return { signal: 'قوي هابط', color: 'bg-red-600', icon: TrendingDown };
    } else if (price > ma20) {
      return { signal: 'اختراق', color: 'bg-blue-600', icon: Target };
    } else if (price < ma5 * 0.95) {
      return { signal: 'تشبع بيعي', color: 'bg-purple-600', icon: AlertTriangle };
    } else if (price > ma5 * 1.05) {
      return { signal: 'تشبع شرائي', color: 'bg-orange-600', icon: AlertTriangle };
    } else {
      return { signal: 'محايد', color: 'bg-gray-500', icon: BarChart3 };
    }
  };

  const getRecommendation = (stock: EnhancedStock) => {
    const technical = getTechnicalSignal(stock);
    const pe = stock.pe_ratio || 0;
    const dividendYield = stock.dividend_yield || 0;
    const change = stock.change_percent || 0;

    let score = 0;
    const reasons: string[] = [];

    // Technical analysis
    if (technical.signal === 'قوي صاعد') {
      score += 3;
      reasons.push('اتجاه فني قوي');
    } else if (technical.signal === 'اختراق') {
      score += 2;
      reasons.push('اختراق المقاومة');
    } else if (technical.signal === 'تشبع بيعي') {
      score += 1;
      reasons.push('فرصة شراء');
    }

    // Fundamental analysis
    if (pe > 0 && pe < 15) {
      score += 2;
      reasons.push('P/E منخفض');
    }
    if (dividendYield > 5) {
      score += 1;
      reasons.push('عائد توزيعات جيد');
    }

    // Performance
    if (change > 0) {
      score += 1;
      reasons.push('أداء إيجابي');
    }

    if (score >= 4) {
      return { recommendation: 'شراء قوي', color: 'text-green-700 bg-green-100', icon: CheckCircle, reasons };
    } else if (score >= 2) {
      return { recommendation: 'شراء', color: 'text-blue-700 bg-blue-100', icon: Star, reasons };
    } else if (score <= -2) {
      return { recommendation: 'بيع', color: 'text-red-700 bg-red-100', icon: XCircle, reasons };
    } else {
      return { recommendation: 'انتظار', color: 'text-gray-700 bg-gray-100', icon: BarChart3, reasons };
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الأسهم...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">مرشح الأسهم المتقدم</h2>
        <Badge variant="secondary" className="text-lg px-4 py-2">
          {filteredStocks.length} سهم
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="screener">المرشح</TabsTrigger>
          <TabsTrigger value="analysis">تحليل القطاعات</TabsTrigger>
          <TabsTrigger value="recommendations">التوصيات</TabsTrigger>
        </TabsList>

        <TabsContent value="screener" className="space-y-6">
          {/* Filters Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                مرشحات البحث
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Search */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">البحث</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="اسم أو رمز السهم..."
                      value={filters.searchTerm}
                      onChange={(e) => setFilters({...filters, searchTerm: e.target.value})}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Sector */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">القطاع</label>
                  <Select value={filters.sector} onValueChange={(value) => setFilters({...filters, sector: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر القطاع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع القطاعات</SelectItem>
                      {sectors.map(sector => (
                        <SelectItem key={sector} value={sector}>{sector}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Technical Signal */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">الإشارة الفنية</label>
                  <Select value={filters.technicalSignal} onValueChange={(value) => setFilters({...filters, technicalSignal: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الإشارة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الإشارات</SelectItem>
                      <SelectItem value="bullish">اتجاه صاعد</SelectItem>
                      <SelectItem value="bearish">اتجاه هابط</SelectItem>
                      <SelectItem value="breakout">اختراق</SelectItem>
                      <SelectItem value="oversold">تشبع بيعي</SelectItem>
                      <SelectItem value="overbought">تشبع شرائي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Range Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">نطاق السعر (جنيه)</label>
                    <div className="px-3">
                      <Slider
                        value={[filters.minPrice, filters.maxPrice]}
                        onValueChange={([min, max]) => setFilters({...filters, minPrice: min, maxPrice: max})}
                        max={1000}
                        min={0}
                        step={5}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{filters.minPrice}</span>
                        <span>{filters.maxPrice}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">نطاق التغيير (%)</label>
                    <div className="px-3">
                      <Slider
                        value={[filters.minChange, filters.maxChange]}
                        onValueChange={([min, max]) => setFilters({...filters, minChange: min, maxChange: max})}
                        max={20}
                        min={-20}
                        step={0.5}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{filters.minChange}%</span>
                        <span>{filters.maxChange}%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">نسبة P/E</label>
                    <div className="px-3">
                      <Slider
                        value={[filters.minPE, filters.maxPE]}
                        onValueChange={([min, max]) => setFilters({...filters, minPE: min, maxPE: max})}
                        max={50}
                        min={0}
                        step={1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{filters.minPE}</span>
                        <span>{filters.maxPE}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">عائد التوزيعات (%)</label>
                    <div className="px-3">
                      <Slider
                        value={[filters.minDividendYield, filters.maxDividendYield]}
                        onValueChange={([min, max]) => setFilters({...filters, minDividendYield: min, maxDividendYield: max})}
                        max={20}
                        min={0}
                        step={0.5}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{filters.minDividendYield}%</span>
                        <span>{filters.maxDividendYield}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Results */}
          <div className="space-y-4">
            {filteredStocks.map((stock) => {
              const technical = getTechnicalSignal(stock);
              const TechnicalIcon = technical.icon;
              
              return (
                <Card key={stock.symbol} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">{stock.symbol}</h3>
                          <p className="text-sm text-gray-600">{stock.name || 'غير متوفر'}</p>
                          <Badge variant="outline" className="mt-1 text-xs">
                            {stock.sector || 'غير محدد'}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-bold text-gray-900">
                          {formatCurrency(stock.current_price || 0)}
                        </div>
                        <div className={`flex items-center ${
                          (stock.change_percent || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {(stock.change_percent || 0) >= 0 ? 
                            <TrendingUp className="h-4 w-4 mr-1" /> :
                            <TrendingDown className="h-4 w-4 mr-1" />
                          }
                          <span className="font-medium">
                            {formatCurrency(Math.abs(stock.change_amount || 0))} 
                            ({Math.abs(stock.change_percent || 0).toFixed(2)}%)
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="text-xs text-gray-600">الحجم</div>
                        <div className="font-medium">{formatNumber(stock.volume || 0)}</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="text-xs text-gray-600">القيمة</div>
                        <div className="font-medium">{formatNumber(stock.turnover || 0)}</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="text-xs text-gray-600">P/E</div>
                        <div className="font-medium">{stock.pe_ratio?.toFixed(1) || '--'}</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="text-xs text-gray-600">التوزيعات</div>
                        <div className="font-medium">{stock.dividend_yield?.toFixed(1) || '--'}%</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <Badge className={`${technical.color} text-white`}>
                        <TechnicalIcon className="h-3 w-3 mr-1" />
                        {technical.signal}
                      </Badge>
                        <div className="flex space-x-2 space-x-reverse">
                        <StockAnalysisLink 
                          symbol={stock.symbol || ''}
                          onAnalyze={openAnalysis}
                          variant="outline"
                          size="sm"
                        />
                        <Button variant="outline" size="sm">
                          تفاصيل
                        </Button>
                        <Button size="sm">
                          <Zap className="h-4 w-4 mr-1" />
                          إضافة للمتابعة
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {marketData?.sectors.map((sector) => (
              <Card key={sector.sector} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">{sector.sector}</CardTitle>
                  <Badge 
                    variant={sector.performance === 'positive' ? 'default' : 
                            sector.performance === 'negative' ? 'destructive' : 'secondary'}
                    className="w-fit"
                  >
                    {sector.avgChange.toFixed(2)}%
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">عدد الشركات:</span>
                    <span className="font-medium">{sector.stockCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">إجمالي الحجم:</span>
                    <span className="font-medium">{formatNumber(sector.totalVolume)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">إجمالي القيمة:</span>
                    <span className="font-medium">{formatNumber(sector.totalTurnover)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">أفضل سهم:</span>
                    <span className="font-medium text-blue-600">{sector.topStock}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <div className="space-y-4">
            {filteredStocks.slice(0, 20).map((stock) => {
              const recommendation = getRecommendation(stock);
              const RecommendationIcon = recommendation.icon;
              
              return (
                <Card key={stock.symbol} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">{stock.symbol}</h3>
                          <p className="text-sm text-gray-600">{stock.name || 'غير متوفر'}</p>
                        </div>
                        <Badge className={recommendation.color}>
                          <RecommendationIcon className="h-3 w-3 mr-1" />
                          {recommendation.recommendation}
                        </Badge>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                          {formatCurrency(stock.current_price || 0)}
                        </div>
                        <div className={`text-sm ${
                          (stock.change_percent || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {(stock.change_percent || 0).toFixed(2)}%
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">أسباب التوصية:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {recommendation.reasons.map((reason, index) => (
                          <li key={index} className="text-sm text-gray-600">
                            {reason}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StockScreener;
