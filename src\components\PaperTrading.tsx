import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { TrendingUp, Bo<PERSON>, Users, Target } from 'lucide-react';
import { useAuthUser } from "@/hooks/useAuthUser";
import { usePaperTrading } from "@/hooks/usePaperTrading";
import PaperTradingAccountList from "./trading/PaperTradingAccountList";
import PaperTradingAccountForm from "./trading/PaperTradingAccountForm";
import PaperTradingTradeList from "./trading/PaperTradingTradeList";
import PaperTradingTradeForm from "./trading/PaperTradingTradeForm";
import TradingHub from './trading/TradingHub';
import { useEnsureUserInUsersTable } from "@/hooks/useEnsureUserInUsersTable";

// This is a refactor: most logic moved out to custom components and hook.
// Paper trading state and forms now managed via backend.

const PaperTrading = () => {
  const [activeSection, setActiveSection] = useState('overview');
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null);
  const { user } = useAuthUser();
  useEnsureUserInUsersTable(user);

  const {
    accountsQuery,
    addAccount,
    deleteAccount,
    addTrade,
    deleteTrade,
    useTrades,
  } = usePaperTrading();

  const loading = accountsQuery.isLoading;
  const error = accountsQuery.error?.message;
  const accounts = accountsQuery.data ?? [];
  const selectedAccount = accounts.find(acc => acc.id === selectedAccountId) || accounts[0] || null;

  // Trades hook specially for the selected account
  const tradesQuery = useTrades(selectedAccount?.id ?? null);
  const trades = tradesQuery.data ?? [];

  if (activeSection === 'advanced') {
    return <TradingHub />;
  }

  // For the edge function to know which account to use
  if (typeof window !== "undefined") {
    (window as unknown as Record<string, unknown>).selectedPaperAccountId = selectedAccountId || null;
  }

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex gap-4 mb-4">
        <Button
          variant={activeSection === 'overview' ? 'default' : 'outline'}
          onClick={() => setActiveSection('overview')}
        >
          نظرة عامة
        </Button>
        <Button
          variant={activeSection === 'advanced' ? 'default' : 'outline'}
          onClick={() => setActiveSection('advanced')}
        >
          ميزات متقدمة
        </Button>
      </div>

      {/* Paper Trading Account Management */}
      <PaperTradingAccountForm
        onCreate={async (data) => {
          await addAccount.mutateAsync(data);
          // No need to manually refresh, react-query does it
        }}
      />
      <PaperTradingAccountList
        accounts={accounts}
        selectedId={selectedAccount?.id || null}
        onSelect={setSelectedAccountId}
        onDelete={async (id) => {
          await deleteAccount.mutateAsync(id);
          setSelectedAccountId(null);
        }}
      />

      {/* Trades for Selected Account */}
      {selectedAccount && (
        <>
          <PaperTradingTradeForm
            onAdd={async (trade) => {
              await addTrade.mutateAsync({
                account_id: selectedAccount.id,
                ...trade,
              });
            }}
          />
          <PaperTradingTradeList
            trades={trades}
            onDelete={async (tradeId) => {
              await deleteTrade.mutateAsync({ tradeId, accountId: selectedAccount.id });
            }}
          />
          <div className="bg-indigo-50 text-indigo-900 rounded p-3 mt-4">
            <strong>رصيد الحساب الحالي:</strong>{" "}
            {selectedAccount.current_balance?.toLocaleString() ?? "-"} ج.م
            <span className="mx-4">
              <strong>إجمالي الأرباح/الخسائر:</strong>{" "}
              {selectedAccount.total_profit_loss?.toLocaleString() ?? "-"} ج.م
            </span>
            <span className="mx-4">
              <strong>عدد الصفقات:</strong>{" "}
              {selectedAccount.total_trades ?? 0}
            </span>
          </div>
        </>
      )}

      {loading && <div className="text-center">جاري التحميل...</div>}
      {error && <div className="text-red-500 text-center">{error}</div>}
    </div>
  );
};

export default PaperTrading;
