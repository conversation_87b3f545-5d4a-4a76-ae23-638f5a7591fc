#!/usr/bin/env python3
"""
فحص سريع لقاعدة البيانات - نسخة مبسطة وآمنة
"""

import os
import sys
from datetime import datetime

try:
    from supabase import create_client, Client
    from dotenv import load_dotenv
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت: pip install supabase python-dotenv")
    sys.exit(1)

def load_config():
    """تحميل إعدادات الاتصال"""
    load_dotenv()
    
    url = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
    key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not key:
        print("❌ متغير SUPABASE_SERVICE_ROLE_KEY مطلوب")
        return None, None
    
    return url, key

def test_connection(supabase):
    """اختبار الاتصال الأساسي"""
    try:
        # اختبار بسيط
        result = supabase.table('stocks_realtime').select('symbol').limit(1).execute()
        return True, len(result.data) if result.data else 0
    except Exception as e:
        return False, str(e)

def get_basic_stats(supabase):
    """جلب إحصائيات أساسية"""
    stats = {}
    
    try:
        # عدد الأسهم
        result = supabase.table('stocks_realtime').select('symbol').execute()
        stats['total_stocks'] = len(result.data) if result.data else 0
        
        # عينة من البيانات
        sample = supabase.table('stocks_realtime').select(
            'symbol, current_price, change_percent, ma5, ma20, target_1, pe_ratio'
        ).limit(3).execute()
        stats['sample_data'] = sample.data if sample.data else []
        
        return True, stats
    except Exception as e:
        return False, str(e)

def check_data_completeness(supabase):
    """فحص اكتمال البيانات"""
    completeness = {}
    
    try:
        # جلب عينة للفحص
        sample = supabase.table('stocks_realtime').select(
            'symbol, ma5, ma20, target_1, pe_ratio, dividend_yield, liquidity_ratio, market_cap'
        ).limit(100).execute()
        
        if not sample.data:
            return False, "لا توجد بيانات"
        
        data = sample.data
        total = len(data)
        
        # حساب النسب
        fields = ['ma5', 'ma20', 'target_1', 'pe_ratio', 'dividend_yield', 'liquidity_ratio', 'market_cap']
        
        for field in fields:
            count = sum(1 for row in data if row.get(field) is not None)
            completeness[field] = {
                'count': count,
                'total': total,
                'percentage': (count / total * 100) if total > 0 else 0
            }
        
        return True, completeness
    except Exception as e:
        return False, str(e)

def main():
    """الوظيفة الرئيسية"""
    
    print("🔍 **فحص سريع لقاعدة البيانات**")
    print("=" * 50)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # تحميل الإعدادات
    url, key = load_config()
    if not url or not key:
        return
    
    try:
        # إنشاء الاتصال
        supabase = create_client(url, key)
        print("🔗 تم إنشاء اتصال قاعدة البيانات...")
        
        # اختبار الاتصال
        connected, result = test_connection(supabase)
        if not connected:
            print(f"❌ فشل الاتصال: {result}")
            return
        
        print("✅ تم الاتصال بنجاح!")
        
        # جلب الإحصائيات الأساسية
        success, stats = get_basic_stats(supabase)
        if success:
            print(f"\n📊 **إحصائيات أساسية:**")
            print(f"   📈 عدد الأسهم: {stats['total_stocks']}")
            
            if stats['sample_data']:
                print(f"\n📋 **عينة من البيانات:**")
                for i, stock in enumerate(stats['sample_data'], 1):
                    print(f"   {i}. {stock.get('symbol', 'N/A')} - السعر: {stock.get('current_price', 'N/A')} - التغير: {stock.get('change_percent', 'N/A')}%")
        else:
            print(f"❌ خطأ في جلب الإحصائيات: {stats}")
            return
        
        # فحص اكتمال البيانات
        success, completeness = check_data_completeness(supabase)
        if success:
            print(f"\n🕳️ **اكتمال البيانات (عينة من 100 سهم):**")
            
            field_names = {
                'ma5': 'MA5',
                'ma20': 'MA20', 
                'target_1': 'الهدف الأول',
                'pe_ratio': 'نسبة P/E',
                'dividend_yield': 'عائد الأرباح',
                'liquidity_ratio': 'نسبة السيولة',
                'market_cap': 'القيمة السوقية'
            }
            
            for field, data in completeness.items():
                name = field_names.get(field, field)
                print(f"   📊 {name}: {data['count']}/{data['total']} ({data['percentage']:.1f}%)")
        else:
            print(f"❌ خطأ في فحص اكتمال البيانات: {completeness}")
        
        # خلاصة
        print(f"\n✅ **خلاصة الفحص:**")
        print("   🔗 قاعدة البيانات متاحة ومتصلة")
        print("   📊 البيانات الأساسية موجودة")
        print("   📋 تم فحص عينة من البيانات")
        
        print(f"\n🎯 **الملاحظات:**")
        if success and completeness:
            # تحليل البيانات المفقودة
            missing_fields = [name for field, data in completeness.items() 
                            if data['percentage'] < 50 
                            for name in [field_names.get(field, field)]]
            
            if missing_fields:
                print("   ⚠️ حقول تحتاج تحسين:")
                for field in missing_fields[:3]:  # أول 3 فقط
                    print(f"      - {field}")
            else:
                print("   ✅ معظم البيانات مكتملة")
        
        print(f"\n🚀 **الخطوة التالية:**")
        print("   - تحسين hooks التطبيق لاستخدام البيانات المتاحة")
        print("   - تجنب الحسابات المكررة في JavaScript")
        print("   - إنشاء views محسوبة للإحصائيات الشائعة")
        
    except Exception as e:
        print(f"❌ خطأ عام في الفحص: {e}")
        print("🔧 تأكد من:")
        print("   - ملف .env موجود ويحتوي على المفاتيح الصحيحة")
        print("   - اتصال الإنترنت متاح")

if __name__ == "__main__":
    main()
