#!/usr/bin/env python3
"""
أداة تنظيف وتحليل ملفات data_sync
تحديد الملفات المستخدمة والمتضاربة والمكررة
"""

import os
import sys
from datetime import datetime
import json
from pathlib import Path

def analyze_data_sync_files():
    """تحليل ملفات data_sync وتحديد التضارب"""
    
    script_dir = Path(__file__).parent
    
    print("📁 **تحليل ملفات data_sync**")
    print("=" * 50)
    
    # قائمة جميع الملفات
    files = list(script_dir.glob("*.py"))
    sql_files = list(script_dir.glob("*.sql"))
    log_files = list(script_dir.glob("*.log"))
    
    print(f"📊 إجمالي الملفات:")
    print(f"  🐍 ملفات Python: {len(files)}")
    print(f"  🗄️ ملفات SQL: {len(sql_files)}")  
    print(f"  📋 ملفات Log: {len(log_files)}")
    
    # تحليل ملفات Python
    categorize_python_files(files)
    
    # تحليل ملفات SQL
    categorize_sql_files(sql_files)
    
    # توصيات التنظيف
    cleanup_recommendations()

def categorize_python_files(files):
    """تصنيف ملفات Python حسب الوظيفة"""
    
    print(f"\n🐍 **تحليل ملفات Python:**")
    
    categories = {
        "data_loaders": {
            "description": "محملات البيانات",
            "files": [],
            "conflicts": []
        },
        "utilities": {
            "description": "أدوات مساعدة", 
            "files": [],
            "conflicts": []
        },
        "checkers": {
            "description": "أدوات الفحص",
            "files": [],
            "conflicts": []
        },
        "maintenance": {
            "description": "أدوات الصيانة",
            "files": [],
            "conflicts": []
        },
        "deprecated": {
            "description": "ملفات قديمة/مهجورة",
            "files": [],
            "conflicts": []
        }
    }
    
    for file in files:
        filename = file.name
        
        # محملات البيانات
        if any(x in filename for x in ['load_', 'sync_']):
            if 'enhanced' in filename:
                categories["data_loaders"]["files"].append(f"✅ {filename} (محسن)")
            else:
                categories["data_loaders"]["conflicts"].append(f"⚠️ {filename} (قديم)")
                
        # أدوات الفحص  
        elif any(x in filename for x in ['check_', 'test_', 'validate_', 'inspect_']):
            categories["checkers"]["files"].append(f"🔍 {filename}")
            
        # أدوات مساعدة
        elif any(x in filename for x in ['quick_', 'simple_', 'enhanced_']):
            categories["utilities"]["files"].append(f"🛠️ {filename}")
            
        # أدوات الصيانة
        elif any(x in filename for x in ['update_', 'fix_', 'create_', 'add_']):
            categories["maintenance"]["files"].append(f"🔧 {filename}")
            
        # ملفات أخرى
        else:
            categories["utilities"]["files"].append(f"📄 {filename}")
    
    # طباعة التصنيف
    for category, data in categories.items():
        if data["files"] or data["conflicts"]:
            print(f"\n📂 **{data['description']}:**")
            
            for file in data["files"]:
                print(f"  {file}")
                
            if data["conflicts"]:
                print(f"  🚨 **تضارب مكتشف:**")
                for conflict in data["conflicts"]:
                    print(f"    {conflict}")

def categorize_sql_files(sql_files):
    """تحليل ملفات SQL"""
    
    print(f"\n🗄️ **تحليل ملفات SQL:**")
    
    schema_files = []
    patch_files = []
    manual_files = []
    
    for file in sql_files:
        filename = file.name
        
        if any(x in filename for x in ['schema', 'create_']):
            schema_files.append(filename)
        elif any(x in filename for x in ['fix_', 'update_', 'add_']):
            patch_files.append(filename)
        elif 'manual' in filename:
            manual_files.append(filename)
        else:
            patch_files.append(filename)
    
    if schema_files:
        print(f"  📋 **ملفات Schema:** {len(schema_files)}")
        for f in schema_files:
            print(f"    📄 {f}")
    
    if patch_files:
        print(f"  🔧 **ملفات Patches:** {len(patch_files)}")
        for f in patch_files:
            print(f"    🔧 {f}")
            
    if manual_files:
        print(f"  ✋ **ملفات يدوية:** {len(manual_files)}")
        for f in manual_files:
            print(f"    ⚠️ {f}")

def cleanup_recommendations():
    """توصيات تنظيف الملفات"""
    
    print(f"\n🧹 **توصيات التنظيف:**")
    print("=" * 50)
    
    files_to_keep = [
        "load_realtime_enhanced.py",
        "load_historical_enhanced.py", 
        "load_financials_enhanced.py",
        "unified_data_sync.py",
        "database_audit_tool.py",
        "check_data.py",
        "validate_data.py",
        "test_connection.py"
    ]
    
    files_to_archive = [
        "load_realtime.py",
        "load_historical.py",
        "load_financials.py", 
        "copy_reference_files.py",
        "quick_enhanced_import.py",
        "load_real_excel_data.py"
    ]
    
    files_to_delete = [
        "manual_schema_update.sql",
        "fix_financials_constraint.sql",
        "*.log files (older than 30 days)",
        "backup files in backups/ folder"
    ]
    
    print("✅ **ملفات يُنصح بالاحتفاظ بها:**")
    for f in files_to_keep:
        print(f"  📄 {f}")
    
    print("\n📦 **ملفات يُنصح بأرشفتها:**")
    for f in files_to_archive:
        print(f"  📦 {f}")
        
    print("\n🗑️ **ملفات يُنصح بحذفها:**")
    for f in files_to_delete:
        print(f"  🗑️ {f}")

def create_unified_data_sync():
    """إنشاء ملف موحد لمزامنة البيانات"""
    
    unified_content = '''#!/usr/bin/env python3
"""
نظام مزامنة البيانات الموحد
Unified Data Synchronization System for EGX Stock AI Oracle
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from supabase import create_client, Client
from dotenv import load_dotenv

class UnifiedDataSync:
    """نظام مزامنة البيانات الموحد"""
    
    def __init__(self):
        """تهيئة النظام"""
        load_dotenv()
        
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        if not self.supabase_key:
            raise ValueError("SUPABASE_SERVICE_ROLE_KEY is required")
            
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        
        # إعداد اللوجنج
        self.setup_logging()
        
    def setup_logging(self):
        """إعداد نظام اللوجنج"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'unified_sync_{datetime.now().strftime("%Y%m%d")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def sync_realtime_data(self, excel_path: str) -> Dict[str, Any]:
        """مزامنة البيانات اللحظية من Excel"""
        self.logger.info("🔄 بدء مزامنة البيانات اللحظية...")
        
        try:
            # استيراد ومعالجة البيانات اللحظية
            # سيتم تنفيذ هذا بناءً على load_realtime_enhanced.py
            pass
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في مزامنة البيانات اللحظية: {e}")
            return {"success": False, "error": str(e)}
            
    def sync_historical_data(self, data_dir: str) -> Dict[str, Any]:
        """مزامنة البيانات التاريخية من ملفات TXT"""
        self.logger.info("🔄 بدء مزامنة البيانات التاريخية...")
        
        try:
            # استيراد ومعالجة البيانات التاريخية
            # سيتم تنفيذ هذا بناءً على load_historical_enhanced.py
            pass
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في مزامنة البيانات التاريخية: {e}")
            return {"success": False, "error": str(e)}
            
    def sync_financial_data(self, csv_path: str) -> Dict[str, Any]:
        """مزامنة البيانات المالية من CSV"""
        self.logger.info("🔄 بدء مزامنة البيانات المالية...")
        
        try:
            # استيراد ومعالجة البيانات المالية
            # سيتم تنفيذ هذا بناءً على load_financials_enhanced.py
            pass
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في مزامنة البيانات المالية: {e}")
            return {"success": False, "error": str(e)}
            
    def validate_data_integrity(self) -> Dict[str, Any]:
        """التحقق من سلامة البيانات"""
        self.logger.info("🔍 بدء التحقق من سلامة البيانات...")
        
        try:
            # فحص البيانات والتحقق من الحسابات
            pass
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص البيانات: {e}")
            return {"success": False, "error": str(e)}
            
    def run_full_sync(self) -> Dict[str, Any]:
        """تشغيل مزامنة شاملة للبيانات"""
        self.logger.info("🚀 بدء المزامنة الشاملة...")
        
        results = {
            "start_time": datetime.now(),
            "realtime": None,
            "historical": None, 
            "financial": None,
            "validation": None,
            "success": False
        }
        
        try:
            # تنفيذ جميع عمليات المزامنة
            # results["realtime"] = self.sync_realtime_data()
            # results["historical"] = self.sync_historical_data()
            # results["financial"] = self.sync_financial_data()
            # results["validation"] = self.validate_data_integrity()
            
            results["end_time"] = datetime.now()
            results["duration"] = (results["end_time"] - results["start_time"]).total_seconds()
            results["success"] = True
            
            self.logger.info(f"✅ اكتملت المزامنة في {results['duration']:.2f} ثانية")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ فشلت المزامنة الشاملة: {e}")
            results["error"] = str(e)
            return results

def main():
    """الوظيفة الرئيسية"""
    try:
        sync_system = UnifiedDataSync()
        results = sync_system.run_full_sync()
        
        if results["success"]:
            print("✅ اكتملت مزامنة البيانات بنجاح")
        else:
            print(f"❌ فشلت مزامنة البيانات: {results.get('error', 'خطأ غير معروف')}")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")

if __name__ == "__main__":
    main()
'''
    
    return unified_content

def main():
    """الوظيفة الرئيسية"""
    
    print("🔍 **أداة تحليل وتنظيف ملفات Data_Sync**")
    print("=" * 80)
    print(f"⏰ وقت التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # تحليل الملفات الموجودة
    analyze_data_sync_files()
    
    # إنشاء ملف موحد (كمثال)
    print(f"\n📄 **إنشاء نظام مزامنة موحد:**")
    unified_content = create_unified_data_sync()
    
    # حفظ الملف الموحد
    unified_path = Path(__file__).parent / "unified_data_sync_template.py"
    with open(unified_path, 'w', encoding='utf-8') as f:
        f.write(unified_content)
    
    print(f"✅ تم إنشاء قالب النظام الموحد: {unified_path}")
    
    print(f"\n🎯 **الخطوات التالية:**")
    print("1. مراجعة توصيات التنظيف أعلاه")
    print("2. نسخ احتياطية للملفات المهمة")
    print("3. حذف الملفات المتضاربة")
    print("4. تنفيذ النظام الموحد")
    print("5. اختبار المزامنة الجديدة")

if __name__ == "__main__":
    main()
