import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  BarChart3, 
  Users, 
  Zap,
  Target,
  Brain,
  DollarSign,
  Building,
  ArrowUpRight,
  ArrowDownRight,
  Gauge,
  Shield,
  Award,
  Lightbulb,
  TrendingUp as TrendIcon,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Percent,
  <PERSON>Chart,
  LineChart
} from 'lucide-react';
import { useAdvancedMarketData } from '@/hooks/useAdvancedMarketData';
import StockAnalysisLink from '@/components/analysis/StockAnalysisLink';
import { useAnalysis } from '@/hooks/useAnalysis';

const AdvancedMarketDashboard = () => {
  const { openAnalysis } = useAnalysis();
  const [activeTab, setActiveTab] = useState('overview');
  const { data: marketData, isLoading, error } = useAdvancedMarketData();

  const formatNumber = (num: number) => {
    if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const formatCurrency = (num: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  const formatPercent = (num: number) => {
    return `${num.toFixed(2)}%`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(12)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !marketData) {
    return (
      <Card className="bg-red-50 border-red-200">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ في تحميل البيانات</h3>
          <p className="text-red-600">حدث خطأ أثناء تحميل بيانات السوق المتقدمة</p>
        </CardContent>
      </Card>
    );
  }

  const { summary, performers, sectors, technical, advanced } = marketData;

  // Calculate market sentiment
  const marketSentiment = summary.gainers > summary.losers ? 'إيجابي' : 
                         summary.losers > summary.gainers ? 'سلبي' : 'محايد';
  const sentimentColor = summary.gainers > summary.losers ? 'text-green-600' : 
                        summary.losers > summary.gainers ? 'text-red-600' : 'text-gray-600';

  return (
    <div className="space-y-6">
      {/* Market Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Market Sentiment */}
        <Card className="bg-gradient-to-br from-blue-600 to-blue-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm font-medium">معنويات السوق</p>
                <p className="text-2xl font-bold">{marketSentiment}</p>
                <p className="text-blue-200 text-xs">
                  {summary.gainers} صاعد / {summary.losers} هابط
                </p>
              </div>
              <Gauge className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        {/* Market Breadth */}
        <Card className="bg-gradient-to-br from-purple-600 to-purple-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">اتساع السوق</p>
                <p className="text-2xl font-bold">{formatPercent(advanced.marketBreadth)}</p>
                <p className="text-purple-200 text-xs">فوق المتوسط 20</p>
              </div>
              <PieChart className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        {/* Total Volume */}
        <Card className="bg-gradient-to-br from-green-600 to-green-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">إجمالي الحجم</p>
                <p className="text-2xl font-bold">{formatNumber(summary.totalVolume)}</p>
                <p className="text-green-200 text-xs">{summary.activeStocks} سهم نشط</p>
              </div>
              <Activity className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        {/* Total Turnover */}
        <Card className="bg-gradient-to-br from-orange-600 to-orange-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">إجمالي القيمة</p>
                <p className="text-2xl font-bold">{formatNumber(summary.totalTurnover)}</p>
                <p className="text-orange-200 text-xs">جنيه مصري</p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Average P/E */}
        <Card className="bg-gradient-to-br from-indigo-600 to-indigo-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-indigo-100 text-sm font-medium">متوسط P/E</p>
                <p className="text-2xl font-bold">{advanced.averagePE.toFixed(1)}</p>
                <p className="text-indigo-200 text-xs">نسبة السعر للأرباح</p>
              </div>
              <BarChart3 className="h-8 w-8 text-indigo-200" />
            </div>
          </CardContent>
        </Card>

        {/* Average Dividend Yield */}
        <Card className="bg-gradient-to-br from-emerald-600 to-emerald-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-emerald-100 text-sm font-medium">متوسط العائد</p>
                <p className="text-2xl font-bold">{formatPercent(advanced.averageDividendYield)}</p>
                <p className="text-emerald-200 text-xs">عائد الأرباح</p>
              </div>
              <Percent className="h-8 w-8 text-emerald-200" />
            </div>
          </CardContent>
        </Card>

        {/* Speculation Opportunities */}
        <Card className="bg-gradient-to-br from-red-600 to-red-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-100 text-sm font-medium">فرص المضاربة</p>
                <p className="text-2xl font-bold">{summary.speculationOpportunities}</p>
                <p className="text-red-200 text-xs">{formatPercent(advanced.speculationIndex)}</p>
              </div>
              <Zap className="h-8 w-8 text-red-200" />
            </div>
          </CardContent>
        </Card>

        {/* Quality Score */}
        <Card className="bg-gradient-to-br from-teal-600 to-teal-800 text-white border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-teal-100 text-sm font-medium">مؤشر الجودة</p>
                <p className="text-2xl font-bold">{formatPercent(advanced.qualityScore)}</p>
                <p className="text-teal-200 text-xs">بيانات مالية كاملة</p>
              </div>
              <Award className="h-8 w-8 text-teal-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="performers">أفضل الأداء</TabsTrigger>
          <TabsTrigger value="sectors">تحليل القطاعات</TabsTrigger>
          <TabsTrigger value="technical">التحليل الفني</TabsTrigger>
          <TabsTrigger value="advanced">متقدم</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Market Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2" />
                  توزيع السوق
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">الأسهم الصاعدة</span>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-green-600">{summary.gainers}</Badge>
                      <span className="text-xs text-gray-500">
                        {formatPercent((summary.gainers / summary.totalStocks) * 100)}
                      </span>
                    </div>
                  </div>
                  <Progress value={(summary.gainers / summary.totalStocks) * 100} className="h-2" />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">الأسهم الهابطة</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="destructive">{summary.losers}</Badge>
                      <span className="text-xs text-gray-500">
                        {formatPercent((summary.losers / summary.totalStocks) * 100)}
                      </span>
                    </div>
                  </div>
                  <Progress value={(summary.losers / summary.totalStocks) * 100} className="h-2" />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">الأسهم الثابتة</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">{summary.unchanged}</Badge>
                      <span className="text-xs text-gray-500">
                        {formatPercent((summary.unchanged / summary.totalStocks) * 100)}
                      </span>
                    </div>
                  </div>
                  <Progress value={(summary.unchanged / summary.totalStocks) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Technical Health */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="h-5 w-5 mr-2" />
                  الصحة الفنية للسوق
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{summary.bullishStocks}</div>
                    <div className="text-xs text-green-800">اتجاه صاعد</div>
                    <div className="text-xs text-gray-500">MA5 &gt; MA20</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{summary.bearishStocks}</div>
                    <div className="text-xs text-red-800">اتجاه هابط</div>
                    <div className="text-xs text-gray-500">MA5 &lt; MA20</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{technical.strongUptrend}</div>
                    <div className="text-xs text-blue-800">اتجاه قوي</div>
                    <div className="text-xs text-gray-500">MA5&gt;MA20&gt;MA50</div>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{technical.strongDowntrend}</div>
                    <div className="text-xs text-orange-800">هبوط قوي</div>
                    <div className="text-xs text-gray-500">MA5&lt;MA20&lt;MA50</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performers" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Top Gainer */}
            {performers.topGainer && (
              <Card className="border-green-200 bg-green-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-green-800">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    أكبر رابح
                  </CardTitle>
                </CardHeader>                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-lg">{performers.topGainer.symbol}</span>
                      <Badge className="bg-green-600">
                        +{formatPercent(performers.topGainer.change_percent || 0)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>السعر: {formatCurrency(performers.topGainer.current_price || 0)}</div>
                      <div>الحجم: {formatNumber(performers.topGainer.volume || 0)}</div>
                      <div>القطاع: {performers.topGainer.sector || 'غير محدد'}</div>
                    </div>
                    <div className="mt-3">
                      <StockAnalysisLink 
                        symbol={performers.topGainer.symbol || ''}
                        onAnalyze={openAnalysis}
                        variant="outline"
                        size="sm"
                        className="w-full"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Top Loser */}
            {performers.topLoser && (
              <Card className="border-red-200 bg-red-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-red-800">
                    <TrendingDown className="h-5 w-5 mr-2" />
                    أكبر خاسر
                  </CardTitle>
                </CardHeader>                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-lg">{performers.topLoser.symbol}</span>
                      <Badge variant="destructive">
                        {formatPercent(performers.topLoser.change_percent || 0)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>السعر: {formatCurrency(performers.topLoser.current_price || 0)}</div>
                      <div>الحجم: {formatNumber(performers.topLoser.volume || 0)}</div>
                      <div>القطاع: {performers.topLoser.sector || 'غير محدد'}</div>
                    </div>
                    <div className="mt-3">
                      <StockAnalysisLink 
                        symbol={performers.topLoser.symbol || ''}
                        onAnalyze={openAnalysis}
                        variant="outline"
                        size="sm"
                        className="w-full"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Most Active */}
            {performers.mostActive && (
              <Card className="border-blue-200 bg-blue-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-blue-800">
                    <Activity className="h-5 w-5 mr-2" />
                    الأكثر نشاطاً
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-lg">{performers.mostActive.symbol}</span>
                      <Badge variant="secondary">
                        {formatNumber(performers.mostActive.volume || 0)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>السعر: {formatCurrency(performers.mostActive.current_price || 0)}</div>
                      <div>التغير: {formatPercent(performers.mostActive.change_percent || 0)}</div>
                      <div>القطاع: {performers.mostActive.sector || 'غير محدد'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Best Dividend */}
            {performers.bestDividend && (
              <Card className="border-purple-200 bg-purple-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-purple-800">
                    <Award className="h-5 w-5 mr-2" />
                    أفضل عائد
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-lg">{performers.bestDividend.symbol}</span>
                      <Badge className="bg-purple-600">
                        {formatPercent(performers.bestDividend.dividend_yield || 0)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>السعر: {formatCurrency(performers.bestDividend.current_price || 0)}</div>
                      <div>P/E: {(performers.bestDividend.pe_ratio || 0).toFixed(1)}</div>
                      <div>القطاع: {performers.bestDividend.sector || 'غير محدد'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Best Liquidity */}
            {performers.bestLiquidity && (
              <Card className="border-teal-200 bg-teal-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-teal-800">
                    <Zap className="h-5 w-5 mr-2" />
                    أفضل سيولة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-lg">{performers.bestLiquidity.symbol}</span>
                      <Badge className="bg-teal-600">
                        {(performers.bestLiquidity.liquidity_ratio || 0).toFixed(1)}%
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>السعر: {formatCurrency(performers.bestLiquidity.current_price || 0)}</div>
                      <div>التدفق: {(performers.bestLiquidity.liquidity_flow || 0).toFixed(2)}</div>
                      <div>القطاع: {performers.bestLiquidity.sector || 'غير محدد'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Strongest Uptrend */}
            {performers.strongestUptrend && (
              <Card className="border-emerald-200 bg-emerald-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-emerald-800">
                    <TrendIcon className="h-5 w-5 mr-2" />
                    أقوى اتجاه صاعد
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-lg">{performers.strongestUptrend.symbol}</span>
                      <Badge className="bg-emerald-600">
                        +{formatPercent(performers.strongestUptrend.change_percent || 0)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>السعر: {formatCurrency(performers.strongestUptrend.current_price || 0)}</div>
                      <div>MA5: {(performers.strongestUptrend.ma5 || 0).toFixed(2)}</div>
                      <div>MA20: {(performers.strongestUptrend.ma20 || 0).toFixed(2)}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="sectors" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sectors.map((sector, index) => (
              <Card key={sector.sector} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center justify-between">
                    <div className="flex items-center">
                      <Building className="h-5 w-5 mr-2" />
                      <span className="truncate">{sector.sector}</span>
                    </div>
                    <Badge variant={
                      sector.performance === 'bullish' ? 'default' : 
                      sector.performance === 'bearish' ? 'destructive' : 'secondary'
                    }>
                      {formatPercent(sector.avgChange)}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">عدد الشركات:</span>
                      <span className="font-medium">{sector.stockCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">إجمالي الحجم:</span>
                      <span className="font-medium">{formatNumber(sector.totalVolume)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">القيمة السوقية:</span>
                      <span className="font-medium">{formatNumber(sector.totalTurnover)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">متوسط P/E:</span>
                      <span className="font-medium">{sector.avgPE.toFixed(1)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">عائد الأرباح:</span>
                      <span className="font-medium">{formatPercent(sector.avgDividendYield)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">أفضل سهم:</span>
                      <span className="font-medium text-blue-600">{sector.topStock}</span>
                    </div>
                    {sector.speculationCount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">فرص مضاربة:</span>
                        <Badge variant="outline" className="text-xs">
                          {sector.speculationCount}
                        </Badge>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="technical" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Technical Signals */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Brain className="h-5 w-5 mr-2" />
                  الإشارات الفنية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                    <div className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      <span className="font-medium">تقاطع صاعد</span>
                    </div>
                    <Badge className="bg-green-600">{technical.bullishCrossover}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg">
                    <div className="flex items-center">
                      <XCircle className="w-4 h-4 text-red-600 mr-2" />
                      <span className="font-medium">تقاطع هابط</span>
                    </div>
                    <Badge className="bg-red-600">{technical.bearishCrossover}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                    <div className="flex items-center">
                      <ArrowUpRight className="w-4 h-4 text-blue-600 mr-2" />
                      <span className="font-medium">اختراق المقاومة</span>
                    </div>
                    <Badge className="bg-blue-600">{technical.breakoutsAboveMA20}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                    <div className="flex items-center">
                      <Target className="w-4 h-4 text-purple-600 mr-2" />
                      <span className="font-medium">قرب الأهداف</span>
                    </div>
                    <Badge className="bg-purple-600">{technical.nearTargets}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg">
                    <div className="flex items-center">
                      <Shield className="w-4 h-4 text-orange-600 mr-2" />
                      <span className="font-medium">قرب وقف الخسارة</span>
                    </div>
                    <Badge className="bg-orange-600">{technical.nearStopLoss}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Liquidity Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  تحليل السيولة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {technical.liquidityInflow}
                    </div>
                    <div className="text-sm text-green-800">تدفق داخل</div>
                    <div className="text-xs text-gray-500">نسبة السيولة &gt; 1</div>
                  </div>
                  
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {technical.liquidityOutflow}
                    </div>
                    <div className="text-sm text-red-800">تدفق خارج</div>
                    <div className="text-xs text-gray-500">نسبة السيولة &lt; 1</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">إجمالي تدفق السيولة:</span>
                    <span className="font-bold">{formatNumber(advanced.totalLiquidityFlow)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">الأسهم عالية الزخم:</span>
                    <span className="font-bold">{technical.highMomentum}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">مؤشر التقلب:</span>
                    <span className="font-bold">{advanced.volatilityIndex.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Market Health Score */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Gauge className="h-5 w-5 mr-2" />
                  مؤشر صحة السوق
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">اتساع السوق</span>
                      <span className="text-sm font-medium">{formatPercent(advanced.marketBreadth)}</span>
                    </div>
                    <Progress value={advanced.marketBreadth} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">مؤشر الجودة</span>
                      <span className="text-sm font-medium">{formatPercent(advanced.qualityScore)}</span>
                    </div>
                    <Progress value={advanced.qualityScore} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">مؤشر المضاربة</span>
                      <span className="text-sm font-medium">{formatPercent(advanced.speculationIndex)}</span>
                    </div>
                    <Progress value={advanced.speculationIndex} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">نقاط الزخم</span>
                      <span className="text-sm font-medium">{advanced.momentumScore.toFixed(1)}</span>
                    </div>
                    <Progress value={Math.min(advanced.momentumScore, 100)} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Key Statistics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  إحصائيات رئيسية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">إجمالي الأسهم:</span>
                    <span className="font-bold">{summary.totalStocks}</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">الأسهم النشطة:</span>
                    <span className="font-bold">{summary.activeStocks}</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">أسهم الحجم العالي:</span>
                    <span className="font-bold">{summary.highVolumeStocks}</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">أسهم الأرباح:</span>
                    <span className="font-bold">{summary.dividendStocks}</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">متوسط التغير:</span>
                    <span className={`font-bold ${summary.avgChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatPercent(summary.avgChange)}
                    </span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">متوسط الحجم:</span>
                    <span className="font-bold">{formatNumber(summary.avgVolume)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedMarketDashboard;
