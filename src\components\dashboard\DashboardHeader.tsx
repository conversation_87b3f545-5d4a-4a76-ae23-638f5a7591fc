
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sun, Moon, Sparkles, Zap } from 'lucide-react';

interface DashboardHeaderProps {
  title: string;
  description: string;
  isDarkMode: boolean;
  toggleTheme: () => void;
}

const DashboardHeader = ({ title, description, isDarkMode, toggleTheme }: DashboardHeaderProps) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 transition-all duration-700 translate-y-0 opacity-100 relative">
      {/* Magical background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-50 rounded-lg"></div>
      
      <div className="relative z-10">
        <h2 className="text-4xl font-bold text-gradient-primary arabic mb-2 relative" dir="rtl">
          {title}
          <Sparkles className="h-6 w-6 text-egx-gold-400 absolute -top-2 -right-2 animate-pulse" />
        </h2>
        <p className="text-lg text-muted-foreground arabic leading-relaxed" dir="rtl">
          {description}
        </p>
      </div>
      <div className="flex items-center gap-3 relative z-10">
        <Button
          variant="outline"
          size="sm"
          onClick={toggleTheme}
          className="btn-animate glass-effect border-2 hover:shadow-magical relative overflow-hidden"
        >
          <div className="flex items-center gap-2">
            {isDarkMode ? 
              <Sun className="h-4 w-4 animate-pulse" /> : 
              <Moon className="h-4 w-4 animate-pulse" />
            }
            {isDarkMode ? 
              <Zap className="h-3 w-3 text-yellow-400" /> : 
              <Sparkles className="h-3 w-3 text-blue-400" />
            }
          </div>
        </Button>
        <Badge className="bg-egx-gold-500 text-egx-gold-900 hover:bg-egx-gold-400 px-4 py-2 text-sm shadow-magical btn-animate relative overflow-hidden">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>آخر تحديث: منذ دقيقتين</span>
            <Sparkles className="h-3 w-3 animate-pulse" />
          </div>
        </Badge>
      </div>
    </div>
  );
};

export default DashboardHeader;
