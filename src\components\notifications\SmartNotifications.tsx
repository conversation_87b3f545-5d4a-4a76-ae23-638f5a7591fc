
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Bell, BellOff, Settings, CheckCircle, AlertTriangle, Info, TrendingUp } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Notification {
  id: string;
  type: 'price' | 'news' | 'portfolio' | 'market';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  priority: 'high' | 'medium' | 'low';
  action?: string;
}

const SmartNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [settings, setSettings] = useState({
    priceAlerts: true,
    newsAlerts: true,
    portfolioAlerts: true,
    marketAlerts: true,
    browserNotifications: true
  });
  const [unreadCount, setUnreadCount] = useState(0);
  const { toast } = useToast();

  useEffect(() => {
    // Generate smart notifications
    const generateNotifications = () => {
      const newNotifications: Notification[] = [
        {
          id: '1',
          type: 'price',
          title: 'تحرك سعري مهم',
          message: 'COMI ارتفع بنسبة 5.2% في آخر ساعة',
          timestamp: new Date(Date.now() - 300000),
          isRead: false,
          priority: 'high'
        },
        {
          id: '2',
          type: 'news',
          title: 'خبر عاجل',
          message: 'البنك المركزي المصري يعلن قرارات جديدة بشأن أسعار الفائدة',
          timestamp: new Date(Date.now() - 600000),
          isRead: false,
          priority: 'high'
        },
        {
          id: '3',
          type: 'portfolio',
          title: 'تحديث المحفظة',
          message: 'محفظتك حققت عائد +3.8% اليوم',
          timestamp: new Date(Date.now() - 1800000),
          isRead: true,
          priority: 'medium'
        },
        {
          id: '4',
          type: 'market',
          title: 'إغلاق السوق',
          message: 'EGX30 أغلق على ارتفاع 1.2% عند 18,450 نقطة',
          timestamp: new Date(Date.now() - 3600000),
          isRead: true,
          priority: 'low'
        }
      ];

      setNotifications(newNotifications);
      setUnreadCount(newNotifications.filter(n => !n.isRead).length);
    };

    generateNotifications();

    // Simulate new notifications
    const interval = setInterval(() => {
      const types = ['price', 'news', 'portfolio', 'market'] as const;
      const randomType = types[Math.floor(Math.random() * types.length)];
      
      const messages = {
        price: ['ETEL انخفض بنسبة 3.1%', 'TALAAT وصل لأعلى مستوى في الشهر'],
        news: ['تقرير أرباح إيجابي لقطاع البنوك', 'ارتفاع معدل التضخم الشهري'],
        portfolio: ['تحديث: محفظتك تفوقت على السوق', 'تنبيه: راجع توزيع محفظتك'],
        market: ['حجم التداول وصل 2.8 مليار جنيه', 'أداء قوي لقطاع الاتصالات']
      };

      if (settings[`${randomType}Alerts` as keyof typeof settings]) {
        const newNotification: Notification = {
          id: Date.now().toString(),
          type: randomType,
          title: randomType === 'price' ? 'تحرك سعري' : 
                randomType === 'news' ? 'خبر جديد' :
                randomType === 'portfolio' ? 'تحديث المحفظة' : 'تحديث السوق',
          message: messages[randomType][Math.floor(Math.random() * messages[randomType].length)],
          timestamp: new Date(),
          isRead: false,
          priority: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low'
        };

        setNotifications(prev => [newNotification, ...prev.slice(0, 9)]);
        setUnreadCount(prev => prev + 1);

        // Show toast
        toast({
          title: newNotification.title,
          description: newNotification.message,
        });

        // Browser notification
        if (settings.browserNotifications && 'Notification' in window && Notification.permission === 'granted') {
          new Notification(newNotification.title, {
            body: newNotification.message,
            icon: '/favicon.ico'
          });
        }
      }
    }, 8000);

    return () => clearInterval(interval);
  }, [settings, toast]);

  const markAsRead = (id: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, isRead: true } : n
    ));
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
    setUnreadCount(0);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'price': return TrendingUp;
      case 'news': return Info;
      case 'portfolio': return CheckCircle;
      case 'market': return AlertTriangle;
      default: return Bell;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setSettings(prev => ({ ...prev, browserNotifications: permission === 'granted' }));
    }
  };

  return (
    <Card className="border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-amber-50">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-yellow-800">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            التنبيهات الذكية
            {unreadCount > 0 && (
              <Badge className="bg-red-500 text-white">
                {unreadCount}
              </Badge>
            )}
          </div>
          <Button variant="outline" size="sm" onClick={markAllAsRead}>
            قراءة الكل
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Notification Settings */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-3 p-4 bg-white/60 rounded-lg">
          {Object.entries(settings).map(([key, value]) => (
            <Button
              key={key}
              variant={value ? "default" : "outline"}
              size="sm"
              onClick={() => {
                if (key === 'browserNotifications' && !value) {
                  requestNotificationPermission();
                } else {
                  setSettings(prev => ({ ...prev, [key]: !value }));
                }
              }}
              className="text-xs"
            >
              {value ? <Bell className="h-3 w-3 mr-1" /> : <BellOff className="h-3 w-3 mr-1" />}
              {key === 'priceAlerts' ? 'أسعار' :
               key === 'newsAlerts' ? 'أخبار' :
               key === 'portfolioAlerts' ? 'محفظة' :
               key === 'marketAlerts' ? 'سوق' : 'متصفح'}
            </Button>
          ))}
        </div>

        {/* Notifications List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد تنبيهات</p>
            </div>
          ) : (
            notifications.map(notification => {
              const TypeIcon = getTypeIcon(notification.type);
              return (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border-l-4 cursor-pointer transition-all duration-300 ${
                    notification.isRead 
                      ? 'bg-white/50 border-l-gray-300' 
                      : 'bg-white/80 border-l-blue-500 shadow-sm'
                  } hover:shadow-md`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <TypeIcon className={`h-5 w-5 mt-1 ${
                        notification.isRead ? 'text-gray-400' : 'text-blue-600'
                      }`} />
                      <div className="flex-1">
                        <div className={`font-medium ${
                          notification.isRead ? 'text-gray-600' : 'text-gray-900'
                        }`}>
                          {notification.title}
                        </div>
                        <div className={`text-sm mt-1 ${
                          notification.isRead ? 'text-gray-500' : 'text-gray-700'
                        }`}>
                          {notification.message}
                        </div>
                        <div className="text-xs text-gray-400 mt-2">
                          {notification.timestamp.toLocaleTimeString('ar-EG')}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={`${getPriorityColor(notification.priority)} text-white text-xs`}>
                        {notification.priority === 'high' ? 'عالي' :
                         notification.priority === 'medium' ? 'متوسط' : 'منخفض'}
                      </Badge>
                      {!notification.isRead && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SmartNotifications;
