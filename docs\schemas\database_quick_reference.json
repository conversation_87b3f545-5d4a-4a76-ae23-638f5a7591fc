{"tables": {"stocks_realtime": {"columns": ["ask_price", "ask_volume", "avg_net_volume_3d", "avg_net_volume_5d", "bid_price", "bid_volume", "book_value", "change_amount", "change_percent", "current_price", "dividend_yield", "eps_annual", "free_shares", "high_price", "kj_indicator", "last_trade_date", "last_trade_time", "liquidity_flow", "liquidity_inflow", "liquidity_outflow", "liquidity_ratio", "low_price", "ma10", "ma100", "ma20", "ma200", "ma5", "ma50", "market_cap", "name", "net_liquidity", "open_price", "opening_value", "pe_ratio", "previous_close", "price_range", "sector", "speculation_opportunity", "stock_status", "stop_loss", "symbol", "target_1", "target_2", "target_3", "tk_indicator", "trades_count", "turnover", "updated_at", "volume", "volume_inflow", "volume_outflow"], "record_count": 225, "last_updated": "2025-06-15T18:14:21.384212+00:00"}, "stocks_financials": {"columns": ["analyst_rating", "assets_to_equity", "assets_turnover", "average_volume_10d", "beta", "beta_5_years", "book_value_per_share", "capex_growth", "capital_expenditures", "cash_and_equivalents", "cash_ratio", "cash_short_term_investments", "cash_to_debt_ratio", "continuous_dividend_growth", "created_at", "current_ratio", "debt_to_assets", "debt_to_equity", "description", "dividend_payout_ratio", "dividend_yield", "dividend_yield_indicated", "dividends_paid_annual", "dividends_per_share", "ebitda", "ebitda_growth", "enterprise_value", "eps_basic_ttm", "eps_diluted_ttm", "eps_estimate_quarterly", "eps_growth_ttm", "eps_growth_yoy", "eps_reported_annual", "eps_ttm", "ev_to_ebitda", "ev_to_revenue", "financing_cash_flow", "fiscal_quarter", "fiscal_year", "float_shares_outstanding", "float_shares_percent", "forward_pe", "free_cash_flow", "free_cash_flow_growth", "goodwill", "gross_margin", "gross_profit", "gross_profit_growth", "id", "index_membership", "industry", "investing_cash_flow", "isin", "market_cap", "market_cap_currency", "net_debt", "net_income", "net_income_growth", "net_income_ttm", "net_margin", "number_of_shareholders", "operating_cash_flow", "operating_income", "operating_margin", "pe_ratio", "performance_ytd", "price_to_book", "price_to_cash_flow", "price_to_free_cash_flow", "price_to_sales", "recent_earnings_date", "relative_volume", "revenue_growth", "revenue_ttm", "roa", "roe", "sector", "symbol", "target_performance_1y", "target_price", "target_price_1y", "total_assets", "total_assets_growth", "total_current_assets", "total_current_liabilities", "total_debt", "total_equity", "total_liabilities", "total_revenue", "total_shares_outstanding", "turnover_1_day", "upcoming_earnings_date", "updated_at", "volatility_1_day", "volume_change"], "record_count": 252, "last_updated": "2025-06-15T18:37:21.28981+00:00"}, "stocks_historical": {"columns": ["adjusted_close", "close", "created_at", "date", "high", "id", "low", "open", "open_interest", "symbol", "volume"], "record_count": 442260, "last_updated": null}}, "quick_reference": {"all_realtime_columns": ["ask_price", "ask_volume", "avg_net_volume_3d", "avg_net_volume_5d", "bid_price", "bid_volume", "book_value", "change_amount", "change_percent", "current_price", "dividend_yield", "eps_annual", "free_shares", "high_price", "kj_indicator", "last_trade_date", "last_trade_time", "liquidity_flow", "liquidity_inflow", "liquidity_outflow", "liquidity_ratio", "low_price", "ma10", "ma100", "ma20", "ma200", "ma5", "ma50", "market_cap", "name", "net_liquidity", "open_price", "opening_value", "pe_ratio", "previous_close", "price_range", "sector", "speculation_opportunity", "stock_status", "stop_loss", "symbol", "target_1", "target_2", "target_3", "tk_indicator", "trades_count", "turnover", "updated_at", "volume", "volume_inflow", "volume_outflow"], "all_financial_columns": ["analyst_rating", "assets_to_equity", "assets_turnover", "average_volume_10d", "beta", "beta_5_years", "book_value_per_share", "capex_growth", "capital_expenditures", "cash_and_equivalents", "cash_ratio", "cash_short_term_investments", "cash_to_debt_ratio", "continuous_dividend_growth", "created_at", "current_ratio", "debt_to_assets", "debt_to_equity", "description", "dividend_payout_ratio", "dividend_yield", "dividend_yield_indicated", "dividends_paid_annual", "dividends_per_share", "ebitda", "ebitda_growth", "enterprise_value", "eps_basic_ttm", "eps_diluted_ttm", "eps_estimate_quarterly", "eps_growth_ttm", "eps_growth_yoy", "eps_reported_annual", "eps_ttm", "ev_to_ebitda", "ev_to_revenue", "financing_cash_flow", "fiscal_quarter", "fiscal_year", "float_shares_outstanding", "float_shares_percent", "forward_pe", "free_cash_flow", "free_cash_flow_growth", "goodwill", "gross_margin", "gross_profit", "gross_profit_growth", "id", "index_membership", "industry", "investing_cash_flow", "isin", "market_cap", "market_cap_currency", "net_debt", "net_income", "net_income_growth", "net_income_ttm", "net_margin", "number_of_shareholders", "operating_cash_flow", "operating_income", "operating_margin", "pe_ratio", "performance_ytd", "price_to_book", "price_to_cash_flow", "price_to_free_cash_flow", "price_to_sales", "recent_earnings_date", "relative_volume", "revenue_growth", "revenue_ttm", "roa", "roe", "sector", "symbol", "target_performance_1y", "target_price", "target_price_1y", "total_assets", "total_assets_growth", "total_current_assets", "total_current_liabilities", "total_debt", "total_equity", "total_liabilities", "total_revenue", "total_shares_outstanding", "turnover_1_day", "upcoming_earnings_date", "updated_at", "volatility_1_day", "volume_change"], "all_historical_columns": ["adjusted_close", "close", "created_at", "date", "high", "id", "low", "open", "open_interest", "symbol", "volume"], "common_relationships": {"stocks_realtime_to_stocks_financials": {"common_symbols": 218, "table1_total": 225, "table2_total": 252, "coverage_percentage": 86.51, "sample_common_symbols": ["EALR", "CNFN", "CCRS", "MTIE", "BIDI", "EDFM", "ORWE", "PHDC", "MFPC", "MPCO"]}, "stocks_realtime_to_stocks_historical": {"common_symbols": 1, "table1_total": 225, "table2_total": 1, "coverage_percentage": 0.44, "sample_common_symbols": ["AALR"]}, "stocks_financials_to_stocks_historical": {"common_symbols": 1, "table1_total": 252, "table2_total": 1, "coverage_percentage": 0.4, "sample_common_symbols": ["AALR"]}}}}