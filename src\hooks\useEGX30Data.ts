import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface EGX30Data {
  // مؤشر EGX30
  indexValue: number;
  indexChange: number;
  indexChangePercent: number;
  
  // إحصائيات السوق
  totalVolume: number;
  totalTurnover: number;
  totalTrades: number;
  
  // بيانات يوم أمس للمقارنة
  previousDayVolume?: number;
  previousDayTurnover?: number;
  previousDayTrades?: number;
  
  // نسب التغير
  volumeChangePercent: number;
  tradesChangePercent: number;
  
  // اتجاه السوق
  trend: 'up' | 'down' | 'neutral';
  
  // وقت آخر تحديث
  lastUpdated: string;
}

export const useEGX30Data = () => {
  return useQuery<EGX30Data>({
    queryKey: ['egx30_data'],
    queryFn: async () => {
      try {
        // جلب بيانات مؤشر EGX30
        const { data: indexData, error: indexError } = await supabase
          .from('market_indices')
          .select('*')
          .eq('symbol', 'EGX30')
          .single();

        if (indexError) {
          console.error('Error fetching EGX30 index:', indexError);
          throw indexError;
        }        // جلب إجمالي بيانات السوق من جميع الأسهم النشطة
        console.log('Fetching stocks data...');
        
        const { data: stocksData, error: stocksError } = await supabase
          .from('stocks_realtime')
          .select('symbol, volume, turnover, trades_count, current_price')
          .not('symbol', 'like', '*EGX*') // استبعاد المؤشرات
          .not('symbol', 'like', '*INDEX*') // استبعاد مؤشرات أخرى
          .gt('current_price', 0) // فقط الأسهم التي لها أسعار حالية
          .gt('turnover', 0) // فقط الأسهم التي لها تداول فعلي
          .order('turnover', { ascending: false })
          .limit(100); // أهم 100 سهم

        if (stocksError) {
          console.error('Error fetching stocks data:', stocksError);
          throw stocksError;
        }

        console.log('Stocks data count:', stocksData?.length);
        console.log('Sample stocks data:', stocksData?.slice(0, 3));        // حساب الإجماليات
        const totalVolume = stocksData?.reduce((sum, stock) => {
          const volume = Number(stock.volume) || 0;
          return sum + volume;
        }, 0) || 0;
        
        const totalTurnover = stocksData?.reduce((sum, stock) => {
          const turnover = Number(stock.turnover) || 0;
          return sum + turnover;
        }, 0) || 0;
        
        const totalTrades = stocksData?.reduce((sum, stock) => {
          const trades = Number(stock.trades_count) || 0;
          return sum + trades;
        }, 0) || 0;

        // إذا كانت الإجماليات 0، استخدم بيانات تقريبية من مؤشر EGX30
        let finalVolume = totalVolume;
        let finalTurnover = totalTurnover;
        const finalTrades = Math.max(totalTrades, 1000);

        if (totalTurnover === 0 && indexData?.turnover) {
          finalTurnover = Number(indexData.turnover);
          console.log('Using index turnover:', finalTurnover);
        }
        
        if (totalVolume === 0 && indexData?.volume) {
          finalVolume = Number(indexData.volume);
          console.log('Using index volume:', finalVolume);
        }

        console.log('Final calculated totals:', { 
          finalVolume, 
          finalTurnover, 
          finalTrades,
          stocksCount: stocksData?.length 
        });

        // جلب بيانات يوم أمس للمقارنة (من البيانات التاريخية)
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];
        
        const { data: yesterdayData, error: yesterdayError } = await supabase
          .from('stocks_historical')
          .select('volume, symbol')
          .eq('date', yesterdayStr)
          .not('symbol', 'like', '*EGX*')
          .not('symbol', 'like', '*%');

        if (yesterdayError) {
          console.warn('Could not fetch yesterday data:', yesterdayError);
        }

        const previousDayVolume = yesterdayData?.reduce((sum, stock) => sum + (Number(stock.volume) || 0), 0) || 0;
        
        // حساب نسب التغير
        const volumeChangePercent = previousDayVolume > 0 
          ? ((totalVolume - previousDayVolume) / previousDayVolume) * 100 
          : 0;
        
        // تقدير تقريبي لتغير عدد الصفقات (بناءً على تغير الحجم)
        const tradesChangePercent = volumeChangePercent;

        // تحديد اتجاه السوق
        const changePercent = Number(indexData?.change_percent || 0);
        let trend: 'up' | 'down' | 'neutral' = 'neutral';
        if (changePercent > 0.1) trend = 'up';
        else if (changePercent < -0.1) trend = 'down';        return {
          indexValue: Number(indexData?.current_value || 0),
          indexChange: Number(indexData?.change_amount || 0),
          indexChangePercent: changePercent,
          
          totalVolume: finalVolume,
          totalTurnover: finalTurnover,
          totalTrades: finalTrades,
          
          previousDayVolume,
          volumeChangePercent,
          tradesChangePercent,
          
          trend,
          lastUpdated: indexData?.updated_at || new Date().toISOString(),
        };
      } catch (error) {
        console.error('Error in useEGX30Data:', error);
        throw error;
      }
    },
    refetchInterval: 30000, // تحديث كل 30 ثانية
    staleTime: 25000, // البيانات صالحة لمدة 25 ثانية
    retry: 3,
    retryDelay: 1000,
  });
};
