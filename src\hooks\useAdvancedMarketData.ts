import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Complete interface matching all available columns
export interface CompleteStock {
  symbol: string;
  name: string | null;
  current_price: number | null;
  change_percent: number | null;
  change_amount: number | null;
  volume: number | null;
  turnover: number | null;
  market_cap: number | null;
  sector: string | null;
  ma5: number | null;
  ma10: number | null;
  ma20: number | null;
  ma50: number | null;
  ma100: number | null;
  ma200: number | null;
  high_price: number | null;
  low_price: number | null;
  open_price: number | null;
  previous_close: number | null;
  updated_at: string | null;
  tk_indicator: number | null;
  kj_indicator: number | null;
  target_1: number | null;
  target_2: number | null;
  target_3: number | null;
  stop_loss: number | null;
  liquidity_ratio: number | null;
  net_liquidity: number | null;
  liquidity_inflow: number | null;
  liquidity_outflow: number | null;
  volume_inflow: number | null;
  volume_outflow: number | null;
  liquidity_flow: number | null;
  eps_annual: number | null;
  book_value: number | null;
  pe_ratio: number | null;
  dividend_yield: number | null;
  free_shares: number | null;
  trades_count: number | null;
  bid_price: number | null;
  ask_price: number | null;
  bid_volume: number | null;
  ask_volume: number | null;
  last_trade_time: string | null;
  last_trade_date: string | null;
  stock_status: number | null;
  speculation_opportunity: boolean | null;
  price_range: number | null;
  avg_net_volume_3d: number | null;
  avg_net_volume_5d: number | null;
  opening_value: number | null;
}

export interface MarketSummary {
  totalStocks: number;
  totalVolume: number;
  totalTurnover: number;
  totalMarketCap: number;
  avgVolume: number;
  avgChange: number;
  gainers: number;
  losers: number;
  unchanged: number;
  activeStocks: number;
  highVolumeStocks: number;
  bullishStocks: number;
  bearishStocks: number;
  dividendStocks: number;
  speculationOpportunities: number;
}

export interface TopPerformers {
  topGainer: CompleteStock | null;
  topLoser: CompleteStock | null;
  mostActive: CompleteStock | null;
  highestTurnover: CompleteStock | null;
  bestDividend: CompleteStock | null;
  bestLiquidity: CompleteStock | null;
  bestPE: CompleteStock | null;
  strongestUptrend: CompleteStock | null;
}

export interface SectorAnalysis {
  sector: string;
  stockCount: number;
  totalVolume: number;
  totalTurnover: number;
  avgChange: number;
  avgPE: number;
  avgDividendYield: number;
  topStock: string;
  performance: 'bullish' | 'bearish' | 'neutral';
  liquidityFlow: number;
  speculationCount: number;
}

export interface TechnicalSignals {
  breakoutsAboveMA20: number;
  bullishCrossover: number;
  bearishCrossover: number;
  oversoldStocks: number;
  overboughtStocks: number;
  strongUptrend: number;
  strongDowntrend: number;
  nearTargets: number;
  nearStopLoss: number;
  liquidityInflow: number;
  liquidityOutflow: number;
  highMomentum: number;
}

export interface AdvancedMetrics {
  averagePE: number;
  averageDividendYield: number;
  totalLiquidityFlow: number;
  marketBreadth: number; // Percentage of stocks above MA20
  momentumScore: number;
  volatilityIndex: number;
  speculationIndex: number;
  qualityScore: number;
}

export interface FullMarketData {
  summary: MarketSummary;
  performers: TopPerformers;
  sectors: SectorAnalysis[];
  technical: TechnicalSignals;
  advanced: AdvancedMetrics;
  stocks: CompleteStock[];
  lastUpdated: string;
}

export function useAdvancedMarketData() {
  return useQuery<FullMarketData>({
    queryKey: ['advanced-market-data'],
    queryFn: async () => {
      console.log('🚀 Fetching advanced market data...');
      
      // Get ALL columns for complete analysis
      const { data: rawStocks, error } = await supabase
        .from('stocks_realtime')
        .select('*')
        .not('symbol', 'like', '%EGX%')
        .not('symbol', 'like', '%ETF%')
        .not('name', 'is', null)
        .order('volume', { ascending: false });

      if (error) {
        console.error('❌ Error fetching stocks:', error);
        throw error;
      }

      if (!rawStocks || rawStocks.length === 0) {
        console.warn('⚠️ No stock data found');
        throw new Error('No stock data available');
      }

      // Cast to our interface
      const stocks = rawStocks as CompleteStock[];
      console.log(`✅ Fetched ${stocks.length} complete stocks`);

      // === MARKET SUMMARY ===
      const summary: MarketSummary = {
        totalStocks: stocks.length,
        totalVolume: stocks.reduce((sum, s) => sum + (s.volume || 0), 0),
        totalTurnover: stocks.reduce((sum, s) => sum + (s.turnover || 0), 0),
        totalMarketCap: stocks.reduce((sum, s) => sum + (s.market_cap || 0), 0),
        avgVolume: 0,
        avgChange: 0,
        gainers: 0,
        losers: 0,
        unchanged: 0,
        activeStocks: stocks.filter(s => (s.volume || 0) > 0).length,
        highVolumeStocks: stocks.filter(s => (s.volume || 0) > 100000).length,
        bullishStocks: stocks.filter(s => (s.ma5 || 0) > (s.ma20 || 0)).length,
        bearishStocks: stocks.filter(s => (s.ma5 || 0) < (s.ma20 || 0)).length,
        dividendStocks: stocks.filter(s => (s.dividend_yield || 0) > 0).length,
        speculationOpportunities: stocks.filter(s => s.speculation_opportunity === true).length
      };

      // Calculate averages and distributions
      let totalChange = 0;
      let validChangeCount = 0;
      
      stocks.forEach(stock => {
        const change = stock.change_percent || 0;
        if (stock.change_percent !== null) {
          totalChange += change;
          validChangeCount++;
        }
        
        if (change > 0) summary.gainers++;
        else if (change < 0) summary.losers++;
        else summary.unchanged++;
      });

      summary.avgVolume = summary.totalVolume / stocks.length;
      summary.avgChange = validChangeCount > 0 ? totalChange / validChangeCount : 0;

      // === TOP PERFORMERS ===
      const validStocks = stocks.filter(s => s.change_percent !== null);
      const activeStocks = stocks.filter(s => (s.volume || 0) > 0);
      
      const performers: TopPerformers = {
        topGainer: validStocks.length > 0 
          ? validStocks.reduce((max, stock) => 
              (stock.change_percent || 0) > (max.change_percent || 0) ? stock : max
            )
          : null,
        topLoser: validStocks.length > 0 
          ? validStocks.reduce((min, stock) => 
              (stock.change_percent || 0) < (min.change_percent || 0) ? stock : min
            )
          : null,
        mostActive: activeStocks.length > 0 
          ? activeStocks.reduce((max, stock) => 
              (stock.volume || 0) > (max.volume || 0) ? stock : max
            )
          : null,
        highestTurnover: activeStocks.length > 0 
          ? activeStocks.reduce((max, stock) => 
              (stock.turnover || 0) > (max.turnover || 0) ? stock : max
            )
          : null,
        bestDividend: stocks.filter(s => (s.dividend_yield || 0) > 0).length > 0
          ? stocks.filter(s => (s.dividend_yield || 0) > 0).reduce((max, stock) => 
              (stock.dividend_yield || 0) > (max.dividend_yield || 0) ? stock : max
            )
          : null,
        bestLiquidity: stocks.filter(s => (s.liquidity_ratio || 0) > 0).length > 0
          ? stocks.filter(s => (s.liquidity_ratio || 0) > 0).reduce((max, stock) => 
              (stock.liquidity_ratio || 0) > (max.liquidity_ratio || 0) ? stock : max
            )
          : null,
        bestPE: stocks.filter(s => (s.pe_ratio || 0) > 0 && (s.pe_ratio || 0) < 50).length > 0
          ? stocks.filter(s => (s.pe_ratio || 0) > 0 && (s.pe_ratio || 0) < 50).reduce((min, stock) => 
              (stock.pe_ratio || 0) < (min.pe_ratio || 0) ? stock : min
            )
          : null,
        strongestUptrend: stocks.filter(s => 
          (s.ma5 || 0) > (s.ma20 || 0) && 
          (s.ma20 || 0) > (s.ma50 || 0) && 
          (s.change_percent || 0) > 0
        ).length > 0
          ? stocks.filter(s => 
              (s.ma5 || 0) > (s.ma20 || 0) && 
              (s.ma20 || 0) > (s.ma50 || 0) && 
              (s.change_percent || 0) > 0
            ).reduce((max, stock) => 
              (stock.change_percent || 0) > (max.change_percent || 0) ? stock : max
            )
          : null
      };

      // === SECTOR ANALYSIS ===
      const sectorMap = new Map<string, {
        stocks: CompleteStock[];
        totalVolume: number;
        totalTurnover: number;
        totalChange: number;
        validChangeCount: number;
        totalPE: number;
        validPECount: number;
        totalDividend: number;
        validDividendCount: number;
        liquidityFlow: number;
        speculationCount: number;
      }>();

      stocks.forEach(stock => {
        if (stock.sector) {
          const existing = sectorMap.get(stock.sector) || {
            stocks: [],
            totalVolume: 0,
            totalTurnover: 0,
            totalChange: 0,
            validChangeCount: 0,
            totalPE: 0,
            validPECount: 0,
            totalDividend: 0,
            validDividendCount: 0,
            liquidityFlow: 0,
            speculationCount: 0
          };
          
          existing.stocks.push(stock);
          existing.totalVolume += (stock.volume || 0);
          existing.totalTurnover += (stock.turnover || 0);
          existing.liquidityFlow += (stock.liquidity_flow || 0);
          
          if (stock.change_percent !== null) {
            existing.totalChange += stock.change_percent;
            existing.validChangeCount++;
          }
          
          if (stock.pe_ratio !== null && stock.pe_ratio > 0) {
            existing.totalPE += stock.pe_ratio;
            existing.validPECount++;
          }
          
          if (stock.dividend_yield !== null && stock.dividend_yield > 0) {
            existing.totalDividend += stock.dividend_yield;
            existing.validDividendCount++;
          }
          
          if (stock.speculation_opportunity === true) {
            existing.speculationCount++;
          }
          
          sectorMap.set(stock.sector, existing);
        }
      });

      const sectors: SectorAnalysis[] = Array.from(sectorMap.entries()).map(([sector, data]) => {
        const avgChange = data.validChangeCount > 0 ? data.totalChange / data.validChangeCount : 0;
        const avgPE = data.validPECount > 0 ? data.totalPE / data.validPECount : 0;
        const avgDividendYield = data.validDividendCount > 0 ? data.totalDividend / data.validDividendCount : 0;
        const topStock = data.stocks.reduce((best, stock) => 
          (stock.change_percent || 0) > (best.change_percent || 0) ? stock : best
        );

        return {
          sector,
          stockCount: data.stocks.length,
          totalVolume: data.totalVolume,
          totalTurnover: data.totalTurnover,
          avgChange,
          avgPE,
          avgDividendYield,
          topStock: topStock.symbol,
          performance: avgChange > 1 ? 'bullish' as const : avgChange < -1 ? 'bearish' as const : 'neutral' as const,
          liquidityFlow: data.liquidityFlow,
          speculationCount: data.speculationCount
        };
      }).sort((a, b) => b.totalTurnover - a.totalTurnover);

      // === TECHNICAL SIGNALS ===
      const technical: TechnicalSignals = {
        breakoutsAboveMA20: stocks.filter(s => 
          (s.current_price || 0) > (s.ma20 || 0) && s.ma20 !== null && s.current_price !== null
        ).length,
        bullishCrossover: stocks.filter(s => 
          (s.ma5 || 0) > (s.ma20 || 0) && s.ma5 !== null && s.ma20 !== null
        ).length,
        bearishCrossover: stocks.filter(s => 
          (s.ma5 || 0) < (s.ma20 || 0) && s.ma5 !== null && s.ma20 !== null
        ).length,
        oversoldStocks: stocks.filter(s => 
          (s.current_price || 0) < (s.ma5 || 0) * 0.95 && s.ma5 !== null && s.current_price !== null
        ).length,
        overboughtStocks: stocks.filter(s => 
          (s.current_price || 0) > (s.ma5 || 0) * 1.05 && s.ma5 !== null && s.current_price !== null
        ).length,
        strongUptrend: stocks.filter(s => 
          (s.ma5 || 0) > (s.ma20 || 0) && 
          (s.ma20 || 0) > (s.ma50 || 0) && 
          s.ma5 !== null && s.ma20 !== null && s.ma50 !== null
        ).length,
        strongDowntrend: stocks.filter(s => 
          (s.ma5 || 0) < (s.ma20 || 0) && 
          (s.ma20 || 0) < (s.ma50 || 0) && 
          s.ma5 !== null && s.ma20 !== null && s.ma50 !== null
        ).length,
        nearTargets: stocks.filter(s => 
          s.target_1 !== null && s.current_price !== null &&
          Math.abs((s.current_price || 0) - (s.target_1 || 0)) / (s.target_1 || 1) < 0.05
        ).length,
        nearStopLoss: stocks.filter(s => 
          s.stop_loss !== null && s.current_price !== null &&
          Math.abs((s.current_price || 0) - (s.stop_loss || 0)) / (s.stop_loss || 1) < 0.05
        ).length,
        liquidityInflow: stocks.filter(s => (s.liquidity_flow || 0) > 1).length,
        liquidityOutflow: stocks.filter(s => (s.liquidity_flow || 0) < 1 && (s.liquidity_flow || 0) > 0).length,
        highMomentum: stocks.filter(s => 
          Math.abs(s.change_percent || 0) > 3 && (s.volume || 0) > 100000
        ).length
      };

      // === ADVANCED METRICS ===
      const validPEStocks = stocks.filter(s => s.pe_ratio !== null && s.pe_ratio > 0);
      const validDividendStocks = stocks.filter(s => s.dividend_yield !== null && s.dividend_yield > 0);
      const validLiquidityStocks = stocks.filter(s => s.liquidity_flow !== null);
      const stocksWithMA = stocks.filter(s => s.ma20 !== null && s.current_price !== null);
      
      const advanced: AdvancedMetrics = {
        averagePE: validPEStocks.length > 0 
          ? validPEStocks.reduce((sum, s) => sum + (s.pe_ratio || 0), 0) / validPEStocks.length 
          : 0,
        averageDividendYield: validDividendStocks.length > 0 
          ? validDividendStocks.reduce((sum, s) => sum + (s.dividend_yield || 0), 0) / validDividendStocks.length 
          : 0,
        totalLiquidityFlow: validLiquidityStocks.reduce((sum, s) => sum + (s.liquidity_flow || 0), 0),
        marketBreadth: stocksWithMA.length > 0 
          ? (stocksWithMA.filter(s => (s.current_price || 0) > (s.ma20 || 0)).length / stocksWithMA.length) * 100 
          : 0,
        momentumScore: Math.abs(summary.avgChange) * (summary.highVolumeStocks / summary.totalStocks) * 100,
        volatilityIndex: stocks.filter(s => s.price_range !== null && s.price_range > 0).length > 0
          ? stocks.filter(s => s.price_range !== null && s.price_range > 0)
                  .reduce((sum, s) => sum + (s.price_range || 0), 0) / 
            stocks.filter(s => s.price_range !== null && s.price_range > 0).length
          : 0,
        speculationIndex: (summary.speculationOpportunities / summary.totalStocks) * 100,
        qualityScore: (validPEStocks.length + validDividendStocks.length) / summary.totalStocks * 100
      };

      const result: FullMarketData = {
        summary,
        performers,
        sectors,
        technical,
        advanced,
        stocks,
        lastUpdated: new Date().toISOString()
      };

      console.log('🎯 Advanced market analysis complete:', {
        totalStocks: summary.totalStocks,
        sectors: sectors.length,
        gainers: summary.gainers,
        losers: summary.losers,
        bullishStocks: summary.bullishStocks,
        bearishStocks: summary.bearishStocks,
        topGainer: performers.topGainer?.symbol,
        topLoser: performers.topLoser?.symbol,
        mostActive: performers.mostActive?.symbol,
        marketBreadth: advanced.marketBreadth.toFixed(2) + '%',
        speculationOpportunities: summary.speculationOpportunities
      });

      return result;
    },
    refetchInterval: 30000, // 30 seconds
    staleTime: 25000, // 25 seconds
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Specialized hooks for different aspects
export function useMarketSummary() {
  const { data, isLoading, error } = useAdvancedMarketData();
  return { data: data?.summary || null, isLoading, error };
}

export function useTopPerformers() {
  const { data, isLoading, error } = useAdvancedMarketData();
  return { data: data?.performers || null, isLoading, error };
}

export function useSectorAnalysis() {
  const { data, isLoading, error } = useAdvancedMarketData();
  return { data: data?.sectors || [], isLoading, error };
}

export function useTechnicalSignals() {
  const { data, isLoading, error } = useAdvancedMarketData();
  return { data: data?.technical || null, isLoading, error };
}

export function useAdvancedMetrics() {
  const { data, isLoading, error } = useAdvancedMarketData();
  return { data: data?.advanced || null, isLoading, error };
}
