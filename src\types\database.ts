// Auto-generated TypeScript interfaces for EGX Stock AI Oracle
// Generated on: 2025-06-15T21:41:02.798497

export interface StocksRealtime {
  symbol: string;
  current_price: number;
  open_price: number;
  high_price: number;
  low_price: number;
  previous_close: number;
  change_amount: number;
  change_percent: number;
  volume: number;
  turnover: number;
  trades_count: string | null;
  bid_price: string | null;
  ask_price: string | null;
  bid_volume: string | null;
  ask_volume: string | null;
  last_trade_time: string | null;
  market_cap: string | null;
  updated_at: string;
  ma5: string | null;
  ma10: string | null;
  ma20: string | null;
  ma50: string | null;
  ma100: string | null;
  ma200: string | null;
  tk_indicator: string | null;
  kj_indicator: string | null;
  target_1: string | null;
  target_2: string | null;
  target_3: string | null;
  stop_loss: string | null;
  stock_status: string | null;
  speculation_opportunity: string | null;
  liquidity_ratio: string | null;
  net_liquidity: string | null;
  liquidity_inflow: string | null;
  liquidity_outflow: string | null;
  volume_inflow: string | null;
  volume_outflow: string | null;
  liquidity_flow: string | null;
  free_shares: string | null;
  eps_annual: string | null;
  book_value: string | null;
  pe_ratio: string | null;
  dividend_yield: string | null;
  sector: string | null;
  price_range: string | null;
  avg_net_volume_3d: string | null;
  avg_net_volume_5d: string | null;
  opening_value: string | null;
  name: string | null;
  last_trade_date: string | null;
}


export interface StocksFinancials {
  id: number;
  symbol: string;
  fiscal_year: number;
  fiscal_quarter: string | null;
  market_cap: number;
  pe_ratio: string | null;
  eps_ttm: string | null;
  eps_growth_yoy: string | null;
  dividend_yield: number;
  book_value_per_share: string | null;
  price_to_book: string | null;
  revenue_ttm: number;
  net_income_ttm: number;
  total_assets: string | null;
  total_debt: number;
  free_cash_flow: number;
  roe: string | null;
  roa: string | null;
  debt_to_equity: string | null;
  current_ratio: string | null;
  gross_margin: string | null;
  operating_margin: string | null;
  net_margin: string | null;
  beta: number;
  analyst_rating: string;
  target_price: string | null;
  created_at: string;
  updated_at: string;
  description: string;
  industry: string;
  isin: string;
  index_membership: string;
  market_cap_currency: string;
  total_shares_outstanding: number;
  float_shares_outstanding: number;
  float_shares_percent: number;
  number_of_shareholders: string | null;
  assets_to_equity: number;
  assets_turnover: string | null;
  cash_ratio: number;
  cash_to_debt_ratio: number;
  debt_to_assets: number;
  price_to_cash_flow: string | null;
  price_to_free_cash_flow: string | null;
  price_to_sales: string | null;
  enterprise_value: string | null;
  ev_to_ebitda: string | null;
  ev_to_revenue: string | null;
  total_revenue: number;
  gross_profit: number;
  operating_income: string | null;
  net_income: number;
  ebitda: string | null;
  total_equity: string | null;
  total_liabilities: number;
  total_current_assets: string | null;
  total_current_liabilities: string | null;
  cash_and_equivalents: number;
  cash_short_term_investments: number;
  net_debt: string | null;
  goodwill: string | null;
  operating_cash_flow: string | null;
  investing_cash_flow: string | null;
  financing_cash_flow: string | null;
  capital_expenditures: string | null;
  eps_basic_ttm: string | null;
  eps_diluted_ttm: string | null;
  eps_reported_annual: number;
  eps_estimate_quarterly: string | null;
  eps_growth_ttm: string | null;
  revenue_growth: string | null;
  net_income_growth: string | null;
  ebitda_growth: string | null;
  gross_profit_growth: string | null;
  free_cash_flow_growth: string | null;
  capex_growth: string | null;
  total_assets_growth: number;
  dividend_yield_indicated: number;
  dividend_payout_ratio: string | null;
  dividends_per_share: number;
  dividends_paid_annual: number;
  continuous_dividend_growth: number;
  forward_pe: number;
  target_price_1y: number;
  target_performance_1y: number;
  performance_ytd: number;
  beta_5_years: number;
  average_volume_10d: number;
  relative_volume: number;
  volatility_1_day: number;
  volume_change: number;
  turnover_1_day: number;
  recent_earnings_date: string;
  upcoming_earnings_date: string | null;
  sector: string;
}


export interface StocksHistorical {
  id: number;
  symbol: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  open_interest: number;
  adjusted_close: string | null;
  created_at: string;
}

