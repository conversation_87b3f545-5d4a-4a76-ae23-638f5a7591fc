# نظام تحديث البيانات اللحظية - EGX Stock AI Oracle

## نظرة عامة على النظام 📊

نظام متكامل لتحديث البيانات اللحظية للبورصة المصرية يعتمد على:
- **Python Scheduler** لجدولة المهام
- **قاعدة بيانات Supabase PostgreSQL** للتخزين
- **ملفات Excel/CSV** كمصادر البيانات الخارجية
- **تحديث تلقائي** حسب الجدول المحدد

---

## معدلات التحديث ⏰

### 1. البيانات اللحظية (Real-time Data)
```
📈 التحديث: كل 5 دقائق أثناء ساعات التداول
📊 المصدر: ملف Excel (stock_synco.xlsx)
📍 الجدول: stocks_realtime
🔄 التشغيل: scheduler.py
```

### 2. البيانات المالية (Financial Data)
```
💰 التحديث: يومياً في الساعة 3 مساءً
📊 المصدر: ملف CSV (financial_data.csv)
📍 الجدول: stocks_financials
🔄 التشغيل: scheduler.py
```

### 3. البيانات التاريخية (Historical Data)
```
📅 التحديث: يومياً أو حسب الطلب
📊 المصدر: ملفات TXT (مجلد meta2)
📍 الجدول: stocks_historical
🔄 التشغيل: scheduler.py
```

---

## بنية النظام 🏗️

### الملفات الرئيسية:

#### 1. المجدول الأساسي (scheduler.py)
```python
# إعدادات التحديث من متغيرات البيئة
realtime_interval = 5 دقائق    # البيانات اللحظية
financial_interval = 24 ساعة   # البيانات المالية
historical_interval = 24 ساعة  # البيانات التاريخية

# جدولة المهام
schedule.every(5).minutes.do(sync_realtime_data)
schedule.every(24).hours.do(sync_financial_data)
schedule.every().day.at("15:00").do(full_data_sync)
```

#### 2. تحديث البيانات اللحظية (load_realtime.py)
```python
# مصدر البيانات
EXCEL_PATH = "stock_synco.xlsx"

# معالجة البيانات
- قراءة ملف Excel
- تحديث stocks_realtime table
- حساب نسب التغير
- معالجة الأخطاء والبيانات المفقودة
```

#### 3. إعدادات البيئة (.env)
```bash
# إعدادات قاعدة البيانات
SUPABASE_URL=https://tbzbrujqjwpatbzffmwq.supabase.co
SUPABASE_SERVICE_ROLE_KEY=***

# مسارات ملفات البيانات
REALTIME_EXCEL_PATH=/path/to/stock_synco.xlsx
FINANCIAL_CSV_PATH=/path/to/financial_data.csv
HISTORICAL_DATA_FOLDER=/path/to/meta2/

# إعدادات التحديث
REALTIME_SYNC_MINUTES=5
FINANCIAL_SYNC_HOURS=24
HISTORICAL_SYNC_HOURS=24
```

---

## آلية العمل أثناء الجلسة 🔄

### تسلسل العمليات:

#### 1. بدء التشغيل
```
🟢 تشغيل scheduler.py
🔍 التحقق من متغيرات البيئة
📊 تحديث أولي للبيانات
⏰ بدء المجدول الزمني
```

#### 2. أثناء الجلسة (كل 5 دقائق)
```
📥 قراءة ملف Excel الجديد
🔄 معالجة البيانات:
   - استخراج الأسعار الحالية
   - حساب نسب التغير
   - تحديث حجم التداول
   - حساب عدد الصفقات

📤 إرسال البيانات إلى Supabase:
   - جدول stocks_realtime
   - جدول market_indices (للمؤشرات)

✅ تسجيل النتائج في ملف log
```

#### 3. تحديث الواجهة الأمامية
```
🌐 Hook useEGX30Data يتحقق كل 30 ثانية
📊 جلب البيانات من قاعدة البيانات
🔄 تحديث المكون FinalEGX30Chart
👀 عرض البيانات للمستخدم
```

---

## جدول البيانات المحدثة 📋

### stocks_realtime (كل 5 دقائق)
| العمود | التحديث | المصدر |
|--------|---------|---------|
| current_price | ✅ | Excel |
| volume | ✅ | Excel |
| turnover | ✅ | محسوب |
| trades_count | ✅ | Excel |
| change_percent | ✅ | محسوب |
| updated_at | ✅ | تلقائي |

### market_indices (كل 5 دقائق)
| العمود | التحديث | المصدر |
|--------|---------|---------|
| current_value | ✅ | محسوب من الأسهم |
| change_amount | ✅ | محسوب |
| change_percent | ✅ | محسوب |
| volume | ✅ | مجموع الأسهم |
| turnover | ✅ | مجموع الأسهم |

---

## كيفية تشغيل النظام 🚀

### طريقة 1: Windows
```batch
# تشغيل المجدول
cd scripts/data_sync
start_scheduler.bat
```

### طريقة 2: Linux/Mac
```bash
# تشغيل المجدول
cd scripts/data_sync
chmod +x start_scheduler.sh
./start_scheduler.sh
```

### طريقة 3: Python مباشرة
```bash
cd scripts/data_sync
python scheduler.py
```

---

## مراقبة النظام 📊

### ملفات السجلات:
```
📁 logs/
├── data_sync_scheduler.log      # سجل المجدول العام
├── realtime_data_sync.log       # سجل البيانات اللحظية
├── financial_data_sync.log      # سجل البيانات المالية
└── historical_data_sync.log     # سجل البيانات التاريخية
```

### مراقبة الحالة:
```python
# فحص حالة النظام
python quick_status.py

# فحص البيانات
python validate_data.py

# اختبار الاتصال
python test_connection.py
```

---

## استكشاف الأخطاء 🔧

### مشاكل شائعة وحلولها:

#### 1. ملف Excel غير موجود
```
❌ المشكلة: Excel file not found
✅ الحل: تحديث REALTIME_EXCEL_PATH في .env
```

#### 2. انقطاع الاتصال بقاعدة البيانات
```
❌ المشكلة: Connection error to Supabase
✅ الحل: التحقق من SUPABASE_SERVICE_ROLE_KEY
```

#### 3. بيانات مفقودة
```
❌ المشكلة: No data in columns
✅ الحل: التحقق من أسماء الأعمدة في Excel
```

#### 4. المجدول لا يعمل
```
❌ المشكلة: Scheduler not running
✅ الحل: التحقق من Python environment و dependencies
```

---

## التحسينات المستقبلية 🔮

### 1. تحديث أسرع (Real-time WebSocket)
```python
# بدلاً من 5 دقائق، تحديث فوري
websocket_connection = connect_to_egx_stream()
```

### 2. مصادر بيانات متعددة
```python
# إضافة مصادر بديلة
sources = ["excel", "api", "websocket", "csv"]
```

### 3. تحليل الأداء
```python
# قياس زمن التحديث
performance_metrics = track_sync_performance()
```

### 4. إنذارات ذكية
```python
# إنذار عند توقف التحديث
if last_update > 10_minutes:
    send_alert("Data sync stopped")
```

---

## ملخص المعدلات الحالية ⏱️

| نوع البيانات | معدل التحديث | التشغيل | الحالة |
|-------------|-------------|---------|--------|
| **أسعار الأسهم** | 5 دقائق | تلقائي | ✅ نشط |
| **مؤشر EGX30** | 5 دقائق | تلقائي | ✅ نشط |
| **حجم التداول** | 5 دقائق | تلقائي | ✅ نشط |
| **عدد الصفقات** | 5 دقائق | تلقائي | ✅ نشط |
| **البيانات المالية** | 24 ساعة | تلقائي | ✅ نشط |
| **البيانات التاريخية** | 24 ساعة | تلقائي | ✅ نشط |
| **واجهة المستخدم** | 30 ثانية | تلقائي | ✅ نشط |

**النتيجة**: نظام متكامل يوفر بيانات محدثة كل 5 دقائق مع واجهة مستخدم تتحدث كل 30 ثانية للحصول على أفضل تجربة ممكنة! 🎯
