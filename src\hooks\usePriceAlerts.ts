import { useCallback, useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuthUser } from "@/hooks/useAuthUser";

export interface PriceAlert {
  id: string;
  symbol: string;
  targetPrice: number;
  currentPrice: number | null;
  condition: "above" | "below";
  isActive: boolean;
  createdAt: string;
}

export function usePriceAlerts() {
  const { user } = useAuthUser();
  const { toast } = useToast();
  const [alerts, setAlerts] = useState<PriceAlert[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch alerts
  const fetchAlerts = useCallback(async () => {
    if (!user?.id) return;
    setLoading(true);
    const { data, error } = await supabase
      .from("price_alerts")
      .select("*")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });
    if (!error && Array.isArray(data)) {
      setAlerts(
        data.map((a) => ({
          id: a.id,
          symbol: a.symbol,
          targetPrice: typeof a.target_value === "string" ? parseFloat(a.target_value) : a.target_value,
          currentPrice:
            a.current_value !== null
              ? typeof a.current_value === "string"
                ? parseFloat(a.current_value)
                : a.current_value
              : null,
          condition: a.alert_type === "above" ? "above" : "below",
          isActive: !!a.is_active,
          createdAt: a.created_at,
        }))
      );
    }
    setLoading(false);
  }, [user]);

  useEffect(() => {
    fetchAlerts();
  }, [fetchAlerts]);

  // Add alert
  const addAlert = async (
    symbol: string,
    targetPrice: number,
    condition: "above" | "below"
  ) => {
    if (!symbol || !targetPrice || !user?.id) return;
    setLoading(true);
    const { data, error } = await supabase
      .from("price_alerts")
      .insert([
        {
          symbol: symbol.toUpperCase(),
          target_value: targetPrice,
          alert_type: condition,
          user_id: user.id,
          is_active: true,
          message_template: null,
        },
      ])
      .select("*")
      .single();
    setLoading(false);
    if (!error && data) {
      setAlerts((prev) => [
        {
          id: data.id,
          symbol: data.symbol,
          targetPrice: typeof data.target_value === "string" ? parseFloat(data.target_value) : data.target_value,
          currentPrice:
            data.current_value !== null
              ? typeof data.current_value === "string"
                ? parseFloat(data.current_value)
                : data.current_value
              : null,
          condition: data.alert_type === "above" ? "above" : "below",
          isActive: !!data.is_active,
          createdAt: data.created_at,
        },
        ...prev,
      ]);
      toast({
        title: "تم إنشاء التنبيه",
        description: `سيتم تنبيهك عندما يصل ${
          data.symbol
        } ${data.alert_type === "above" ? "أعلى من" : "أقل من"} ${
          data.target_value
        } EGP`,
      });
      return true;
    } else {
      toast({ title: "خطأ", description: error?.message });
      return false;
    }
  };

  // Delete alert
  const deleteAlert = async (id: string) => {
    setLoading(true);
    await supabase.from("price_alerts").delete().eq("id", id);
    setAlerts((prev) => prev.filter((a) => a.id !== id));
    setLoading(false);
  };

  // Toggle is_active
  const toggleAlert = async (id: string, forcedStatus?: boolean) => {
    const alert = alerts.find((a) => a.id === id);
    if (!alert) return;
    const isActive =
      typeof forcedStatus === "boolean" ? forcedStatus : !alert.isActive;
    setLoading(true);
    await supabase.from("price_alerts").update({ is_active: isActive }).eq("id", id);
    setAlerts((prev) =>
      prev.map((a) =>
        a.id === id ? { ...a, isActive, currentPrice: a.currentPrice } : a
      )
    );
    setLoading(false);
  };

  // Expose state & actions
  return {
    alerts,
    loading,
    addAlert,
    deleteAlert,
    toggleAlert,
    setAlerts,
  };
}
