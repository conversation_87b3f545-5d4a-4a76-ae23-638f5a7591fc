
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Newspaper, TrendingUp, TrendingDown, Search, Clock, Eye } from 'lucide-react';

interface NewsItem {
  id: string;
  title: string;
  summary: string;
  source: string;
  timestamp: Date;
  relatedStocks: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  readTime: number;
  category: string;
}

const NewsIntegration = () => {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [filteredNews, setFilteredNews] = useState<NewsItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSentiment, setSelectedSentiment] = useState('all');

  useEffect(() => {
    // Mock news data
    const mockNews: NewsItem[] = [
      {
        id: '1',
        title: 'البنك التجاري الدولي يعلن عن أرباح قوية للربع الثالث',
        summary: 'أعلن البنك التجاري الدولي مصر عن زيادة في الأرباح بنسبة 25% مقارنة بالربع السابق، مما يعكس قوة الأداء المالي والنمو المستمر في قاعدة العملاء.',
        source: 'البورصة نيوز',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        relatedStocks: ['COMI'],
        sentiment: 'positive',
        readTime: 3,
        category: 'earnings'
      },
      {
        id: '2',
        title: 'قطاع الاتصالات يواجه تحديات في الربع الحالي',
        summary: 'تشهد شركات الاتصالات المصرية ضغوطاً متزايدة بسبب زيادة تكاليف التشغيل وانخفاض هوامش الربح، مما قد يؤثر على الأداء المالي.',
        source: 'مال وأعمال',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
        relatedStocks: ['ETEL'],
        sentiment: 'negative',
        readTime: 2,
        category: 'sector'
      },
      {
        id: '3',
        title: 'مجموعة طلعت مصطفى تطلق مشروع عقاري جديد بالعاصمة الإدارية',
        summary: 'أعلنت مجموعة طلعت مصطفى عن إطلاق مشروع عقاري متكامل في العاصمة الإدارية الجديدة بقيمة استثمارية تزيد عن 10 مليار جنيه.',
        source: 'العقارية اليوم',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
        relatedStocks: ['TALAAT'],
        sentiment: 'positive',
        readTime: 4,
        category: 'investment'
      },
      {
        id: '4',
        title: 'البنك المركزي يعلن عن تغييرات في أسعار الفائدة',
        summary: 'قرر البنك المركزي المصري الإبقاء على أسعار الفائدة الحالية دون تغيير، مما يعكس استقرار السياسة النقدية والظروف الاقتصادية.',
        source: 'الاقتصادية',
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
        relatedStocks: ['COMI', 'EFG'],
        sentiment: 'neutral',
        readTime: 3,
        category: 'monetary'
      },
      {
        id: '5',
        title: 'أوراسكوم للاستثمار تتوسع في أسواق جديدة',
        summary: 'تعلن شركة أوراسكوم للاستثمار عن خطط للتوسع في الأسواق الأفريقية والاستثمار في مشاريع التكنولوجيا والطاقة المتجددة.',
        source: 'الاستثمار والمال',
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
        relatedStocks: ['OTMT'],
        sentiment: 'positive',
        readTime: 5,
        category: 'expansion'
      }
    ];

    setNews(mockNews);
    setFilteredNews(mockNews);
  }, []);

  useEffect(() => {
    let filtered = news;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.relatedStocks.some(stock => stock.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by sentiment
    if (selectedSentiment !== 'all') {
      filtered = filtered.filter(item => item.sentiment === selectedSentiment);
    }

    setFilteredNews(filtered);
  }, [news, searchTerm, selectedCategory, selectedSentiment]);

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `منذ ${hours} ساعة`;
    }
    return `منذ ${minutes} دقيقة`;
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800 border-green-200';
      case 'negative': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return <TrendingUp className="h-3 w-3" />;
      case 'negative': return <TrendingDown className="h-3 w-3" />;
      default: return null;
    }
  };

  const categories = [
    { value: 'all', label: 'جميع الفئات' },
    { value: 'earnings', label: 'الأرباح' },
    { value: 'sector', label: 'قطاعي' },
    { value: 'investment', label: 'استثمار' },
    { value: 'monetary', label: 'نقدي' },
    { value: 'expansion', label: 'توسع' }
  ];

  const sentiments = [
    { value: 'all', label: 'جميع المشاعر' },
    { value: 'positive', label: 'إيجابي' },
    { value: 'negative', label: 'سلبي' },
    { value: 'neutral', label: 'محايد' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800 arabic" dir="rtl">
            <Newspaper className="h-5 w-5" />
            الأخبار المالية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الأخبار أو رموز الأسهم..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 text-right"
                  dir="rtl"
                />
              </div>
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-input rounded-md text-right"
              dir="rtl"
            >
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>{cat.label}</option>
              ))}
            </select>
            
            <select
              value={selectedSentiment}
              onChange={(e) => setSelectedSentiment(e.target.value)}
              className="px-3 py-2 border border-input rounded-md text-right"
              dir="rtl"
            >
              {sentiments.map(sentiment => (
                <option key={sentiment.value} value={sentiment.value}>{sentiment.label}</option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* News List */}
      <div className="space-y-4">
        {filteredNews.map(newsItem => (
          <Card key={newsItem.id} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-right arabic leading-relaxed" dir="rtl">
                      {newsItem.title}
                    </h3>
                    <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                      <span className="arabic">{newsItem.source}</span>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatTimeAgo(newsItem.timestamp)}
                      </div>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {newsItem.readTime} دقائق قراءة
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col gap-2">
                    <Badge className={`${getSentimentColor(newsItem.sentiment)} border`}>
                      <div className="flex items-center gap-1">
                        {getSentimentIcon(newsItem.sentiment)}
                        <span className="text-xs">
                          {newsItem.sentiment === 'positive' ? 'إيجابي' : 
                           newsItem.sentiment === 'negative' ? 'سلبي' : 'محايد'}
                        </span>
                      </div>
                    </Badge>
                  </div>
                </div>

                {/* Summary */}
                <p className="text-muted-foreground text-right arabic leading-relaxed" dir="rtl">
                  {newsItem.summary}
                </p>

                {/* Related Stocks */}
                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    {newsItem.relatedStocks.map(stock => (
                      <Badge key={stock} variant="outline" className="text-blue-600 border-blue-200">
                        {stock}
                      </Badge>
                    ))}
                  </div>
                  
                  <Button variant="outline" size="sm" className="arabic">
                    قراءة المزيد
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredNews.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Newspaper className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-muted-foreground mb-2">لا توجد أخبار</h3>
            <p className="text-muted-foreground">لم يتم العثور على أخبار تطابق معايير البحث المحددة</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default NewsIntegration;
