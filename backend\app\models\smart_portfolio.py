"""
Smart Portfolio Database Models - نماذج قاعدة بيانات المحفظة الذكية
تحتوي على جميع الجداول المطلوبة لإدارة المحفظة الذكية ونسخ التداول
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, List, Optional

Base = declarative_base()

class SmartPortfolioPosition(Base):
    """مراكز المحفظة الذكية"""
    __tablename__ = "smart_portfolio_positions"
    
    id = Column(Integer, primary_key=True, index=True)
    position_id = Column(String(50), unique=True, index=True, nullable=False)
    
    # معلومات السهم
    stock_code = Column(String(10), index=True, nullable=False)
    stock_name_ar = Column(String(100))
    
    # معلومات الدخول
    entry_price = Column(Float, nullable=False)
    entry_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    initial_shares = Column(Integer, nullable=False)
    remaining_shares = Column(Integer, nullable=False)
    
    # إدارة المخاطر والأهداف
    stop_loss_price = Column(Float, nullable=False)
    trailing_stop_distance = Column(Float)
    highest_price_reached = Column(Float, default=0.0)
    protected_profit = Column(Float, default=0.0)
    
    # أهداف الربح وحالة التنفيذ
    profit_targets = Column(JSON)  # قائمة بأهداف الربح
    targets_achieved = Column(JSON, default=list)  # الأهداف المحققة
    
    # مقاييس الأداء
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    total_fees = Column(Float, default=0.0)
    
    # معلومات الإشارة والمعالجة
    signal_id = Column(String(50))
    signal_confidence = Column(Float)
    position_size_reasoning = Column(Text)
    
    # تفاصيل حساب حجم المركز
    kelly_fraction = Column(Float)
    risk_percentage = Column(Float)
    volatility_adjustment = Column(Float)
    liquidity_adjustment = Column(Float)
    
    # حالة المركز
    status = Column(String(20), default="ACTIVE", index=True)  # ACTIVE, CLOSED, PARTIAL_CLOSED
    close_reason = Column(String(50))  # TARGET_REACHED, STOP_LOSS, MANUAL, TRAILING_STOP
    
    # التوقيتات
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    closed_at = Column(DateTime)
    
    # العلاقات
    transactions = relationship("SmartPortfolioTransaction", back_populates="position", cascade="all, delete-orphan")
    
    # الفهارس
    __table_args__ = (
        Index('idx_stock_status', 'stock_code', 'status'),
        Index('idx_entry_date', 'entry_date'),
    )

class SmartPortfolioTransaction(Base):
    """معاملات المحفظة الذكية"""
    __tablename__ = "smart_portfolio_transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    transaction_id = Column(String(50), unique=True, index=True, nullable=False)
    position_id = Column(String(50), ForeignKey('smart_portfolio_positions.position_id'), nullable=False)
    
    # تفاصيل المعاملة
    transaction_type = Column(String(20), nullable=False)  # BUY, SELL_PARTIAL, SELL_FULL, STOP_LOSS, TRAILING_STOP
    shares = Column(Integer, nullable=False)
    price = Column(Float, nullable=False)
    total_value = Column(Float, nullable=False)
    fees = Column(Float, default=0.0)
    
    # سبب المعاملة
    trigger_reason = Column(String(50))  # SIGNAL_ENTRY, TARGET_1, TARGET_2, TARGET_3, STOP_LOSS, TRAILING_STOP, MANUAL
    target_number = Column(Integer)  # رقم الهدف إذا كان سبب البيع
    
    # الربح/الخسارة
    pnl = Column(Float, default=0.0)
    pnl_percentage = Column(Float, default=0.0)
    
    # معلومات إضافية
    notes = Column(Text)
    execution_quality = Column(String(20))  # GOOD, FAIR, POOR
    
    # التوقيت
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    
    # العلاقات
    position = relationship("SmartPortfolioPosition", back_populates="transactions")
    
    # الفهارس
    __table_args__ = (
        Index('idx_position_type', 'position_id', 'transaction_type'),
        Index('idx_timestamp', 'timestamp'),
    )

class SmartPortfolioMetrics(Base):
    """مقاييس أداء المحفظة الذكية"""
    __tablename__ = "smart_portfolio_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(DateTime, default=datetime.utcnow, unique=True, index=True)
    
    # قيم المحفظة
    total_portfolio_value = Column(Float, nullable=False)
    cash_balance = Column(Float, default=0.0)
    invested_value = Column(Float, default=0.0)
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    
    # مقاييس العائد
    daily_return = Column(Float, default=0.0)
    total_return = Column(Float, default=0.0)
    total_return_percentage = Column(Float, default=0.0)
    annualized_return = Column(Float, default=0.0)
    
    # مقاييس المخاطر
    sharpe_ratio = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    current_drawdown = Column(Float, default=0.0)
    volatility = Column(Float, default=0.0)
    
    # مقاييس مخاطر متقدمة
    var_95 = Column(Float, default=0.0)  # Value at Risk 95%
    var_99 = Column(Float, default=0.0)  # Value at Risk 99%
    expected_shortfall = Column(Float, default=0.0)
    beta = Column(Float, default=1.0)
    
    # إحصائيات التداول
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    profit_factor = Column(Float, default=0.0)
    
    # متوسط الربح والخسارة
    average_win = Column(Float, default=0.0)
    average_loss = Column(Float, default=0.0)
    largest_win = Column(Float, default=0.0)
    largest_loss = Column(Float, default=0.0)
    
    # إحصائيات الإشارات
    signals_processed_today = Column(Integer, default=0)
    signals_executed_today = Column(Integer, default=0)
    signals_rejected_today = Column(Integer, default=0)
    signal_acceptance_rate = Column(Float, default=0.0)
    
    # توزيع المحفظة
    active_positions_count = Column(Integer, default=0)
    sector_distribution = Column(JSON)  # توزيع القطاعات
    position_sizes = Column(JSON)  # أحجام المراكز
    
    # معلومات إضافية
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

class CopyTradingSubscription(Base):
    """اشتراكات نسخ التداول"""
    __tablename__ = "copy_trading_subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)  # معرف المستخدم
    subscription_id = Column(String(50), unique=True, index=True)
    
    # حالة الاشتراك
    is_active = Column(Boolean, default=False)
    subscription_type = Column(String(20), default='VIP')  # VIP, PREMIUM
    
    # إعدادات النسخ
    copy_percentage = Column(Float, default=1.0)  # نسبة النسخ من المحفظة الذكية
    max_position_size = Column(Float)  # حد أقصى لحجم المركز بالجنيه
    max_positions_count = Column(Integer, default=10)  # حد أقصى لعدد المراكز
    
    # مرشحات التحكم
    min_signal_confidence = Column(Float, default=0.7)  # حد أدنى لثقة الإشارة
    max_risk_per_trade = Column(Float, default=0.02)  # حد أقصى للمخاطرة لكل صفقة
    excluded_stocks = Column(JSON, default=list)  # أسهم مستبعدة
    included_sectors = Column(JSON, default=list)  # قطاعات مُدرجة فقط
    
    # حدود مالية
    available_balance = Column(Float, default=0.0)  # الرصيد المتاح للنسخ
    reserved_balance = Column(Float, default=0.0)  # الرصيد المحجوز
    
    # إحصائيات الأداء
    total_copied_trades = Column(Integer, default=0)
    successful_copies = Column(Integer, default=0)
    total_pnl = Column(Float, default=0.0)
    total_fees_paid = Column(Float, default=0.0)
    
    # إعدادات الإشعارات
    notifications_enabled = Column(Boolean, default=True)
    telegram_chat_id = Column(String(50))
    email_notifications = Column(Boolean, default=True)
    
    # التوقيتات
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity = Column(DateTime)
    
    # العلاقات
    copied_trades = relationship("CopiedTrade", back_populates="subscription", cascade="all, delete-orphan")

class CopiedTrade(Base):
    """الصفقات المنسوخة"""
    __tablename__ = "copied_trades"
    
    id = Column(Integer, primary_key=True, index=True)
    copy_trade_id = Column(String(50), unique=True, index=True)
    subscription_id = Column(String(50), ForeignKey('copy_trading_subscriptions.subscription_id'), nullable=False)
    original_position_id = Column(String(50), nullable=False)  # معرف المركز الأصلي في المحفظة الذكية
    
    # معلومات السهم
    stock_code = Column(String(10), nullable=False)
    stock_name_ar = Column(String(100))
    
    # تفاصيل النسخ
    original_shares = Column(Integer, nullable=False)  # عدد الأسهم في المحفظة الذكية
    copied_shares = Column(Integer, nullable=False)    # عدد الأسهم المنسوخة
    copy_ratio = Column(Float, nullable=False)         # نسبة النسخ المطبقة
    
    # معلومات الدخول
    entry_price = Column(Float, nullable=False)
    entry_date = Column(DateTime, default=datetime.utcnow)
    total_investment = Column(Float, nullable=False)
    fees_paid = Column(Float, default=0.0)
    
    # حالة الصفقة
    status = Column(String(20), default="ACTIVE")  # ACTIVE, PARTIAL_CLOSED, CLOSED
    remaining_shares = Column(Integer)
    
    # الأداء
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    total_pnl = Column(Float, default=0.0)
    
    # معلومات الإغلاق
    close_reason = Column(String(50))
    close_date = Column(DateTime)
    
    # التوقيتات
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    subscription = relationship("CopyTradingSubscription", back_populates="copied_trades")
    
    # الفهارس
    __table_args__ = (
        Index('idx_subscription_stock', 'subscription_id', 'stock_code'),
        Index('idx_status_date', 'status', 'entry_date'),
    )

class SmartPortfolioSignalProcessing(Base):
    """سجل معالجة الإشارات للمحفظة الذكية"""
    __tablename__ = "smart_portfolio_signal_processing"
    
    id = Column(Integer, primary_key=True, index=True)
    processing_id = Column(String(50), unique=True, index=True)
    
    # معلومات الإشارة
    signal_id = Column(String(50))
    stock_code = Column(String(10), nullable=False)
    signal_type = Column(String(20))  # BUY, SELL, TP, SL
    
    # نتيجة المعالجة
    processing_result = Column(String(20))  # EXECUTED, REJECTED, ERROR
    decision_reason = Column(Text)
    
    # تفاصيل التحليل
    signal_confidence = Column(Float)
    risk_reward_ratio = Column(Float)
    position_size_calculated = Column(Float)
    
    # معايير القبول/الرفض
    criteria_checks = Column(JSON)  # نتائج فحص المعايير
    stock_analysis = Column(JSON)   # نتائج تحليل السهم
    
    # معلومات التنفيذ (إذا تم)
    position_id = Column(String(50))
    execution_price = Column(Float)
    executed_shares = Column(Integer)
    
    # التوقيت
    received_at = Column(DateTime, default=datetime.utcnow)
    processed_at = Column(DateTime, default=datetime.utcnow)
    processing_duration = Column(Float)  # بالثواني
    
    # الفهارس
    __table_args__ = (
        Index('idx_stock_result', 'stock_code', 'processing_result'),
        Index('idx_processed_at', 'processed_at'),
    )

class PortfolioPerformanceSnapshot(Base):
    """لقطات أداء المحفظة للمقارنة"""
    __tablename__ = "portfolio_performance_snapshots"
    
    id = Column(Integer, primary_key=True, index=True)
    snapshot_id = Column(String(50), unique=True, index=True)
    
    # معلومات اللقطة
    snapshot_date = Column(DateTime, default=datetime.utcnow, index=True)
    snapshot_type = Column(String(20))  # DAILY, WEEKLY, MONTHLY, QUARTERLY
    
    # قيم المحفظة
    portfolio_value = Column(Float, nullable=False)
    cash_value = Column(Float, default=0.0)
    positions_value = Column(Float, default=0.0)
    
    # العوائد
    period_return = Column(Float, default=0.0)
    cumulative_return = Column(Float, default=0.0)
    
    # مقارنة بالمؤشر
    egx30_value = Column(Float)  # قيمة مؤشر EGX30
    egx30_return = Column(Float) # عائد المؤشر
    alpha = Column(Float)        # الفا (العائد الإضافي)
    beta = Column(Float)         # بيتا (الحساسية للسوق)
    
    # إحصائيات المراكز
    positions_count = Column(Integer, default=0)
    winning_positions = Column(Integer, default=0)
    losing_positions = Column(Integer, default=0)
    
    # مقاييس المخاطر
    portfolio_volatility = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    
    # بيانات تفصيلية
    positions_snapshot = Column(JSON)  # لقطة المراكز
    sector_allocation = Column(JSON)   # توزيع القطاعات
    
    created_at = Column(DateTime, default=datetime.utcnow)

# Views للاستعلامات المعقدة
class SmartPortfolioSummaryView:
    """عرض ملخص المحفظة الذكية"""
    
    @staticmethod
    def get_current_summary(db_session) -> Dict:
        """الحصول على ملخص المحفظة الحالي"""
        from sqlalchemy import text
        
        query = text("""
        SELECT 
            COUNT(*) as total_positions,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_positions,
            SUM(CASE WHEN status = 'ACTIVE' THEN remaining_shares * entry_price ELSE 0 END) as current_investment,
            SUM(realized_pnl) as total_realized_pnl,
            AVG(signal_confidence) as avg_signal_confidence,
            COUNT(CASE WHEN realized_pnl > 0 THEN 1 END) as winning_trades,
            COUNT(CASE WHEN realized_pnl < 0 THEN 1 END) as losing_trades
        FROM smart_portfolio_positions
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        """)
        
        result = db_session.execute(query).fetchone()
        return dict(result) if result else {}

# Helper functions للتصدير
def create_smart_portfolio_tables(engine):
    """إنشاء جداول المحفظة الذكية"""
    Base.metadata.create_all(bind=engine)

def get_table_names():
    """الحصول على أسماء الجداول"""
    return [
        'smart_portfolio_positions',
        'smart_portfolio_transactions', 
        'smart_portfolio_metrics',
        'copy_trading_subscriptions',
        'copied_trades',
        'smart_portfolio_signal_processing',
        'portfolio_performance_snapshots'
    ]
