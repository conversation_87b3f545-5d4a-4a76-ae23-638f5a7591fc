import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AIInsights from '../../components/AIInsights';
import { createTestQueryClient, TestWrapper } from '../test-utils';
import { QueryClient } from '@tanstack/react-query';

// Mock the ML predictions hook
vi.mock('../../hooks/useMlPredictions', () => ({
  useMlPredictions: vi.fn(() => ({
    data: {
      predictions: [
        {
          symbol: 'ATQA',
          currentPrice: 15.5,
          predictedPrice: 17.2,
          confidence: 0.82,
          trend: 'bullish',
          timeframe: '1D',
          factors: ['Volume increase', 'Technical breakout', 'Sector momentum']
        },
        {
          symbol: 'CLHO',
          currentPrice: 8.2,
          predictedPrice: 7.8,
          confidence: 0.75,
          trend: 'bearish',
          timeframe: '1D',
          factors: ['Resistance level', 'Market sentiment', 'Profit taking']
        }
      ],
      marketSentiment: {
        overall: 'neutral',
        score: 0.52,
        confidence: 0.78,
        factors: ['Mixed earnings', 'Economic indicators', 'Global markets']
      }
    },
    isLoading: false,
    error: null,
    refetch: vi.fn()
  }))
}));

describe('AIInsights', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  beforeEach(() => {
    queryClient = createTestQueryClient();
    vi.clearAllMocks();
  });

  it('renders AI insights with predictions and market sentiment', async () => {
    render(
      <TestWrapper>
        <AIInsights />
      </TestWrapper>
    );

    // Check for AI predictions section
    expect(screen.getByText(/AI Predictions/i)).toBeInTheDocument();
    
    // Check for specific stock predictions
    expect(screen.getByText('ATQA')).toBeInTheDocument();
    expect(screen.getByText('15.50')).toBeInTheDocument();
    expect(screen.getByText('17.20')).toBeInTheDocument();
    expect(screen.getByText('82%')).toBeInTheDocument();

    // Check for market sentiment
    expect(screen.getByText(/Market Sentiment/i)).toBeInTheDocument();
    expect(screen.getByText(/neutral/i)).toBeInTheDocument();
    expect(screen.getByText('52%')).toBeInTheDocument();
  });

  it('displays bullish and bearish indicators correctly', () => {
    render(
      <TestWrapper>
        <AIInsights />
      </TestWrapper>
    );

    // Check for trend indicators
    const bullishElements = screen.getAllByText(/bullish/i);
    const bearishElements = screen.getAllByText(/bearish/i);
    
    expect(bullishElements.length).toBeGreaterThan(0);
    expect(bearishElements.length).toBeGreaterThan(0);
  });

  it('shows prediction factors when expanded', async () => {
    render(
      <TestWrapper>
        <AIInsights />
      </TestWrapper>
    );

    // Look for expand/details button
    const expandButton = screen.getByRole('button', { name: /details|expand|factors/i });
    await user.click(expandButton);

    // Check if factors are displayed
    await waitFor(() => {
      expect(screen.getByText('Volume increase')).toBeInTheDocument();
      expect(screen.getByText('Technical breakout')).toBeInTheDocument();
    });
  });

  it('handles loading state correctly', () => {
    vi.doMock('../../hooks/useMlPredictions', () => ({
      useMlPredictions: () => ({
        data: null,
        isLoading: true,
        error: null,
        refetch: vi.fn()
      })
    }));

    render(
      <TestWrapper>
        <AIInsights />
      </TestWrapper>
    );

    expect(screen.getByText(/Loading AI insights/i)).toBeInTheDocument();
  });

  it('handles error state and retry functionality', async () => {
    const mockRefetch = vi.fn();
    
    vi.doMock('../../hooks/useMlPredictions', () => ({
      useMlPredictions: () => ({
        data: null,
        isLoading: false,
        error: new Error('Failed to load AI predictions'),
        refetch: mockRefetch
      })
    }));

    render(
      <TestWrapper>
        <AIInsights />
      </TestWrapper>
    );

    expect(screen.getByText(/Error loading AI insights/i)).toBeInTheDocument();
    
    const retryButton = screen.getByRole('button', { name: /retry/i });
    await user.click(retryButton);
    
    expect(mockRefetch).toHaveBeenCalled();
  });

  it('formats prediction confidence and percentages correctly', () => {
    render(
      <TestWrapper>
        <AIInsights />
      </TestWrapper>
    );

    // Check for proper percentage formatting
    expect(screen.getByText('82%')).toBeInTheDocument();
    expect(screen.getByText('75%')).toBeInTheDocument();
    expect(screen.getByText('52%')).toBeInTheDocument();
    expect(screen.getByText('78%')).toBeInTheDocument();
  });

  it('displays price predictions with proper formatting', () => {
    render(
      <TestWrapper>
        <AIInsights />
      </TestWrapper>
    );

    // Check current prices
    expect(screen.getByText('15.50')).toBeInTheDocument();
    expect(screen.getByText('8.20')).toBeInTheDocument();

    // Check predicted prices
    expect(screen.getByText('17.20')).toBeInTheDocument();
    expect(screen.getByText('7.80')).toBeInTheDocument();
  });
});
