
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Zap, 
  Gauge, 
  Database, 
  Wifi, 
  Monitor, 
  CheckCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

const PerformanceOptimizer = () => {
  const [performanceScore, setPerformanceScore] = useState(0);
  const [optimizations, setOptimizations] = useState({
    caching: { enabled: true, impact: 'high' },
    compression: { enabled: true, impact: 'medium' },
    lazyLoading: { enabled: true, impact: 'high' },
    webWorkers: { enabled: false, impact: 'medium' },
    offlineMode: { enabled: true, impact: 'high' }
  });

  useEffect(() => {
    // Calculate performance score based on enabled optimizations
    const enabledCount = Object.values(optimizations).filter(opt => opt.enabled).length;
    const totalCount = Object.keys(optimizations).length;
    setPerformanceScore((enabledCount / totalCount) * 100);
  }, [optimizations]);

  const toggleOptimization = (key: string) => {
    setOptimizations(prev => ({
      ...prev,
      [key]: {
        ...prev[key as keyof typeof prev],
        enabled: !prev[key as keyof typeof prev].enabled
      }
    }));
  };

  const optimizationItems = [
    {
      key: 'caching',
      title: 'التخزين المؤقت الذكي',
      description: 'تخزين البيانات محلياً لتسريع التحميل',
      icon: Database
    },
    {
      key: 'compression',
      title: 'ضغط البيانات',
      description: 'تقليل حجم البيانات المنقولة',
      icon: Gauge
    },
    {
      key: 'lazyLoading',
      title: 'التحميل التدريجي',
      description: 'تحميل المحتوى عند الحاجة فقط',
      icon: RefreshCw
    },
    {
      key: 'webWorkers',
      title: 'معالجة متوازية',
      description: 'استخدام خيوط متعددة للمعالجة',
      icon: Monitor
    },
    {
      key: 'offlineMode',
      title: 'الوضع غير المتصل',
      description: 'العمل بدون اتصال بالإنترنت',
      icon: Wifi
    }
  ];

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'ممتاز';
    if (score >= 60) return 'جيد';
    return 'يحتاج تحسين';
  };

  return (
    <div className="space-y-6">
      {/* Performance Score */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Zap className="h-5 w-5" />
            نقاط الأداء
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <div className={`text-4xl font-bold ${getScoreColor(performanceScore)}`}>
                {Math.round(performanceScore)}%
              </div>
              <div className="text-muted-foreground">
                {getScoreLabel(performanceScore)}
              </div>
            </div>
            <Progress value={performanceScore} className="h-3" />
          </div>
        </CardContent>
      </Card>

      {/* Optimization Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="arabic">إعدادات التحسين</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {optimizationItems.map(item => {
              const optimization = optimizations[item.key as keyof typeof optimizations];
              const IconComponent = item.icon;
              
              return (
                <div key={item.key} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded ${optimization.enabled ? 'bg-green-100' : 'bg-gray-100'}`}>
                        <IconComponent className={`h-4 w-4 ${optimization.enabled ? 'text-green-600' : 'text-gray-600'}`} />
                      </div>
                      <div>
                        <h4 className="font-semibold arabic">{item.title}</h4>
                        <p className="text-sm text-muted-foreground arabic">{item.description}</p>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <Button
                        size="sm"
                        variant={optimization.enabled ? "default" : "outline"}
                        onClick={() => toggleOptimization(item.key)}
                      >
                        {optimization.enabled ? <CheckCircle className="h-3 w-3" /> : <AlertTriangle className="h-3 w-3" />}
                      </Button>
                      <Badge 
                        variant={optimization.impact === 'high' ? 'default' : optimization.impact === 'medium' ? 'secondary' : 'outline'}
                        className="text-xs"
                      >
                        {optimization.impact === 'high' ? 'تأثير عالي' : optimization.impact === 'medium' ? 'تأثير متوسط' : 'تأثير منخفض'}
                      </Badge>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="arabic">مقاييس الأداء المباشرة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">98ms</div>
              <div className="text-sm text-muted-foreground">زمن التحميل</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">2.1MB</div>
              <div className="text-sm text-muted-foreground">حجم البيانات</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">60fps</div>
              <div className="text-sm text-muted-foreground">معدل الإطارات</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">85%</div>
              <div className="text-sm text-muted-foreground">كفاءة الذاكرة</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceOptimizer;
