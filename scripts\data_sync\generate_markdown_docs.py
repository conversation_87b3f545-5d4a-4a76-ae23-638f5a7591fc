#!/usr/bin/env python3
"""
Generate Markdown Documentation from Schema Reference
"""

import json
import os
from datetime import datetime

def generate_markdown_docs():
    """Generate markdown documentation from schema JSON"""
    
    # Load the schema reference
    try:
        with open('database_schema_reference.json', 'r', encoding='utf-8') as f:
            schema = json.load(f)
    except FileNotFoundError:
        print("❌ Schema reference file not found. Run inspect_database_schema.py first.")
        return
    
    # Get the table data from the correct structure
    main_tables = schema.get('main_tables', {})
    additional_tables = schema.get('additional_tables', {})
    all_tables = {**main_tables, **additional_tables}
    
    # Generate markdown content
    markdown_content = f"""# EGX Stock AI Oracle - Database Schema Reference

*Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*

## 📊 Database Overview

This document provides a comprehensive reference for all database tables and columns in the EGX Stock AI Oracle system.

### 🎯 Quick Stats
- **Total Tables**: {len(all_tables)}
- **Total Records**: {schema.get('inspection_summary', {}).get('total_records_across_main_tables', 'N/A')}
- **Main Stock Data Tables**: {len(schema.get('inspection_summary', {}).get('main_tables_list', []))}

---

"""
    
    # Add table documentation
    for table_name, table_info in all_tables.items():
        if table_info.get('error'):
            continue
            
        record_count = table_info.get('total_records', 'N/A')
        column_count = table_info.get('total_columns', len(table_info.get('columns', {})))
        
        markdown_content += f"""## 📋 Table: `{table_name}`

**Records**: {record_count} | **Columns**: {column_count}

{table_info.get('description', 'Database table for EGX Stock AI Oracle system')}

### Columns ({column_count})

| Column | Type | Sample Value | Description |
|--------|------|--------------|-------------|
"""
        
        # Add column information
        for col_name, col_info in table_info.get('columns', {}).items():
            col_type = col_info.get('data_type', 'Unknown')
            sample_val = str(col_info.get('sample_value', ''))[:50]  # Truncate long values
            
            # Add descriptions based on column names
            descriptions = {
                'symbol': 'Stock symbol identifier',
                'current_price': 'Current trading price in EGP',
                'volume': 'Trading volume',
                'market_cap': 'Market capitalization',
                'pe_ratio': 'Price-to-Earnings ratio',
                'dividend_yield': 'Annual dividend yield percentage',
                'sector': 'Industry sector classification',
                'updated_at': 'Last update timestamp',
                'created_at': 'Record creation timestamp',
                'ma5': '5-day moving average',
                'ma20': '20-day moving average',
                'target_1': 'First price target',
                'stop_loss': 'Stop loss price level',
                'speculation_opportunity': 'Trading opportunity flag'
            }
            
            description = descriptions.get(col_name, 'Data field')
            
            markdown_content += f"| `{col_name}` | {col_type} | {sample_val} | {description} |\n"
        
        markdown_content += "\n---\n\n"
    
    # Add relationships section
    if 'relationships' in schema:
        markdown_content += """## 🔗 Table Relationships

### Symbol-based Relationships
- **stocks_realtime** ↔ **stocks_financials**: Both keyed by `symbol`
- **stocks_realtime** ↔ **stocks_historical**: Both keyed by `symbol`
- **stocks_financials** ↔ **stocks_historical**: Both keyed by `symbol`

### Cross-Table Query Examples

#### Get Complete Stock Data
```sql
SELECT 
  r.symbol,
  r.current_price,
  r.volume,
  f.pe_ratio,
  f.market_cap,
  f.sector
FROM stocks_realtime r
LEFT JOIN stocks_financials f ON r.symbol = f.symbol
WHERE r.volume > 100000;
```

#### Get Stock with Historical Performance
```sql
SELECT 
  r.symbol,
  r.current_price,
  h.date,
  h.close_price,
  h.volume as historical_volume
FROM stocks_realtime r
LEFT JOIN stocks_historical h ON r.symbol = h.symbol
ORDER BY h.date DESC;
```

"""
    
    # Add frontend integration guide
    markdown_content += """## 🚀 Frontend Integration Guide

### TypeScript Interfaces
Use the generated `database_types.ts` file for type-safe database queries.

### Common Query Patterns

#### 1. Get Active Stocks for Dashboard
```typescript
const { data: activeStocks } = await supabase
  .from('stocks_realtime')
  .select('symbol, current_price, change_percent, volume')
  .gt('volume', 0)
  .order('volume', { ascending: false })
  .limit(50);
```

#### 2. Get Stock Details with Financials
```typescript
const { data: stockDetails } = await supabase
  .from('stocks_realtime')
  .select(`
    *,
    stocks_financials (
      pe_ratio,
      market_cap,
      dividend_yield,
      sector
    )
  `)
  .eq('symbol', stockSymbol)
  .single();
```

#### 3. Get Sector Performance
```typescript
const { data: sectorData } = await supabase
  .from('stocks_realtime')
  .select(`
    symbol,
    current_price,
    change_percent,
    stocks_financials!inner (sector)
  `)
  .eq('stocks_financials.sector', sectorName);
```

### Key Fields for Frontend Components

#### Quick Stats Grid
- `current_price`, `change_percent`, `volume`, `market_cap`

#### Stock Screener
- `pe_ratio`, `dividend_yield`, `sector`, `market_cap`, `volume`

#### Technical Analysis
- `ma5`, `ma10`, `ma20`, `ma50`, `ma100`, `ma200`
- `tk_indicator`, `kj_indicator`

#### Trading Signals
- `target_1`, `target_2`, `target_3`, `stop_loss`
- `speculation_opportunity`, `stock_status`

---

*📄 This documentation is automatically generated. For updates, re-run the schema inspection script.*
"""
    
    # Save markdown file
    with open('DATABASE_SCHEMA_REFERENCE.md', 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print("✅ Markdown documentation generated: DATABASE_SCHEMA_REFERENCE.md")

if __name__ == "__main__":
    generate_markdown_docs()
