import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, TrendingDown, Activity, Eye, ArrowRight } from 'lucide-react';
import { useLiveMarketData } from '@/hooks/useLiveMarketData';
import { cn } from '@/lib/utils';

interface TopStocksWidgetProps {
  onViewAll?: () => void;
  maxItems?: number;
}

const TopStocksWidget: React.FC<TopStocksWidgetProps> = ({ 
  onViewAll, 
  maxItems = 5 
}) => {
  const { stocks, marketStats, isLoading } = useLiveMarketData();

  // تنسيق الأرقام
  const formatPrice = (price: number) => price.toFixed(2);
  const formatPercent = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toLocaleString();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            أهم الأسهم النشطة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-12 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const topGainers = marketStats?.topGainers.slice(0, maxItems) || [];
  const topLosers = marketStats?.topLosers.slice(0, maxItems) || [];
  const mostActive = marketStats?.mostActive.slice(0, maxItems) || [];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* أكبر المكاسب */}
      <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between text-green-800">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              أكبر المكاسب
            </div>
            <Badge variant="outline" className="bg-green-100 text-green-700">
              {marketStats?.gainers || 0}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {topGainers.map((stock, index) => (
            <div 
              key={stock.symbol}
              className={cn(
                "flex items-center justify-between p-2 rounded-lg transition-all duration-300",
                "hover:bg-green-200/50",
                stock.flashColor === 'green' && "bg-green-200 animate-pulse"
              )}
            >
              <div className="flex-1">
                <div className="font-bold text-sm text-green-800">
                  #{index + 1} {stock.symbol}
                </div>
                <div className="text-xs text-green-600 truncate">
                  {stock.name}
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-green-700">
                  {formatPrice(stock.current_price)}
                </div>
                <div className="text-xs font-semibold text-green-600">
                  {formatPercent(stock.change_percent)}
                </div>
              </div>
            </div>
          ))}
          
          {topGainers.length === 0 && (
            <div className="text-center py-4 text-green-600">
              لا توجد أسهم صاعدة حالياً
            </div>
          )}
        </CardContent>
      </Card>

      {/* أكبر الخسائر */}
      <Card className="border-red-200 bg-gradient-to-br from-red-50 to-red-100">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between text-red-800">
            <div className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5" />
              أكبر الخسائر
            </div>
            <Badge variant="outline" className="bg-red-100 text-red-700">
              {marketStats?.losers || 0}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {topLosers.map((stock, index) => (
            <div 
              key={stock.symbol}
              className={cn(
                "flex items-center justify-between p-2 rounded-lg transition-all duration-300",
                "hover:bg-red-200/50",
                stock.flashColor === 'red' && "bg-red-200 animate-pulse"
              )}
            >
              <div className="flex-1">
                <div className="font-bold text-sm text-red-800">
                  #{index + 1} {stock.symbol}
                </div>
                <div className="text-xs text-red-600 truncate">
                  {stock.name}
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-red-700">
                  {formatPrice(stock.current_price)}
                </div>
                <div className="text-xs font-semibold text-red-600">
                  {formatPercent(stock.change_percent)}
                </div>
              </div>
            </div>
          ))}
          
          {topLosers.length === 0 && (
            <div className="text-center py-4 text-red-600">
              لا توجد أسهم هابطة حالياً
            </div>
          )}
        </CardContent>
      </Card>

      {/* الأكثر نشاطاً */}
      <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between text-blue-800">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              الأكثر نشاطاً
            </div>
            <Badge variant="outline" className="bg-blue-100 text-blue-700">
              {marketStats?.activeStocks || 0}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {mostActive.map((stock, index) => (
            <div 
              key={stock.symbol}
              className={cn(
                "flex items-center justify-between p-2 rounded-lg transition-all duration-300",
                "hover:bg-blue-200/50",
                stock.isNewUpdate && "bg-blue-200 animate-pulse"
              )}
            >
              <div className="flex-1">
                <div className="font-bold text-sm text-blue-800">
                  #{index + 1} {stock.symbol}
                </div>
                <div className="text-xs text-blue-600 truncate">
                  {stock.name}
                </div>
                <div className="text-xs text-blue-500">
                  {formatNumber(stock.turnover || 0)} جنيه
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-blue-700">
                  {formatPrice(stock.current_price)}
                </div>
                <div className={cn(
                  "text-xs font-semibold",
                  stock.change_percent > 0 ? "text-green-600" : 
                  stock.change_percent < 0 ? "text-red-600" : "text-gray-600"
                )}>
                  {formatPercent(stock.change_percent)}
                </div>
              </div>
            </div>
          ))}
          
          {mostActive.length === 0 && (
            <div className="text-center py-4 text-blue-600">
              لا توجد أسهم نشطة حالياً
            </div>
          )}
        </CardContent>
      </Card>

      {/* زر عرض الشاشة الكاملة */}
      {onViewAll && (
        <div className="lg:col-span-3 flex justify-center mt-4">
          <Button 
            onClick={onViewAll}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            size="lg"
          >
            <Eye className="h-4 w-4 mr-2" />
            عرض الشاشة اللحظية الكاملة
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default TopStocksWidget;
