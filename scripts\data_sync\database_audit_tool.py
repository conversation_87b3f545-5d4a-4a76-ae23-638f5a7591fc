#!/usr/bin/env python3
"""
أداة فحص شاملة لقاعدة البيانات
تحليل البيانات المتاحة والمحسوبة والمفقودة
"""

import os
import sys
from datetime import datetime
import json
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_SERVICE_ROLE_KEY:
    print("❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def check_data_completeness():
    """فحص اكتمال البيانات في الجداول الرئيسية"""
    
    print("🔍 **فحص اكتمال البيانات في قاعدة البيانات**")
    print("=" * 60)
    
    # فحص stocks_realtime
    print("\n📊 **جدول stocks_realtime:**")
    try:
        result = supabase.rpc('check_realtime_completeness').execute()
        if result.data:
            print(f"✅ إجمالي الأسهم: {result.data[0].get('total_stocks', 'غير متاح')}")
            print(f"📈 أسهم لها أسعار حالية: {result.data[0].get('stocks_with_prices', 'غير متاح')}")
            print(f"📊 أسهم لها MA5: {result.data[0].get('stocks_with_ma5', 'غير متاح')}")
            print(f"📊 أسهم لها MA20: {result.data[0].get('stocks_with_ma20', 'غير متاح')}")
            print(f"🎯 أسهم لها أهداف: {result.data[0].get('stocks_with_targets', 'غير متاح')}")
            print(f"💰 أسهم لها P/E: {result.data[0].get('stocks_with_pe', 'غير متاح')}")
    except Exception as e:
        print(f"❌ خطأ في فحص stocks_realtime: {e}")        # فحص بديل
        try:
            basic_check = supabase.from('stocks_realtime').select('*', count='exact').execute()
            print(f"✅ عدد الأسهم الأساسي: {basic_check.count}")
        except Exception as e2:
            print(f"❌ خطأ في الفحص البديل: {e2}")

def check_computed_vs_raw_data():
    """مقارنة البيانات المحسوبة مع البيانات الأولية"""
    
    print("\n🔄 **مقارنة البيانات المحسوبة vs البيانات الأولية:**")
    print("=" * 60)
    
    try:
        # جلب عينة من البيانات
        sample = supabase.from('stocks_realtime').select('''
            symbol, current_price, previous_close, change_amount, change_percent,
            volume, turnover, ma5, ma20, ma50, 
            pe_ratio, dividend_yield, target_1, stop_loss,
            liquidity_ratio, speculation_opportunity
        ''').limit(5).execute()
        
        if sample.data:
            print("\n📋 **عينة من البيانات المتاحة:**")
            for stock in sample.data:
                print(f"\n🏢 السهم: {stock.get('symbol', 'غير متاح')}")
                print(f"  💰 السعر الحالي: {stock.get('current_price', 'غير متاح')}")
                print(f"  📊 التغير %: {stock.get('change_percent', 'غير متاح')}")
                print(f"  📈 MA5: {stock.get('ma5', 'غير متاح')}")
                print(f"  📈 MA20: {stock.get('ma20', 'غير متاح')}")
                print(f"  🎯 الهدف 1: {stock.get('target_1', 'غير متاح')}")
                print(f"  💹 P/E: {stock.get('pe_ratio', 'غير متاح')}")
                print(f"  💧 نسبة السيولة: {stock.get('liquidity_ratio', 'غير متاح')}")
                
                # تحقق من الحسابات
                if (stock.get('current_price') and stock.get('previous_close') and 
                    stock.get('change_amount') is not None):
                    calculated_change = stock['current_price'] - stock['previous_close']
                    db_change = stock['change_amount']
                    print(f"  🔍 التغير المحسوب: {calculated_change:.2f} vs DB: {db_change}")
                    
                    if abs(calculated_change - (db_change or 0)) > 0.01:
                        print(f"  ⚠️  تضارب في حساب التغير!")
        
    except Exception as e:
        print(f"❌ خطأ في مقارنة البيانات: {e}")

def check_missing_calculations():
    """فحص الحسابات المفقودة أو الفارغة"""
    
    print("\n🕳️ **فحص البيانات المفقودة:**")
    print("=" * 60)
    
    try:
        # فحص الحقول الفارغة
        missing_data = supabase.from('stocks_realtime').select('''
            COUNT(*) as total,
            COUNT(ma5) as has_ma5,
            COUNT(ma20) as has_ma20,
            COUNT(pe_ratio) as has_pe,
            COUNT(dividend_yield) as has_dividend,
            COUNT(target_1) as has_target1,
            COUNT(stop_loss) as has_stop_loss,
            COUNT(liquidity_ratio) as has_liquidity,
            COUNT(market_cap) as has_market_cap
        ''').execute()
        
        if missing_data.data and len(missing_data.data) > 0:
            data = missing_data.data[0]
            total = data.get('total', 0)
            
            print(f"\n📊 **إحصائيات البيانات المتاحة من أصل {total} سهم:**")
            print(f"  📈 MA5: {data.get('has_ma5', 0)} ({(data.get('has_ma5', 0)/total*100):.1f}%)")
            print(f"  📈 MA20: {data.get('has_ma20', 0)} ({(data.get('has_ma20', 0)/total*100):.1f}%)")
            print(f"  💹 P/E: {data.get('has_pe', 0)} ({(data.get('has_pe', 0)/total*100):.1f}%)")
            print(f"  💰 عائد الأرباح: {data.get('has_dividend', 0)} ({(data.get('has_dividend', 0)/total*100):.1f}%)")
            print(f"  🎯 الهدف 1: {data.get('has_target1', 0)} ({(data.get('has_target1', 0)/total*100):.1f}%)")
            print(f"  🛑 وقف الخسارة: {data.get('has_stop_loss', 0)} ({(data.get('has_stop_loss', 0)/total*100):.1f}%)")
            print(f"  💧 نسبة السيولة: {data.get('has_liquidity', 0)} ({(data.get('has_liquidity', 0)/total*100):.1f}%)")
            print(f"  🏛️ القيمة السوقية: {data.get('has_market_cap', 0)} ({(data.get('has_market_cap', 0)/total*100):.1f}%)")
            
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات المفقودة: {e}")

def check_duplicate_calculations():
    """فحص الحسابات المكررة في التطبيق"""
    
    print("\n🔄 **الحسابات المكررة المكتشفة في التطبيق:**")
    print("=" * 60)
    
    print("""
    ⚠️  **المشاكل المكتشفة:**
    
    1. **في useAdvancedMarketData.ts:**
       - حساب gainers/losers محلياً رغم توفرها في DB
       - حساب المتوسطات محلياً 
       - فلترة الأسهم حسب MA5 > MA20 محلياً
    
    2. **في useMarketOverview.ts:**
       - حساب إجمالي الحجم محلياً
       - حساب النسب المئوية للتغيير محلياً
    
    3. **في hooks أخرى:**
       - إعادة حساب المؤشرات الفنية
       - حساب توزيع القطاعات محلياً
    
    🎯 **الحل المقترح:**
       - استخدام البيانات المحسوبة مسبقاً من قاعدة البيانات
       - إنشاء views محسوبة للإحصائيات الشائعة
       - تحسين الاستعلامات لجلب البيانات الجاهزة
    """)

def generate_optimization_recommendations():
    """إنشاء توصيات التحسين"""
    
    print("\n🚀 **توصيات التحسين الفورية:**")
    print("=" * 60)
    
    recommendations = {
        "immediate_actions": [
            "إنشاء materialized views للإحصائيات الشائعة",
            "تحديث hooks لاستخدام البيانات المحسوبة",
            "إنشاء stored procedures للحسابات المعقدة",
            "حذف الحسابات المكررة من JavaScript"
        ],
        "data_sync_cleanup": [
            "حذف ملفات data_sync المتضاربة",
            "إنشاء unified_data_sync.py",
            "جدولة المزامنة التلقائية",
            "إضافة validation للبيانات الجديدة"
        ],
        "performance_optimizations": [
            "استخدام SELECT محددة بدلاً من SELECT *",
            "إضافة indexes للاستعلامات الشائعة", 
            "تحسين استعلامات الـ hooks",
            "إضافة caching للبيانات الثابتة"
        ]
    }
    
    for category, items in recommendations.items():
        print(f"\n📋 **{category.replace('_', ' ').title()}:**")
        for i, item in enumerate(items, 1):
            print(f"  {i}. {item}")

def main():
    """الوظيفة الرئيسية لفحص قاعدة البيانات"""
    
    print("🔍 **EGX Stock AI Oracle - فحص شامل لقاعدة البيانات**")
    print("=" * 80)
    print(f"⏰ وقت الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # اختبار الاتصال
        test = supabase.from('stocks_realtime').select('count', head=True).execute()
        print(f"✅ تم الاتصال بقاعدة البيانات بنجاح")
        print(f"📊 عدد الأسهم: {test.count}")
        
        # فحص اكتمال البيانات
        check_data_completeness()
        
        # مقارنة البيانات
        check_computed_vs_raw_data()
        
        # فحص البيانات المفقودة
        check_missing_calculations()
        
        # فحص الحسابات المكررة
        check_duplicate_calculations()
        
        # توصيات التحسين
        generate_optimization_recommendations()
        
        print("\n✅ **اكتمل الفحص بنجاح!**")
        print("📄 راجع الملف: docs/DATA_ANALYSIS_AND_CLEANUP_PLAN.md للخطة الشاملة")
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        print("🔧 تأكد من متغيرات البيئة وحالة قاعدة البيانات")

if __name__ == "__main__":
    main()
