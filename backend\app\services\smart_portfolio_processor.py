"""
Smart Portfolio Processor - معالج المحفظة الذكية المتقدم
المسؤول عن معالجة الإشارات واتخاذ قرارات التداول الذكية
"""

import logging
import asyncio
import random
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
import numpy as np
import pandas as pd

from ..models.trading_signals import LiveRecommendations, TradingSignal
from ..models.smart_portfolio import SmartPortfolioPosition, SmartPortfolioTransaction, SmartPortfolioMetrics
from ..services.notification_service import NotificationService
from ..services.telegram_service import TelegramService
from ..services.advanced_technical_analyzer import AdvancedTechnicalAnalyzer
from ..services.advanced_position_sizer import AdvancedPositionSizer
from ..services.copy_trading_service import AdvancedCopyTradingService
from ..services.portfolio_analytics import PortfolioAnalytics
from ..database import get_db
from ..config import settings

logger = logging.getLogger(__name__)

class SmartPortfolioProcessor:
    """معالج المحفظة الذكية المتقدم"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService()
        self.telegram_service = TelegramService()
        self.technical_analyzer = AdvancedTechnicalAnalyzer()
        self.position_sizer = AdvancedPositionSizer(db)
        self.copy_trading_service = AdvancedCopyTradingService(db)
        
        # إعدادات المحفظة
        self.initial_capital = 1000000.0  # رأس المال الأولي: مليون جنيه
        self.max_position_risk = 0.02      # أقصى مخاطرة لكل صفقة: 2%
        self.max_portfolio_risk = 0.06     # أقصى مخاطرة للمحفظة: 6%
        self.min_signal_confidence = 0.65  # حد أدنى لثقة الإشارة        self.trailing_stop_factor = 2.0    # عامل الـ trailing stop
        
        # استراتيجية الربح الجزئي
        self.profit_taking_strategy = {
            'target_1': {'percentage': 0.25, 'price_multiplier': 1.03},
            'target_2': {'percentage': 0.35, 'price_multiplier': 1.06}, 
            'target_3': {'percentage': 0.40, 'price_multiplier': 1.10},
            'trailing_remainder': 0.40
        }
    
    async def process_incoming_signal(self, signal_data: Dict) -> Dict:
        """
        معالجة الإشارة الواردة واتخاذ القرار الذكي
        """
        try:
            logger.info(f"🧠 Processing SMART PORTFOLIO signal for {signal_data.get('stock_code')}")
            
            # 1. تحليل شامل ومتقدم للسهم والسوق
            stock_analysis = await self._analyze_stock_comprehensive(signal_data['stock_code'])
            
            # 2. تقييم جودة الإشارة باستخدام الذكاء الاصطناعي المتقدم
            signal_quality = await self._evaluate_signal_quality_advanced(signal_data, stock_analysis)
              # 3. فحص المعايير الأساسية مع التحليل الفني المتقدم
            acceptance_decision = await self._check_advanced_acceptance_criteria(
                signal_data, signal_quality, stock_analysis
            )
            
            if not acceptance_decision['accepted']:
                logger.info(f"❌ Signal rejected: {acceptance_decision['reason']}")
                await self._log_signal_rejection(signal_data, acceptance_decision['reason'])
                return {
                    'action': 'REJECTED',
                    'reason': acceptance_decision['reason'],
                    'signal_quality': signal_quality,
                    'stock_analysis': stock_analysis,
                    'risk_assessment': acceptance_decision.get('risk_assessment', {})
                }
            
            # 4. حساب حجم المركز الأمثل باستخدام Kelly Criterion المحسن
            position_sizing = await self._calculate_advanced_position_size(
                signal_data, stock_analysis, signal_quality
            )
            
            # 5. تطبيق استراتيجية إدارة المخاطر المتقدمة
            risk_management = await self._apply_advanced_risk_management(
                signal_data, position_sizing, stock_analysis
            )
            
            # 6. تنفيذ قرار الشراء مع التحقق النهائي
            execution_result = await self._execute_smart_buy_decision(
                signal_data, position_sizing, risk_management, signal_quality, stock_analysis
            )
            
            # 7. إرسال الإشعارات المتقدمة
            await self._send_advanced_execution_notifications(signal_data, execution_result)
            
            # 8. تحديث إحصائيات المحفظة
            await self._update_portfolio_statistics(execution_result)
            
            logger.info(f"✅ Smart portfolio signal processed successfully for {signal_data.get('stock_code')}")
            return execution_result
            
        except Exception as e:
            logger.error(f"💥 Error processing smart portfolio signal: {e}")
            await self._handle_processing_error(signal_data, str(e))
            return {'action': 'ERROR', 'error': str(e)}
    
    async def _analyze_stock_comprehensive(self, stock_code: str) -> Dict:
        """تحليل شامل ومتقدم لحالة السهم مع التحليل الفني المتطور"""
        try:
            # الحصول على البيانات التاريخية للسهم (90 يوم للتحليل المتقدم)
            historical_data = await self._get_stock_historical_data(stock_code, days=90)
            
            if not historical_data:
                return {'error': 'No historical data available', 'quality_score': 0}
            
            df = pd.DataFrame(historical_data)
            current_price = df['close'].iloc[-1] if not df.empty else 0
              # التحليل الفني المتقدم
            technical_analysis = await self.technical_analyzer.perform_advanced_technical_analysis(df)
            
            # تحليل السيولة والحجم المتقدم
            liquidity_analysis = await self._analyze_liquidity_comprehensive(df)
              # تحليل التقلبات والمخاطر
            volatility_analysis = await self.technical_analyzer.analyze_volatility_advanced(df)
            
            # تحليل الأداء النسبي مقارنة بالسوق
            relative_performance = await self._analyze_relative_performance(stock_code, df)
            
            # تحليل مستويات الدعم والمقاومة المتقدم
            support_resistance = await self._identify_support_resistance_levels(df)
            
            # تقييم جودة السهم العام
            stock_quality_score = await self._calculate_stock_quality_score(
                technical_analysis, liquidity_analysis, volatility_analysis, relative_performance
            )
            
            # تحليل المخاطر الخاصة بالسهم
            stock_specific_risks = await self._assess_stock_specific_risks(stock_code, df)
            
            return {
                'stock_code': stock_code,
                'current_price': current_price,
                'technical_analysis': technical_analysis,
                'liquidity_analysis': liquidity_analysis,
                'volatility_analysis': volatility_analysis,
                'relative_performance': relative_performance,
                'support_resistance': support_resistance,
                'quality_score': stock_quality_score,
                'specific_risks': stock_specific_risks,
                'analysis_timestamp': datetime.now().isoformat(),
                'data_points': len(df),
                'analysis_confidence': min(100, len(df) * 1.2)  # confidence based on data availability
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive stock analysis for {stock_code}: {e}")
            return {'error': str(e), 'quality_score': 0}
    
    async def _evaluate_signal_quality_advanced(self, signal_data: Dict, stock_analysis: Dict) -> Dict:
        """تقييم متقدم لجودة الإشارة باستخدام عوامل متعددة"""
        try:
            quality_factors = {}
            quality_score = 0
            
            # 1. قوة الإشارة الأساسية
            base_confidence = signal_data.get('confidence', 0.5)
            quality_factors['base_confidence'] = base_confidence * 25  # حتى 25 نقطة
              # 2. تطابق الإشارة مع التحليل الفني
            technical_alignment = await self._check_technical_alignment(signal_data, stock_analysis)
            alignment_score = technical_alignment.get('alignment_score', 0) if isinstance(technical_alignment, dict) else 0
            quality_factors['technical_alignment'] = (alignment_score / 100) * 20  # حتى 20 نقطة
            
            # 3. جودة السهم العامة
            stock_quality = stock_analysis.get('quality_score', 50)
            quality_factors['stock_quality'] = (stock_quality / 100) * 15  # حتى 15 نقطة
            
            # 4. ظروف السوق العامة
            market_conditions = await self._assess_market_conditions()
            quality_factors['market_conditions'] = market_conditions * 15  # حتى 15 نقطة
            
            # 5. قوة السيولة
            liquidity_score = stock_analysis.get('liquidity_analysis', {}).get('liquidity_score', 50)
            quality_factors['liquidity'] = (liquidity_score / 100) * 10  # حتى 10 نقطة
            
            # 6. استقرار التقلبات
            volatility_stability = 100 - stock_analysis.get('volatility_analysis', {}).get('volatility_percentile', 50)
            quality_factors['volatility_stability'] = (volatility_stability / 100) * 10  # حتى 10 نقاط
            
            # 7. قوة الاتجاه
            trend_strength = stock_analysis.get('technical_analysis', {}).get('trend_strength', 50)
            quality_factors['trend_strength'] = (trend_strength / 100) * 5  # حتى 5 نقاط
            
            # حساب النتيجة الإجمالية
            quality_score = sum(quality_factors.values())
            
            # تحديد مستوى الجودة
            if quality_score >= 85:
                quality_level = 'EXCELLENT'
                recommendation = 'STRONG_BUY'
            elif quality_score >= 70:
                quality_level = 'HIGH'
                recommendation = 'BUY'
            elif quality_score >= 55:
                quality_level = 'MEDIUM'
                recommendation = 'CONDITIONAL_BUY'
            elif quality_score >= 40:
                quality_level = 'LOW'
                recommendation = 'CAUTION'
            else:
                quality_level = 'POOR'
                recommendation = 'AVOID'
            
            return {
                'overall_score': round(quality_score, 2),
                'quality_level': quality_level,
                'recommendation': recommendation,
                'quality_factors': quality_factors,
                'detailed_breakdown': {
                    'signal_strength': base_confidence,
                    'technical_confirmation': technical_alignment,
                    'stock_fundamentals': stock_quality,
                    'market_environment': market_conditions,
                    'liquidity_adequacy': liquidity_score,
                    'risk_level': stock_analysis.get('volatility_analysis', {}).get('risk_level', 'MEDIUM')
                }
            }
            
        except Exception as e:
            logger.error(f"Error evaluating signal quality: {e}")
            return {'overall_score': 30, 'quality_level': 'POOR', 'error': str(e)}
    
    async def _check_advanced_acceptance_criteria(self, signal_data: Dict, signal_quality: Dict, stock_analysis: Dict) -> Dict:
        """فحص معايير القبول المتقدمة مع التحليل الشامل"""
        try:
            acceptance_checks = {}
            rejection_reasons = []
            risk_flags = []
              # 1. فحص جودة الإشارة
            signal_score = signal_quality.get('overall_score', 0)
            if isinstance(signal_score, dict):
                signal_score = signal_score.get('score', 0)
            
            min_score_threshold = self.min_signal_confidence * 100
            if signal_score < min_score_threshold:
                rejection_reasons.append(f"Signal quality too low: {signal_score:.1f}%")
                acceptance_checks['signal_quality'] = False
            else:
                acceptance_checks['signal_quality'] = True
            
            # 2. فحص سيولة السهم
            liquidity_score = stock_analysis.get('liquidity_analysis', {}).get('liquidity_score', 0)
            if liquidity_score < 60:
                rejection_reasons.append(f"Insufficient liquidity: {liquidity_score:.1f}%")
                acceptance_checks['liquidity'] = False
            else:
                acceptance_checks['liquidity'] = True
            
            # 3. فحص التقلبات المفرطة
            volatility_level = stock_analysis.get('volatility_analysis', {}).get('risk_level', 'MEDIUM')
            if volatility_level == 'EXTREME':
                rejection_reasons.append("Excessive volatility detected")
                risk_flags.append('HIGH_VOLATILITY')
                acceptance_checks['volatility'] = False
            else:
                acceptance_checks['volatility'] = True
              # 4. فحص تركز المحفظة
            portfolio_concentration = await self._check_portfolio_concentration(signal_data['stock_code'])
            would_exceed_limit = portfolio_concentration.get('is_concentrated', False) or portfolio_concentration.get('recommendation') in ['REDUCE_SIZE', 'CAUTION']
            
            if would_exceed_limit:
                concentration_pct = portfolio_concentration.get('current_stock_weight', 0)
                rejection_reasons.append(f"Would exceed position limit: {concentration_pct:.1f}%")
                acceptance_checks['concentration'] = False
            else:
                acceptance_checks['concentration'] = True
            
            # 5. فحص إجمالي المخاطر في المحفظة
            portfolio_risk = await self._assess_current_portfolio_risk()
            if portfolio_risk['total_risk'] > self.max_portfolio_risk:
                rejection_reasons.append(f"Portfolio risk too high: {portfolio_risk['total_risk']:.1f}%")
                acceptance_checks['portfolio_risk'] = False
            else:
                acceptance_checks['portfolio_risk'] = True
            
            # 6. فحص ظروف السوق
            market_conditions = await self._assess_market_conditions()
            if market_conditions < 0.3:  # ظروف سوق سيئة جداً
                rejection_reasons.append("Adverse market conditions")
                risk_flags.append('POOR_MARKET_CONDITIONS')
                acceptance_checks['market_conditions'] = False
            else:
                acceptance_checks['market_conditions'] = True
            
            # 7. فحص التحليل الفني
            technical_score = stock_analysis.get('technical_analysis', {}).get('overall_score', 50)
            if technical_score < 40:
                rejection_reasons.append(f"Poor technical setup: {technical_score:.1f}%")
                acceptance_checks['technical_analysis'] = False
            else:
                acceptance_checks['technical_analysis'] = True
            
            # 8. فحص المراكز المتضاربة
            conflicting_positions = await self._check_for_conflicting_positions(signal_data['stock_code'])
            if conflicting_positions['has_conflicts']:
                rejection_reasons.append("Conflicting positions detected")
                acceptance_checks['position_conflicts'] = False
            else:
                acceptance_checks['position_conflicts'] = True
            
            # 9. فحص رأس المال المتاح
            available_capital = await self._get_available_capital()
            min_required = 50000  # 50,000 جنيه كحد أدنى للصفقة
            if available_capital < min_required:
                rejection_reasons.append(f"Insufficient capital: {available_capital:.0f} EGP available")
                acceptance_checks['capital_availability'] = False
            else:
                acceptance_checks['capital_availability'] = True
            
            # قرار القبول النهائي
            all_checks_passed = all(acceptance_checks.values())
            
            # إحصائيات القبول
            passed_checks = sum(acceptance_checks.values())
            total_checks = len(acceptance_checks)
            acceptance_score = (passed_checks / total_checks) * 100
            
            return {
                'accepted': all_checks_passed,
                'acceptance_score': acceptance_score,
                'checks_passed': passed_checks,
                'total_checks': total_checks,
                'individual_checks': acceptance_checks,
                'rejection_reasons': rejection_reasons,
                'risk_flags': risk_flags,
                'reason': rejection_reasons[0] if rejection_reasons else 'All criteria met',
                'risk_assessment': {
                    'portfolio_risk_level': portfolio_risk.get('risk_level', 'MEDIUM'),
                    'stock_specific_risk': volatility_level,
                    'market_risk': 'HIGH' if market_conditions < 0.4 else 'MEDIUM' if market_conditions < 0.7 else 'LOW',
                    'liquidity_risk': 'HIGH' if liquidity_score < 40 else 'MEDIUM' if liquidity_score < 70 else 'LOW'
                }
            }
            
        except Exception as e:
            logger.error(f"Error checking acceptance criteria: {e}")
            return {
                'accepted': False,
                'reason': f'Error in acceptance check: {str(e)}',
                'error': str(e)
            }
            
            # التحليل الفني
            technical_analysis = {
                'volatility_daily': df['close'].pct_change().std() * np.sqrt(252),
                'atr_14': self._calculate_atr(df, period=14),
                'rsi_14': self._calculate_rsi(df, period=14),
                'moving_avg_20': df['close'].rolling(20).mean().iloc[-1],
                'moving_avg_50': df['close'].rolling(50).mean().iloc[-1],
                'price_vs_ma20': (df['close'].iloc[-1] / df['close'].rolling(20).mean().iloc[-1] - 1) * 100,
                'volume_avg_30': df['volume'].rolling(30).mean().iloc[-1],
                'volume_current': df['volume'].iloc[-1],
                'volume_ratio': df['volume'].iloc[-1] / df['volume'].rolling(30).mean().iloc[-1]
            }
            
            # تحليل الاتجاه
            trend_analysis = {
                'trend_strength': self._calculate_trend_strength(df),
                'support_level': self._find_support_level(df),
                'resistance_level': self._find_resistance_level(df),
                'trend_direction': 'bullish' if df['close'].iloc[-1] > df['close'].rolling(20).mean().iloc[-1] else 'bearish'
            }
            
            # تحليل السيولة
            liquidity_analysis = {
                'avg_volume': technical_analysis['volume_avg_30'],
                'current_volume': technical_analysis['volume_current'],
                'liquidity_score': min(1.0, technical_analysis['volume_avg_30'] / 1000000),  # نسبة إلى مليون سهم
                'volume_trend': 'increasing' if technical_analysis['volume_ratio'] > 1.2 else 'normal'
            }
            
            # الحصول على معدل نجاح الإشارات السابقة
            historical_success = await self._get_historical_signal_success(stock_code)
            
            return {
                'technical': technical_analysis,
                'trend': trend_analysis,
                'liquidity': liquidity_analysis,
                'historical_success': historical_success,
                'market_sentiment': await self._analyze_market_sentiment()
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive stock analysis: {e}")
            return {'error': str(e)}
    
    async def _evaluate_signal_quality(self, signal_data: Dict, stock_analysis: Dict) -> Dict:
        """تقييم جودة الإشارة"""
        try:
            # العوامل المؤثرة على جودة الإشارة
            factors = {}
            
            # 1. معدل النجاح التاريخي للسهم
            historical_success_rate = stock_analysis.get('historical_success', {}).get('success_rate', 0.5)
            factors['historical_success'] = historical_success_rate
            
            # 2. قوة الاتجاه
            trend_strength = stock_analysis.get('trend', {}).get('trend_strength', 0.5)
            factors['trend_strength'] = trend_strength
            
            # 3. حالة السيولة
            liquidity_score = stock_analysis.get('liquidity', {}).get('liquidity_score', 0.5)
            factors['liquidity'] = liquidity_score
            
            # 4. التقلبات (كلما قل كان أفضل)
            volatility = stock_analysis.get('technical', {}).get('volatility_daily', 0.3)
            volatility_score = max(0.1, min(1.0, 0.3 / volatility))  # عكسي
            factors['volatility'] = volatility_score
            
            # 5. قوة الحجم
            volume_ratio = stock_analysis.get('technical', {}).get('volume_ratio', 1.0)
            volume_score = min(1.0, volume_ratio / 1.5)
            factors['volume_strength'] = volume_score
            
            # 6. المؤشرات الفنية
            rsi = stock_analysis.get('technical', {}).get('rsi_14', 50)
            rsi_score = 1.0 if 30 <= rsi <= 70 else 0.6  # RSI في المنطقة المعتدلة
            factors['rsi_condition'] = rsi_score
            
            # حساب الثقة الإجمالية (متوسط مرجح)
            weights = {
                'historical_success': 0.25,
                'trend_strength': 0.20,
                'liquidity': 0.15,
                'volatility': 0.15,
                'volume_strength': 0.15,
                'rsi_condition': 0.10
            }
            
            confidence_score = sum(factors[factor] * weights[factor] for factor in factors)
            
            # حساب نسبة الربح المتوقعة
            targets = [signal_data.get(f'target_{i}') for i in range(1, 4) if signal_data.get(f'target_{i}')]
            entry_price = signal_data.get('buy_price', 0)
            
            if targets and entry_price:
                expected_return = max(((target - entry_price) / entry_price) * 100 for target in targets)
            else:
                expected_return = 5.0  # افتراضي 5%
            
            # حساب نسبة المخاطرة المتوقعة
            expected_risk = volatility * 100 * 2  # ضعف التقلبات اليومية
            
            # نسبة المكافأة/المخاطرة
            risk_reward_ratio = expected_return / expected_risk if expected_risk > 0 else 0
            
            return {
                'confidence_score': confidence_score,
                'factors': factors,
                'expected_return': expected_return,
                'expected_risk': expected_risk,
                'risk_reward_ratio': risk_reward_ratio,
                'success_probability': min(0.95, historical_success_rate * confidence_score)
            }
            
        except Exception as e:
            logger.error(f"Error evaluating signal quality: {e}")
            return {'confidence_score': 0.5, 'error': str(e)}
    
    async def _check_acceptance_criteria(self, signal_data: Dict, signal_quality: Dict, stock_analysis: Dict) -> Dict:
        """فحص معايير قبول الإشارة"""
        
        criteria_checks = {}
        
        # 1. ثقة الإشارة
        criteria_checks['confidence'] = {
            'passed': signal_quality['confidence_score'] >= self.min_signal_confidence,
            'value': signal_quality['confidence_score'],
            'threshold': self.min_signal_confidence
        }
        
        # 2. نسبة المكافأة/المخاطرة
        criteria_checks['risk_reward'] = {
            'passed': signal_quality.get('risk_reward_ratio', 0) >= 1.5,
            'value': signal_quality.get('risk_reward_ratio', 0),
            'threshold': 1.5
        }
        
        # 3. السيولة
        avg_volume = stock_analysis.get('liquidity', {}).get('avg_volume', 0)
        criteria_checks['liquidity'] = {
            'passed': avg_volume >= 500000,  # 500 ألف سهم يومياً
            'value': avg_volume,
            'threshold': 500000
        }
        
        # 4. التقلبات
        volatility = stock_analysis.get('technical', {}).get('volatility_daily', 1.0)
        criteria_checks['volatility'] = {
            'passed': volatility <= 0.08,  # أقل من 8% تقلبات يومية
            'value': volatility,
            'threshold': 0.08
        }
        
        # 5. فحص المراكز الموجودة (تجنب التركيز المفرط)
        existing_position = await self._check_existing_position(signal_data['stock_code'])
        criteria_checks['position_limit'] = {
            'passed': not existing_position,
            'value': bool(existing_position),
            'threshold': False
        }
        
        # 6. فحص حد المخاطرة الإجمالي للمحفظة
        portfolio_risk = await self._calculate_current_portfolio_risk()
        criteria_checks['portfolio_risk'] = {
            'passed': portfolio_risk <= self.max_portfolio_risk,
            'value': portfolio_risk,
            'threshold': self.max_portfolio_risk
        }
        
        # القرار النهائي
        all_passed = all(check['passed'] for check in criteria_checks.values())
        failed_criteria = [name for name, check in criteria_checks.items() if not check['passed']]
        
        return {
            'accepted': all_passed,
            'criteria_checks': criteria_checks,
            'failed_criteria': failed_criteria,
            'reason': f"فشل في المعايير: {', '.join(failed_criteria)}" if failed_criteria else "جميع المعايير مستوفاة"
        }
    
    async def _calculate_optimal_position_size(self, signal_data: Dict, stock_analysis: Dict, signal_quality: Dict) -> Dict:
        """حساب حجم المركز الأمثل"""
        try:
            entry_price = signal_data.get('buy_price', 0)
            
            # 1. الحجم الأساسي بناءً على المخاطرة
            portfolio_value = await self._get_current_portfolio_value()
            max_risk_amount = portfolio_value * self.max_position_risk
            
            # 2. تطبيق Kelly Criterion
            win_prob = signal_quality.get('success_probability', 0.6)
            expected_return = signal_quality.get('expected_return', 5.0) / 100
            expected_risk = signal_quality.get('expected_risk', 3.0) / 100
            
            # Kelly fraction
            kelly_fraction = (win_prob * expected_return - (1 - win_prob) * expected_risk) / expected_return
            kelly_fraction = max(0.1, min(1.0, kelly_fraction))  # بين 10% و 100%
            
            # 3. تعديلات إضافية
            
            # تعديل حسب التقلبات
            volatility = stock_analysis.get('technical', {}).get('volatility_daily', 0.05)
            volatility_adjustment = min(1.0, 0.05 / volatility)
            
            # تعديل حسب السيولة
            liquidity_score = stock_analysis.get('liquidity', {}).get('liquidity_score', 0.5)
            liquidity_adjustment = liquidity_score
            
            # تعديل حسب قوة الإشارة
            confidence_adjustment = signal_quality.get('confidence_score', 0.7)
            
            # تعديل حسب حالة السوق
            market_sentiment = stock_analysis.get('market_sentiment', {}).get('adjustment_factor', 1.0)
            
            # الحجم النهائي
            final_multiplier = (
                kelly_fraction * 
                volatility_adjustment * 
                liquidity_adjustment * 
                confidence_adjustment * 
                market_sentiment
            )
            
            position_value = max_risk_amount * final_multiplier
            shares_count = int(position_value / entry_price) if entry_price > 0 else 0
            
            # حساب مستوى وقف الخسارة
            atr = stock_analysis.get('technical', {}).get('atr_14', entry_price * 0.03)
            support_level = stock_analysis.get('trend', {}).get('support_level', entry_price * 0.95)
            
            # وقف الخسارة = الأقرب من (سعر الدخول - 2*ATR) أو (مستوى الدعم - 0.5%)
            stop_loss = max(entry_price - (2 * atr), support_level * 0.995)
            stop_loss = min(stop_loss, entry_price * 0.95)  # حد أقصى 5% خسارة
            
            return {
                'shares_count': shares_count,
                'position_value': position_value,
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'kelly_fraction': kelly_fraction,
                'final_multiplier': final_multiplier,
                'adjustments': {
                    'volatility': volatility_adjustment,
                    'liquidity': liquidity_adjustment,
                    'confidence': confidence_adjustment,
                    'market': market_sentiment
                },
                'risk_amount': position_value * ((entry_price - stop_loss) / entry_price),
                'risk_percentage': (position_value / portfolio_value) * 100
            }
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {'error': str(e)}
    
    async def _execute_buy_decision(self, signal_data: Dict, position_sizing: Dict, signal_quality: Dict, stock_analysis: Dict) -> Dict:
        """تنفيذ قرار الشراء وإنشاء المركز"""
        try:
            # إنشاء معرف فريد للمركز
            position_id = f"SP_{signal_data['stock_code']}_{int(datetime.now().timestamp())}"
            
            # حساب أهداف الربح
            entry_price = position_sizing['entry_price']
            profit_targets = []
            
            for i, (target_name, target_config) in enumerate(self.profit_taking_strategy.items(), 1):
                if target_name.startswith('target_'):
                    target_price = signal_data.get(f'target_{i}', entry_price * target_config['price_multiplier'])
                    profit_targets.append({
                        'target_number': i,
                        'price': target_price,
                        'sell_percentage': target_config['percentage'],
                        'achieved': False
                    })
            
            # إنشاء سجل المركز في قاعدة البيانات
            portfolio_position = SmartPortfolioPosition(
                position_id=position_id,
                stock_code=signal_data['stock_code'],
                stock_name_ar=signal_data.get('stock_name_ar', signal_data['stock_code']),
                entry_price=entry_price,
                entry_date=datetime.now(),
                initial_shares=position_sizing['shares_count'],
                remaining_shares=position_sizing['shares_count'],
                stop_loss_price=position_sizing['stop_loss'],
                trailing_stop_distance=stock_analysis.get('technical', {}).get('atr_14', entry_price * 0.02) * self.trailing_stop_factor,
                highest_price_reached=entry_price,
                profit_targets=profit_targets,
                targets_achieved=[],
                signal_id=signal_data.get('signal_id'),
                signal_confidence=signal_quality['confidence_score'],
                position_size_reasoning=f"Kelly: {position_sizing['kelly_fraction']:.2f}, Adjustments: {position_sizing['adjustments']}",
                status='ACTIVE'
            )
            
            self.db.add(portfolio_position)
            
            # إنشاء سجل المعاملة
            transaction = SmartPortfolioTransaction(
                transaction_id=f"T_{position_id}_BUY",
                position_id=position_id,
                transaction_type='BUY',
                shares=position_sizing['shares_count'],
                price=entry_price,
                total_value=position_sizing['position_value'],
                fees=position_sizing['position_value'] * 0.002,  # عمولة 0.2%
                trigger_reason='SIGNAL_ENTRY',
                pnl=0.0,
                pnl_percentage=0.0
            )
            
            self.db.add(transaction)
            self.db.commit()
            
            # تحديث إحصائيات المحفظة
            await self._update_portfolio_metrics()
            
            logger.info(f"Smart portfolio position created: {position_id}")
            
            return {
                'action': 'EXECUTED',
                'position_id': position_id,
                'stock_code': signal_data['stock_code'],
                'entry_price': entry_price,
                'shares': position_sizing['shares_count'],
                'position_value': position_sizing['position_value'],
                'stop_loss': position_sizing['stop_loss'],
                'profit_targets': profit_targets,
                'signal_quality': signal_quality,
                'risk_percentage': position_sizing['risk_percentage'],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing buy decision: {e}")
            self.db.rollback()
            return {'action': 'ERROR', 'error': str(e)}
    
    async def _perform_advanced_technical_analysis(self, df: pd.DataFrame) -> Dict:
        """تحليل فني متقدم شامل"""
        try:
            if df.empty:
                return {'error': 'No data available', 'overall_score': 0}
            
            # المؤشرات الأساسية
            current_price = df['close'].iloc[-1]
            
            # Moving Averages
            ma_5 = df['close'].rolling(5).mean()
            ma_10 = df['close'].rolling(10).mean()
            ma_20 = df['close'].rolling(20).mean()
            ma_50 = df['close'].rolling(50).mean()
            
            # RSI
            rsi_14 = self._calculate_rsi(df, 14)
            rsi_7 = self._calculate_rsi(df, 7)
            
            # MACD
            macd_line, macd_signal, macd_histogram = self._calculate_macd(df)
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(df)
            
            # Stochastic
            stoch_k, stoch_d = self._calculate_stochastic(df)
            
            # Williams %R
            williams_r = self._calculate_williams_r(df)
            
            # ATR for volatility
            atr_14 = self._calculate_atr(df, 14)
            
            # Volume indicators
            volume_sma = df['volume'].rolling(20).mean().iloc[-1]
            volume_ratio = df['volume'].iloc[-1] / volume_sma if volume_sma > 0 else 1
            
            # On Balance Volume
            obv = self._calculate_obv(df)
            
            # Price momentum
            momentum_5 = (current_price / df['close'].iloc[-6] - 1) * 100 if len(df) > 5 else 0
            momentum_10 = (current_price / df['close'].iloc[-11] - 1) * 100 if len(df) > 10 else 0
            
            # Support and Resistance levels
            support_levels = self._find_multiple_support_levels(df)
            resistance_levels = self._find_multiple_resistance_levels(df)
            
            # Trend analysis
            trend_direction = self._determine_trend_direction(df)
            trend_strength = self._calculate_advanced_trend_strength(df)
            
            # Volatility analysis
            volatility_20 = df['close'].pct_change().rolling(20).std() * np.sqrt(252)
            volatility_percentile = self._calculate_volatility_percentile(df)
            
            # Pattern recognition
            patterns = await self._detect_chart_patterns(df)
            
            # Technical score calculation
            technical_scores = {
                'trend_alignment': self._score_trend_alignment(current_price, ma_5, ma_10, ma_20, ma_50),
                'momentum': self._score_momentum(rsi_14, rsi_7, momentum_5, momentum_10),
                'volume_confirmation': self._score_volume(volume_ratio, obv),
                'oscillator_convergence': self._score_oscillators(rsi_14, stoch_k, williams_r),
                'macd_signal': self._score_macd(macd_line, macd_signal, macd_histogram),
                'bollinger_position': self._score_bollinger_bands(current_price, bb_upper, bb_middle, bb_lower),
                'support_resistance': self._score_support_resistance(current_price, support_levels, resistance_levels)
            }
            
            # Overall technical score (weighted average)
            weights = {
                'trend_alignment': 0.25,
                'momentum': 0.20,
                'volume_confirmation': 0.15,
                'oscillator_convergence': 0.15,
                'macd_signal': 0.10,
                'bollinger_position': 0.10,
                'support_resistance': 0.05
            }
            
            overall_score = sum(score * weights[indicator] for indicator, score in technical_scores.items())
            
            return {
                'overall_score': round(overall_score, 2),
                'current_price': current_price,
                'moving_averages': {
                    'ma_5': ma_5.iloc[-1] if not ma_5.empty else current_price,
                    'ma_10': ma_10.iloc[-1] if not ma_10.empty else current_price,
                    'ma_20': ma_20.iloc[-1] if not ma_20.empty else current_price,
                    'ma_50': ma_50.iloc[-1] if not ma_50.empty else current_price
                },
                'oscillators': {
                    'rsi_14': rsi_14,
                    'rsi_7': rsi_7,
                    'stoch_k': stoch_k,
                    'stoch_d': stoch_d,
                    'williams_r': williams_r
                },
                'macd': {
                    'line': macd_line,
                    'signal': macd_signal,
                    'histogram': macd_histogram
                },
                'bollinger_bands': {
                    'upper': bb_upper,
                    'middle': bb_middle,
                    'lower': bb_lower,
                    'width': (bb_upper - bb_lower) / bb_middle * 100 if bb_middle > 0 else 0
                },
                'volume_analysis': {
                    'current_volume': df['volume'].iloc[-1],
                    'average_volume': volume_sma,
                    'volume_ratio': volume_ratio,
                    'obv': obv
                },
                'trend_analysis': {
                    'direction': trend_direction,
                    'strength': trend_strength,
                    'momentum_5d': momentum_5,
                    'momentum_10d': momentum_10
                },
                'volatility': {
                    'atr_14': atr_14,
                    'volatility_20d': volatility_20.iloc[-1] if not volatility_20.empty else 0,
                    'volatility_percentile': volatility_percentile
                },
                'support_resistance': {
                    'support_levels': support_levels,
                    'resistance_levels': resistance_levels
                },
                'patterns': patterns,
                'individual_scores': technical_scores
            }
            
        except Exception as e:
            logger.error(f"Error in advanced technical analysis: {e}")
            return {'error': str(e), 'overall_score': 0}

    def _calculate_macd(self, df: pd.DataFrame, fast=12, slow=26, signal=9) -> Tuple[float, float, float]:
        """حساب MACD"""
        try:
            exp1 = df['close'].ewm(span=fast).mean()
            exp2 = df['close'].ewm(span=slow).mean()
            macd = exp1 - exp2
            signal_line = macd.ewm(span=signal).mean()
            histogram = macd - signal_line
            
            return (
                macd.iloc[-1] if not macd.empty else 0,
                signal_line.iloc[-1] if not signal_line.empty else 0,
                histogram.iloc[-1] if not histogram.empty else 0
            )
        except:
            return (0, 0, 0)

    def _calculate_bollinger_bands(self, df: pd.DataFrame, period=20, std_dev=2) -> Tuple[float, float, float]:
        """حساب Bollinger Bands"""
        try:
            sma = df['close'].rolling(period).mean()
            std = df['close'].rolling(period).std()
            
            upper = sma + (std * std_dev)
            lower = sma - (std * std_dev)
            
            return (
                upper.iloc[-1] if not upper.empty else df['close'].iloc[-1],
                sma.iloc[-1] if not sma.empty else df['close'].iloc[-1],
                lower.iloc[-1] if not lower.empty else df['close'].iloc[-1]
            )
        except:
            current_price = df['close'].iloc[-1]
            return (current_price * 1.02, current_price, current_price * 0.98)

    def _calculate_stochastic(self, df: pd.DataFrame, k_period=14, d_period=3) -> Tuple[float, float]:
        """حساب Stochastic Oscillator"""
        try:
            lowest_low = df['low'].rolling(k_period).min()
            highest_high = df['high'].rolling(k_period).max()
            
            k_percent = 100 * ((df['close'] - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(d_period).mean()
            
            return (
                k_percent.iloc[-1] if not k_percent.empty else 50,
                d_percent.iloc[-1] if not d_percent.empty else 50
            )
        except:
            return (50, 50)

    def _calculate_williams_r(self, df: pd.DataFrame, period=14) -> float:
        """حساب Williams %R"""
        try:
            highest_high = df['high'].rolling(period).max()
            lowest_low = df['low'].rolling(period).min()
            
            williams_r = -100 * ((highest_high - df['close']) / (highest_high - lowest_low))
            return williams_r.iloc[-1] if not williams_r.empty else -50
        except:
            return -50

    def _calculate_obv(self, df: pd.DataFrame) -> float:
        """حساب On Balance Volume"""
        try:
            obv = []
            obv_value = 0
            
            for i in range(len(df)):
                if i == 0:
                    obv.append(df['volume'].iloc[i])
                    obv_value = df['volume'].iloc[i]
                else:
                    if df['close'].iloc[i] > df['close'].iloc[i-1]:
                        obv_value += df['volume'].iloc[i]
                    elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                        obv_value -= df['volume'].iloc[i]
                    obv.append(obv_value)
            
            return obv[-1] if obv else 0
        except:
            return 0

    def _find_multiple_support_levels(self, df: pd.DataFrame) -> List[float]:
        """العثور على مستويات دعم متعددة"""
        try:
            support_levels = []
            
            # مستويات الدعم من القيعان المحلية
            for i in range(2, len(df) - 2):
                if (df['low'].iloc[i] < df['low'].iloc[i-1] and 
                    df['low'].iloc[i] < df['low'].iloc[i-2] and
                    df['low'].iloc[i] < df['low'].iloc[i+1] and 
                    df['low'].iloc[i] < df['low'].iloc[i+2]):
                    support_levels.append(df['low'].iloc[i])
            
            # ترتيب وإزالة المستويات المتقاربة
            support_levels = sorted(set(support_levels))
            
            # إبقاء أقوى 3 مستويات دعم
            return support_levels[-3:] if len(support_levels) > 3 else support_levels
            
        except:
            current_price = df['close'].iloc[-1]
            return [current_price * 0.95, current_price * 0.92, current_price * 0.88]

    def _find_multiple_resistance_levels(self, df: pd.DataFrame) -> List[float]:
        """العثور على مستويات مقاومة متعددة"""
        try:
            resistance_levels = []
            
            # مستويات المقاومة من القمم المحلية
            for i in range(2, len(df) - 2):
                if (df['high'].iloc[i] > df['high'].iloc[i-1] and 
                    df['high'].iloc[i] > df['high'].iloc[i-2] and
                    df['high'].iloc[i] > df['high'].iloc[i+1] and 
                    df['high'].iloc[i] > df['high'].iloc[i+2]):
                    resistance_levels.append(df['high'].iloc[i])
            
            # ترتيب وإزالة المستويات المتقاربة
            resistance_levels = sorted(set(resistance_levels), reverse=True)
            
            # إبقاء أقوى 3 مستويات مقاومة
            return resistance_levels[:3] if len(resistance_levels) > 3 else resistance_levels
            
        except:
            current_price = df['close'].iloc[-1]
            return [current_price * 1.05, current_price * 1.08, current_price * 1.12]

    def _determine_trend_direction(self, df: pd.DataFrame) -> str:
        """تحديد اتجاه الترند"""
        try:
            if len(df) < 20:
                return 'NEUTRAL'
            
            ma_5 = df['close'].rolling(5).mean().iloc[-1]
            ma_10 = df['close'].rolling(10).mean().iloc[-1]
            ma_20 = df['close'].rolling(20).mean().iloc[-1]
            current_price = df['close'].iloc[-1]
            
            if current_price > ma_5 > ma_10 > ma_20:
                return 'STRONG_BULLISH'
            elif current_price > ma_5 > ma_10:
                return 'BULLISH'
            elif current_price < ma_5 < ma_10 < ma_20:
                return 'STRONG_BEARISH'
            elif current_price < ma_5 < ma_10:
                return 'BEARISH'
            else:
                return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _calculate_advanced_trend_strength(self, df: pd.DataFrame) -> float:
        """حساب قوة الاتجاه المتقدم"""
        try:
            if len(df) < 20:
                return 50
            
            # استخدام ADX (Average Directional Index)
            high = df['high']
            low = df['low']
            close = df['close']
            
            # حساب True Range
            tr1 = high - low
            tr2 = abs(high - close.shift())
            tr3 = abs(low - close.shift())
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # حساب Directional Movement
            dm_plus = np.where((high - high.shift()) > (low.shift() - low), 
                              np.maximum(high - high.shift(), 0), 0)
            dm_minus = np.where((low.shift() - low) > (high - high.shift()), 
                               np.maximum(low.shift() - low, 0), 0)
            
            # تحويل إلى Series
            dm_plus = pd.Series(dm_plus, index=df.index)
            dm_minus = pd.Series(dm_minus, index=df.index)
            
            # حساب Smoothed values
            period = 14
            tr_smooth = tr.rolling(period).mean()
            dm_plus_smooth = dm_plus.rolling(period).mean()
            dm_minus_smooth = dm_minus.rolling(period).mean()
            
            # حساب DI+ و DI-
            di_plus = 100 * (dm_plus_smooth / tr_smooth)
            di_minus = 100 * (dm_minus_smooth / tr_smooth)
            
            # حساب ADX
            dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
            adx = dx.rolling(period).mean()
            
            trend_strength = adx.iloc[-1] if not adx.empty else 25
            
            # تحويل إلى نسبة مئوية (0-100)
            return min(100, max(0, trend_strength))
            
        except:
            return 50

    def _calculate_volatility_percentile(self, df: pd.DataFrame) -> float:
        """حساب percentile للتقلبات"""
        try:
            returns = df['close'].pct_change().dropna()
            if len(returns) < 20:
                return 50
            
            current_volatility = returns.tail(10).std()
            historical_volatilities = [returns.iloc[i:i+10].std() for i in range(len(returns)-10)]
            
            percentile = (sum(1 for vol in historical_volatilities if vol < current_volatility) / 
                         len(historical_volatilities)) * 100
            
            return percentile
        except:
            return 50

    async def _send_execution_notifications(self, signal_data: Dict, execution_result: Dict):
        """إرسال إشعارات تنفيذ الصفقة"""
        try:
            if execution_result['action'] == 'EXECUTED':
                message = f"""
🧠 المحفظة الذكية - صفقة جديدة
📈 السهم: {signal_data['stock_code']}
💰 سعر الدخول: {execution_result['entry_price']:.2f}
📊 الكمية: {execution_result['shares']:,}
🛡️ وقف الخسارة: {execution_result['stop_loss']:.2f}
⭐ جودة الإشارة: {execution_result['signal_quality']['confidence_score']:.1%}
💎 المخاطرة: {execution_result['risk_percentage']:.1f}%
"""
                
                # إرسال إشعار WebSocket
                await self.notification_service.send_websocket_notification({
                    'type': 'smart_portfolio_execution',
                    'data': execution_result
                })
                
                # إرسال إشعار تيليجرام إذا كان متاحاً
                try:
                    await self.telegram_service.send_message(message)
                except:
                    logger.warning("Could not send Telegram notification")
                    
        except Exception as e:            logger.error(f"Error sending execution notifications: {e}")

    # المساعدات الإضافية
    async def _get_stock_historical_data(self, stock_code: str, days: int = 60) -> List[Dict]:
        """الحصول على البيانات التاريخية للسهم"""
        try:
            # الحصول على البيانات التاريخية من قاعدة البيانات
            from datetime import datetime, timedelta
            import random
            
            start_date = datetime.now() - timedelta(days=days)
            
            # محاولة الحصول على البيانات الحقيقية من قاعدة البيانات
            # يجب تعديل هذا بناءً على الجدول الفعلي للبيانات التاريخية
            historical_data = []
            
            # إذا لم تكن هناك بيانات، نستخدم بيانات وهمية للاختبار
            if not historical_data:
                # إنشاء بيانات وهمية للاختبار
                base_price = 100.0
                for i in range(days):
                    date = start_date + timedelta(days=i)
                    price_change = (random.random() - 0.5) * 0.1  # تغيير عشوائي 10%
                    base_price *= (1 + price_change)
                    
                    historical_data.append({
                        'date': date.strftime('%Y-%m-%d'),
                        'open': base_price * 0.995,
                        'high': base_price * 1.02,
                        'low': base_price * 0.98,
                        'close': base_price,
                        'volume': random.randint(10000, 100000)
                    })
            
            return historical_data
            
        except Exception as e:
            logger.error(f"Error getting historical data for {stock_code}: {e}")
            return []
    
    async def _get_historical_signal_success(self, stock_code: str) -> Dict:
        """الحصول على معدل نجاح الإشارات السابقة"""
        try:
            # البحث في الإشارات السابقة للسهم
            successful_signals = self.db.query(LiveRecommendations).filter(
                and_(
                    LiveRecommendations.stock_code == stock_code,
                    LiveRecommendations.status == 'COMPLETED',
                    LiveRecommendations.total_pnl > 0
                )
            ).count()
            
            total_signals = self.db.query(LiveRecommendations).filter(
                and_(
                    LiveRecommendations.stock_code == stock_code,
                    LiveRecommendations.status == 'COMPLETED'
                )
            ).count()
            
            success_rate = successful_signals / total_signals if total_signals > 0 else 0.6
            
            return {
                'success_rate': success_rate,
                'total_signals': total_signals,
                'successful_signals': successful_signals
            }
        except:
            return {'success_rate': 0.6, 'total_signals': 0, 'successful_signals': 0}
    
    async def _analyze_market_sentiment(self) -> Dict:
        """تحليل حالة السوق العامة"""
        return {
            'sentiment': 'neutral',
            'adjustment_factor': 1.0,
            'volatility_regime': 'normal'
        }
    
    async def _check_existing_position(self, stock_code: str) -> bool:
        """فحص وجود مركز نشط للسهم"""
        existing = self.db.query(SmartPortfolioPosition).filter(
            and_(
                SmartPortfolioPosition.stock_code == stock_code,
                SmartPortfolioPosition.status == 'ACTIVE'
            )
        ).first()
        
        return existing is not None
    
    async def _calculate_current_portfolio_risk(self) -> float:
        """حساب مخاطر المحفظة الحالية"""
        try:
            active_positions = self.db.query(SmartPortfolioPosition).filter(
                SmartPortfolioPosition.status == 'ACTIVE'
            ).all()
            
            total_risk = 0.0
            portfolio_value = await self._get_current_portfolio_value()
            
            for position in active_positions:
                position_value = position.remaining_shares * position.entry_price
                risk_per_share = position.entry_price - position.stop_loss_price
                position_risk = position.remaining_shares * risk_per_share
                total_risk += position_risk
            
            return (total_risk / portfolio_value) if portfolio_value > 0 else 0.0
        except:
            return 0.0
    
    async def _get_current_portfolio_value(self) -> float:
        """الحصول على القيمة الحالية للمحفظة"""
        try:
            # حساب القيمة الإجمالية (نقد + أسهم)
            active_positions = self.db.query(SmartPortfolioPosition).filter(
                SmartPortfolioPosition.status == 'ACTIVE'
            ).all()
            
            total_investment = sum(
                pos.remaining_shares * pos.entry_price for pos in active_positions
            )
            
            # افتراض أن باقي المال نقد
            cash_balance = self.initial_capital - total_investment
            
            return self.initial_capital  # للتبسيط نُعيد القيمة الأولية
        except:
            return self.initial_capital
    
        """تحديث إحصائيات المحفظة"""
        try:
            # هنا سنحسب ونحفظ إحصائيات المحفظة اليومية
            # سيتم تنفيذها لاحقاً مع باقي المقاييس
            pass
        except Exception as e:
            logger.error(f"Error updating portfolio metrics: {e}")    # =============================================================================
    # MISSING HELPER METHODS - تم إضافتها لإصلاح مشاكل الاستيراد
    # =============================================================================
    
    def _calculate_rsi(self, prices, period: int = 14) -> pd.Series:
        """حساب مؤشر القوة النسبية RSI"""
        try:
            # إذا كان المدخل DataFrame، استخدم عمود 'close'
            if isinstance(prices, pd.DataFrame):
                if 'close' in prices.columns:
                    prices = prices['close']
                else:
                    logger.error("DataFrame doesn't have 'close' column")
                    return pd.Series([50.0] * len(prices), index=prices.index)
            
            # إذا كان المدخل Series
            if isinstance(prices, pd.Series):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi.fillna(50.0)
            
            # إذا كان المدخل قائمة أو array
            if isinstance(prices, (list, np.ndarray)):
                prices = pd.Series(prices)
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi.fillna(50.0)
            
            return pd.Series([50.0])
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return pd.Series([50.0])
    
    async def _analyze_liquidity_comprehensive(self, df: pd.DataFrame) -> Dict:
        """تحليل شامل للسيولة - استخدام الطريقة من AdvancedTechnicalAnalyzer"""
        try:
            return await self.technical_analyzer.analyze_liquidity_comprehensive(df)
        except Exception as e:
            logger.error(f"Error in liquidity analysis: {e}")
            return {'liquidity_score': 0, 'quality': 'POOR', 'error': str(e)}
    
    async def _check_technical_alignment(self, signal_data: Dict, stock_analysis: Dict) -> Dict:
        """فحص توافق التحليل الفني مع الإشارة"""
        try:
            signal_action = signal_data.get('action', '').upper()
            technical_analysis = stock_analysis.get('technical_analysis', {})
            
            alignment_score = 0
            factors = []
            
            # فحص RSI
            rsi = technical_analysis.get('current_indicators', {}).get('rsi', 50)
            if signal_action == 'BUY':
                if rsi < 70:  # ليس في منطقة التشبع الشرائي
                    alignment_score += 25
                    factors.append('RSI_FAVORABLE')
                if rsi < 30:  # في منطقة التشبع البيعي
                    alignment_score += 15
                    factors.append('RSI_OVERSOLD')
            elif signal_action == 'SELL':
                if rsi > 30:  # ليس في منطقة التشبع البيعي
                    alignment_score += 25
                    factors.append('RSI_FAVORABLE')
                if rsi > 70:  # في منطقة التشبع الشرائي
                    alignment_score += 15
                    factors.append('RSI_OVERBOUGHT')
            
            # فحص الاتجاه العام
            trend_strength = technical_analysis.get('trend_strength', 'NEUTRAL')
            if signal_action == 'BUY' and 'BULLISH' in trend_strength:
                alignment_score += 30
                factors.append('BULLISH_TREND')
            elif signal_action == 'SELL' and 'BEARISH' in trend_strength:
                alignment_score += 30
                factors.append('BEARISH_TREND')
            
            # فحص الزخم
            momentum = technical_analysis.get('momentum', {})
            momentum_score = momentum.get('momentum_score', 50)
            if signal_action == 'BUY' and momentum_score > 60:
                alignment_score += 20
                factors.append('POSITIVE_MOMENTUM')
            elif signal_action == 'SELL' and momentum_score < 40:
                alignment_score += 20
                factors.append('NEGATIVE_MOMENTUM')
            
            # تحديد مستوى التوافق
            if alignment_score >= 70:
                alignment_level = 'STRONG'
            elif alignment_score >= 50:
                alignment_level = 'MODERATE'
            elif alignment_score >= 30:
                alignment_level = 'WEAK'
            else:
                alignment_level = 'POOR'
            
            return {
                'alignment_score': alignment_score,
                'alignment_level': alignment_level,
                'supporting_factors': factors,
                'is_aligned': alignment_score >= 50
            }
            
        except Exception as e:
            logger.error(f"Error checking technical alignment: {e}")
            return {
                'alignment_score': 0,
                'alignment_level': 'POOR',
                'supporting_factors': [],
                'is_aligned': False,
                'error': str(e)
            }
    
    async def _check_portfolio_concentration(self, stock_code: str) -> Dict:
        """فحص تركز المحفظة وإدارة المخاطر"""
        try:
            from ..models.smart_portfolio import SmartPortfolioPosition
            
            # الحصول على المراكز النشطة
            active_positions = self.db.query(SmartPortfolioPosition).filter(
                SmartPortfolioPosition.status == 'ACTIVE'
            ).all()
            
            if not active_positions:
                return {
                    'concentration_risk': 'LOW',
                    'sector_concentration': 0,
                    'position_count': 0,
                    'max_position_weight': 0,
                    'is_concentrated': False,
                    'recommendation': 'PROCEED'
                }
            
            # حساب القيم الحالية
            total_portfolio_value = sum(pos.remaining_shares * pos.entry_price for pos in active_positions)
            position_count = len(active_positions)
            
            # فحص تركز السهم الواحد
            stock_positions = [pos for pos in active_positions if pos.stock_code == stock_code]
            current_stock_weight = 0
            if stock_positions:
                current_stock_value = sum(pos.remaining_shares * pos.entry_price for pos in stock_positions)
                current_stock_weight = (current_stock_value / total_portfolio_value) * 100 if total_portfolio_value > 0 else 0
            
            # فحص تركز القطاعات (تقدير أساسي)
            stock_codes = [pos.stock_code for pos in active_positions]
            unique_stocks = len(set(stock_codes))
            
            # حساب الحد الأقصى لوزن المركز
            max_position_weights = [(pos.remaining_shares * pos.entry_price / total_portfolio_value) * 100 
                                  for pos in active_positions] if total_portfolio_value > 0 else [0]
            max_position_weight = max(max_position_weights) if max_position_weights else 0
            
            # تقييم مستوى التركز
            concentration_risk = 'LOW'
            is_concentrated = False
            recommendation = 'PROCEED'
            
            if current_stock_weight > 15 or max_position_weight > 20:
                concentration_risk = 'HIGH'
                is_concentrated = True
                recommendation = 'REDUCE_SIZE'
            elif current_stock_weight > 10 or max_position_weight > 15:
                concentration_risk = 'MEDIUM'
                is_concentrated = True
                recommendation = 'CAUTION'
            elif position_count > 15:
                concentration_risk = 'MEDIUM'
                recommendation = 'CONSIDER_CONSOLIDATION'
            return {
                'concentration_risk': concentration_risk,
                'current_stock_weight': round(current_stock_weight, 2),
                'position_count': position_count,
                'unique_stocks': unique_stocks,
                'max_position_weight': round(max_position_weight, 2),
                'total_portfolio_value': total_portfolio_value,
                'is_concentrated': is_concentrated,
                'recommendation': recommendation,
                'diversification_score': min(100, unique_stocks * 10)
            }
            
        except Exception as e:
            logger.error(f"Error checking portfolio concentration: {e}")
            return {
                'concentration_risk': 'UNKNOWN',
                'error': str(e),
                'is_concentrated': False,
                'recommendation': 'PROCEED'
            }
    
    async def _log_signal_rejection(self, signal_data: Dict, reason: str):
        """تسجيل رفض الإشارة مع السبب"""
        try:
            from ..models.smart_portfolio import SmartPortfolioSignalProcessing
            
            log_entry = SmartPortfolioSignalProcessing(
                signal_id=signal_data.get('signal_id', f"signal_{int(datetime.now().timestamp())}"),
                stock_code=signal_data.get('stock_code'),
                signal_type=signal_data.get('action'),
                signal_confidence=signal_data.get('confidence', 0),
                processing_result='REJECTED',
                decision_reason=reason,
                received_at=datetime.now(),
                processed_at=datetime.now()
            )
            
            self.db.add(log_entry)
            self.db.commit()
            
            logger.info(f"📝 Signal rejection logged for {signal_data.get('stock_code')}: {reason}")
            
        except Exception as e:
            logger.error(f"Error logging signal rejection: {e}")
    
    async def _handle_processing_error(self, signal_data: Dict, error_message: str):
        """معالجة أخطاء المعالجة وتسجيلها"""
        try:
            from ..models.smart_portfolio import SmartPortfolioSignalProcessing
            
            # تسجيل الخطأ في قاعدة البيانات
            error_log = SmartPortfolioSignalProcessing(
                signal_id=signal_data.get('signal_id', f"error_{int(datetime.now().timestamp())}"),
                stock_code=signal_data.get('stock_code'),
                signal_type=signal_data.get('action'),
                signal_confidence=signal_data.get('confidence', 0),
                processing_result='ERROR',
                decision_reason='PROCESSING_ERROR',
                received_at=datetime.now(),
                processed_at=datetime.now()
            )
            
            self.db.add(error_log)
            self.db.commit()
            
            # إرسال تنبيه
            await self.notification_service.send_error_alert(
                f"Smart Portfolio Processing Error",
                f"Error processing signal for {signal_data.get('stock_code')}: {error_message}"
            )
            
            logger.error(f"🚨 Processing error handled for {signal_data.get('stock_code')}: {error_message}")
            
        except Exception as e:
            logger.error(f"Error in error handler: {e}")

    async def _analyze_relative_performance(self, stock_code: str, df: pd.DataFrame) -> Dict:
        """تحليل الأداء النسبي للسهم مقارنة بالسوق"""
        try:
            if df.empty or len(df) < 20:
                return {
                    'relative_strength': 0.5,
                    'market_correlation': 0.0,
                    'outperformance_score': 0.0,
                    'trend_alignment': 'NEUTRAL',
                    'performance_rating': 'AVERAGE'
                }
            
            # حساب العوائد
            returns = df['close'].pct_change().dropna()
              # حساب القوة النسبية
            if len(df) >= 14:
                rsi_series = self._calculate_rsi(df['close'], period=14)
                if isinstance(rsi_series, pd.Series) and not rsi_series.empty:
                    relative_strength = rsi_series.iloc[-1] / 100
                else:
                    relative_strength = 0.5
            else:
                relative_strength = 0.5
            
            # تحديد اتجاه الترند
            sma_20 = df['close'].rolling(20).mean().iloc[-1] if len(df) >= 20 else df['close'].iloc[-1]
            current_price = df['close'].iloc[-1]
            
            if current_price > sma_20 * 1.02:
                trend_alignment = 'BULLISH'
            elif current_price < sma_20 * 0.98:
                trend_alignment = 'BEARISH'
            else:
                trend_alignment = 'NEUTRAL'
            
            return {
                'relative_strength': round(relative_strength, 3),
                'market_correlation': 0.0,
                'outperformance_score': 0.0,
                'trend_alignment': trend_alignment,
                'performance_rating': 'AVERAGE'
            }
            
        except Exception as e:
            logger.error(f"Error analyzing relative performance for {stock_code}: {e}")
            return {
                'relative_strength': 0.5,
                'market_correlation': 0.0,
                'outperformance_score': 0.0,
                'trend_alignment': 'NEUTRAL',                'performance_rating': 'AVERAGE'
            }

    async def _assess_current_portfolio_risk(self) -> Dict:
        """تقييم المخاطر الحالية للمحفظة"""
        try:
            from ..models.smart_portfolio import SmartPortfolioPosition
            
            # الحصول على المراكز النشطة
            active_positions = self.db.query(SmartPortfolioPosition).filter(
                SmartPortfolioPosition.status == 'ACTIVE'
            ).all()
            
            if not active_positions:
                return {
                    'total_risk': 0.0,
                    'position_count': 0,
                    'risk_level': 'LOW',
                    'concentration_risk': 'LOW',
                    'sector_risk': 'LOW',
                    'liquidity_risk': 'LOW',
                    'overall_risk_rating': 'LOW',
                    'risk_budget_used': 0.0,
                    'available_risk_budget': self.max_portfolio_risk
                }
            
            # حساب إجمالي قيمة المحفظة
            total_portfolio_value = sum(pos.remaining_shares * pos.current_price for pos in active_positions if pos.current_price)
            
            if total_portfolio_value == 0:
                return {
                    'total_risk': 0.0,
                    'position_count': len(active_positions),
                    'risk_level': 'LOW',
                    'concentration_risk': 'LOW',
                    'sector_risk': 'LOW',
                    'liquidity_risk': 'LOW',
                    'overall_risk_rating': 'LOW',
                    'risk_budget_used': 0.0,
                    'available_risk_budget': self.max_portfolio_risk
                }
            
            # حساب المخاطر لكل مركز
            position_risks = []
            total_risk_exposure = 0
            
            for pos in active_positions:
                if pos.current_price and pos.stop_loss_price:
                    position_value = pos.remaining_shares * pos.current_price
                    position_weight = position_value / total_portfolio_value
                    
                    # حساب المخاطرة المحتملة
                    potential_loss = abs(pos.current_price - pos.stop_loss_price) / pos.current_price
                    position_risk = position_weight * potential_loss
                    
                    position_risks.append({
                        'stock_code': pos.stock_code,
                        'weight': position_weight,
                        'risk': position_risk
                    })
                    
                    total_risk_exposure += position_risk
            
            # تقييم مخاطر التركز
            position_weights = [pr['weight'] for pr in position_risks]
            max_position_weight = max(position_weights) if position_weights else 0
            
            if max_position_weight > 0.2:
                concentration_risk = 'HIGH'
            elif max_position_weight > 0.15:
                concentration_risk = 'MEDIUM'
            else:
                concentration_risk = 'LOW'
            
            # تقييم مخاطر القطاعات (تبسيط)
            unique_stocks = len(set(pos.stock_code for pos in active_positions))
            if unique_stocks < 5:
                sector_risk = 'HIGH'
            elif unique_stocks < 8:
                sector_risk = 'MEDIUM'
            else:
                sector_risk = 'LOW'
            
            # تقييم مخاطر السيولة
            if len(active_positions) > 15:
                liquidity_risk = 'MEDIUM'
            elif len(active_positions) > 20:
                liquidity_risk = 'HIGH'
            else:
                liquidity_risk = 'LOW'
            
            # تحديد مستوى المخاطر العام
            if total_risk_exposure > self.max_portfolio_risk * 0.8:
                risk_level = 'HIGH'
            elif total_risk_exposure > self.max_portfolio_risk * 0.6:
                risk_level = 'MEDIUM'
            else:
                risk_level = 'LOW'
            
            # التقييم العام للمخاطر
            risk_factors = [concentration_risk, sector_risk, liquidity_risk]
            high_risk_count = risk_factors.count('HIGH')
            medium_risk_count = risk_factors.count('MEDIUM')
            
            if high_risk_count >= 2 or total_risk_exposure > self.max_portfolio_risk * 0.8:
                overall_risk_rating = 'HIGH'
            elif high_risk_count >= 1 or medium_risk_count >= 2:
                overall_risk_rating = 'MEDIUM'
            else:
                overall_risk_rating = 'LOW'
            
            return {
                'total_risk': round(total_risk_exposure, 4),
                'position_count': len(active_positions),
                'risk_level': risk_level,
                'concentration_risk': concentration_risk,
                'sector_risk': sector_risk,
                'liquidity_risk': liquidity_risk,
                'overall_risk_rating': overall_risk_rating,
                'risk_budget_used': round(total_risk_exposure / self.max_portfolio_risk, 2),
                'available_risk_budget': round(self.max_portfolio_risk - total_risk_exposure, 4),
                'max_position_weight': round(max_position_weight, 3),
                'portfolio_value': round(total_portfolio_value, 2)
            }
            
        except Exception as e:
            logger.error(f"Error assessing portfolio risk: {e}")
            return {
                'total_risk': 0.0,
                'position_count': 0,
                'risk_level': 'UNKNOWN',
                'concentration_risk': 'UNKNOWN',
                'sector_risk': 'UNKNOWN',
                'liquidity_risk': 'UNKNOWN',
                'overall_risk_rating': 'HIGH',  # Conservative default
                'risk_budget_used': 1.0,
                'available_risk_budget': 0.0
            }

    async def _identify_support_resistance_levels(self, df: pd.DataFrame) -> Dict:
        """تحديد مستويات الدعم والمقاومة"""
        try:
            if df.empty or len(df) < 10:
                current_price = df['close'].iloc[-1] if not df.empty else 100
                return {
                    'support_levels': [current_price * 0.95, current_price * 0.92],
                    'resistance_levels': [current_price * 1.05, current_price * 1.08],
                    'nearest_support': current_price * 0.95,
                    'nearest_resistance': current_price * 1.05,
                    'support_strength': 'MODERATE',
                    'resistance_strength': 'MODERATE'
                }
            
            current_price = df['close'].iloc[-1]
            
            # العثور على القمم والقيعان المحلية
            support_levels = []
            resistance_levels = []
            
            # البحث عن القيعان (مستويات الدعم)
            for i in range(2, len(df) - 2):
                if (df['low'].iloc[i] < df['low'].iloc[i-1] and 
                    df['low'].iloc[i] < df['low'].iloc[i-2] and
                    df['low'].iloc[i] < df['low'].iloc[i+1] and 
                    df['low'].iloc[i] < df['low'].iloc[i+2]):
                    support_levels.append(df['low'].iloc[i])
            
            # البحث عن القمم (مستويات المقاومة)
            for i in range(2, len(df) - 2):
                if (df['high'].iloc[i] > df['high'].iloc[i-1] and 
                    df['high'].iloc[i] > df['high'].iloc[i-2] and
                    df['high'].iloc[i] > df['high'].iloc[i+1] and 
                    df['high'].iloc[i] > df['high'].iloc[i+2]):
                    resistance_levels.append(df['high'].iloc[i])
            
            # ترتيب المستويات وإزالة المتكررة
            support_levels = sorted(list(set(support_levels)), reverse=True)
            resistance_levels = sorted(list(set(resistance_levels)))
            
            # الحصول على أقرب مستويات للسعر الحالي
            supports_below = [s for s in support_levels if s < current_price]
            resistances_above = [r for r in resistance_levels if r > current_price]
            nearest_support = supports_below[0] if supports_below else current_price * 0.95
            nearest_resistance = resistances_above[0] if resistances_above else current_price * 1.05
            
            # تقييم قوة المستويات
            support_strength = 'STRONG' if len(supports_below) >= 3 else 'MODERATE' if len(supports_below) >= 1 else 'WEAK'
            resistance_strength = 'STRONG' if len(resistances_above) >= 3 else 'MODERATE' if len(resistances_above) >= 1 else 'WEAK'
            
            return {
                'support_levels': supports_below[:3],
                'resistance_levels': resistances_above[:3],
                'nearest_support': nearest_support,
                'nearest_resistance': nearest_resistance,
                'support_strength': support_strength,
                'resistance_strength': resistance_strength
            }
            
        except Exception as e:
            logger.error(f"Error identifying support/resistance levels: {e}")
            current_price = df['close'].iloc[-1] if not df.empty else 100
            return {
                'support_levels': [current_price * 0.95],
                'resistance_levels': [current_price * 1.05],
                'nearest_support': current_price * 0.95,
                'nearest_resistance': current_price * 1.05,
                'support_strength': 'UNKNOWN',
                'resistance_strength': 'UNKNOWN'
            }

    async def _assess_market_conditions(self) -> float:
        """تقييم ظروف السوق العامة"""
        try:
            # تحليل أساسي لظروف السوق
            market_factors = {
                'volatility_regime': 0.6,
                'liquidity_conditions': 0.7,
                'sentiment_indicator': 0.6,                
                'economic_indicators': 0.65,
                'technical_momentum': 0.55
            }
            
            # حساب المتوسط المرجح
            weights = {
                'volatility_regime': 0.25,
                'liquidity_conditions': 0.20,
                'sentiment_indicator': 0.20,
                'economic_indicators': 0.20,
                'technical_momentum': 0.15
            }
            
            market_score = sum(factor * weights[name] for name, factor in market_factors.items())
            market_score = max(0.0, min(1.0, market_score))
            
            return market_score
            
        except Exception as e:
            logger.error(f"Error assessing market conditions: {e}")
            return 0.5

    async def _calculate_stock_quality_score(self, technical_analysis: Dict, liquidity_analysis: Dict, 
                                           volatility_analysis: Dict, relative_performance: Dict) -> float:
        """حساب نقاط جودة السهم الإجمالية"""
        try:
            # أوزان العوامل المختلفة
            weights = {
                'technical': 0.3,
                'liquidity': 0.25,
                'volatility': 0.2,
                'relative_performance': 0.25
            }
            
            # نقاط التحليل الفني
            technical_score = technical_analysis.get('overall_score', 50)
            
            # نقاط السيولة
            liquidity_score = liquidity_analysis.get('liquidity_score', 50)
            
            # نقاط التقلبات (منخفضة أفضل)
            volatility_level = volatility_analysis.get('risk_level', 'MEDIUM')
            volatility_score = {'LOW': 80, 'MEDIUM': 60, 'HIGH': 40, 'EXTREME': 20}.get(volatility_level, 50)
            
            # نقاط الأداء النسبي
            relative_strength = relative_performance.get('relative_strength', 0.5)
            performance_score = relative_strength * 100
            
            # حساب النقاط المرجحة
            total_score = (
                technical_score * weights['technical'] +
                liquidity_score * weights['liquidity'] +
                volatility_score * weights['volatility'] +
                performance_score * weights['relative_performance']
            )
            
            return min(100, max(0, total_score))
            
        except Exception as e:
            logger.error(f"Error calculating stock quality score: {e}")
            return 50.0

    async def _assess_stock_specific_risks(self, stock_code: str, df: pd.DataFrame) -> Dict:
        """تقييم المخاطر الخاصة بالسهم"""
        try:
            risks = []
            risk_level = 'LOW'
            
            if df.empty:
                return {'risks': ['No data available'], 'risk_level': 'HIGH'}
            
            # فحص التقلبات العالية
            if len(df) >= 20:
                volatility = df['close'].pct_change().rolling(20).std() * np.sqrt(252)
                if not volatility.empty and volatility.iloc[-1] > 0.4:
                    risks.append('High volatility detected')
                    risk_level = 'HIGH'
            
            # فحص السيولة المنخفضة
            if 'volume' in df.columns:
                avg_volume = df['volume'].rolling(20).mean().iloc[-1] if len(df) >= 20 else df['volume'].iloc[-1]
                if avg_volume < 100000:
                    risks.append('Low liquidity')
                    risk_level = 'MEDIUM' if risk_level == 'LOW' else 'HIGH'
            
            # فحص الانخفاض الحاد
            if len(df) >= 5:
                recent_change = (df['close'].iloc[-1] / df['close'].iloc[-5] - 1) * 100
                if recent_change < -10:
                    risks.append('Sharp recent decline')
                    risk_level = 'MEDIUM' if risk_level == 'LOW' else 'HIGH'
            
            return {
                'risks': risks if risks else ['No major risks detected'],
                'risk_level': risk_level,
                'risk_count': len(risks)
            }
            
        except Exception as e:
            logger.error(f"Error assessing stock risks for {stock_code}: {e}")
            return {'risks': ['Error in risk assessment'], 'risk_level': 'HIGH'}

    async def _check_for_conflicting_positions(self, stock_code: str) -> Dict:
        """فحص المراكز المتضاربة في المحفظة"""
        try:
            from ..models.smart_portfolio import SmartPortfolioPosition
            
            # البحث عن مراكز نشطة في نفس السهم
            existing_positions = self.db.query(SmartPortfolioPosition).filter(
                and_(
                    SmartPortfolioPosition.stock_code == stock_code,
                    SmartPortfolioPosition.status == 'ACTIVE'
                )
            ).all()
            
            has_conflicts = len(existing_positions) > 0
            
            conflict_details = []
            if has_conflicts:
                for pos in existing_positions:
                    conflict_details.append({
                        'position_id': pos.position_id,
                        'entry_date': pos.entry_date.isoformat() if pos.entry_date else None,
                        'shares': pos.remaining_shares,
                        'entry_price': pos.entry_price
                    })
            
            return {
                'has_conflicts': has_conflicts,
                'conflict_count': len(existing_positions),
                'existing_positions': conflict_details,
                'recommendation': 'AVOID' if has_conflicts else 'PROCEED'
            }
            
        except Exception as e:
            logger.error(f"Error checking conflicting positions for {stock_code}: {e}")
            return {
                'has_conflicts': False,
                'conflict_count': 0,
                'existing_positions': [],
                'recommendation': 'PROCEED'
            }

    async def _get_available_capital(self) -> float:
        """حساب رأس المال المتاح للاستثمار"""
        try:
            from ..models.smart_portfolio import SmartPortfolioPosition
            
            # حساب رأس المال المستثمر حالياً
            active_positions = self.db.query(SmartPortfolioPosition).filter(
                SmartPortfolioPosition.status == 'ACTIVE'
            ).all()
            
            invested_capital = sum(
                pos.remaining_shares * pos.entry_price 
                for pos in active_positions 
                if pos.entry_price and pos.remaining_shares
            )
            
            available_capital = self.initial_capital - invested_capital
            
            return max(0, available_capital)
            
        except Exception as e:
            logger.error(f"Error calculating available capital: {e}")
            return self.initial_capital * 0.1  # احتياطي 10%