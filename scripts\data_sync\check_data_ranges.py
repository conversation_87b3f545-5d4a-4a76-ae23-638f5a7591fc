#!/usr/bin/env python3
"""
Check available columns and data for improved criteria
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Supabase client
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key)

print("🔍 Checking available data for improved criteria...")

# Check stocks_realtime columns
response = supabase.table('stocks_realtime').select('*').limit(1).execute()
if response.data:
    print("\n📊 stocks_realtime columns:")
    for col in response.data[0].keys():
        print(f"  • {col}")

# Check if we have historical data for technical indicators
response = supabase.table('stocks_historical').select('*').limit(1).execute()
if response.data:
    print("\n📈 stocks_historical columns:")
    for col in response.data[0].keys():
        print(f"  • {col}")

# Check financial data (try different table names)
try:
    response = supabase.table('financial_data').select('*').limit(1).execute()
    if response.data:
        print("\n💰 financial_data columns:")
        for col in response.data[0].keys():
            print(f"  • {col}")
except Exception as e:
    print(f"\n💰 No financial_data table found: {e}")

# Check sample data ranges
print("\n📊 Sample data analysis...")
response = supabase.table('stocks_realtime').select('symbol, volume, market_cap, change_percent, turnover').gt('volume', 0).not_('symbol', 'like', '*EGX*').order('volume', desc=True).limit(10).execute()
stocks = response.data

print(f"\nTop 10 stocks by volume:")
for stock in stocks:
    market_cap = stock.get('market_cap', 0) or 0
    turnover = stock.get('turnover', 0) or 0
    print(f"  • {stock['symbol']}: Vol={stock['volume']:,}, Cap={market_cap:,}, Change={stock['change_percent']}%, Turnover={turnover:,}")

# Check volume and market cap ranges
response = supabase.table('stocks_realtime').select('volume, market_cap').gt('volume', 0).not_('symbol', 'like', '*EGX*').execute()
all_stocks = response.data

volumes = [s['volume'] for s in all_stocks if s.get('volume')]
market_caps = [s['market_cap'] for s in all_stocks if s.get('market_cap')]

if volumes:
    print(f"\n📊 Volume statistics:")
    print(f"  • Min: {min(volumes):,}")
    print(f"  • Max: {max(volumes):,}")
    print(f"  • Avg: {sum(volumes)/len(volumes):,.0f}")

if market_caps:
    print(f"\n💰 Market Cap statistics:")
    print(f"  • Min: {min(market_caps):,}")
    print(f"  • Max: {max(market_caps):,}")
    print(f"  • Avg: {sum(market_caps)/len(market_caps):,.0f}")
