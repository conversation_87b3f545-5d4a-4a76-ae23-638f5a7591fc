#!/usr/bin/env python3
"""
Update stocks_financials table schema with missing columns
"""

import os
import sys
import logging
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financials_schema_update.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    logger.error("Missing required environment variables")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def create_financials_schema_sql():
    """Create SQL to add missing columns to stocks_financials table"""
    
    logger.info("🔧 Creating SQL for stocks_financials missing columns...")
    
    # Define all missing columns based on the enhanced import script
    missing_columns = [
        # Basic Info
        ('description', 'TEXT'),
        ('industry', 'VARCHAR(200)'),
        ('isin', 'VARCHAR(20)'),
        ('index_membership', 'VARCHAR(100)'),
        ('market_cap_currency', 'VARCHAR(10)'),
        
        # Shares & Float
        ('total_shares_outstanding', 'BIGINT'),
        ('float_shares_outstanding', 'BIGINT'),
        ('float_shares_percent', 'DECIMAL'),
        ('number_of_shareholders', 'INTEGER'),
        
        # Financial Ratios (Extended)
        ('assets_to_equity', 'DECIMAL'),
        ('assets_turnover', 'DECIMAL'),
        ('cash_ratio', 'DECIMAL'),
        ('cash_to_debt_ratio', 'DECIMAL'),
        ('debt_to_assets', 'DECIMAL'),
        ('price_to_cash_flow', 'DECIMAL'),
        ('price_to_free_cash_flow', 'DECIMAL'),
        ('price_to_sales', 'DECIMAL'),
        
        # Enterprise Value Metrics
        ('enterprise_value', 'DECIMAL'),
        ('ev_to_ebitda', 'DECIMAL'),
        ('ev_to_revenue', 'DECIMAL'),
        
        # Income Statement
        ('total_revenue', 'DECIMAL'),
        ('gross_profit', 'DECIMAL'),
        ('operating_income', 'DECIMAL'),
        ('net_income', 'DECIMAL'),
        ('ebitda', 'DECIMAL'),
        
        # Balance Sheet
        ('total_equity', 'DECIMAL'),
        ('total_liabilities', 'DECIMAL'),
        ('total_current_assets', 'DECIMAL'),
        ('total_current_liabilities', 'DECIMAL'),
        ('cash_and_equivalents', 'DECIMAL'),
        ('cash_short_term_investments', 'DECIMAL'),
        ('net_debt', 'DECIMAL'),
        ('goodwill', 'DECIMAL'),
        
        # Cash Flow
        ('operating_cash_flow', 'DECIMAL'),
        ('investing_cash_flow', 'DECIMAL'),
        ('financing_cash_flow', 'DECIMAL'),
        ('capital_expenditures', 'DECIMAL'),
        
        # EPS Variants
        ('eps_basic_ttm', 'DECIMAL'),
        ('eps_diluted_ttm', 'DECIMAL'),
        ('eps_reported_annual', 'DECIMAL'),
        ('eps_estimate_quarterly', 'DECIMAL'),
        
        # Growth Rates
        ('eps_growth_ttm', 'DECIMAL'),
        ('revenue_growth', 'DECIMAL'),
        ('net_income_growth', 'DECIMAL'),
        ('ebitda_growth', 'DECIMAL'),
        ('gross_profit_growth', 'DECIMAL'),
        ('free_cash_flow_growth', 'DECIMAL'),
        ('capex_growth', 'DECIMAL'),
        ('total_assets_growth', 'DECIMAL'),
        
        # Dividends
        ('dividend_yield_indicated', 'DECIMAL'),
        ('dividend_payout_ratio', 'DECIMAL'),
        ('dividends_per_share', 'DECIMAL'),
        ('dividends_paid_annual', 'DECIMAL'),
        ('continuous_dividend_growth', 'INTEGER'),
        
        # Valuation & Performance
        ('forward_pe', 'DECIMAL'),
        ('target_price_1y', 'DECIMAL'),
        ('target_performance_1y', 'DECIMAL'),
        ('performance_ytd', 'DECIMAL'),
        
        # Trading Metrics
        ('beta_5_years', 'DECIMAL'),
        ('average_volume_10d', 'BIGINT'),
        ('relative_volume', 'DECIMAL'),
        ('volatility_1_day', 'DECIMAL'),
        ('volume_change', 'DECIMAL'),
        ('turnover_1_day', 'DECIMAL'),
        
        # Dates
        ('recent_earnings_date', 'DATE'),
        ('upcoming_earnings_date', 'DATE'),
    ]
    
    # Generate SQL statements
    sql_statements = []
    sql_statements.append("-- Enhanced EGX Stock AI Oracle - Financial Data Schema Update")
    sql_statements.append("-- Execute this script in Supabase SQL editor")
    sql_statements.append("")
    
    for column_name, column_type in missing_columns:
        sql = f"ALTER TABLE stocks_financials ADD COLUMN IF NOT EXISTS {column_name} {column_type};"
        sql_statements.append(sql)
        logger.info(f"Adding column: {column_name} ({column_type})")
    
    sql_statements.append("")
    sql_statements.append("-- Create index on symbol for better performance")
    sql_statements.append("CREATE INDEX IF NOT EXISTS idx_stocks_financials_symbol ON stocks_financials(symbol);")
    sql_statements.append("")
    sql_statements.append("-- Update RLS policies")
    sql_statements.append("ALTER TABLE stocks_financials ENABLE ROW LEVEL SECURITY;")
    sql_statements.append("DROP POLICY IF EXISTS \"Enable read access for all users\" ON stocks_financials;")
    sql_statements.append("CREATE POLICY \"Enable read access for all users\" ON stocks_financials FOR SELECT USING (true);")
    
    return "\n".join(sql_statements)

def main():
    """Main function"""
    logger.info("🚀 Starting Financial Schema Update...")
    logger.info("=" * 60)
    
    try:
        # Create SQL script
        sql_content = create_financials_schema_sql()
        
        # Save SQL to file
        sql_file = "financials_schema_update.sql"
        with open(sql_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        logger.info(f"💾 SQL script saved to: {sql_file}")
        
        # Test connection
        logger.info("🔍 Testing database connection...")
        result = supabase.table('stocks_financials').select('symbol').limit(1).execute()
        logger.info(f"✅ Connection successful! Found {len(result.data)} record(s)")
        
        logger.info("=" * 60)
        logger.info("📋 Financial Schema Update Summary:")
        logger.info("   New columns to add: 69")
        logger.info("   SQL script: financials_schema_update.sql")
        logger.info("")
        logger.info("💡 Next Steps:")
        logger.info("1. Execute 'financials_schema_update.sql' in Supabase SQL editor")
        logger.info("2. Re-run financial data import:")
        logger.info("   python load_financials_enhanced.py")
        logger.info("3. Verify with: python test_enhanced_system.py")
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
