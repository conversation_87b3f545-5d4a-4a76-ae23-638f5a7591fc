
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Link, Wifi, WifiOff, Settings, Shield, AlertTriangle, Loader2 } from 'lucide-react';
import { useAuthUser } from "@/hooks/useAuthUser";
import { toast } from "@/components/ui/use-toast";

interface BrokerConfig {
  id: string;
  name: string;
  status: 'connected' | 'disconnected' | 'error';
  apiKey: string;
  permissions: string[];
  lastSync: string;
}

const BrokerIntegration = () => {
  const { user, loading: authLoading } = useAuthUser();
  const [brokers, setBrokers] = useState<BrokerConfig[]>([
    {
      id: '1',
      name: 'الأهلي كابيتال',
      status: 'connected',
      apiKey: 'محفوظ',
      permissions: ['read_portfolio', 'place_orders'],
      lastSync: '2024-01-15 15:30'
    },
    {
      id: '2',
      name: 'بنك مصر للاستثمار',
      status: 'disconnected',
      apiKey: '',
      permissions: [],
      lastSync: 'غير متاح'
    }
  ]);
  const [newBroker, setNewBroker] = useState({
    name: '',
    apiKey: '',
    secretKey: ''
  });
  const [selectedBroker, setSelectedBroker] = useState('');
  const [loading, setLoading] = useState(false);

  const supportedBrokers = [
    'الأهلي كابيتال',
    'بنك مصر للاستثمار',
    'سيتي كابيتال',
    'فاروس',
    'هيرميس'
  ];
  useEffect(() => {
    // Check if credentials stored for selected broker after component mounts / user selects
    if (!user?.id || !selectedBroker) return;
    setLoading(true);
    fetch(
      `${import.meta.env.VITE_SUPABASE_URL || "https://tbzbrujqjwpatbzffmwq.supabase.co"}/functions/v1/broker-credentials?broker=${encodeURIComponent(selectedBroker)}`,
      {
        headers: {
          "Authorization": `Bearer ${localStorage.getItem("sb-access-token")}`,
        },
      }
    )
      .then(res => res.json())
      .then(data => {
        setNewBroker(b => ({
          ...b,
          apiKey: "",
          secretKey: "",
        }));
        if (data.hasCredentials) {
          toast({ title: "المفاتيح محفوظة بأمان للسيرفر!", description: "المفتاح موجود على السيرفر" });
        }
      })
      .finally(() => setLoading(false));
  }, [selectedBroker, user?.id]);

  const connectBroker = async () => {
    if (!selectedBroker || !newBroker.apiKey || !newBroker.secretKey) {
      toast({ title: "الرجاء تعبئة جميع الحقول" });
      return;
    }
    setLoading(true);
    fetch(
      `${import.meta.env.VITE_SUPABASE_URL || "https://tbzbrujqjwpatbzffmwq.supabase.co"}/functions/v1/broker-credentials`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("sb-access-token")}`,
        },
        body: JSON.stringify({
          broker: selectedBroker,
          apiKey: newBroker.apiKey,
          secretKey: newBroker.secretKey,
        }),
      }
    )
      .then(res => res.json())
      .then(data => {
        if (data.ok) {
          toast({ title: "تم حفظ مفاتيح الوسيط بأمان 🔒", description: "المفتاح محفوظ في السيرفر فقط." });
          setBrokers([...brokers, {
            id: Date.now().toString(),
            name: selectedBroker,
            status: "connected",
            apiKey: "محفوظ في السيرفر",
            permissions: ["read_portfolio"],
            lastSync: new Date().toLocaleString('ar-EG'),
          }]);
          setNewBroker({ name: '', apiKey: '', secretKey: '' });
        } else {
          toast({ title: "خطأ", description: data.error || "تعذر الحفظ" });
        }
      })
      .catch(() => toast({ title: "خطأ غير متوقع عند الحفظ" }))
      .finally(() => setLoading(false));
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <Wifi className="h-4 w-4 text-green-600" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <WifiOff className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'connected': return 'متصل';
      case 'error': return 'خطأ';
      default: return 'غير متصل';
    }
  };

  return (
    <Card className="border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-amber-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <Link className="h-5 w-5" />
          ربط الوسطاء
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add New Broker Connection */}
        <div className="p-4 bg-white/60 rounded-lg space-y-4">
          <h3 className="font-medium">إضافة وسيط جديد (مفتاحك يبقى في السيرفر فقط!)</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select value={selectedBroker} onValueChange={setSelectedBroker}>
              <SelectTrigger>
                <SelectValue placeholder="اختر الوسيط" />
              </SelectTrigger>
              <SelectContent>
                {supportedBrokers.map(broker => (
                  <SelectItem key={broker} value={broker}>
                    {broker}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="password"
              placeholder="API Key"
              value={newBroker.apiKey}
              onChange={(e) => setNewBroker({ ...newBroker, apiKey: e.target.value })}
              disabled={loading}
            />
            <Input
              type="password"
              placeholder="Secret Key"
              value={newBroker.secretKey}
              onChange={(e) => setNewBroker({ ...newBroker, secretKey: e.target.value })}
              disabled={loading}
            />
            <Button onClick={connectBroker} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Link className="h-4 w-4 mr-2" />}
              ربط آمن
            </Button>
          </div>

          <div className="p-3 bg-blue-50 rounded text-sm">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-blue-600" />
              <span className="font-medium">أمان البيانات</span>
            </div>
            <p className="text-blue-700">
              جميع بيانات API محمية بالكامل في السيرفر باستخدام تقنيات Vault.
              المفاتيح لا تحفظ أبداً محلياً أو في المتصفح.
            </p>
          </div>
        </div>

        {/* Connected Brokers */}
        <div>
          <h3 className="text-lg font-semibold mb-4">الوسطاء المتصلين</h3>
          <div className="space-y-4">
            {brokers.map(broker => (
              <div key={broker.id} className="p-4 bg-white/80 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(broker.status)}
                      <div>
                        <div className="font-medium">{broker.name}</div>
                        <div className="text-sm text-gray-600">API: {broker.apiKey}</div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <div className="text-xs text-gray-500">آخر مزامنة</div>
                      <div className="text-sm">{broker.lastSync}</div>
                    </div>
                    <Badge className={getStatusColor(broker.status)}>
                      {getStatusLabel(broker.status)}
                    </Badge>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Settings className="h-4 w-4 mr-1" />
                        إعدادات
                      </Button>
                      {broker.status === 'connected' && (
                        <Button size="sm" variant="outline">
                          مزامنة
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
                {broker.permissions.length > 0 && (
                  <div className="mt-3">
                    <div className="text-sm font-medium mb-2">الصلاحيات:</div>
                    <div className="flex gap-2">
                      {broker.permissions.map(permission => (
                        <Badge key={permission} variant="outline">
                          {permission === 'read_portfolio' ? 'قراءة المحفظة' : 
                            permission === 'place_orders' ? 'وضع الأوامر' : permission}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Available Features */}
        <div>
          <h3 className="text-lg font-semibold mb-4">الميزات المتاحة</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-green-50">
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">مزامنة تلقائية للمحفظة</h4>
                <p className="text-sm text-gray-600">
                  احصل على تحديثات فورية لأرصدة ومراكز محفظتك
                </p>
              </CardContent>
            </Card>

            <Card className="bg-blue-50">
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">تنفيذ الأوامر المباشر</h4>
                <p className="text-sm text-gray-600">
                  نفذ أوامر الشراء والبيع مباشرة من التطبيق
                </p>
              </CardContent>
            </Card>

            <Card className="bg-purple-50">
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">تتبع الأداء الفوري</h4>
                <p className="text-sm text-gray-600">
                  راقب أداء استثماراتك في الوقت الفعلي
                </p>
              </CardContent>
            </Card>

            <Card className="bg-yellow-50">
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">تنبيهات ذكية</h4>
                <p className="text-sm text-gray-600">
                  احصل على تنبيهات عند تحقق شروط معينة
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
export default BrokerIntegration;
