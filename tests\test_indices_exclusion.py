#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_stocks_exclusion():
    """Test that market indices are excluded from stock lists"""
    print("🧪 اختبار استبعاد المؤشرات من قوائم الأسهم")
    print("=" * 60)
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}"
    }
    
    tests = [
        {
            "name": "شريط الأسعار (Stock Ticker)",
            "query": "stocks_realtime?select=symbol,current_price,change_amount,change_percent,volume&symbol=not.like.*EGX*&order=volume.desc&limit=10"
        },
        {
            "name": "أفضل الأسهم أداءً (Top Gainers)",
            "query": "stocks_realtime?select=symbol,change_percent&symbol=not.like.*EGX*&change_percent=gt.0&order=change_percent.desc&limit=3"
        },
        {
            "name": "الأكثر تداولاً (Top Volume)",
            "query": "stocks_realtime?select=symbol,volume&symbol=not.like.*EGX*&order=volume.desc&limit=3"
        },
        {
            "name": "الفرص الاستثمارية (High Volatility)",
            "query": "stocks_realtime?select=symbol,change_percent,volume&symbol=not.like.*EGX*&or=(change_percent.gte.2,change_percent.lte.-2)&order=volume.desc&limit=3"
        },
        {
            "name": "فحص الأسهم (Stock Screener)",
            "query": "stocks_realtime?select=symbol,current_price,change_amount,change_percent,volume,market_cap&symbol=not.like.*EGX*&limit=5"
        }
    ]
    
    for test in tests:
        print(f"\n🔍 اختبار: {test['name']}")
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/{test['query']}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if any EGX indices are included
                egx_symbols = [item for item in data if 'EGX' in item['symbol']]
                non_egx_symbols = [item for item in data if 'EGX' not in item['symbol']]
                
                if egx_symbols:
                    print(f"   ❌ يحتوي على مؤشرات: {[s['symbol'] for s in egx_symbols]}")
                else:
                    print(f"   ✅ خالي من المؤشرات ({len(non_egx_symbols)} أسهم)")
                    if non_egx_symbols and len(non_egx_symbols) <= 5:
                        sample_symbols = [s['symbol'] for s in non_egx_symbols[:3]]
                        print(f"      عينة: {', '.join(sample_symbols)}")
                    
            else:
                print(f"   ❌ خطأ في الاستعلام: {response.status_code}")
                print(f"      {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✅ اكتمل الاختبار!")
    print("🔄 قم بتحديث الفرونت اند لرؤية التغييرات")

def show_current_indices():
    """Show what indices are currently in the database"""
    print("\n📊 المؤشرات الموجودة في قاعدة البيانات:")
    
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw"
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/stocks_realtime?select=symbol,current_price&symbol=like.*EGX*",
            headers={
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}"
            }
        )
        
        if response.status_code == 200:
            indices = response.json()
            print(f"   إجمالي المؤشرات: {len(indices)}")
            for idx in indices:
                print(f"   📈 {idx['symbol']}: {idx['current_price']}")
        else:
            print(f"   ❌ خطأ: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")

if __name__ == "__main__":
    show_current_indices()
    test_stocks_exclusion()
