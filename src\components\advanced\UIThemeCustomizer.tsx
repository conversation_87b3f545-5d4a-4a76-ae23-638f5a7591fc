import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Moon, 
  Monitor, 
  Brush,
  Eye,
  Sparkles,
  Save
} from 'lucide-react';

interface ThemeCustomization {
  primaryColor: string;
  accentColor: string;
  borderRadius: number;
  fontSize: number;
  spacing: number;
  animations: boolean;
  glassMorphism: boolean;
}

const UIThemeCustomizer = () => {
  const [theme, setTheme] = useState<ThemeCustomization>({
    primaryColor: '#3b82f6',
    accentColor: '#f59e0b',
    borderRadius: 8,
    fontSize: 16,
    spacing: 16,
    animations: true,
    glassMorphism: false
  });

  const [previewMode, setPreviewMode] = useState(false);

  const colorPresets = [
    { name: 'الافتراضي', primary: '#3b82f6', accent: '#f59e0b' },
    { name: 'احترافي', primary: '#1f2937', accent: '#10b981' },
    { name: 'أنيق', primary: '#7c3aed', accent: '#ec4899' },
    { name: 'طبيعي', primary: '#059669', accent: '#f97316' },
    { name: 'دافئ', primary: '#dc2626', accent: '#fbbf24' }
  ];

  const applyTheme = useCallback(() => {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    root.style.setProperty('--primary-hue', theme.primaryColor);
    root.style.setProperty('--accent-hue', theme.accentColor);
    root.style.setProperty('--border-radius', `${theme.borderRadius}px`);
    root.style.setProperty('--font-size-base', `${theme.fontSize}px`);
    root.style.setProperty('--spacing-base', `${theme.spacing}px`);
    
    // Apply body classes
    document.body.classList.toggle('animations-disabled', !theme.animations);
    document.body.classList.toggle('glass-morphism', theme.glassMorphism);
  }, [theme]);

  useEffect(() => {
    if (previewMode) {
      applyTheme();
    }
  }, [theme, previewMode, applyTheme]);

  const saveTheme = () => {
    localStorage.setItem('custom-theme', JSON.stringify(theme));
    applyTheme();
    setPreviewMode(false);
  };

  const resetTheme = () => {
    setTheme({
      primaryColor: '#3b82f6',
      accentColor: '#f59e0b',
      borderRadius: 8,
      fontSize: 16,
      spacing: 16,
      animations: true,
      glassMorphism: false
    });
  };

  return (
    <div className="space-y-6">
      {/* Theme Preview Toggle */}
      <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-purple-800">
              <Palette className="h-5 w-5" />
              تخصيص المظهر والواجهة
            </div>
            <div className="flex gap-2">
              <Button
                variant={previewMode ? "default" : "outline"}
                size="sm"
                onClick={() => setPreviewMode(!previewMode)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {previewMode ? 'إيقاف المعاينة' : 'معاينة مباشرة'}
              </Button>
              {previewMode && (
                <Button size="sm" onClick={saveTheme}>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Color Customization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic">
              <Brush className="h-5 w-5" />
              الألوان
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Color Presets */}
            <div>
              <label className="text-sm font-medium mb-3 block arabic">الإعدادات المسبقة</label>
              <div className="grid grid-cols-2 gap-2">
                {colorPresets.map(preset => (
                  <Button
                    key={preset.name}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2 h-auto p-3"
                    onClick={() => setTheme(prev => ({
                      ...prev,
                      primaryColor: preset.primary,
                      accentColor: preset.accent
                    }))}
                  >
                    <div className="flex gap-1">
                      <div 
                        className="w-3 h-3 rounded-full border"
                        style={{ backgroundColor: preset.primary }}
                      />
                      <div 
                        className="w-3 h-3 rounded-full border"
                        style={{ backgroundColor: preset.accent }}
                      />
                    </div>
                    <span className="arabic">{preset.name}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Custom Colors */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block arabic">اللون الأساسي</label>
                <div className="flex items-center gap-3">
                  <input
                    type="color"
                    value={theme.primaryColor}
                    onChange={(e) => setTheme(prev => ({ ...prev, primaryColor: e.target.value }))}
                    className="w-12 h-8 rounded border"
                    title="اختر اللون الأساسي"
                  />
                  <span className="text-sm text-muted-foreground">{theme.primaryColor}</span>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block arabic">لون التمييز</label>
                <div className="flex items-center gap-3">
                  <input
                    type="color"
                    value={theme.accentColor}
                    onChange={(e) => setTheme(prev => ({ ...prev, accentColor: e.target.value }))}
                    className="w-12 h-8 rounded border"
                    title="اختر لون التمييز"
                  />
                  <span className="text-sm text-muted-foreground">{theme.accentColor}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Layout & Typography */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic">التخطيط والخطوط</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="text-sm font-medium mb-3 block arabic">
                انحناء الحواف: {theme.borderRadius}px
              </label>
              <Slider
                value={[theme.borderRadius]}
                onValueChange={([value]) => setTheme(prev => ({ ...prev, borderRadius: value }))}
                max={20}
                min={0}
                step={1}
                className="w-full"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-3 block arabic">
                حجم الخط: {theme.fontSize}px
              </label>
              <Slider
                value={[theme.fontSize]}
                onValueChange={([value]) => setTheme(prev => ({ ...prev, fontSize: value }))}
                max={20}
                min={12}
                step={1}
                className="w-full"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-3 block arabic">
                المسافات: {theme.spacing}px
              </label>
              <Slider
                value={[theme.spacing]}
                onValueChange={([value]) => setTheme(prev => ({ ...prev, spacing: value }))}
                max={24}
                min={8}
                step={2}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Effects & Animations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 arabic">
            <Sparkles className="h-5 w-5" />
            التأثيرات والحركات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium arabic">الحركات والانتقالات</h4>
                <p className="text-sm text-muted-foreground arabic">تأثيرات حركية سلسة</p>
              </div>
              <Button
                variant={theme.animations ? "default" : "outline"}
                size="sm"
                onClick={() => setTheme(prev => ({ ...prev, animations: !prev.animations }))}
              >
                {theme.animations ? 'مفعل' : 'معطل'}
              </Button>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium arabic">تأثير الزجاج</h4>
                <p className="text-sm text-muted-foreground arabic">خلفيات شفافة وضبابية</p>
              </div>
              <Button
                variant={theme.glassMorphism ? "default" : "outline"}
                size="sm"
                onClick={() => setTheme(prev => ({ ...prev, glassMorphism: !prev.glassMorphism }))}
              >
                {theme.glassMorphism ? 'مفعل' : 'معطل'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center gap-4">
            <Button onClick={resetTheme} variant="outline">
              استعادة الافتراضي
            </Button>
            <Button onClick={saveTheme} disabled={!previewMode}>
              <Save className="h-4 w-4 mr-2" />
              حفظ التخصيص
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UIThemeCustomizer;
