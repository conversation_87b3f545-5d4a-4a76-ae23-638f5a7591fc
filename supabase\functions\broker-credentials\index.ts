
// Edge Function: Handle broker credentials for authenticated users securely
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.50.0";
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};
const SUPABASE_URL = Deno.env.get("SUPABASE_URL")!;
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY")!;

// Store credentials in Supabase secrets/vault
async function setBrokerCredentials(userId: string, broker: string, creds: { apiKey: string; secretKey: string }) {
  // Compose key unique to user+broker
  const key = `broker_${broker}_${userId}`;
  // Note: use Deno KV for "vault" style, or you can use a secure table. For demonstration:
  await Deno.env.set(key + "_API", creds.apiKey);
  await Deno.env.set(key + "_SECRET", creds.secretKey);
  return true;
}
async function getBrokerCredentials(userId: string, broker: string) {
  const key = `broker_${broker}_${userId}`;
  const apiKey = Deno.env.get(key + "_API");
  const secretKey = Deno.env.get(key + "_SECRET");
  return apiKey && secretKey ? { apiKey, secretKey } : null;
}

serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === "OPTIONS") return new Response(null, { headers: corsHeaders });
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, { global: { headers: { Authorization: req.headers.get("Authorization")! } } });
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return new Response(JSON.stringify({ error: "Auth required" }), { status: 401, headers: corsHeaders });

  if (req.method === "POST") {
    // Save credentials
    const { broker, apiKey, secretKey } = await req.json();
    if (!broker || !apiKey || !secretKey) {
      return new Response(JSON.stringify({ error: "All fields required" }), { status: 400, headers: corsHeaders });
    }
    await setBrokerCredentials(user.id, broker, { apiKey, secretKey });
    return new Response(JSON.stringify({ ok: true }), { headers: corsHeaders });
  }
  if (req.method === "GET") {
    // Get (list) user credentials -- NEVER send secret values!
    const url = new URL(req.url);
    const broker = url.searchParams.get("broker");
    if (!broker) return new Response(JSON.stringify({ error: "broker required" }), { status: 400, headers: corsHeaders });
    const creds = await getBrokerCredentials(user.id, broker);
    return new Response(JSON.stringify({ apiKey: creds?.apiKey ? "stored" : null, hasCredentials: !!creds }), { headers: corsHeaders });
  }
  return new Response("Method not allowed", { status: 405, headers: corsHeaders });
});
