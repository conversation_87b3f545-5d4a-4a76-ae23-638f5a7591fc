import React from 'react';
import { SIGNAL_TYPES, formatSignalPrice, formatSignalTime, formatPnL } from '../../hooks/useTradingSignals';

// Signal Card Component for displaying individual signals
const SignalCard = ({ signal, onAction, onDismiss, compact = false }) => {
  const signalType = SIGNAL_TYPES[signal.signal_type] || SIGNAL_TYPES.buy;
  
  // Calculate time elapsed
  const timeElapsed = signal.created_at ? 
    Math.floor((new Date() - new Date(signal.created_at)) / (1000 * 60)) : 0;
  
  return (
    <div className={`signal-card rounded-lg border-2 ${signalType.borderColor} ${signalType.bgColor} p-4 mb-3 hover:shadow-lg transition-all duration-200`}>
      {/* Signal Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <span className="text-2xl">{signalType.icon}</span>
          <div>
            <h4 className="font-bold text-lg text-gray-900">{signal.stock_code}</h4>
            <span className={`inline-block px-2 py-1 rounded text-sm font-medium ${signalType.bgColor} ${signalType.textColor}`}>
              {signalType.label}
            </span>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-gray-600">
            منذ {timeElapsed} دقيقة
          </div>
          <div className="text-xs text-gray-500">
            {formatSignalTime(signal.created_at)}
          </div>
        </div>
      </div>
      
      {/* Signal Details */}
      <div className="signal-details space-y-2">
        {signal.buy_price && (
          <div className="flex justify-between items-center">
            <span className="text-gray-700">سعر الدخول:</span>
            <span className="font-semibold text-blue-600">
              {formatSignalPrice(signal.buy_price)}
            </span>
          </div>
        )}
        
        {signal.sell_price && (
          <div className="flex justify-between items-center">
            <span className="text-gray-700">سعر البيع:</span>
            <span className="font-semibold text-red-600">
              {formatSignalPrice(signal.sell_price)}
            </span>
          </div>
        )}
        
        {/* Target Prices */}
        {(signal.tp1 || signal.tp2 || signal.tp3) && (
          <div className="targets-section mt-3">
            <div className="text-sm text-gray-600 mb-2">الأهداف:</div>
            <div className="grid grid-cols-3 gap-2">
              {signal.tp1 && (
                <div className="text-center">
                  <div className="text-xs text-gray-500">هدف 1</div>
                  <div className="font-semibold text-green-600">
                    {formatSignalPrice(signal.tp1)}
                  </div>
                </div>
              )}
              {signal.tp2 && (
                <div className="text-center">
                  <div className="text-xs text-gray-500">هدف 2</div>
                  <div className="font-semibold text-green-600">
                    {formatSignalPrice(signal.tp2)}
                  </div>
                </div>
              )}
              {signal.tp3 && (
                <div className="text-center">
                  <div className="text-xs text-gray-500">هدف 3</div>
                  <div className="font-semibold text-green-600">
                    {formatSignalPrice(signal.tp3)}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Stop Loss */}
        {signal.stop_loss && (
          <div className="flex justify-between items-center">
            <span className="text-gray-700">وقف الخسارة:</span>
            <span className="font-semibold text-red-600">
              {formatSignalPrice(signal.stop_loss)}
            </span>
          </div>
        )}
      </div>
      
      {/* Actions */}
      {!compact && (
        <div className="signal-actions flex space-x-2 rtl:space-x-reverse mt-4">
          <button 
            onClick={() => onAction?.(signal)}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded font-medium transition-colors duration-200"
          >
            عرض التفاصيل
          </button>
          <button 
            onClick={() => onDismiss?.(signal)}
            className="px-4 py-2 border border-gray-300 hover:bg-gray-50 rounded font-medium transition-colors duration-200"
          >
            إخفاء
          </button>
        </div>
      )}
    </div>
  );
};

// Live Recommendation Card Component
const LiveRecommendationCard = ({ recommendation, onUpdate }) => {
  const pnl = formatPnL(recommendation.current_pnl);
  const isProfit = recommendation.current_pnl > 0;
  
  // Calculate progress to targets
  const calculateProgress = (entry, current, target) => {
    if (!entry || !current || !target) return 0;
    const progress = ((current - entry) / (target - entry)) * 100;
    return Math.max(0, Math.min(100, progress));
  };
  
  return (
    <div className="live-recommendation-card bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <h4 className="font-bold text-lg text-gray-900">{recommendation.stock_code}</h4>
          <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium">
            {recommendation.action === 'buy' ? 'شراء' : 'بيع'}
          </span>
        </div>
        
        <div className="text-right">
          {recommendation.current_pnl !== null && (
            <div className={`text-lg font-bold ${pnl.color}`}>
              {pnl.text}
            </div>
          )}
          <div className="text-xs text-gray-500">
            {formatSignalTime(recommendation.created_at)}
          </div>
        </div>
      </div>
      
      {/* Price Information */}
      <div className="price-info grid grid-cols-2 gap-4 mb-3">
        <div>
          <div className="text-xs text-gray-500">سعر الدخول</div>
          <div className="font-semibold text-blue-600">
            {formatSignalPrice(recommendation.entry_price)}
          </div>
        </div>
        <div>
          <div className="text-xs text-gray-500">السعر الحالي</div>
          <div className="font-semibold text-gray-900">
            {formatSignalPrice(recommendation.current_price) || 'جاري التحديث...'}
          </div>
        </div>
      </div>
      
      {/* Targets Progress */}
      <div className="targets-progress space-y-2">
        {recommendation.tp1 && (
          <div className="target-item">
            <div className="flex justify-between text-xs mb-1">
              <span>الهدف الأول</span>
              <span className={recommendation.tp1_hit ? 'text-green-600 font-bold' : 'text-gray-600'}>
                {formatSignalPrice(recommendation.tp1)}
                {recommendation.tp1_hit && ' ✅'}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  recommendation.tp1_hit ? 'bg-green-500' : 'bg-blue-500'
                }`}
                style={{ 
                  width: `${recommendation.tp1_hit ? 100 : 
                    calculateProgress(recommendation.entry_price, recommendation.current_price, recommendation.tp1)}%` 
                }}
              ></div>
            </div>
          </div>
        )}
        
        {recommendation.tp2 && (
          <div className="target-item">
            <div className="flex justify-between text-xs mb-1">
              <span>الهدف الثاني</span>
              <span className={recommendation.tp2_hit ? 'text-green-600 font-bold' : 'text-gray-600'}>
                {formatSignalPrice(recommendation.tp2)}
                {recommendation.tp2_hit && ' ✅'}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  recommendation.tp2_hit ? 'bg-green-500' : 'bg-blue-500'
                }`}
                style={{ 
                  width: `${recommendation.tp2_hit ? 100 : 
                    calculateProgress(recommendation.entry_price, recommendation.current_price, recommendation.tp2)}%` 
                }}
              ></div>
            </div>
          </div>
        )}
        
        {recommendation.tp3 && (
          <div className="target-item">
            <div className="flex justify-between text-xs mb-1">
              <span>الهدف الثالث</span>
              <span className={recommendation.tp3_hit ? 'text-green-600 font-bold' : 'text-gray-600'}>
                {formatSignalPrice(recommendation.tp3)}
                {recommendation.tp3_hit && ' ✅'}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  recommendation.tp3_hit ? 'bg-green-500' : 'bg-blue-500'
                }`}
                style={{ 
                  width: `${recommendation.tp3_hit ? 100 : 
                    calculateProgress(recommendation.entry_price, recommendation.current_price, recommendation.tp3)}%` 
                }}
              ></div>
            </div>
          </div>
        )}
      </div>
      
      {/* Stop Loss */}
      {recommendation.stop_loss && (
        <div className="stop-loss mt-3 p-2 bg-red-50 rounded border-l-4 border-red-500">
          <div className="flex justify-between items-center">
            <span className="text-red-700 text-sm">وقف الخسارة:</span>
            <span className="font-semibold text-red-600">
              {formatSignalPrice(recommendation.stop_loss)}
            </span>
          </div>
        </div>
      )}
      
      {/* Risk/Reward Ratio */}
      {recommendation.risk_reward_ratio && (
        <div className="mt-3 text-center">
          <div className="text-xs text-gray-500">نسبة المخاطرة/العائد</div>
          <div className="font-semibold text-blue-600">
            1:{recommendation.risk_reward_ratio.toFixed(2)}
          </div>
        </div>
      )}
    </div>
  );
};

// Signal Statistics Component
const SignalStatistics = ({ signals = [], performance = null }) => {
  const totalSignals = signals.length;
  const buySignals = signals.filter(s => s.signal_type === 'buy').length;
  const sellSignals = signals.filter(s => s.signal_type === 'sell').length;
  const tpSignals = signals.filter(s => s.signal_type.includes('tp')).length;
  
  return (
    <div className="signal-statistics bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mt-4">
      <h3 className="text-lg font-bold text-gray-900 mb-4 text-center">
        إحصائيات الإشارات
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="stat-item text-center">
          <div className="text-2xl font-bold text-blue-600">{totalSignals}</div>
          <div className="text-sm text-gray-600">إجمالي الإشارات</div>
        </div>
        
        <div className="stat-item text-center">
          <div className="text-2xl font-bold text-green-600">{buySignals}</div>
          <div className="text-sm text-gray-600">إشارات شراء</div>
        </div>
        
        <div className="stat-item text-center">
          <div className="text-2xl font-bold text-red-600">{sellSignals}</div>
          <div className="text-sm text-gray-600">إشارات بيع</div>
        </div>
        
        <div className="stat-item text-center">
          <div className="text-2xl font-bold text-purple-600">{tpSignals}</div>
          <div className="text-sm text-gray-600">تحقق أهداف</div>
        </div>
      </div>
      
      {performance && (
        <div className="performance-section mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="stat-item text-center">
              <div className="text-xl font-bold text-green-600">
                {performance.win_rate?.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">معدل النجاح</div>
            </div>
            
            <div className="stat-item text-center">
              <div className="text-xl font-bold text-blue-600">
                {performance.avg_return?.toFixed(2)}%
              </div>
              <div className="text-sm text-gray-600">متوسط العائد</div>
            </div>
            
            <div className="stat-item text-center">
              <div className="text-xl font-bold text-purple-600">
                {performance.total_signals || 0}
              </div>
              <div className="text-sm text-gray-600">إشارات مُغلقة</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Filter Component
const SignalFilters = ({ filters, onChange, onReset }) => {
  const handleFilterChange = (key, value) => {
    onChange({
      ...filters,
      [key]: value
    });
  };
  
  return (
    <div className="signal-filters bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900">فلترة الإشارات</h3>
        <button 
          onClick={onReset}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          إعادة تعيين
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نوع الإشارة
          </label>
          <select 
            value={filters.signalType || 'all'}
            onChange={(e) => handleFilterChange('signalType', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">جميع الإشارات</option>
            <option value="buy">إشارات شراء</option>
            <option value="sell">إشارات بيع</option>
            <option value="tp1done">تحقق الهدف الأول</option>
            <option value="tp2done">تحقق الهدف الثاني</option>
            <option value="tp3done">تحقق الهدف الثالث</option>
            <option value="tsl">وقف خسارة متحرك</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            الإطار الزمني
          </label>
          <select 
            value={filters.timeframe || '1d'}
            onChange={(e) => handleFilterChange('timeframe', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="1h">آخر ساعة</option>
            <option value="4h">آخر 4 ساعات</option>
            <option value="1d">اليوم</option>
            <option value="3d">آخر 3 أيام</option>
            <option value="1w">الأسبوع</option>
            <option value="1m">الشهر</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            مستوى المخاطرة
          </label>
          <select 
            value={filters.riskLevel || 'all'}
            onChange={(e) => handleFilterChange('riskLevel', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">جميع المستويات</option>
            <option value="low">مخاطرة منخفضة</option>
            <option value="medium">مخاطرة متوسطة</option>
            <option value="high">مخاطرة عالية</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export { SignalCard, LiveRecommendationCard, SignalStatistics, SignalFilters };
