#!/usr/bin/env python3
"""
EGX Stock AI Oracle - Data Sync Setup Script
Sets up the data synchronization environment
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def install_requirements():
    """Install required Python packages"""
    logger.info("Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Successfully installed all requirements")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    logger.info("Creating directories...")
    
    directories = [
        "logs",
        "reports",
        "backups"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def setup_env_file():
    """Set up environment file if it doesn't exist"""
    logger.info("Setting up environment file...")
    
    if os.path.exists(".env"):
        logger.info(".env file already exists")
        return
    
    if os.path.exists(".env.example"):
        # Copy example to .env
        with open(".env.example", "r") as src, open(".env", "w") as dst:
            dst.write(src.read())
        logger.info("Created .env file from .env.example")
        logger.warning("Please update .env file with your actual Supabase credentials")
    else:
        logger.error(".env.example file not found")

def validate_environment():
    """Validate environment setup"""
    logger.info("Validating environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    logger.info(f"Python version: {sys.version}")
    
    # Check if .env exists
    if not os.path.exists(".env"):
        logger.error(".env file not found. Please create it from .env.example")
        return False
    
    # Try importing required modules
    required_modules = ["pandas", "supabase", "tqdm", "schedule", "openpyxl"]
    
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✓ {module} imported successfully")
        except ImportError as e:
            logger.error(f"✗ Failed to import {module}: {e}")
            return False
    
    logger.info("Environment validation passed")
    return True

def create_startup_scripts():
    """Create platform-specific startup scripts"""
    logger.info("Creating startup scripts...")
    
    # Windows batch script
    windows_script = """@echo off
echo EGX Stock AI Oracle - Data Sync Scheduler
echo Starting data synchronization...

cd /d "%~dp0"
python scheduler.py

pause
"""
    
    with open("start_scheduler.bat", "w") as f:
        f.write(windows_script)
    logger.info("Created start_scheduler.bat")
    
    # Linux/Mac shell script
    unix_script = """#!/bin/bash
echo "EGX Stock AI Oracle - Data Sync Scheduler"
echo "Starting data synchronization..."

cd "$(dirname "$0")"
python3 scheduler.py
"""
    
    with open("start_scheduler.sh", "w") as f:
        f.write(unix_script)
    
    # Make shell script executable
    try:
        os.chmod("start_scheduler.sh", 0o755)
        logger.info("Created start_scheduler.sh")
    except OSError:
        logger.warning("Could not set executable permissions on start_scheduler.sh")

def display_next_steps():
    """Display next steps for user"""
    next_steps = """
╔═══════════════════════════════════════════════════════════════╗
║                        SETUP COMPLETE!                        ║
╠═══════════════════════════════════════════════════════════════╣
║                                                               ║
║  Next Steps:                                                  ║
║                                                               ║
║  1. Edit .env file with your Supabase credentials:           ║
║     - SUPABASE_SERVICE_ROLE_KEY                              ║
║     - Update data file paths if needed                       ║
║                                                               ║
║  2. Verify data file locations:                              ║
║     - Historical: /mnt/c/.../stocks/meta2/*.TXT              ║
║     - Real-time:  /mnt/c/.../stocks/stock_synco.xlsx         ║
║     - Financial:  /mnt/c/.../stocks/financial_data.csv       ║
║                                                               ║
║  3. Test individual scripts:                                 ║
║     python load_realtime.py                                  ║
║     python validate_data.py                                  ║
║                                                               ║
║  4. Start the scheduler:                                     ║
║     python scheduler.py                                      ║
║     OR double-click start_scheduler.bat (Windows)            ║
║     OR ./start_scheduler.sh (Linux/Mac)                      ║
║                                                               ║
║  5. Monitor logs in:                                         ║
║     - data_sync_scheduler.log                                ║
║     - realtime_data_sync.log                                 ║
║     - historical_data_sync.log                               ║
║     - financial_data_sync.log                                ║
║                                                               ║
║  For support, check the README.md file                      ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
"""
    print(next_steps)

def main():
    logger.info("EGX Stock AI Oracle - Data Sync Setup")
    logger.info("=" * 50)
    
    # Create directories
    create_directories()
    
    # Setup environment file
    setup_env_file()
    
    # Install requirements
    if not install_requirements():
        logger.error("Setup failed: Could not install requirements")
        sys.exit(1)
    
    # Validate environment
    if not validate_environment():
        logger.error("Setup failed: Environment validation failed")
        sys.exit(1)
    
    # Create startup scripts
    create_startup_scripts()
    
    # Display next steps
    display_next_steps()
    
    logger.info("Setup completed successfully!")

if __name__ == "__main__":
    main()
