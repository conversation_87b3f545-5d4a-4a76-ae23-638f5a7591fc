"""
Authentication utilities for the Smart Portfolio system
"""
from typing import Optional, Dict, Any
from fastapi import HTTPEx<PERSON>, status
from pydantic import BaseModel

class CurrentUser(BaseModel):
    """Current user model"""
    id: int
    username: Optional[str] = None
    email: Optional[str] = None
    is_vip: bool = False
    is_active: bool = True

def get_current_user() -> CurrentUser:
    """
    Get current user - Mock implementation for now
    In production, this would validate JWT tokens and return real user data
    """
    # Mock user for development/testing
    return CurrentUser(
        id=1,
        username="test_user",
        email="<EMAIL>", 
        is_vip=True,
        is_active=True
    )

def require_vip_access() -> CurrentUser:
    """
    Require VIP access - Mock implementation for now
    In production, this would check user's VIP status
    """
    user = get_current_user()
    
    if not user.is_vip:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="VIP access required for this feature"
        )
    
    return user

def get_user_context() -> Dict[str, Any]:
    """Get user context for logging and tracking"""
    user = get_current_user()
    return {
        "user_id": user.id,
        "username": user.username,
        "is_vip": user.is_vip
    }
