#!/usr/bin/env python3
"""
Comprehensive Test Script for Enhanced EGX Data Sync System
Tests all enhanced loaders and validates data quality
"""

import os
import sys
import logging
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_system_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    logger.error("Missing required environment variables")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def test_enhanced_data_availability():
    """Test the enhanced data availability and quality"""
    
    logger.info("🔍 Testing Enhanced Data System...")
    
    # Test stocks_realtime enhanced columns
    logger.info("\n📊 Testing Real-time Data Enhancement...")
    try:
        response = supabase.table('stocks_realtime').select('*').limit(5).execute()
        if response.data:
            sample_record = response.data[0]
            enhanced_columns = [
                'ma5', 'ma10', 'ma20', 'ma50', 'ma100', 'ma200',
                'target_1', 'target_2', 'target_3', 'stop_loss',
                'liquidity_inflow', 'liquidity_outflow', 'net_liquidity',
                'eps_annual', 'book_value', 'pe_ratio', 'dividend_yield',
                'sector', 'free_shares', 'speculation_opportunity'
            ]
            
            available_enhanced = []
            for col in enhanced_columns:
                if col in sample_record and sample_record[col] is not None:
                    available_enhanced.append(col)
            
            logger.info(f"✅ Enhanced columns available: {len(available_enhanced)}/{len(enhanced_columns)}")
            logger.info(f"Enhanced columns: {', '.join(available_enhanced[:10])}...")
            
            # Test data quality
            total_response = supabase.table('stocks_realtime').select('symbol', count='exact').execute()
            logger.info(f"📈 Total real-time records: {total_response.count}")
            
            # Test enhanced data penetration
            ma_response = supabase.table('stocks_realtime').select('symbol', count='exact').not_('ma20', 'is', 'null').execute()
            logger.info(f"📊 Records with MA20 data: {ma_response.count}")
            
            target_response = supabase.table('stocks_realtime').select('symbol', count='exact').not_('target_1', 'is', 'null').execute()
            logger.info(f"🎯 Records with target data: {target_response.count}")
            
        else:
            logger.warning("❌ No real-time data found")
            
    except Exception as e:
        logger.error(f"❌ Error testing real-time data: {e}")
    
    # Test stocks_financials enhanced columns
    logger.info("\n💰 Testing Financial Data Enhancement...")
    try:
        response = supabase.table('stocks_financials').select('*').limit(5).execute()
        if response.data:
            sample_record = response.data[0]
            enhanced_financial_columns = [
                'market_capitalization', 'pe_ratio', 'price_to_book', 'dividend_yield',
                'eps_diluted_ttm', 'revenue_growth', 'net_income_growth', 'current_ratio',
                'debt_to_equity', 'free_cash_flow', 'ebitda', 'beta_5_years',
                'analyst_rating', 'target_price_1y', 'sector', 'industry'
            ]
            
            available_financial = []
            for col in enhanced_financial_columns:
                if col in sample_record and sample_record[col] is not None:
                    available_financial.append(col)
            
            logger.info(f"✅ Enhanced financial columns available: {len(available_financial)}/{len(enhanced_financial_columns)}")
            logger.info(f"Financial columns: {', '.join(available_financial[:10])}...")
            
            # Test financial data quality
            total_response = supabase.table('stocks_financials').select('symbol', count='exact').execute()
            logger.info(f"💼 Total financial records: {total_response.count}")
            
            pe_response = supabase.table('stocks_financials').select('symbol', count='exact').not_('pe_ratio', 'is', 'null').execute()
            logger.info(f"📊 Records with P/E ratio: {pe_response.count}")
            
            sector_response = supabase.table('stocks_financials').select('symbol', count='exact').not_('sector', 'is', 'null').execute()
            logger.info(f"🏢 Records with sector data: {sector_response.count}")
            
        else:
            logger.warning("❌ No financial data found")
            
    except Exception as e:
        logger.error(f"❌ Error testing financial data: {e}")
    
    # Test stocks_historical data
    logger.info("\n📈 Testing Historical Data...")
    try:
        total_response = supabase.table('stocks_historical').select('symbol', count='exact').execute()
        logger.info(f"📊 Total historical records: {total_response.count}")
        
        # Get date range
        latest_response = supabase.table('stocks_historical').select('date').order('date', desc=True).limit(1).execute()
        oldest_response = supabase.table('stocks_historical').select('date').order('date', asc=True).limit(1).execute()
        
        if latest_response.data and oldest_response.data:
            logger.info(f"📅 Historical data range: {oldest_response.data[0]['date']} to {latest_response.data[0]['date']}")
        
        # Test symbols with historical data
        symbols_response = supabase.table('stocks_historical').select('symbol').execute()
        unique_symbols = set(record['symbol'] for record in symbols_response.data)
        logger.info(f"📈 Unique symbols with historical data: {len(unique_symbols)}")
        
    except Exception as e:
        logger.error(f"❌ Error testing historical data: {e}")

def test_data_integration():
    """Test how well the data integrates across tables"""
    
    logger.info("\n🔗 Testing Cross-Table Data Integration...")
    
    try:
        # Get symbols that exist in all three tables
        realtime_symbols = set()
        financial_symbols = set()
        historical_symbols = set()
        
        # Get realtime symbols
        response = supabase.table('stocks_realtime').select('symbol').execute()
        realtime_symbols = set(record['symbol'] for record in response.data)
        
        # Get financial symbols
        response = supabase.table('stocks_financials').select('symbol').execute()
        financial_symbols = set(record['symbol'] for record in response.data)
        
        # Get historical symbols (sample)
        response = supabase.table('stocks_historical').select('symbol').limit(1000).execute()
        historical_symbols = set(record['symbol'] for record in response.data)
        
        # Calculate overlaps
        all_three = realtime_symbols & financial_symbols & historical_symbols
        realtime_financial = realtime_symbols & financial_symbols
        realtime_historical = realtime_symbols & historical_symbols
        
        logger.info(f"📊 Symbols in real-time: {len(realtime_symbols)}")
        logger.info(f"💰 Symbols in financials: {len(financial_symbols)}")
        logger.info(f"📈 Symbols in historical (sample): {len(historical_symbols)}")
        logger.info(f"🎯 Symbols in all three tables: {len(all_three)}")
        logger.info(f"🔗 Real-time + Financial: {len(realtime_financial)}")
        logger.info(f"🔗 Real-time + Historical: {len(realtime_historical)}")
        
        # Show some examples
        if all_three:
            logger.info(f"✅ Example symbols in all tables: {list(all_three)[:5]}")
        
    except Exception as e:
        logger.error(f"❌ Error testing data integration: {e}")

def test_enhanced_query_capabilities():
    """Test advanced querying capabilities with enhanced data"""
    
    logger.info("\n🚀 Testing Enhanced Query Capabilities...")
    
    try:
        # Test complex real-time queries
        logger.info("📊 Testing Real-time Advanced Queries...")
        
        # Stocks with technical signals
        response = supabase.table('stocks_realtime').select('symbol, ma20, target_1, stop_loss').not_('ma20', 'is', 'null').not_('target_1', 'is', 'null').limit(10).execute()
        logger.info(f"🎯 Stocks with technical analysis data: {len(response.data)}")
        
        # High liquidity stocks
        response = supabase.table('stocks_realtime').select('symbol, volume, net_liquidity').gt('volume', 1000000).not_('net_liquidity', 'is', 'null').order('volume', desc=True).limit(5).execute()
        logger.info(f"💧 High liquidity stocks found: {len(response.data)}")
        
        if response.data:
            top_stock = response.data[0]
            logger.info(f"🏆 Top by volume: {top_stock['symbol']} (Volume: {top_stock['volume']:,})")
        
        # Test financial data queries
        logger.info("💰 Testing Financial Advanced Queries...")
        
        # Growth stocks
        response = supabase.table('stocks_financials').select('symbol, revenue_growth, eps_growth_ttm, pe_ratio').gt('revenue_growth', 10).gt('eps_growth_ttm', 15).not_('pe_ratio', 'is', 'null').order('revenue_growth', desc=True).limit(5).execute()
        logger.info(f"📈 Growth stocks (Revenue >10%, EPS >15%): {len(response.data)}")
        
        # Value stocks
        response = supabase.table('stocks_financials').select('symbol, pe_ratio, price_to_book, dividend_yield').lt('pe_ratio', 15).lt('price_to_book', 2).gt('dividend_yield', 3).limit(5).execute()
        logger.info(f"💎 Value stocks (P/E <15, P/B <2, Div >3%): {len(response.data)}")
        
        # Test sector analysis
        response = supabase.table('stocks_financials').select('sector').not_('sector', 'is', 'null').execute()
        if response.data:
            sectors = set(record['sector'] for record in response.data if record['sector'])
            logger.info(f"🏢 Unique sectors identified: {len(sectors)}")
            logger.info(f"🏭 Sectors: {', '.join(list(sectors)[:5])}...")
        
    except Exception as e:
        logger.error(f"❌ Error testing enhanced queries: {e}")

def test_performance_indicators():
    """Test system performance and data freshness"""
    
    logger.info("\n⚡ Testing Performance Indicators...")
    
    try:
        # Check data freshness
        response = supabase.table('stocks_realtime').select('updated_at').order('updated_at', desc=True).limit(1).execute()
        if response.data:
            latest_update = response.data[0]['updated_at']
            logger.info(f"🕐 Latest real-time update: {latest_update}")
        
        response = supabase.table('stocks_financials').select('updated_at').order('updated_at', desc=True).limit(1).execute()
        if response.data:
            latest_financial_update = response.data[0]['updated_at']
            logger.info(f"💰 Latest financial update: {latest_financial_update}")
        
        # Test query performance (simple timing)
        import time
        
        start_time = time.time()
        response = supabase.table('stocks_realtime').select('symbol, current_price, volume').gt('volume', 100000).order('volume', desc=True).limit(50).execute()
        query_time = time.time() - start_time
        logger.info(f"⚡ Query performance (50 active stocks): {query_time:.3f} seconds")
        
        start_time = time.time()
        response = supabase.table('stocks_financials').select('symbol, market_capitalization, pe_ratio').not_('market_capitalization', 'is', 'null').order('market_capitalization', desc=True).limit(50).execute()
        query_time = time.time() - start_time
        logger.info(f"⚡ Query performance (50 financial records): {query_time:.3f} seconds")
        
    except Exception as e:
        logger.error(f"❌ Error testing performance: {e}")

def generate_system_report():
    """Generate comprehensive system report"""
    
    logger.info("\n📋 Generating System Report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'tables': {},
        'integration': {},
        'quality': {}
    }
    
    try:
        # Table statistics
        for table in ['stocks_realtime', 'stocks_financials', 'stocks_historical']:
            try:
                response = supabase.table(table).select('*', count='exact').limit(1).execute()
                report['tables'][table] = {
                    'total_records': response.count,
                    'columns': len(response.data[0].keys()) if response.data else 0
                }
            except Exception as e:
                report['tables'][table] = {'error': str(e)}
        
        # Data quality metrics
        try:
            # Active stocks (with volume > 0)
            response = supabase.table('stocks_realtime').select('symbol', count='exact').gt('volume', 0).execute()
            report['quality']['active_stocks'] = response.count
            
            # Stocks with enhanced data
            response = supabase.table('stocks_realtime').select('symbol', count='exact').not_('ma20', 'is', 'null').execute()
            report['quality']['technical_analysis_coverage'] = response.count
            
            # Financial data coverage
            response = supabase.table('stocks_financials').select('symbol', count='exact').not_('pe_ratio', 'is', 'null').execute()
            report['quality']['financial_coverage'] = response.count
            
        except Exception as e:
            report['quality']['error'] = str(e)
        
        logger.info("📊 System Report Summary:")
        for table, stats in report['tables'].items():
            if 'error' not in stats:
                logger.info(f"  {table}: {stats['total_records']:,} records, {stats['columns']} columns")
        
        logger.info("🎯 Quality Metrics:")
        for metric, value in report['quality'].items():
            if metric != 'error':
                logger.info(f"  {metric}: {value:,}")
        
        # Save detailed report
        import json
        with open('enhanced_system_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        logger.info("💾 Detailed report saved to: enhanced_system_report.json")
        
    except Exception as e:
        logger.error(f"❌ Error generating report: {e}")

def main():
    """Main test function"""
    logger.info("🚀 Starting Enhanced EGX Data System Test...")
    logger.info("=" * 60)
    
    try:
        test_enhanced_data_availability()
        test_data_integration()
        test_enhanced_query_capabilities()
        test_performance_indicators()
        generate_system_report()
        
        logger.info("=" * 60)
        logger.info("✅ Enhanced System Test Completed Successfully!")
        logger.info("📋 Check 'enhanced_system_report.json' for detailed metrics")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
