#!/usr/bin/env python3
"""
Enhanced Real-time Data Loader for EGX Stock AI Oracle
Loads comprehensive real-time data from Excel file with full column mapping
"""

import pandas as pd
from supabase import create_client, Client
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
import sys

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('realtime_data_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://tbzbrujqjwpatbzffmwq.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
EXCEL_PATH = os.getenv("REALTIME_EXCEL_PATH", "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx")

if not SUPABASE_SERVICE_ROLE_KEY:
    logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable is required")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def get_column_value(row, possible_columns):
    """Get value from row using multiple possible column names"""
    for col in possible_columns:
        if col in row.index and pd.notna(row[col]):
            return row[col]
    return None

def safe_float_convert(value):
    """Safely convert value to float"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A':
            return None
        return float(value)
    except (ValueError, TypeError):
        return None

def safe_int_convert(value):
    """Safely convert value to int"""
    try:
        if pd.isna(value) or value == '' or value == 'N/A':
            return None
        return int(float(value))
    except (ValueError, TypeError):
        return None

def safe_bool_convert(value):
    """Safely convert value to boolean"""
    if pd.isna(value) or value == '' or value == 'N/A':
        return None
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ['true', '1', 'yes', 'نعم']
    return bool(value)

def process_realtime_data():
    """Process and sync comprehensive real-time data from Excel file"""
    try:
        if not os.path.exists(EXCEL_PATH):
            logger.error(f"Excel file not found: {EXCEL_PATH}")
            return
        
        logger.info(f"Reading Excel file: {EXCEL_PATH}")
        
        # Read Excel file
        try:
            df = pd.read_excel(EXCEL_PATH, sheet_name=0)
        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            return
        
        if df.empty:
            logger.warning("Excel file is empty")
            return
        
        logger.info(f"Loaded {len(df)} rows from Excel file")
        logger.info(f"Available columns: {list(df.columns)}")
        
        # Enhanced column mapping for comprehensive data extraction
        column_mappings = {
            'symbol': ["الرمز", "Symbol", "symbol", "رمز", "الكود", "Code"],
            'name': ["الاسم", "Name", "name", "اسم"],
            'current_price': ["الاغلاق", "Close", "close", "آخر سعر", "Last Price", "Current Price"],
            'open_price': ["إفتتاح", "Open", "open", "افتتاح"],
            'high_price': ["أعلى", "High", "high", "اعلى"],
            'low_price': ["أدنى", "Low", "low", "ادنى"],
            'previous_close': ["الاغلاق السابق", "Previous Close", "prev_close"],
            'change_percent': ["التغير %", "Change %", "change_percent", "نسبة التغير"],
            'volume': ["الحجم", "Volume", "volume", "حجم", "Quantity", "الكمية"],
            'turnover': ["القيمة", "Value", "Turnover", "turnover", "قيمة"],
            'trades_count': ["الصفقات", "Trades", "trades", "صفقات"],
            'bid_price': ["سعر العرض", "Bid Price", "Bid", "عرض"],
            'ask_price': ["سعر الطلب", "Ask Price", "Ask", "طلب"],
            'bid_volume': ["كمية العرض", "Bid Volume", "Bid Qty"],
            'ask_volume': ["كمية الطلب", "Ask Volume", "Ask Qty"],
            'market_cap': ["القيمة السوقية", "Market Cap", "رأس المال السوقي"],
            
            # Advanced Technical Indicators
            'ma5': ["ma5 (يوم)", "MA5", "متوسط 5"],
            'ma10': ["ma10 (يوم)", "MA10", "متوسط 10"],
            'ma20': ["ma20 (يوم)", "MA20", "متوسط 20"],
            'ma50': ["ma50 (يوم)", "MA50", "متوسط 50"],
            'ma100': ["ma100 (يوم)", "MA100", "متوسط 100"],
            'ma200': ["ma200 (يوم)", "MA200", "متوسط 200"],
            
            # Trading Signals & Targets
            'target_1': ["هدف 1 (يوم)", "Target 1", "الهدف الأول"],
            'target_2': ["هدف 2 (يوم)", "Target 2", "الهدف الثاني"],
            'target_3': ["هدف 3 (يوم)", "Target 3", "الهدف الثالث"],
            'stop_loss': ["وقف خسارة (يوم)", "Stop Loss", "وقف الخسارة"],
            'stock_status': ["حاله السهم (يوم)", "Stock Status", "حالة السهم"],
            'speculation_opportunity': ["فرص مضاربة ساعة", "Speculation Hour", "فرص المضاربة"],
            
            # Liquidity & Flow Analysis
            'liquidity_ratio': ["نسبة السيولة %", "Liquidity %", "نسبة السيولة"],
            'net_liquidity': ["صافي السيولة", "Net Liquidity", "السيولة الصافية"],
            'liquidity_inflow': ["قيمة السيولة الداخلة", "Liquidity Inflow", "السيولة الداخلة"],
            'liquidity_outflow': ["قيمة السيولة الخارجة", "Liquidity Outflow", "السيولة الخارجة"],
            'volume_inflow': ["حجم السيولة الداخلة", "Volume Inflow", "الحجم الداخل"],
            'volume_outflow': ["حجم السيولة الخارجة", "Volume Outflow", "الحجم الخارج"],
            'liquidity_flow': ["تدفق السيولة", "Liquidity Flow", "تدفق"],
            
            # Financial Metrics
            'free_shares': ["الاسهم الحرة", "Free Shares", "الأسهم الحرة"],
            'eps_annual': ["ربح السهم (سنوي)", "EPS Annual", "ربح السهم"],
            'book_value': ["القيمة الدفترية", "Book Value", "القيمة الدفترية"],
            'pe_ratio': ["مكرر الأرباح", "P/E Ratio", "مكرر الربحية"],
            'dividend_yield': ["العائد الربحي (٪)", "Dividend Yield", "العائد"],
            'sector': ["القطاع", "Sector", "القطاع"],
            
            # Technical Analysis
            'tk_indicator': ["TK (يوم)", "TK", "مؤشر TK"],
            'kj_indicator': ["KJ (يوم)", "KJ", "مؤشر KJ"],
            'price_range': ["المدى", "Range", "المدى"],
            'avg_net_volume_3d': ["متوسط صافي الحجم 3 يوم", "Avg Net Volume 3D"],
            'avg_net_volume_5d': ["متوسط صافي الحجم 5 يوم", "Avg Net Volume 5D"],
            
            # Date & Time
            'last_trade_date': ["التاريخ", "Date", "تاريخ"],
            'opening_value': ["قيمة الإفتتاح", "Opening Value", "قيمة الافتتاح"]
        }
        
        processed_count = 0
        batch_payload = []
        
        for index, row in df.iterrows():
            try:
                # Extract symbol (required field)
                symbol = get_column_value(row, column_mappings['symbol'])
                if not symbol:
                    logger.warning(f"No symbol found in row {index}")
                    continue
                
                record_data = {'symbol': str(symbol).strip()}
                
                # Extract all available data fields
                for field, possible_cols in column_mappings.items():
                    if field == 'symbol':  # Already processed
                        continue
                    
                    value = get_column_value(row, possible_cols)
                    if value is not None:
                        # Convert to appropriate data type
                        if field in ['current_price', 'open_price', 'high_price', 'low_price', 'previous_close', 
                                   'change_percent', 'liquidity_ratio', 'target_1', 'target_2', 'target_3',
                                   'stop_loss', 'ma5', 'ma10', 'ma20', 'ma50', 'ma100', 'ma200',
                                   'net_liquidity', 'eps_annual', 'book_value', 'pe_ratio', 'dividend_yield',
                                   'bid_price', 'ask_price', 'market_cap', 'liquidity_inflow', 'liquidity_outflow',
                                   'price_range', 'avg_net_volume_3d', 'avg_net_volume_5d', 'tk_indicator', 
                                   'kj_indicator', 'opening_value']:
                            record_data[field] = safe_float_convert(value)
                        elif field in ['volume', 'trades_count', 'free_shares', 'volume_inflow', 'volume_outflow',
                                     'bid_volume', 'ask_volume']:
                            record_data[field] = safe_int_convert(value)
                        elif field in ['speculation_opportunity']:
                            record_data[field] = safe_bool_convert(value)
                        else:
                            record_data[field] = str(value).strip() if value not in [None, ''] else None
                
                # Calculate derived metrics
                current_price = record_data.get('current_price')
                previous_close = record_data.get('previous_close')
                
                if current_price and previous_close and previous_close > 0:
                    change_amount = current_price - previous_close
                    record_data['change_amount'] = change_amount
                    if not record_data.get('change_percent'):
                        record_data['change_percent'] = (change_amount / previous_close) * 100
                
                # Add metadata
                record_data['updated_at'] = datetime.now(timezone.utc).isoformat()
                record_data['last_trade_time'] = record_data.get('last_trade_date')
                
                batch_payload.append(record_data)
                processed_count += 1
                
                # Process in batches
                if len(batch_payload) >= 50:
                    if upsert_batch(batch_payload):
                        logger.info(f"Successfully processed batch of {len(batch_payload)} records")
                    batch_payload = []
                    
            except Exception as e:
                logger.error(f"Error processing row {index} for symbol {symbol if 'symbol' in locals() else 'unknown'}: {e}")
                continue
        
        # Process remaining records
        if batch_payload:
            if upsert_batch(batch_payload):
                logger.info(f"Successfully processed final batch of {len(batch_payload)} records")
        
        logger.info(f"Real-time data sync completed. Processed {processed_count} records")
        
    except Exception as e:
        logger.error(f"Error in process_realtime_data: {e}")

def upsert_batch(batch_data):
    """Upsert batch data to stocks_realtime table"""
    try:
        if not batch_data:
            return True
        
        # Upsert data
        response = supabase.table("stocks_realtime").upsert(
            batch_data,
            on_conflict="symbol",
            returning="minimal"
        ).execute()
        
        logger.info(f"Upserted {len(batch_data)} records to stocks_realtime")
        return True
        
    except Exception as e:
        logger.error(f"Error upserting batch data: {e}")
        return False

def main():
    """Main function"""
    logger.info("Starting enhanced real-time data sync...")
    
    try:
        process_realtime_data()
        logger.info("Real-time data sync completed successfully")
    except Exception as e:
        logger.error(f"Real-time data sync failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
