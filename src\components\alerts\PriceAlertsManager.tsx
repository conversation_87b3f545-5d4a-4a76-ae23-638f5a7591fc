
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Bell } from 'lucide-react';
import { AddAlertForm } from './AddAlertForm';
import { AlertRow } from './AlertRow';
import { usePriceAlerts } from '@/hooks/usePriceAlerts';
import { useMockPriceUpdater } from '@/hooks/useMockPriceUpdater';

// PriceAlertsManager: presentational only, logic moved to custom hooks
const PriceAlertsManager = () => {
  const {
    alerts,
    loading,
    addAlert,
    deleteAlert,
    toggleAlert,
    setAlerts,
  } = usePriceAlerts();

  // Simulate prices and trigger alerts (mock only!)
  useMockPriceUpdater({
    alerts,
    setAlerts,
    toggleAlert,
  });

  return (
    <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800">
          <Bell className="h-5 w-5" />
          تنبيهات الأسعار الذكية
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <AddAlertForm loading={loading} onAdd={addAlert} />
        <div className="space-y-3">
          {alerts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد تنبيهات نشطة</p>
            </div>
          ) : (
            alerts.map((alert) => (
              <AlertRow
                key={alert.id}
                alert={alert}
                onToggle={toggleAlert}
                onDelete={deleteAlert}
              />
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PriceAlertsManager;
