import React, { useState, useEffect, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  AlertTriangle, 
  RefreshCw, 
  ExternalLink,
  BarChart3,
  Info
} from 'lucide-react';
import TradingViewChart from './TradingViewChart';

interface EgyptianStockChartProps {
  symbol: string;
  height?: number;
  theme?: 'light' | 'dark';
  interval?: string;
  className?: string;
}

const EgyptianStockChart: React.FC<EgyptianStockChartProps> = memo(({
  symbol,
  height = 600,
  theme = 'light',
  interval = 'D',
  className = ''
}) => {
  const [currentSymbolFormat, setCurrentSymbolFormat] = useState(0);
  const [showFallback, setShowFallback] = useState(false);
  
  // تنسيقات مختلفة للأسهم المصرية
  const symbolFormats = [
    `EGX:${symbol}`,
    `CASE:${symbol}`,
    symbol,
    `${symbol}.CA` // Cairo Exchange
  ];

  const handleSymbolChange = () => {
    const nextFormat = (currentSymbolFormat + 1) % symbolFormats.length;
    setCurrentSymbolFormat(nextFormat);
  };

  const handleShowFallback = () => {
    setShowFallback(true);
  };

  if (showFallback) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            شارت {symbol} 
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-3">
                <p>شارت السهم غير متاح حالياً من TradingView للرمز "{symbol}"</p>
                
                <div className="space-y-2">
                  <p className="font-semibold">يمكنك:</p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>البحث يدوياً عن السهم في TradingView</li>
                    <li>استخدام موقع البورصة المصرية الرسمي</li>
                    <li>التحقق من تطبيق البروكر الخاص بك</li>
                  </ul>
                </div>

                <div className="flex gap-2 mt-4">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowFallback(false)}
                    className="flex items-center gap-1"
                  >
                    <RefreshCw className="h-4 w-4" />
                    إعادة المحاولة
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(`https://tradingview.com/chart/?symbol=${symbolFormats[currentSymbolFormat]}`, '_blank')}
                    className="flex items-center gap-1"
                  >
                    <ExternalLink className="h-4 w-4" />
                    فتح في TradingView
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open('https://www.egx.com.eg/', '_blank')}
                    className="flex items-center gap-1"
                  >
                    <ExternalLink className="h-4 w-4" />
                    البورصة المصرية
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* عناصر التحكم */}
      <Card className="mb-4">
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">رمز السهم:</span>
              <Badge variant="outline">{symbolFormats[currentSymbolFormat]}</Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSymbolChange}
                className="text-xs"
              >
                تجربة تنسيق آخر
              </Button>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleShowFallback}
              className="flex items-center gap-1"
            >
              <AlertTriangle className="h-4 w-4" />
              مشكلة في التحميل؟
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* الشارت */}
      <Card>
        <CardContent className="p-0">
          <TradingViewChart
            symbol={symbolFormats[currentSymbolFormat].replace('EGX:', '').replace('CASE:', '')}
            height={height}
            theme={theme}
            interval={interval}
            timezone="Africa/Cairo"
            locale="ar"
            range="12M"
            allow_symbol_change={true}
            hide_side_toolbar={false}
            autosize={false}
          />
        </CardContent>
      </Card>

      {/* معلومات مفيدة */}
      <Card className="mt-4 bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="text-sm text-blue-800">
            <h4 className="font-semibold mb-2">💡 نصائح لاستخدام الشارت:</h4>
            <ul className="space-y-1 list-disc list-inside">
              <li>استخدم خاصية البحث داخل الشارت للعثور على السهم</li>
              <li>جرب البحث باسم الشركة بدلاً من الرمز</li>
              <li>تأكد من تحديد البورصة المصرية (EGX) في النتائج</li>
              <li>يمكنك تغيير الإطار الزمني من شريط الأدوات</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
});

EgyptianStockChart.displayName = 'EgyptianStockChart';

export default EgyptianStockChart;
