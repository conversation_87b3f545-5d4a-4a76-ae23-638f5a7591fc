#!/usr/bin/env python3
"""
Check for suspended/delisted stocks in our database
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Supabase client
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key)

def check_suspended_stocks():
    """Check for stocks that might be suspended or delisted"""
    
    print("🔍 Checking for potentially suspended/delisted stocks...")
      # Check stocks with zero volume
    response = supabase.table('stocks_realtime').select('symbol, volume, price, change_percent').eq('volume', 0).execute()
    zero_volume_stocks = response.data
    
    print(f"\n📊 Stocks with zero volume (potentially suspended): {len(zero_volume_stocks)}")
    for stock in zero_volume_stocks[:10]:  # Show first 10
        print(f"  • {stock['symbol']}: Volume={stock['volume']}, Price={stock['price']}, Change={stock['change_percent']}%")
    
    # Check stocks with null volume
    response = supabase.table('stocks_realtime').select('symbol, volume, price, change_percent').is_('volume', 'null').execute()
    null_volume_stocks = response.data
    
    print(f"\n📊 Stocks with null volume: {len(null_volume_stocks)}")
    for stock in null_volume_stocks[:10]:  # Show first 10
        print(f"  • {stock['symbol']}: Volume={stock['volume']}, Price={stock['price']}, Change={stock['change_percent']}%")
    
    # Check market indices (should be excluded)
    response = supabase.table('stocks_realtime').select('symbol, volume, price').like('symbol', '*EGX*').execute()
    indices = response.data
    
    print(f"\n📈 Market indices (should be excluded from stock lists): {len(indices)}")
    for index in indices:
        print(f"  • {index['symbol']}: Volume={index['volume']}, Price={index['price']}")
    
    # Check what our frontend queries would actually return
    print("\n🎯 Testing frontend queries...")
    
    # Top gainers query (same as QuickStatsGrid)
    response = supabase.table('stocks_realtime').select('symbol, change_percent, volume').not_('symbol', 'like', '*EGX*').gt('change_percent', 0).gt('volume', 0).order('change_percent', desc=True).limit(5).execute()
    top_gainers = response.data
    
    print(f"\n📈 Top gainers (filtered): {len(top_gainers)}")
    for stock in top_gainers:
        print(f"  • {stock['symbol']}: +{stock['change_percent']}%, Volume={stock['volume']}")
    
    # Most active by volume query
    response = supabase.table('stocks_realtime').select('symbol, volume').not_('symbol', 'like', '*EGX*').gt('volume', 0).order('volume', desc=True).limit(5).execute()
    top_volume = response.data
    
    print(f"\n📊 Most active by volume (filtered): {len(top_volume)}")
    for stock in top_volume:
        print(f"  • {stock['symbol']}: Volume={stock['volume']}")
    
    # Check total active stocks
    response = supabase.table('stocks_realtime').select('symbol', count='exact').not_('symbol', 'like', '*EGX*').gt('volume', 0).execute()
    active_count = response.count
    
    print(f"\n✅ Total active stocks (excluding indices and zero volume): {active_count}")
    
    # Check total stocks in database
    response = supabase.table('stocks_realtime').select('symbol', count='exact').execute()
    total_count = response.count
    
    print(f"📊 Total stocks in database: {total_count}")
    print(f"🚫 Filtered out: {total_count - active_count} stocks/indices")

if __name__ == "__main__":
    check_suspended_stocks()
