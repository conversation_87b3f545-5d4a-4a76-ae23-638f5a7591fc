#!/usr/bin/env python3
"""
اختبار شامل للـ Views المُنشأة حديثاً
Comprehensive test for newly created Views
"""

import os
import sys
from datetime import datetime
from supabase import create_client, Client

# إعداد البيئة
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Missing Supabase configuration")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("✅ Connected to Supabase successfully")
except Exception as e:
    print(f"❌ Error connecting to Supabase: {e}")
    sys.exit(1)

def test_view(view_name, description, limit=None):
    """اختبار view محدد"""
    try:
        print(f"\n🔍 اختبار {view_name}: {description}")
        
        query = supabase.table(view_name).select("*")
        if limit:
            query = query.limit(limit)
        
        response = query.execute()
        
        if response.data is not None:
            count = len(response.data)
            print(f"✅ {view_name} - يعمل بشكل صحيح ({count} صف)")
            
            # عرض عينة من البيانات
            if count > 0:
                sample = response.data[0]
                print(f"   📊 عينة البيانات: {list(sample.keys())[:5]}...")
                
            return True, count
        else:
            print(f"⚠️  {view_name} - لا يُرجع بيانات")
            return False, 0
            
    except Exception as e:
        print(f"❌ {view_name} - خطأ: {e}")
        return False, 0

def test_market_summary():
    """اختبار مفصل لـ market_summary_view"""
    try:
        print("\n📊 اختبار تفصيلي لـ market_summary_view...")
        
        response = supabase.table("market_summary_view").select("*").execute()
        
        if response.data and len(response.data) > 0:
            data = response.data[0]
            print("✅ إحصائيات السوق:")
            print(f"   📈 إجمالي الأسهم: {data.get('total_stocks', 'N/A')}")
            print(f"   🔥 الأسهم النشطة: {data.get('active_stocks', 'N/A')}")
            print(f"   📊 الرابحين: {data.get('gainers', 'N/A')}")
            print(f"   📉 الخاسرين: {data.get('losers', 'N/A')}")
            print(f"   ⚖️  متوسط التغيير: {data.get('avg_change', 'N/A')}%")
            print(f"   💰 إجمالي الحجم: {data.get('total_volume', 'N/A'):,}")
            return True
        else:
            print("❌ لا توجد بيانات في market_summary_view")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في market_summary_view: {e}")
        return False

def test_top_performers():
    """اختبار أفضل الأسهم أداءً"""
    try:
        print("\n🏆 اختبار top_performers_view...")
        
        # الرابحين
        gainers = supabase.table("top_performers_view")\
            .select("*")\
            .eq("category", "gainers")\
            .limit(3)\
            .execute()
            
        if gainers.data:
            print(f"✅ أفضل الرابحين ({len(gainers.data)} سهم):")
            for stock in gainers.data:
                print(f"   🔥 {stock.get('symbol')} - {stock.get('change_percent', 0):.2f}%")
        
        # الخاسرين
        losers = supabase.table("top_performers_view")\
            .select("*")\
            .eq("category", "losers")\
            .limit(3)\
            .execute()
            
        if losers.data:
            print(f"✅ أكبر الخاسرين ({len(losers.data)} سهم):")
            for stock in losers.data:
                print(f"   📉 {stock.get('symbol')} - {stock.get('change_percent', 0):.2f}%")
        
        # الأكثر نشاطاً
        active = supabase.table("top_performers_view")\
            .select("*")\
            .eq("category", "most_active")\
            .limit(3)\
            .execute()
            
        if active.data:
            print(f"✅ الأكثر نشاطاً ({len(active.data)} سهم):")
            for stock in active.data:
                print(f"   📊 {stock.get('symbol')} - حجم: {stock.get('volume', 0):,}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في top_performers_view: {e}")
        return False

def main():
    """الاختبار الشامل"""
    print("🚀 بدء الاختبار الشامل للـ Views...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # قائمة Views للاختبار
    views_to_test = [
        ("market_summary_view", "ملخص السوق العام"),
        ("technical_analysis_view", "التحليل الفني"),
        ("advanced_metrics_view", "المقاييس المتقدمة"),
        ("top_performers_view", "أفضل الأسهم أداءً", 20),
        ("sector_performance_view", "أداء القطاعات"),
        ("market_liquidity_view", "تحليل السيولة")
    ]
    
    working_views = []
    total_data_count = 0
    
    # اختبار كل view
    for view_test in views_to_test:
        view_name = view_test[0]
        description = view_test[1]
        limit = view_test[2] if len(view_test) > 2 else None
        
        success, count = test_view(view_name, description, limit)
        if success:
            working_views.append(view_name)
            total_data_count += count
    
    # اختبارات تفصيلية
    print("\n" + "="*50)
    print("🔬 اختبارات تفصيلية:")
    
    test_market_summary()
    test_top_performers()
    
    # النتائج النهائية
    print("\n" + "="*50)
    print("📊 نتائج الاختبار الشامل:")
    print(f"✅ Views تعمل: {len(working_views)}/{len(views_to_test)}")
    print(f"📈 إجمالي البيانات: {total_data_count} صف")
    
    if len(working_views) == len(views_to_test):
        print("\n🎉 نجح الاختبار بنسبة 100%!")
        print("✅ جميع الـ Views تعمل بشكل مثالي")
        print("🚀 النظام جاهز للاستخدام الكامل!")
    else:
        print(f"\n⚠️  {len(views_to_test) - len(working_views)} views تحتاج مراجعة")
    
    print("\n💡 الخطوة التالية: تحديث الـ hooks لاستخدام الـ Views الجديدة")

if __name__ == "__main__":
    main()
