#!/usr/bin/env python3
"""
Simple check for suspended stocks
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Supabase client
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key)

# Check first few records to see column names
print("🔍 Checking database columns...")
response = supabase.table('stocks_realtime').select('*').limit(3).execute()
if response.data:
    first_record = response.data[0]
    print("Available columns:", list(first_record.keys()))
    print("\nSample record:")
    for key, value in first_record.items():
        print(f"  {key}: {value}")

# Check for zero volume stocks
print("\n📊 Checking for zero volume stocks...")
response = supabase.table('stocks_realtime').select('symbol, volume').eq('volume', 0).limit(10).execute()
zero_volume = response.data
print(f"Found {len(zero_volume)} stocks with zero volume:")
for stock in zero_volume:
    print(f"  • {stock['symbol']}: Volume={stock['volume']}")

# Check for indices
print("\n📈 Checking for market indices...")
response = supabase.table('stocks_realtime').select('symbol, volume').like('symbol', '*EGX*').execute()
indices = response.data
print(f"Found {len(indices)} market indices:")
for index in indices:
    print(f"  • {index['symbol']}: Volume={index.get('volume', 'N/A')}")

# Check what frontend would get (active stocks only)
print("\n✅ Frontend query test (active stocks only)...")
try:
    # Get all non-index stocks with volume > 0
    response = supabase.table('stocks_realtime').select('symbol, volume, change_percent').execute()
    all_data = response.data
    
    # Filter out indices and zero volume stocks manually for simplicity
    active_stocks = [stock for stock in all_data 
                    if stock['volume'] and stock['volume'] > 0 
                    and 'EGX' not in stock['symbol']]
    
    # Sort by volume descending and get top 5
    active_stocks.sort(key=lambda x: x['volume'], reverse=True)
    top_active = active_stocks[:5]
    
    print(f"Active stocks that frontend would show ({len(top_active)}):")
    for stock in top_active:
        print(f"  • {stock['symbol']}: Volume={stock['volume']}, Change={stock.get('change_percent', 'N/A')}%")
        
    print(f"\nTotal active stocks (excluding indices): {len(active_stocks)}")
        
except Exception as e:
    print(f"Error testing frontend query: {e}")
