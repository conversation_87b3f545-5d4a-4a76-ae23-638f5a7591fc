#!/usr/bin/env python3
"""
إنشاء دوال SQL محسنة في قاعدة البيانات لحل مشاكل TypeScript
Create optimized SQL functions in database to solve TypeScript issues
"""

import os
import sys
from supabase import create_client, Client

# إعداد البيئة
load_dotenv = lambda: None
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# إعداد الاتصال بقاعدة البيانات
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Missing Supabase configuration")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("✅ Connected to Supabase successfully")
except Exception as e:
    print(f"❌ Error connecting to Supabase: {e}")
    sys.exit(1)

def create_market_overview_function():
    """إنشاء دالة SQL للحصول على نظرة عامة للسوق"""
    
    sql_function = '''
    CREATE OR REPLACE FUNCTION get_market_overview_data()
    RETURNS JSON AS $$
    DECLARE
        result JSON;
    BEGIN
        WITH market_data AS (
            SELECT 
                symbol,
                name,
                current_price,
                change_percent,
                volume,
                turnover,
                ma5,
                ma20,
                target_1
            FROM stocks_realtime 
            WHERE symbol NOT LIKE '%EGX%' 
              AND name IS NOT NULL 
              AND current_price IS NOT NULL
        ),
        summary_stats AS (
            SELECT 
                COUNT(*) as total_stocks,
                COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
                COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
                COUNT(CASE WHEN change_percent = 0 THEN 1 END) as unchanged,
                COALESCE(SUM(turnover), 0) as total_value,
                COALESCE(SUM(volume), 0) as total_volume,
                COALESCE(ROUND(AVG(change_percent), 2), 0) as avg_change,
                CASE 
                    WHEN COUNT(CASE WHEN change_percent > 0 THEN 1 END) > 
                         COUNT(CASE WHEN change_percent < 0 THEN 1 END) * 1.2 THEN 'bullish'
                    WHEN COUNT(CASE WHEN change_percent < 0 THEN 1 END) > 
                         COUNT(CASE WHEN change_percent > 0 THEN 1 END) * 1.2 THEN 'bearish'
                    ELSE 'neutral'
                END as market_trend
            FROM market_data
        ),
        top_gainers AS (
            SELECT json_agg(
                json_build_object(
                    'symbol', symbol,
                    'name', name,
                    'current_price', current_price,
                    'change_percent', change_percent,
                    'volume', volume,
                    'turnover', turnover
                ) ORDER BY change_percent DESC
            ) as data
            FROM (
                SELECT * FROM market_data 
                WHERE change_percent > 0 
                ORDER BY change_percent DESC 
                LIMIT 5
            ) t
        ),
        top_losers AS (
            SELECT json_agg(
                json_build_object(
                    'symbol', symbol,
                    'name', name,
                    'current_price', current_price,
                    'change_percent', change_percent,
                    'volume', volume,
                    'turnover', turnover
                ) ORDER BY change_percent ASC
            ) as data
            FROM (
                SELECT * FROM market_data 
                WHERE change_percent < 0 
                ORDER BY change_percent ASC 
                LIMIT 5
            ) t
        ),
        most_active AS (
            SELECT json_agg(
                json_build_object(
                    'symbol', symbol,
                    'name', name,
                    'current_price', current_price,
                    'change_percent', change_percent,
                    'volume', volume,
                    'turnover', turnover
                ) ORDER BY volume DESC
            ) as data
            FROM (
                SELECT * FROM market_data 
                WHERE volume > 0 
                ORDER BY volume DESC 
                LIMIT 5
            ) t
        ),
        technical_counts AS (
            SELECT 
                COUNT(CASE WHEN ma5 > ma20 AND ma5 IS NOT NULL AND ma20 IS NOT NULL THEN 1 END) as bullish_ma,
                COUNT(CASE WHEN ma5 < ma20 AND ma5 IS NOT NULL AND ma20 IS NOT NULL THEN 1 END) as bearish_ma,
                COUNT(CASE WHEN ma5 > ma20 AND change_percent > 0 AND ma5 IS NOT NULL AND ma20 IS NOT NULL THEN 1 END) as strong_uptrend,
                COUNT(CASE WHEN target_1 IS NOT NULL AND target_1 > 0 AND current_price >= target_1 * 0.95 THEN 1 END) as near_targets
            FROM market_data
        )
        SELECT json_build_object(
            'summary', json_build_object(
                'totalStocks', s.total_stocks,
                'gainers', s.gainers,
                'losers', s.losers,
                'unchanged', s.unchanged,
                'totalValue', s.total_value,
                'totalVolume', s.total_volume,
                'avgChange', s.avg_change,
                'marketTrend', s.market_trend
            ),
            'topGainers', COALESCE(g.data, '[]'::json),
            'topLosers', COALESCE(l.data, '[]'::json),
            'mostActive', COALESCE(a.data, '[]'::json),
            'technicalCounts', json_build_object(
                'bullishMA', t.bullish_ma,
                'bearishMA', t.bearish_ma,
                'strongUptrend', t.strong_uptrend,
                'nearTargets', t.near_targets
            )
        ) INTO result
        FROM summary_stats s
        CROSS JOIN top_gainers g
        CROSS JOIN top_losers l  
        CROSS JOIN most_active a
        CROSS JOIN technical_counts t;
        
        RETURN result;
    END;
    $$ LANGUAGE plpgsql;
    '''
    
    try:
        print("🔧 إنشاء دالة get_market_overview_data...")
        
        # تنفيذ الدالة SQL
        response = supabase.rpc('exec', {'sql': sql_function})
        
        if response.error:
            print(f"❌ خطأ في إنشاء الدالة: {response.error}")
            return False
        
        print("✅ تم إنشاء دالة get_market_overview_data بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في إنشاء الدالة: {e}")
        return False

def test_market_overview_function():
    """اختبار دالة النظرة العامة للسوق"""
    try:
        print("🧪 اختبار دالة get_market_overview_data...")
        
        response = supabase.rpc('get_market_overview_data')
        
        if response.error:
            print(f"❌ خطأ في اختبار الدالة: {response.error}")
            return False
        
        data = response.data
        if data:
            print("✅ تعمل دالة get_market_overview_data بنجاح!")
            print(f"📊 إجمالي الأسهم: {data.get('summary', {}).get('totalStocks', 0)}")
            print(f"📈 الرابحون: {data.get('summary', {}).get('gainers', 0)}")
            print(f"📉 الخاسرون: {data.get('summary', {}).get('losers', 0)}")
            print(f"🎯 اتجاه السوق: {data.get('summary', {}).get('marketTrend', 'غير محدد')}")
            return True
        else:
            print("⚠️ الدالة تعمل لكن لم ترجع بيانات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدالة: {e}")
        return False

def create_simple_views():
    """إنشاء مناظر بسيطة للبيانات"""
    
    views_sql = [
        # منظر بسيط للأسهم النشطة
        '''
        CREATE OR REPLACE VIEW active_stocks_view AS
        SELECT 
            symbol,
            name,
            current_price,
            change_percent,
            volume,
            turnover,
            ma5,
            ma20,
            target_1,
            updated_at
        FROM stocks_realtime 
        WHERE symbol NOT LIKE '%EGX%' 
          AND name IS NOT NULL 
          AND current_price IS NOT NULL
          AND volume > 0
        ORDER BY volume DESC;
        ''',
        
        # منظر للملخص اليومي
        '''
        CREATE OR REPLACE VIEW daily_summary_view AS
        SELECT 
            COUNT(*) as total_stocks,
            COUNT(CASE WHEN change_percent > 0 THEN 1 END) as gainers,
            COUNT(CASE WHEN change_percent < 0 THEN 1 END) as losers,
            COUNT(CASE WHEN change_percent = 0 THEN 1 END) as unchanged,
            COALESCE(SUM(turnover), 0) as total_turnover,
            COALESCE(SUM(volume), 0) as total_volume,
            COALESCE(ROUND(AVG(change_percent), 2), 0) as avg_change,
            MAX(updated_at) as last_updated
        FROM stocks_realtime 
        WHERE symbol NOT LIKE '%EGX%' 
          AND name IS NOT NULL 
          AND current_price IS NOT NULL;
        '''
    ]
    
    for i, view_sql in enumerate(views_sql, 1):
        try:
            print(f"🔧 إنشاء المنظر {i}...")
            
            # تنفيذ SQL للمنظر
            response = supabase.rpc('exec', {'sql': view_sql})
            
            if response.error:
                print(f"❌ خطأ في إنشاء المنظر {i}: {response.error}")
            else:
                print(f"✅ تم إنشاء المنظر {i} بنجاح!")
                
        except Exception as e:
            print(f"❌ خطأ عام في إنشاء المنظر {i}: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء الدوال والمناظر المحسنة...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # إنشاء دالة النظرة العامة
    if create_market_overview_function():
        # اختبار الدالة
        test_market_overview_function()
    
    # إنشاء المناظر البسيطة
    create_simple_views()
    
    print("✅ تم إنشاء جميع الدوال والمناظر!")

if __name__ == "__main__":
    from datetime import datetime
    main()
