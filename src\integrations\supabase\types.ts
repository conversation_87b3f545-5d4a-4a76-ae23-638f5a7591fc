export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      analytics_backtests: {
        Row: {
          finished_at: string | null
          id: string
          parameters: Json | null
          result_summary: Json | null
          result_trades: Json | null
          started_at: string | null
          status: string | null
          strategy_name: string
          symbol: string
          timeframe: string | null
          user_id: string | null
        }
        Insert: {
          finished_at?: string | null
          id?: string
          parameters?: Json | null
          result_summary?: Json | null
          result_trades?: Json | null
          started_at?: string | null
          status?: string | null
          strategy_name: string
          symbol: string
          timeframe?: string | null
          user_id?: string | null
        }
        Update: {
          finished_at?: string | null
          id?: string
          parameters?: Json | null
          result_summary?: Json | null
          result_trades?: Json | null
          started_at?: string | null
          status?: string | null
          strategy_name?: string
          symbol?: string
          timeframe?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      analytics_correlations: {
        Row: {
          analyzed_at: string
          correlation: number
          id: string
          stock1: string
          stock2: string
          strength: string
        }
        Insert: {
          analyzed_at?: string
          correlation: number
          id?: string
          stock1: string
          stock2: string
          strength: string
        }
        Update: {
          analyzed_at?: string
          correlation?: number
          id?: string
          stock1?: string
          stock2?: string
          strength?: string
        }
        Relationships: []
      }
      analytics_ml_predictions: {
        Row: {
          confidence: number
          factors: Json | null
          generated_at: string
          id: string
          prediction: string
          symbol: string
          target_price: number
          timeframe: string
        }
        Insert: {
          confidence: number
          factors?: Json | null
          generated_at?: string
          id?: string
          prediction: string
          symbol: string
          target_price: number
          timeframe: string
        }
        Update: {
          confidence?: number
          factors?: Json | null
          generated_at?: string
          id?: string
          prediction?: string
          symbol?: string
          target_price?: number
          timeframe?: string
        }
        Relationships: []
      }
      analytics_patterns: {
        Row: {
          detected_at: string
          expected: string
          id: string
          pattern: string
          reliability: number
          symbol: string
          timeframe: string
        }
        Insert: {
          detected_at?: string
          expected: string
          id?: string
          pattern: string
          reliability: number
          symbol: string
          timeframe: string
        }
        Update: {
          detected_at?: string
          expected?: string
          id?: string
          pattern?: string
          reliability?: number
          symbol?: string
          timeframe?: string
        }
        Relationships: []
      }
      analytics_volatility: {
        Row: {
          analyzed_at: string
          avg_volatility: number
          current_volatility: number
          id: string
          symbol: string
          trend: string
        }
        Insert: {
          analyzed_at?: string
          avg_volatility: number
          current_volatility: number
          id?: string
          symbol: string
          trend: string
        }
        Update: {
          analyzed_at?: string
          avg_volatility?: number
          current_volatility?: number
          id?: string
          symbol?: string
          trend?: string
        }
        Relationships: []
      }
      api_usage_logs: {
        Row: {
          api_key: string | null
          created_at: string | null
          data_points_returned: number | null
          endpoint: string
          id: string
          ip_address: unknown | null
          method: string
          query_params: Json | null
          response_status: number | null
          response_time_ms: number | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          api_key?: string | null
          created_at?: string | null
          data_points_returned?: number | null
          endpoint: string
          id?: string
          ip_address?: unknown | null
          method: string
          query_params?: Json | null
          response_status?: number | null
          response_time_ms?: number | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          api_key?: string | null
          created_at?: string | null
          data_points_returned?: number | null
          endpoint?: string
          id?: string
          ip_address?: unknown | null
          method?: string
          query_params?: Json | null
          response_status?: number | null
          response_time_ms?: number | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "api_usage_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      market_indices: {
        Row: {
          change_amount: number | null
          change_percent: number | null
          constituents_count: number | null
          current_value: number | null
          high_value: number | null
          low_value: number | null
          market_cap: number | null
          name: string
          name_ar: string | null
          open_value: number | null
          previous_close: number | null
          symbol: string
          turnover: number | null
          updated_at: string | null
          volume: number | null
        }
        Insert: {
          change_amount?: number | null
          change_percent?: number | null
          constituents_count?: number | null
          current_value?: number | null
          high_value?: number | null
          low_value?: number | null
          market_cap?: number | null
          name: string
          name_ar?: string | null
          open_value?: number | null
          previous_close?: number | null
          symbol: string
          turnover?: number | null
          updated_at?: string | null
          volume?: number | null
        }
        Update: {
          change_amount?: number | null
          change_percent?: number | null
          constituents_count?: number | null
          current_value?: number | null
          high_value?: number | null
          low_value?: number | null
          market_cap?: number | null
          name?: string
          name_ar?: string | null
          open_value?: number | null
          previous_close?: number | null
          symbol?: string
          turnover?: number | null
          updated_at?: string | null
          volume?: number | null
        }
        Relationships: []
      }
      market_news: {
        Row: {
          author: string | null
          categories: string[] | null
          content: string | null
          content_ar: string | null
          created_at: string | null
          id: string
          image_url: string | null
          impact_score: number | null
          published_at: string | null
          related_symbols: string[] | null
          sentiment_score: number | null
          source: string | null
          summary: string | null
          summary_ar: string | null
          title: string
          title_ar: string | null
          url: string | null
          view_count: number | null
        }
        Insert: {
          author?: string | null
          categories?: string[] | null
          content?: string | null
          content_ar?: string | null
          created_at?: string | null
          id?: string
          image_url?: string | null
          impact_score?: number | null
          published_at?: string | null
          related_symbols?: string[] | null
          sentiment_score?: number | null
          source?: string | null
          summary?: string | null
          summary_ar?: string | null
          title: string
          title_ar?: string | null
          url?: string | null
          view_count?: number | null
        }
        Update: {
          author?: string | null
          categories?: string[] | null
          content?: string | null
          content_ar?: string | null
          created_at?: string | null
          id?: string
          image_url?: string | null
          impact_score?: number | null
          published_at?: string | null
          related_symbols?: string[] | null
          sentiment_score?: number | null
          source?: string | null
          summary?: string | null
          summary_ar?: string | null
          title?: string
          title_ar?: string | null
          url?: string | null
          view_count?: number | null
        }
        Relationships: []
      }
      paper_trades: {
        Row: {
          account_id: string | null
          commission: number | null
          entry_price: number
          entry_time: string | null
          exit_price: number | null
          exit_time: string | null
          id: string
          notes: string | null
          profit_loss: number | null
          quantity: number
          status: string | null
          stop_loss: number | null
          strategy_name: string | null
          symbol: string
          take_profit: number | null
          trade_type: string
        }
        Insert: {
          account_id?: string | null
          commission?: number | null
          entry_price: number
          entry_time?: string | null
          exit_price?: number | null
          exit_time?: string | null
          id?: string
          notes?: string | null
          profit_loss?: number | null
          quantity: number
          status?: string | null
          stop_loss?: number | null
          strategy_name?: string | null
          symbol: string
          take_profit?: number | null
          trade_type: string
        }
        Update: {
          account_id?: string | null
          commission?: number | null
          entry_price?: number
          entry_time?: string | null
          exit_price?: number | null
          exit_time?: string | null
          id?: string
          notes?: string | null
          profit_loss?: number | null
          quantity?: number
          status?: string | null
          stop_loss?: number | null
          strategy_name?: string | null
          symbol?: string
          take_profit?: number | null
          trade_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "paper_trades_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "paper_trading_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      paper_trading_accounts: {
        Row: {
          account_name: string
          created_at: string | null
          current_balance: number | null
          id: string
          initial_balance: number | null
          is_active: boolean | null
          losing_trades: number | null
          max_drawdown: number | null
          sharpe_ratio: number | null
          total_profit_loss: number | null
          total_trades: number | null
          updated_at: string | null
          user_id: string | null
          win_rate: number | null
          winning_trades: number | null
        }
        Insert: {
          account_name: string
          created_at?: string | null
          current_balance?: number | null
          id?: string
          initial_balance?: number | null
          is_active?: boolean | null
          losing_trades?: number | null
          max_drawdown?: number | null
          sharpe_ratio?: number | null
          total_profit_loss?: number | null
          total_trades?: number | null
          updated_at?: string | null
          user_id?: string | null
          win_rate?: number | null
          winning_trades?: number | null
        }
        Update: {
          account_name?: string
          created_at?: string | null
          current_balance?: number | null
          id?: string
          initial_balance?: number | null
          is_active?: boolean | null
          losing_trades?: number | null
          max_drawdown?: number | null
          sharpe_ratio?: number | null
          total_profit_loss?: number | null
          total_trades?: number | null
          updated_at?: string | null
          user_id?: string | null
          win_rate?: number | null
          winning_trades?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "paper_trading_accounts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      portfolio_holdings: {
        Row: {
          average_buy_price: number
          created_at: string | null
          current_price: number | null
          current_value: number | null
          id: string
          notes: string | null
          portfolio_id: string | null
          profit_loss: number | null
          profit_loss_percent: number | null
          purchase_date: string | null
          quantity: number
          symbol: string
          total_cost: number | null
          updated_at: string | null
        }
        Insert: {
          average_buy_price: number
          created_at?: string | null
          current_price?: number | null
          current_value?: number | null
          id?: string
          notes?: string | null
          portfolio_id?: string | null
          profit_loss?: number | null
          profit_loss_percent?: number | null
          purchase_date?: string | null
          quantity: number
          symbol: string
          total_cost?: number | null
          updated_at?: string | null
        }
        Update: {
          average_buy_price?: number
          created_at?: string | null
          current_price?: number | null
          current_value?: number | null
          id?: string
          notes?: string | null
          portfolio_id?: string | null
          profit_loss?: number | null
          profit_loss_percent?: number | null
          purchase_date?: string | null
          quantity?: number
          symbol?: string
          total_cost?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "portfolio_holdings_portfolio_id_fkey"
            columns: ["portfolio_id"]
            isOneToOne: false
            referencedRelation: "user_portfolios"
            referencedColumns: ["id"]
          },
        ]
      }
      price_alerts: {
        Row: {
          alert_type: string
          condition_met: boolean | null
          created_at: string | null
          current_value: number | null
          id: string
          is_active: boolean | null
          message_template: string | null
          notification_methods: Json | null
          symbol: string
          target_value: number
          triggered_at: string | null
          user_id: string | null
        }
        Insert: {
          alert_type: string
          condition_met?: boolean | null
          created_at?: string | null
          current_value?: number | null
          id?: string
          is_active?: boolean | null
          message_template?: string | null
          notification_methods?: Json | null
          symbol: string
          target_value: number
          triggered_at?: string | null
          user_id?: string | null
        }
        Update: {
          alert_type?: string
          condition_met?: boolean | null
          created_at?: string | null
          current_value?: number | null
          id?: string
          is_active?: boolean | null
          message_template?: string | null
          notification_methods?: Json | null
          symbol?: string
          target_value?: number
          triggered_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "price_alerts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      stocks_financials: {
        Row: {
          analyst_rating: string | null
          beta: number | null
          book_value_per_share: number | null
          created_at: string | null
          current_ratio: number | null
          debt_to_equity: number | null
          dividend_yield: number | null
          eps_growth_yoy: number | null
          eps_ttm: number | null
          fiscal_quarter: number | null
          fiscal_year: number | null
          free_cash_flow: number | null
          gross_margin: number | null
          id: number
          market_cap: number | null
          net_income_ttm: number | null
          net_margin: number | null
          operating_margin: number | null
          pe_ratio: number | null
          price_to_book: number | null
          revenue_ttm: number | null
          roa: number | null
          roe: number | null
          symbol: string
          target_price: number | null
          total_assets: number | null
          total_debt: number | null
          updated_at: string | null
        }
        Insert: {
          analyst_rating?: string | null
          beta?: number | null
          book_value_per_share?: number | null
          created_at?: string | null
          current_ratio?: number | null
          debt_to_equity?: number | null
          dividend_yield?: number | null
          eps_growth_yoy?: number | null
          eps_ttm?: number | null
          fiscal_quarter?: number | null
          fiscal_year?: number | null
          free_cash_flow?: number | null
          gross_margin?: number | null
          id?: number
          market_cap?: number | null
          net_income_ttm?: number | null
          net_margin?: number | null
          operating_margin?: number | null
          pe_ratio?: number | null
          price_to_book?: number | null
          revenue_ttm?: number | null
          roa?: number | null
          roe?: number | null
          symbol: string
          target_price?: number | null
          total_assets?: number | null
          total_debt?: number | null
          updated_at?: string | null
        }
        Update: {
          analyst_rating?: string | null
          beta?: number | null
          book_value_per_share?: number | null
          created_at?: string | null
          current_ratio?: number | null
          debt_to_equity?: number | null
          dividend_yield?: number | null
          eps_growth_yoy?: number | null
          eps_ttm?: number | null
          fiscal_quarter?: number | null
          fiscal_year?: number | null
          free_cash_flow?: number | null
          gross_margin?: number | null
          id?: number
          market_cap?: number | null
          net_income_ttm?: number | null
          net_margin?: number | null
          operating_margin?: number | null
          pe_ratio?: number | null
          price_to_book?: number | null
          revenue_ttm?: number | null
          roa?: number | null
          roe?: number | null
          symbol?: string
          target_price?: number | null
          total_assets?: number | null
          total_debt?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      stocks_historical: {
        Row: {
          adjusted_close: number | null
          close: number
          created_at: string | null
          date: string
          high: number
          id: number
          low: number
          open: number
          open_interest: number | null
          symbol: string
          volume: number | null
        }
        Insert: {
          adjusted_close?: number | null
          close: number
          created_at?: string | null
          date: string
          high: number
          id?: number
          low: number
          open: number
          open_interest?: number | null
          symbol: string
          volume?: number | null
        }
        Update: {
          adjusted_close?: number | null
          close?: number
          created_at?: string | null
          date?: string
          high?: number
          id?: number
          low?: number
          open?: number
          open_interest?: number | null
          symbol?: string
          volume?: number | null
        }
        Relationships: []
      }
      stocks_master: {
        Row: {
          created_at: string | null
          free_float_shares: number | null
          id: number
          industry: string | null
          is_active: boolean | null
          isin: string | null
          listing_date: string | null
          market: string | null
          name: string
          name_ar: string | null
          sector: string | null
          sector_ar: string | null
          symbol: string
          total_shares: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          free_float_shares?: number | null
          id?: number
          industry?: string | null
          is_active?: boolean | null
          isin?: string | null
          listing_date?: string | null
          market?: string | null
          name: string
          name_ar?: string | null
          sector?: string | null
          sector_ar?: string | null
          symbol: string
          total_shares?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          free_float_shares?: number | null
          id?: number
          industry?: string | null
          is_active?: boolean | null
          isin?: string | null
          listing_date?: string | null
          market?: string | null
          name?: string
          name_ar?: string | null
          sector?: string | null
          sector_ar?: string | null
          symbol?: string
          total_shares?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      stocks_realtime: {
        Row: {
          ask_price: number | null
          ask_volume: number | null
          bid_price: number | null
          bid_volume: number | null
          change_amount: number | null
          change_percent: number | null
          current_price: number | null
          high_price: number | null
          last_trade_time: string | null
          low_price: number | null
          market_cap: number | null
          open_price: number | null
          previous_close: number | null
          symbol: string
          trades_count: number | null
          turnover: number | null
          updated_at: string | null
          volume: number | null
          // الأعمدة الجديدة المضافة
          name: string | null
          last_trade_date: string | null
          ma5: number | null
          ma10: number | null
          ma20: number | null
          ma50: number | null
          ma100: number | null
          ma200: number | null
          tk_indicator: number | null
          kj_indicator: number | null
          target_1: number | null
          target_2: number | null
          target_3: number | null
          stop_loss: number | null
          stock_status: string | null
          speculation_opportunity: boolean | null
          liquidity_ratio: number | null
          net_liquidity: number | null
          liquidity_inflow: number | null
          liquidity_outflow: number | null
          volume_inflow: number | null
          volume_outflow: number | null
          liquidity_flow: number | null
          free_shares: number | null
          eps_annual: number | null
          book_value: number | null
          pe_ratio: number | null
          dividend_yield: number | null
          sector: string | null
          price_range: number | null
          avg_net_volume_3d: number | null
          avg_net_volume_5d: number | null
          opening_value: number | null
        }
        Insert: {
          ask_price?: number | null
          ask_volume?: number | null
          bid_price?: number | null
          bid_volume?: number | null
          change_amount?: number | null
          change_percent?: number | null
          current_price?: number | null
          high_price?: number | null
          last_trade_time?: string | null
          low_price?: number | null
          market_cap?: number | null
          open_price?: number | null
          previous_close?: number | null
          symbol: string
          trades_count?: number | null
          turnover?: number | null
          updated_at?: string | null
          volume?: number | null
          // الأعمدة الجديدة المضافة
          name?: string | null
          last_trade_date?: string | null
          ma5?: number | null
          ma10?: number | null
          ma20?: number | null
          ma50?: number | null
          ma100?: number | null
          ma200?: number | null
          tk_indicator?: number | null
          kj_indicator?: number | null
          target_1?: number | null
          target_2?: number | null
          target_3?: number | null
          stop_loss?: number | null
          stock_status?: string | null
          speculation_opportunity?: boolean | null
          liquidity_ratio?: number | null
          net_liquidity?: number | null
          liquidity_inflow?: number | null
          liquidity_outflow?: number | null
          volume_inflow?: number | null
          volume_outflow?: number | null
          liquidity_flow?: number | null
          free_shares?: number | null
          eps_annual?: number | null
          book_value?: number | null
          pe_ratio?: number | null
          dividend_yield?: number | null
          sector?: string | null
          price_range?: number | null
          avg_net_volume_3d?: number | null
          avg_net_volume_5d?: number | null
          opening_value?: number | null
        }
        Update: {
          ask_price?: number | null
          ask_volume?: number | null
          bid_price?: number | null
          bid_volume?: number | null
          change_amount?: number | null
          change_percent?: number | null
          current_price?: number | null
          high_price?: number | null
          last_trade_time?: string | null
          low_price?: number | null
          market_cap?: number | null
          open_price?: number | null
          previous_close?: number | null
          symbol?: string
          trades_count?: number | null
          turnover?: number | null
          updated_at?: string | null
          volume?: number | null
          // الأعمدة الجديدة المضافة
          name?: string | null
          last_trade_date?: string | null
          ma5?: number | null
          ma10?: number | null
          ma20?: number | null
          ma50?: number | null
          ma100?: number | null
          ma200?: number | null
          tk_indicator?: number | null
          kj_indicator?: number | null
          target_1?: number | null
          target_2?: number | null
          target_3?: number | null
          stop_loss?: number | null
          stock_status?: string | null
          speculation_opportunity?: boolean | null
          liquidity_ratio?: number | null
          net_liquidity?: number | null
          liquidity_inflow?: number | null
          liquidity_outflow?: number | null
          volume_inflow?: number | null
          volume_outflow?: number | null
          liquidity_flow?: number | null
          free_shares?: number | null
          eps_annual?: number | null
          book_value?: number | null
          pe_ratio?: number | null
          dividend_yield?: number | null
          sector?: string | null
          price_range?: number | null
          avg_net_volume_3d?: number | null
          avg_net_volume_5d?: number | null
          opening_value?: number | null
        }
        Relationships: []
      }
      technical_indicators: {
        Row: {
          adx_14: number | null
          atr_14: number | null
          bb_lower: number | null
          bb_middle: number | null
          bb_upper: number | null
          cci_20: number | null
          created_at: string | null
          date: string
          ema_12: number | null
          ema_26: number | null
          id: number
          macd: number | null
          macd_histogram: number | null
          macd_signal: number | null
          momentum_10: number | null
          roc_10: number | null
          rsi_14: number | null
          sma_10: number | null
          sma_100: number | null
          sma_20: number | null
          sma_200: number | null
          sma_5: number | null
          sma_50: number | null
          stoch_d: number | null
          stoch_k: number | null
          symbol: string
          timeframe: string | null
          volume_sma_20: number | null
          williams_r: number | null
        }
        Insert: {
          adx_14?: number | null
          atr_14?: number | null
          bb_lower?: number | null
          bb_middle?: number | null
          bb_upper?: number | null
          cci_20?: number | null
          created_at?: string | null
          date: string
          ema_12?: number | null
          ema_26?: number | null
          id?: number
          macd?: number | null
          macd_histogram?: number | null
          macd_signal?: number | null
          momentum_10?: number | null
          roc_10?: number | null
          rsi_14?: number | null
          sma_10?: number | null
          sma_100?: number | null
          sma_20?: number | null
          sma_200?: number | null
          sma_5?: number | null
          sma_50?: number | null
          stoch_d?: number | null
          stoch_k?: number | null
          symbol: string
          timeframe?: string | null
          volume_sma_20?: number | null
          williams_r?: number | null
        }
        Update: {
          adx_14?: number | null
          atr_14?: number | null
          bb_lower?: number | null
          bb_middle?: number | null
          bb_upper?: number | null
          cci_20?: number | null
          created_at?: string | null
          date?: string
          ema_12?: number | null
          ema_26?: number | null
          id?: number
          macd?: number | null
          macd_histogram?: number | null
          macd_signal?: number | null
          momentum_10?: number | null
          roc_10?: number | null
          rsi_14?: number | null
          sma_10?: number | null
          sma_100?: number | null
          sma_20?: number | null
          sma_200?: number | null
          sma_5?: number | null
          sma_50?: number | null
          stoch_d?: number | null
          stoch_k?: number | null
          symbol?: string
          timeframe?: string | null
          volume_sma_20?: number | null
          williams_r?: number | null
        }
        Relationships: []
      }
      user_notifications: {
        Row: {
          action_url: string | null
          created_at: string | null
          id: string
          is_read: boolean | null
          message: string
          metadata: Json | null
          priority: string | null
          related_symbol: string | null
          title: string
          type: string | null
          user_id: string | null
        }
        Insert: {
          action_url?: string | null
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message: string
          metadata?: Json | null
          priority?: string | null
          related_symbol?: string | null
          title: string
          type?: string | null
          user_id?: string | null
        }
        Update: {
          action_url?: string | null
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message?: string
          metadata?: Json | null
          priority?: string | null
          related_symbol?: string | null
          title?: string
          type?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_portfolios: {
        Row: {
          created_at: string | null
          currency: string | null
          description: string | null
          id: string
          is_default: boolean | null
          name: string
          total_cost: number | null
          total_profit_loss: number | null
          total_value: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          currency?: string | null
          description?: string | null
          id?: string
          is_default?: boolean | null
          name: string
          total_cost?: number | null
          total_profit_loss?: number | null
          total_value?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          currency?: string | null
          description?: string | null
          id?: string
          is_default?: boolean | null
          name?: string
          total_cost?: number | null
          total_profit_loss?: number | null
          total_value?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_portfolios_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          api_calls_limit: number | null
          api_calls_reset_date: string | null
          api_calls_used: number | null
          api_key: string | null
          country: string | null
          created_at: string | null
          email: string
          email_verified: boolean | null
          full_name: string | null
          id: string
          is_active: boolean | null
          password_hash: string | null
          phone: string | null
          preferred_language: string | null
          subscription_tier: string | null
          updated_at: string | null
        }
        Insert: {
          api_calls_limit?: number | null
          api_calls_reset_date?: string | null
          api_calls_used?: number | null
          api_key?: string | null
          country?: string | null
          created_at?: string | null
          email: string
          email_verified?: boolean | null
          full_name?: string | null
          id?: string
          is_active?: boolean | null
          password_hash?: string | null
          phone?: string | null
          preferred_language?: string | null
          subscription_tier?: string | null
          updated_at?: string | null
        }
        Update: {
          api_calls_limit?: number | null
          api_calls_reset_date?: string | null
          api_calls_used?: number | null
          api_key?: string | null
          country?: string | null
          created_at?: string | null
          email?: string
          email_verified?: boolean | null
          full_name?: string | null
          id?: string
          is_active?: boolean | null
          password_hash?: string | null
          phone?: string | null
          preferred_language?: string | null
          subscription_tier?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
