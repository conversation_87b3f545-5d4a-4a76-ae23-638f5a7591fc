# Telegram Service for Trading Signals
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import os

# Note: You'll need to install python-telegram-bot
# pip install python-telegram-bot

try:
    import telegram
    from telegram import Bo<PERSON>
    from telegram.error import TelegramError
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    logging.warning("python-telegram-bot not installed. Telegram features disabled.")

logger = logging.getLogger(__name__)

class TelegramService:
    """
    Telegram service for sending trading signals and managing bot interactions
    """
    
    def __init__(self):
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.channel_id = os.getenv('TELEGRAM_CHANNEL_ID')  # For broadcasting signals
        self.admin_chat_id = os.getenv('TELEGRAM_ADMIN_CHAT_ID')
        
        if TELEGRAM_AVAILABLE and self.bot_token:
            self.bot = Bot(token=self.bot_token)
        else:
            self.bot = None
            if not self.bot_token:
                logger.warning("TELEGRAM_BOT_TOKEN not set. Telegram service disabled.")
    
    async def send_signal_message(self, message: str, chat_id: Optional[str] = None):
        """Send trading signal message to Telegram"""
        if not self.bot:
            logger.warning("Telegram bot not available")
            return False
        
        try:
            target_chat = chat_id or self.channel_id
            if target_chat:
                await self.bot.send_message(
                    chat_id=target_chat,
                    text=message,
                    parse_mode='HTML'
                )
                logger.info(f"Signal message sent to Telegram chat {target_chat}")
                return True
            else:
                logger.warning("No Telegram chat ID configured")
                return False
                
        except TelegramError as e:
            logger.error(f"Telegram error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error sending Telegram message: {str(e)}")
            return False
    
    async def send_admin_alert(self, message: str):
        """Send alert message to admin"""
        if self.admin_chat_id:
            return await self.send_signal_message(message, self.admin_chat_id)
        return False
    
    async def broadcast_to_subscribers(self, message: str, subscriber_ids: List[str]):
        """Broadcast message to multiple subscribers"""
        if not self.bot:
            return []
        
        results = []
        for chat_id in subscriber_ids:
            try:
                await self.bot.send_message(
                    chat_id=chat_id,
                    text=message,
                    parse_mode='HTML'
                )
                results.append({'chat_id': chat_id, 'success': True})
            except TelegramError as e:
                logger.error(f"Failed to send to {chat_id}: {str(e)}")
                results.append({'chat_id': chat_id, 'success': False, 'error': str(e)})
        
        return results
    
    def format_buy_signal(self, signal_data: Dict[str, Any]) -> str:
        """Format buy signal for Telegram"""
        stock_code = signal_data.get('stock_code', '')
        buy_price = signal_data.get('buy_price', 0)
        tp1 = signal_data.get('tp1', 0)
        tp2 = signal_data.get('tp2', 0)
        tp3 = signal_data.get('tp3', 0)
        sl = signal_data.get('sl', 0)
        
        # Calculate risk-reward
        risk = buy_price - sl if buy_price and sl else 0
        reward = tp1 - buy_price if tp1 and buy_price else 0
        rr_ratio = reward / risk if risk > 0 else 0
        
        return f"""
🟢 <b>إشارة شراء جديدة</b>

📈 <b>السهم:</b> {stock_code}
💰 <b>سعر الشراء:</b> {buy_price:.2f} جنيه

🎯 <b>الأهداف:</b>
   الأول: {tp1:.2f} جنيه
   الثاني: {tp2:.2f} جنيه
   الثالث: {tp3:.2f} جنيه

⛔ <b>وقف الخسارة:</b> {sl:.2f} جنيه
📊 <b>نسبة المخاطرة/العائد:</b> 1:{rr_ratio:.2f}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    def format_sell_signal(self, signal_data: Dict[str, Any], pnl: Optional[float] = None) -> str:
        """Format sell signal for Telegram"""
        stock_code = signal_data.get('stock_code', '')
        sell_price = signal_data.get('sell_price', 0)
        
        pnl_text = ""
        pnl_emoji = ""
        if pnl is not None:
            if pnl > 0:
                pnl_emoji = "📈"
                pnl_text = f"\n💰 <b>الربح:</b> +{pnl:.2f}%"
            elif pnl < 0:
                pnl_emoji = "📉"
                pnl_text = f"\n💰 <b>الخسارة:</b> {pnl:.2f}%"
            else:
                pnl_emoji = "🔄"
                pnl_text = f"\n💰 <b>النتيجة:</b> {pnl:.2f}% (تعادل)"
        
        return f"""
🔴 <b>إشارة بيع</b>

📈 <b>السهم:</b> {stock_code}
💰 <b>سعر البيع:</b> {sell_price:.2f} جنيه{pnl_text}
{pnl_emoji} <b>تم إغلاق الصفقة</b>

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    def format_tp_signal(self, signal_data: Dict[str, Any], tp_number: str) -> str:
        """Format take profit signal for Telegram"""
        stock_code = signal_data.get('stock_code', '')
        tp_price = signal_data.get(f'tp{tp_number}', 0)
        new_sl = signal_data.get('sl', 0)
        
        return f"""
🎯 <b>تحقق الهدف {tp_number}</b>

📈 <b>السهم:</b> {stock_code}
💰 <b>سعر الهدف:</b> {tp_price:.2f} جنيه
🔄 <b>وقف الخسارة الجديد:</b> {new_sl:.2f} جنيه
✅ <b>نقل الصفقة لمنطقة الأمان</b>

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    def format_stop_loss_signal(self, signal_data: Dict[str, Any]) -> str:
        """Format stop loss signal for Telegram"""
        stock_code = signal_data.get('stock_code', '')
        sl_price = signal_data.get('sl', 0)
        
        return f"""
⛔ <b>تفعيل وقف الخسارة</b>

📈 <b>السهم:</b> {stock_code}
💰 <b>سعر التنفيذ:</b> {sl_price:.2f} جنيه
❌ <b>تم إغلاق الصفقة</b>

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    async def send_daily_summary(self, summary_data: Dict[str, Any]):
        """Send daily trading summary"""
        if not self.bot or not self.channel_id:
            return False
        
        try:
            total_signals = summary_data.get('total_signals', 0)
            active_signals = summary_data.get('active_signals', 0)
            closed_signals = summary_data.get('closed_signals', 0)
            win_rate = summary_data.get('win_rate', 0)
            total_pnl = summary_data.get('total_pnl', 0)
            
            summary_message = f"""
📊 <b>ملخص التداول اليومي</b>

🔢 <b>إجمالي الإشارات:</b> {total_signals}
🟢 <b>الإشارات النشطة:</b> {active_signals}
🔴 <b>الإشارات المغلقة:</b> {closed_signals}
📈 <b>معدل النجاح:</b> {win_rate:.1f}%
💰 <b>إجمالي الربح/الخسارة:</b> {total_pnl:+.2f}%

📅 {datetime.now().strftime('%Y-%m-%d')}
"""
            
            await self.bot.send_message(
                chat_id=self.channel_id,
                text=summary_message,
                parse_mode='HTML'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending daily summary: {str(e)}")
            return False
    
    async def test_connection(self) -> bool:
        """Test Telegram bot connection"""
        if not self.bot:
            return False
        
        try:
            me = await self.bot.get_me()
            logger.info(f"Telegram bot connected: {me.username}")
            return True
        except Exception as e:
            logger.error(f"Telegram connection test failed: {str(e)}")
            return False
    
    async def send_warning_message(self, message: str, chat_id: Optional[str] = None):
        """Send warning message to Telegram (typically to admin)"""
        if not self.bot:
            logger.warning("Telegram bot not available for warning message")
            return False
        
        try:
            # Send warnings to admin chat if available, otherwise to channel
            target_chat = chat_id or self.admin_chat_id or self.channel_id
            if target_chat:
                await self.bot.send_message(
                    chat_id=target_chat,
                    text=message,
                    parse_mode='HTML'
                )
                logger.info(f"Warning message sent to Telegram: {target_chat}")
                return True
            else:
                logger.warning("No Telegram chat ID configured for warnings")
                return False
                
        except Exception as e:
            logger.error(f"Error sending warning message to Telegram: {str(e)}")
            return False

# Global telegram service instance
telegram_service = TelegramService()
