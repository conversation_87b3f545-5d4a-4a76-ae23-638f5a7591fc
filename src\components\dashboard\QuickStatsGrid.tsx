import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, BarChart3, Brain } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import StockAnalysisLink from '@/components/analysis/StockAnalysisLink';
import { useAnalysis } from '@/hooks/useAnalysis';

interface StockData {
  symbol: string;
  change_percent: number;
  volume: number;
  market_cap?: number;
  turnover?: number;
  current_price?: number;
  high_price?: number;
  low_price?: number;
  bid_price?: number;
  ask_price?: number;
  qualityScore?: number;
  activityScore?: number;
  opportunityScore?: number;
  riskLevel?: string;
  signal?: string;
}

const QuickStatsGrid = () => {
  const { openAnalysis } = useAnalysis();

  // Fetch top gainers with enhanced criteria
  const { data: topGainers, isLoading: topGainersLoading } = useQuery({
    queryKey: ['top_gainers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select('symbol, change_percent, volume, market_cap, turnover')
        .not('symbol', 'like', '*EGX*') // Exclude market indices
        .gt('change_percent', 0) // Only positive performers
        .gt('volume', 100000) // Minimum volume for liquidity
        .not('market_cap', 'is', null) // Must have market cap data
        .gt('market_cap', 50000000) // Minimum market cap of 50M EGP
        .order('change_percent', { ascending: false })
        .limit(5);
      
      if (error) throw error;
      
      // Further filter and rank by quality score
      const qualityRanked = (data || []).map(stock => ({
        ...stock,
        qualityScore: calculateQualityScore(stock)
      })).sort((a, b) => b.qualityScore - a.qualityScore).slice(0, 3);
      
      return qualityRanked;
    },
    refetchInterval: 60000, // Refetch every minute
  });
  // Fetch most active by volume with enhanced criteria
  const { data: topVolume, isLoading: topVolumeLoading } = useQuery({
    queryKey: ['top_volume'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select('symbol, volume, turnover, market_cap, change_percent')
        .not('symbol', 'like', '*EGX*') // Exclude market indices
        .gt('volume', 0) // Exclude stocks with zero or null volume (delisted/suspended)
        .not('market_cap', 'is', null) // Must have market cap data
        .gt('turnover', 1000000) // Minimum turnover of 1M EGP for significant activity
        .order('volume', { ascending: false })
        .limit(5);
      
      if (error) throw error;
      
      // Rank by activity score (volume + turnover consideration)
      const activityRanked = (data || []).map(stock => ({
        ...stock,
        activityScore: calculateActivityScore(stock)
      })).sort((a, b) => b.activityScore - a.activityScore).slice(0, 3);
      
      return activityRanked;
    },
    refetchInterval: 60000,
  });
  // Fetch investment opportunities with sophisticated criteria
  const { data: opportunities, isLoading: opportunitiesLoading } = useQuery({
    queryKey: ['opportunities'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stocks_realtime')
        .select('symbol, change_percent, volume, market_cap, turnover, current_price, high_price, low_price, bid_price, ask_price')
        .not('symbol', 'like', '*EGX*') // Exclude market indices
        .gt('volume', 500000) // Minimum volume for liquidity
        .not('market_cap', 'is', null) // Must have market cap data
        .gt('market_cap', 100000000) // Minimum market cap of 100M EGP for stability
        .or('change_percent.gte.2,change_percent.lte.-2') // High volatility
        .order('volume', { ascending: false })
        .limit(10);
      
      if (error) throw error;
      
      // Apply sophisticated filtering and ranking
      const opportunityRanked = (data || []).map(stock => ({
        ...stock,
        opportunityScore: calculateOpportunityScore(stock),
        riskLevel: calculateRiskLevel(stock),
        signal: getAdvancedOpportunitySignal(stock)
      })).filter(stock => stock.opportunityScore > 0.3) // Filter out low-quality opportunities
        .sort((a, b) => b.opportunityScore - a.opportunityScore)
        .slice(0, 3);
      
      return opportunityRanked;
    },
    refetchInterval: 60000,
  });
  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    }
    return `${(volume / 1000).toFixed(0)}K`;
  };

  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 1000000000) {
      return `${(marketCap / 1000000000).toFixed(1)}B`;
    }
    if (marketCap >= 1000000) {
      return `${(marketCap / 1000000).toFixed(0)}M`;
    }
    return `${(marketCap / 1000).toFixed(0)}K`;
  };
  // Enhanced calculation functions
  const calculateQualityScore = (stock: StockData) => {
    let score = 0;
    
    // Performance weight (40%)
    const performanceScore = Math.min(stock.change_percent / 10, 1) * 0.4;
    score += performanceScore;
    
    // Liquidity weight (30%)
    const liquidityScore = Math.min(stock.volume / 10000000, 1) * 0.3;
    score += liquidityScore;
    
    // Market cap stability weight (30%)
    const marketCapScore = (stock.market_cap || 0) > 500000000 ? 0.3 : 
                          (stock.market_cap || 0) > 100000000 ? 0.2 : 0.1;
    score += marketCapScore;
    
    return score;
  };

  const calculateActivityScore = (stock: StockData) => {
    let score = 0;
    
    // Volume weight (50%)
    const volumeScore = Math.min(stock.volume / 50000000, 1) * 0.5;
    score += volumeScore;
    
    // Turnover weight (40%)
    const turnoverScore = Math.min((stock.turnover || 0) / 100000000, 1) * 0.4;
    score += turnoverScore;
    
    // Market presence weight (10%)
    const presenceScore = (stock.market_cap || 0) > 1000000000 ? 0.1 : 0.05;
    score += presenceScore;
    
    return score;
  };

  const calculateOpportunityScore = (stock: StockData) => {
    let score = 0;
    
    // Volatility opportunity (35%)
    const volatilityScore = Math.min(Math.abs(stock.change_percent) / 10, 1) * 0.35;
    score += volatilityScore;
    
    // Liquidity safety (25%)
    const liquidityScore = Math.min(stock.volume / 5000000, 1) * 0.25;
    score += liquidityScore;
    
    // Market cap stability (25%)
    const stabilityScore = (stock.market_cap || 0) > 500000000 ? 0.25 : 
                          (stock.market_cap || 0) > 200000000 ? 0.15 : 0.05;
    score += stabilityScore;
    
    // Bid-ask spread efficiency (15%)
    const spreadScore = stock.bid_price && stock.ask_price && stock.current_price ? 
      Math.max(0, 0.15 - ((stock.ask_price - stock.bid_price) / stock.current_price) * 0.15) : 0.05;
    score += spreadScore;
    
    return score;
  };

  const calculateRiskLevel = (stock: StockData) => {
    const volatility = Math.abs(stock.change_percent);
    const liquidityRisk = stock.volume < 1000000 ? 'عالي' : stock.volume < 5000000 ? 'متوسط' : 'منخفض';
    const priceRisk = volatility > 5 ? 'عالي' : volatility > 2 ? 'متوسط' : 'منخفض';
    
    if (liquidityRisk === 'عالي' || priceRisk === 'عالي') return 'عالي';
    if (liquidityRisk === 'متوسط' || priceRisk === 'متوسط') return 'متوسط';
    return 'منخفض';
  };

  const getAdvancedOpportunitySignal = (stock: StockData) => {
    const changePercent = stock.change_percent;
    const volume = stock.volume;
    const pricePosition = stock.current_price && stock.high_price && stock.low_price ? 
      stock.current_price / ((stock.high_price + stock.low_price) / 2) : 1;
    
    if (changePercent > 5 && volume > 5000000) return 'اختراق قوي للمقاومة';
    if (changePercent > 3 && pricePosition > 0.95) return 'كسر مستوى المقاومة';
    if (changePercent < -5 && volume > 5000000) return 'فرصة شراء ممتازة';
    if (changePercent < -3 && pricePosition < 0.85) return 'فرصة شراء جيدة';
    if (Math.abs(changePercent) > 2 && volume > 2000000) return 'حركة نشطة';
    return 'نمط متذبذب';
  };

  const getOpportunitySignal = (changePercent: number) => {
    if (changePercent > 3) return 'كسر مستوى المقاومة';
    if (changePercent < -3) return 'فرصة شراء';
    return 'نمط متذبذب';
  };

  const getOpportunityStrength = (volume: number, changePercent: number) => {
    if (volume > 1000000 && Math.abs(changePercent) > 3) return 'قوي';
    if (volume > 500000 || Math.abs(changePercent) > 2) return 'متوسط';
    return 'ضعيف';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">      {/* Top Performers */}
      <Card className="border-2 border-green-200 hover:border-green-300 hover:shadow-elevated transition-all duration-500 card-hover bg-gradient-to-br from-green-50 to-emerald-50 translate-y-0 opacity-100">
        <CardHeader>
          <CardTitle className="text-center text-green-700 flex items-center justify-center gap-2">
            <TrendingUp className="h-5 w-5" />
            أفضل الأسهم أداءً اليوم
          </CardTitle>
        </CardHeader>        <CardContent>
          <div className="space-y-3">
            {topGainersLoading ? (
              <div className="text-center py-4">جاري التحليل...</div>
            ) : topGainers?.map((stock, index) => (              <div 
                key={stock.symbol} 
                className="flex justify-between items-center p-3 bg-white/60 rounded-lg border border-green-200 hover:bg-white/80 transition-all duration-300"
              >
                <div className="flex-1">
                  <div className="font-bold text-gray-800">{stock.symbol}</div>
                  <div className="text-xs text-gray-600 flex items-center gap-2">
                    <span>الحجم: {formatVolume(stock.volume)}</span>
                    {stock.market_cap && (
                      <span>• {formatMarketCap(stock.market_cap)}</span>
                    )}
                  </div>
                  <div className="text-xs text-green-600 mt-1">
                    درجة الجودة: {Math.round((stock.qualityScore || 0) * 100)}%
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <div className="bg-market-green text-white shadow-lg font-bold px-3 py-2 rounded-lg text-sm mb-1">
                      {formatPercentage(stock.change_percent)}
                    </div>
                    {stock.turnover && (
                      <div className="text-xs text-gray-600">القيمة: {formatMarketCap(stock.turnover)}</div>
                    )}
                  </div>
                  <StockAnalysisLink 
                    symbol={stock.symbol} 
                    size="sm" 
                    onAnalyze={openAnalysis}
                    showText={false}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>{/* Most Active */}
      <Card className="border-2 border-blue-200 hover:border-blue-300 hover:shadow-elevated transition-all duration-500 card-hover bg-gradient-to-br from-blue-50 to-sky-50 translate-y-0 opacity-100">
        <CardHeader>
          <CardTitle className="text-center text-blue-700 flex items-center justify-center gap-2">
            <BarChart3 className="h-5 w-5" />
            أكثر الأسهم تداولاً
          </CardTitle>
        </CardHeader>        <CardContent>
          <div className="space-y-3">
            {topVolumeLoading ? (
              <div className="text-center py-4">جاري التحميل...</div>
            ) : topVolume?.map((stock, index) => (              <div 
                key={stock.symbol} 
                className="flex justify-between items-center p-3 bg-white/60 rounded-lg border border-blue-200 hover:bg-white/80 transition-all duration-300"
              >
                <div className="flex-1">
                  <span className="font-bold text-gray-800">{stock.symbol}</span>
                  {stock.market_cap && (
                    <div className="text-xs text-gray-600">رأس المال: {formatMarketCap(stock.market_cap)}</div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <div className="text-sm font-semibold text-blue-700 bg-blue-100 px-2 py-1 rounded">
                      {formatVolume(stock.volume)}
                    </div>
                    {stock.turnover && (
                      <div className="text-xs text-gray-600 mt-1">القيمة: {formatMarketCap(stock.turnover)}</div>
                    )}
                  </div>
                  <StockAnalysisLink 
                    symbol={stock.symbol} 
                    size="sm" 
                    onAnalyze={openAnalysis}
                    showText={false}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>      {/* Investment Opportunities */}
      <Card className="border-2 border-purple-200 hover:border-purple-300 hover:shadow-elevated transition-all duration-500 card-hover bg-gradient-to-br from-purple-50 to-violet-50 translate-y-0 opacity-100">
        <CardHeader>
          <CardTitle className="text-center text-purple-700 flex items-center justify-center gap-2">
            <Brain className="h-5 w-5" />
            فرص استثمارية متقدمة
          </CardTitle>
        </CardHeader>        <CardContent>
          <div className="space-y-4">
            {opportunitiesLoading ? (
              <div className="text-center py-4">جاري التحليل...</div>
            ) : opportunities?.map((opportunity, index) => (              <div key={opportunity.symbol} className="p-4 bg-white/70 rounded-lg border border-purple-200 hover:bg-white/90 transition-all duration-300">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex-1">
                    <div className="font-bold text-gray-800 text-lg">{opportunity.symbol}</div>
                    <div className="text-sm text-gray-600">
                      {formatPercentage(opportunity.change_percent)} • {formatVolume(opportunity.volume)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-right">
                      <Badge 
                        className={`mb-1 ${
                          opportunity.riskLevel === 'منخفض' 
                            ? 'bg-green-100 text-green-700 border-green-200' 
                            : opportunity.riskLevel === 'متوسط'
                            ? 'bg-yellow-100 text-yellow-700 border-yellow-200'
                            : 'bg-red-100 text-red-700 border-red-200'
                        }`}
                      >
                        مخاطر {opportunity.riskLevel}
                      </Badge>
                      <div className="text-xs text-gray-500">
                        درجة الفرصة: {Math.round((opportunity.opportunityScore || 0) * 100)}%
                      </div>
                    </div>
                    <StockAnalysisLink 
                      symbol={opportunity.symbol} 
                      size="sm" 
                      onAnalyze={openAnalysis}
                      showText={false}
                    />
                  </div>
                </div>
                <div className="text-sm font-medium text-purple-700 bg-purple-50 px-3 py-2 rounded border border-purple-100">
                  {opportunity.signal}
                </div>
                {opportunity.market_cap && (
                  <div className="text-xs text-gray-500 mt-2">
                    رأس المال السوقي: {formatMarketCap(opportunity.market_cap)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuickStatsGrid;
