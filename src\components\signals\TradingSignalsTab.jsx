import React, { useState, useEffect, useRef } from 'react';
import useWebSocket from 'react-use-websocket';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { 
  useTradingSignals, 
  useLiveRecommendations, 
  useSignalPerformance,
  TradingSignalsAPI 
} from '../../hooks/useTradingSignals';
import { 
  SignalCard, 
  LiveRecommendationCard, 
  SignalStatistics, 
  SignalFilters 
} from './SignalComponents';

// Trading Signals Tab - Priority Feature
const TradingSignalsTab = () => {
  const [liveSignals, setLiveSignals] = useState([]);
  const [filters, setFilters] = useState({
    signalType: 'all',
    timeframe: '1d',
    riskLevel: 'all'
  });
  const [isConnected, setIsConnected] = useState(false);
  const audioRef = useRef(null);

  // Use custom hooks for data fetching
  const { signals: signalHistory, loading: signalsLoading, error: signalsError, refetch: refetchSignals } = useTradingSignals();
  const { recommendations: liveRecommendations, loading: recommendationsLoading, refetch: refetchRecommendations } = useLiveRecommendations();
  const { performance: signalPerformance, loading: performanceLoading } = useSignalPerformance(30);

  // WebSocket connection for real-time signals
  const WS_URL = 'ws://localhost:8003/ws/signals';
  
  const {
    sendMessage,
    lastMessage,
    readyState
  } = useWebSocket(WS_URL, {
    onOpen: () => {
      console.log('WebSocket Connected to Trading Signals');
      setIsConnected(true);
      toast.success('متصل بالإشارات المباشرة');
    },
    onClose: () => {
      console.log('WebSocket Disconnected');
      setIsConnected(false);
      toast.error('انقطع الاتصال بالإشارات المباشرة');
    },
    onError: (error) => {
      console.error('WebSocket Error:', error);
      toast.error('خطأ في الاتصال بالإشارات');
    },
    shouldReconnect: (closeEvent) => true,
    reconnectAttempts: 10,
    reconnectInterval: 3000,
  });

  // Handle new signal from WebSocket
  useEffect(() => {
    if (lastMessage !== null) {
      try {
        const signal = JSON.parse(lastMessage.data);
        handleNewSignal(signal);
      } catch (error) {
        console.error('Error parsing signal data:', error);
      }
    }
  }, [lastMessage]);

  // Load initial signals and statistics
  useEffect(() => {
    loadSignalHistory();
    loadSignalStatistics();
    
    // Request notification permission
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const handleNewSignal = (signal) => {
    console.log('New signal received:', signal);
    
    // Add to live signals (keep only latest 20)
    setLiveSignals(prev => {
      const newSignals = [signal, ...prev.slice(0, 19)];
      return newSignals;
    });
    
    // Add to history
    setSignalHistory(prev => [signal, ...prev]);
    
    // Play notification sound
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Show browser notification
    if (Notification.permission === 'granted') {
      const stockName = signal.stock_name_ar || signal.stock_code;
      const signalTypeAr = getSignalTypeArabic(signal.recommendation_type);
      
      new Notification(`إشارة جديدة: ${stockName}`, {
        body: `${signalTypeAr} - ${signal.message_ar}`,
        icon: '/favicon.ico',
        tag: `signal-${signal.id}`
      });
    }
    
    // Show toast notification
    const signalTypeAr = getSignalTypeArabic(signal.recommendation_type);
    toast.success(`إشارة ${signalTypeAr} جديدة على ${signal.stock_name_ar || signal.stock_code}`);
    
    // Update statistics
    setSignalStats(prev => ({
      ...prev,
      totalToday: prev.totalToday + 1
    }));
  };

  const loadSignalHistory = async () => {
    try {
      const response = await fetch('/api/v1/webhooks/live-recommendations?limit=50');
      const data = await response.json();
      setSignalHistory(data.recommendations || []);
    } catch (error) {
      console.error('Error loading signal history:', error);
      toast.error('خطأ في تحميل تاريخ الإشارات');
    }
  };

  const loadSignalStatistics = async () => {
    try {
      const response = await fetch('/api/v1/webhooks/signal-performance?days=1');
      const data = await response.json();
      setSignalStats({
        totalToday: data.total_signals,
        successfulToday: data.profitable_signals,
        accuracy: data.accuracy_percentage,
        averageReturn: data.average_profit_percentage
      });
    } catch (error) {
      console.error('Error loading signal statistics:', error);
    }
  };

  const getSignalTypeArabic = (type) => {
    const types = {
      'buy': 'شراء',
      'sell': 'بيع',
      'tp1done': 'تحقق الهدف الأول',
      'tp2done': 'تحقق الهدف الثاني', 
      'tp3done': 'تحقق الهدف الثالث',
      'tsl': 'وقف خسارة'
    };
    return types[type] || type;
  };

  const getSignalPriority = (signal) => {
    if (signal.recommendation_type === 'tsl') return 'critical';
    if (signal.recommendation_type === 'buy' || signal.recommendation_type === 'sell') return 'high';
    return 'medium';
  };

  const handleExecuteSignal = (signal) => {
    // This would integrate with trading platform
    toast.success(`تم تنفيذ الإشارة على ${signal.stock_name_ar}`);
  };

  const handleDismissSignal = (signalId) => {
    setLiveSignals(prev => prev.filter(s => s.id !== signalId));
  };

  const handleShareSignal = (signal) => {
    const shareText = `إشارة ${getSignalTypeArabic(signal.recommendation_type)} على ${signal.stock_name_ar}\n${signal.message_ar}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'إشارة تداول',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText);
      toast.success('تم نسخ الإشارة');
    }
  };

  const filteredSignals = liveSignals.filter(signal => {
    if (filters.signalType !== 'all' && !signal.recommendation_type.includes(filters.signalType)) {
      return false;
    }
    return true;
  });

  return (
    <div className="trading-signals-tab bg-gray-50 min-h-screen" dir="rtl">
      {/* Header */}
      <SignalsHeader 
        isConnected={isConnected}
        totalSignals={liveSignals.length}
        stats={signalStats}
      />
      
      {/* Connection Status */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm font-medium">
              {isConnected ? 'متصل بالإشارات المباشرة' : 'غير متصل'}
            </span>
          </div>
          <div className="text-sm text-gray-500">
            آخر تحديث: {liveSignals[0] ? format(new Date(liveSignals[0].created_at), 'HH:mm:ss', { locale: ar }) : 'لا يوجد'}
          </div>
        </div>
      </div>

      {/* Filters */}
      <SignalFilters 
        filters={filters}
        onChange={setFilters}
      />

      {/* Live Signals Section */}
      <div className="px-6 py-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <h2 className="text-lg font-semibold text-gray-900">التوصيات اللحظية </h2>
              <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                مباشر
              </span>
            </div>
          </div>
          
          <div className="divide-y divide-gray-200">
            {filteredSignals.length > 0 ? (
              filteredSignals.map(signal => (
                <LiveSignalItem
                  key={`${signal.id}-${signal.created_at}`}
                  signal={signal}
                  onExecute={handleExecuteSignal}
                  onDismiss={handleDismissSignal}
                  onShare={handleShareSignal}
                />
              ))
            ) : (
              <div className="px-6 py-8 text-center">
                <div className="text-gray-400 mb-2">
                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5V3h0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">لا توجد إشارات مباشرة</h3>
                <p className="text-gray-500">
                  {isConnected ? 'في انتظار إشارات جديدة...' : 'الرجاء التحقق من الاتصال'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Signal History */}
      <SignalHistorySection 
        signals={signalHistory}
        onViewDetails={(signal) => console.log('View details:', signal)}
      />

      {/* Signal Performance Widget */}
      <SignalPerformanceWidget 
        stats={signalStats}
        onRefresh={loadSignalStatistics}
      />

      {/* Audio notification */}
      <audio 
        ref={audioRef} 
        src="/notification-sound.mp3" 
        preload="auto"
        style={{ display: 'none' }}
      />
    </div>
  );
};

// Signals Header Component
const SignalsHeader = ({ isConnected, totalSignals, stats }) => {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">التوصيات اللحظية</h1>
          <p className="text-gray-600 mt-1">إشارات التداول المباشرة مع الذكاء الاصطناعي</p>
        </div>
        
        <div className="flex items-center space-x-6 space-x-reverse">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{totalSignals}</div>
            <div className="text-sm text-gray-500">إشارات مباشرة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.accuracy}%</div>
            <div className="text-sm text-gray-500">دقة الإشارات</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.totalToday}</div>
            <div className="text-sm text-gray-500">إشارات اليوم</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Live Signal Item Component
const LiveSignalItem = ({ signal, onExecute, onDismiss, onShare }) => {
  const [timeLeft, setTimeLeft] = useState(0);
  
  useEffect(() => {
    if (signal.expires_at) {
      const timer = setInterval(() => {
        const now = new Date();
        const expiry = new Date(signal.expires_at);
        const diff = expiry - now;
        setTimeLeft(Math.max(0, Math.floor(diff / 1000)));
      }, 1000);
      
      return () => clearInterval(timer);
    }
  }, [signal.expires_at]);

  const formatTimeLeft = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getSignalTypeColor = (type) => {
    const colors = {
      'buy': 'bg-green-100 text-green-800',
      'sell': 'bg-red-100 text-red-800',
      'tp1done': 'bg-blue-100 text-blue-800',
      'tp2done': 'bg-blue-100 text-blue-800',
      'tp3done': 'bg-blue-100 text-blue-800',
      'tsl': 'bg-orange-100 text-orange-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'critical': 'border-r-red-500',
      'high': 'border-r-orange-500',
      'medium': 'border-r-blue-500',
      'low': 'border-r-gray-500'
    };
    return colors[priority] || 'border-r-gray-500';
  };

  return (
    <div className={`p-6 border-r-4 ${getPriorityColor(signal.priority)} hover:bg-gray-50 transition-colors`}>
      {/* Signal Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3 space-x-reverse mb-2">
            <h3 className="text-lg font-semibold text-gray-900">
              {signal.stock_name_ar || signal.stock_code}
            </h3>
            <span className="text-sm text-gray-500">({signal.stock_code})</span>
            <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getSignalTypeColor(signal.recommendation_type)}`}>
              {signal.recommendation_type_ar}
            </span>
          </div>
          
          {signal.expires_at && timeLeft > 0 && (
            <div className="flex items-center space-x-1 space-x-reverse text-sm text-orange-600">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>ينتهي خلال: {formatTimeLeft(timeLeft)}</span>
            </div>
          )}
        </div>
        
        <div className="text-left">
          <div className="text-sm text-gray-500 mb-1">
            {format(new Date(signal.created_at), 'HH:mm', { locale: ar })}
          </div>
          {signal.confidence && (
            <div className="text-sm text-blue-600 font-medium">
              دقة: {(signal.confidence * 100).toFixed(0)}%
            </div>
          )}
        </div>
      </div>

      {/* Signal Content */}
      <div className="mb-4">
        <div className="text-gray-700 mb-3 whitespace-pre-line">
          {signal.message_ar}
        </div>
        
        {/* Price Information */}
        {(signal.entry_price || signal.target_price || signal.stop_loss) && (
          <div className="grid grid-cols-3 gap-4 mt-3 p-3 bg-gray-50 rounded-lg">
            {signal.entry_price && (
              <div className="text-center">
                <div className="text-sm text-gray-500">سعر الدخول</div>
                <div className="font-semibold text-green-600">{signal.entry_price} جنيه</div>
              </div>
            )}
            {signal.target_price && (
              <div className="text-center">
                <div className="text-sm text-gray-500">الهدف</div>
                <div className="font-semibold text-blue-600">{signal.target_price} جنيه</div>
              </div>
            )}
            {signal.stop_loss && (
              <div className="text-center">
                <div className="text-sm text-gray-500">وقف الخسارة</div>
                <div className="font-semibold text-red-600">{signal.stop_loss} جنيه</div>
              </div>
            )}
          </div>
        )}

        {/* Risk/Reward Ratio */}
        {signal.risk_reward_ratio && (
          <div className="mt-3 flex items-center space-x-2 space-x-reverse">
            <span className="text-sm text-gray-500">نسبة المخاطرة/المكافأة:</span>
            <span className="font-medium text-purple-600">1:{signal.risk_reward_ratio}</span>
          </div>
        )}
      </div>

      {/* Signal Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <button 
            onClick={() => onExecute(signal)}
            disabled={timeLeft === 0}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            تنفيذ الإشارة
          </button>
          <button 
            onClick={() => onShare(signal)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            مشاركة
          </button>
          <button 
            onClick={() => onDismiss(signal.id)}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            إخفاء
          </button>
        </div>
        
        <div className="text-xs text-gray-500">
          المصدر: {signal.source || 'نظام التحليل الذكي'}
        </div>
      </div>
    </div>
  );
};

// Signal Filters Component
const SignalFilters = ({ filters, onChange }) => {
  const filterOptions = {
    signalType: [
      { value: 'all', label: 'جميع الإشارات' },
      { value: 'buy', label: 'إشارات الشراء' },
      { value: 'sell', label: 'إشارات البيع' },
      { value: 'tp', label: 'تحقق الأهداف' },
      { value: 'sl', label: 'وقف الخسارة' }
    ],
    timeframe: [
      { value: '1d', label: 'اليوم' },
      { value: '3d', label: '3 أيام' },
      { value: '1w', label: 'أسبوع' },
      { value: '1m', label: 'شهر' }
    ],
    riskLevel: [
      { value: 'all', label: 'جميع المستويات' },
      { value: 'low', label: 'مخاطرة منخفضة' },
      { value: 'medium', label: 'مخاطرة متوسطة' },
      { value: 'high', label: 'مخاطرة عالية' }
    ]
  };

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center space-x-6 space-x-reverse">
        <div className="flex items-center space-x-2 space-x-reverse">
          <label className="text-sm font-medium text-gray-700">نوع الإشارة:</label>
          <select 
            value={filters.signalType}
            onChange={(e) => onChange({...filters, signalType: e.target.value})}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {filterOptions.signalType.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <label className="text-sm font-medium text-gray-700">الفترة الزمنية:</label>
          <select 
            value={filters.timeframe}
            onChange={(e) => onChange({...filters, timeframe: e.target.value})}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {filterOptions.timeframe.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <label className="text-sm font-medium text-gray-700">مستوى المخاطرة:</label>
          <select 
            value={filters.riskLevel}
            onChange={(e) => onChange({...filters, riskLevel: e.target.value})}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {filterOptions.riskLevel.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
};

// Signal History Section
const SignalHistorySection = ({ signals, onViewDetails }) => {
  const [expanded, setExpanded] = useState(false);
  
  return (
    <div className="px-6 py-4">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div 
          className="px-6 py-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50"
          onClick={() => setExpanded(!expanded)}
        >
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">سجل الإشارات</h3>
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="text-sm text-gray-500">{signals.length} إشارة</span>
              <svg 
                className={`w-5 h-5 text-gray-400 transition-transform ${expanded ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
        
        {expanded && (
          <div className="max-h-96 overflow-y-auto">
            {signals.slice(0, 20).map(signal => (
              <div 
                key={signal.id} 
                className="px-6 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                onClick={() => onViewDetails(signal)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900">
                      {signal.stock_name_ar || signal.stock_code} - {signal.recommendation_type_ar}
                    </div>
                    <div className="text-sm text-gray-500 truncate">
                      {signal.message_ar}
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {format(new Date(signal.created_at), 'HH:mm', { locale: ar })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Signal Performance Widget
const SignalPerformanceWidget = ({ stats, onRefresh }) => {
  return (
    <div className="px-6 py-4">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">أداء الإشارات</h3>
            <button 
              onClick={onRefresh}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              تحديث
            </button>
          </div>
        </div>
        
        <div className="px-6 py-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.accuracy}%</div>
              <div className="text-sm text-gray-600 mt-1">دقة الإشارات</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.successfulToday}</div>
              <div className="text-sm text-gray-600 mt-1">إشارات ناجحة اليوم</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.averageReturn}%</div>
              <div className="text-sm text-gray-600 mt-1">متوسط العائد</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{stats.totalToday}</div>
              <div className="text-sm text-gray-600 mt-1">إجمالي إشارات اليوم</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradingSignalsTab;
