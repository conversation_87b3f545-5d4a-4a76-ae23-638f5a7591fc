import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Search, 
  RefreshCw,
  BarChart3,
  Volume2,
  Loader2
} from 'lucide-react';
import { useSimpleLiveData, SimpleStockData } from '@/hooks/useSimpleLiveData';
import { cn } from '@/lib/utils';

interface EnhancedLiveTradingScreenProps {
  className?: string;
}

const EnhancedLiveTradingScreen: React.FC<EnhancedLiveTradingScreenProps> = ({ className }) => {
  const { data: stocks, isLoading, error, refetch } = useSimpleLiveData();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'change_percent' | 'volume' | 'turnover' | 'symbol'>('change_percent');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'all' | 'gainers' | 'losers' | 'active'>('all');

  // تصفية وترتيب البيانات
  const filteredStocks = useMemo(() => {
    let filtered = stocks || [];

    // تصفية حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(stock => 
        stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (stock.name && stock.name.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // تصفية حسب نوع العرض
    switch (viewMode) {
      case 'gainers':
        filtered = filtered.filter(stock => stock.change_percent > 0);
        break;
      case 'losers':
        filtered = filtered.filter(stock => stock.change_percent < 0);
        break;
      case 'active':
        filtered = filtered.filter(stock => (stock.turnover || 0) > 100000);
        break;
    }

    // ترتيب البيانات
    filtered.sort((a, b) => {
      const multiplier = sortOrder === 'desc' ? -1 : 1;
      
      switch (sortBy) {
        case 'change_percent':
          return (a.change_percent - b.change_percent) * multiplier;
        case 'volume':
          return ((a.volume || 0) - (b.volume || 0)) * multiplier;
        case 'turnover':
          return ((a.turnover || 0) - (b.turnover || 0)) * multiplier;
        case 'symbol':
          return a.symbol.localeCompare(b.symbol) * multiplier;
        default:
          return 0;
      }
    });

    return filtered;
  }, [stocks, searchTerm, sortBy, sortOrder, viewMode]);

  // حساب إحصائيات السوق من البيانات المتاحة
  const marketStats = useMemo(() => {
    if (!stocks) return null;
    
    const gainers = stocks.filter(s => s.change_percent > 0).length;
    const losers = stocks.filter(s => s.change_percent < 0).length;
    const unchanged = stocks.filter(s => s.change_percent === 0).length;
    const totalVolume = stocks.reduce((sum, s) => sum + (s.volume || 0), 0);
    const totalTurnover = stocks.reduce((sum, s) => sum + (s.turnover || 0), 0);

    return {
      totalStocks: stocks.length,
      gainers,
      losers,
      unchanged,
      totalVolume,
      totalTurnover,
    };
  }, [stocks]);

  // تنسيق الأرقام
  const formatNumber = (num: number) => {
    if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toLocaleString();
  };

  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  // مكون صف الأسهم
  const StockRow: React.FC<{ stock: SimpleStockData }> = ({ stock }) => {
    const changeColor = stock.change_percent > 0 ? 'text-green-600' :
                       stock.change_percent < 0 ? 'text-red-600' : 'text-gray-600';
    
    const changeIcon = stock.change_percent > 0 ? <TrendingUp className="w-4 h-4" /> :
                      stock.change_percent < 0 ? <TrendingDown className="w-4 h-4" /> : null;

    return (
      <div className="grid grid-cols-6 gap-4 p-3 border-b hover:bg-gray-50 transition-colors">
        {/* الرمز والاسم */}
        <div className="flex flex-col">
          <span className="font-semibold text-gray-900">{stock.symbol}</span>
          {stock.name && (
            <span className="text-sm text-gray-500 truncate">{stock.name}</span>
          )}
        </div>

        {/* السعر الحالي */}
        <div className="flex items-center font-semibold">
          {formatPrice(stock.current_price)} ج.م
        </div>

        {/* التغير */}
        <div className={cn("flex items-center gap-1", changeColor)}>
          {changeIcon}
          <span className="font-medium">
            {stock.change_percent.toFixed(2)}%
          </span>
        </div>

        {/* الحجم */}
        <div className="flex items-center gap-1">
          <Volume2 className="w-4 h-4 text-gray-400" />
          <span>{formatNumber(stock.volume || 0)}</span>
        </div>

        {/* القيمة المتداولة */}
        <div className="flex items-center gap-1">
          <BarChart3 className="w-4 h-4 text-gray-400" />
          <span>{formatNumber(stock.turnover || 0)}</span>
        </div>

        {/* وقت التحديث */}
        <div className="text-sm text-gray-500">
          {new Date(stock.updated_at).toLocaleTimeString('ar-EG')}
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            خطأ في تحميل البيانات: {error.message}
            <Button onClick={() => refetch()} className="mt-2">
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* إحصائيات السوق */}
      {marketStats && (
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{marketStats.totalStocks}</div>
              <div className="text-sm text-gray-500">إجمالي الأسهم</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{marketStats.gainers}</div>
              <div className="text-sm text-gray-500">مرتفعة</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{marketStats.losers}</div>
              <div className="text-sm text-gray-500">منخفضة</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-600">{marketStats.unchanged}</div>
              <div className="text-sm text-gray-500">ثابتة</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-xl font-bold text-purple-600">
                {formatNumber(marketStats.totalTurnover)}
              </div>
              <div className="text-sm text-gray-500">القيمة المتداولة</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* أدوات التحكم */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-600" />
              الشاشة اللحظية المحسنة
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                onClick={() => refetch()}
                disabled={isLoading}
                variant="outline"
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                تحديث
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* شريط البحث */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="البحث عن سهم..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* أزرار التصفية */}
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'الكل' },
              { key: 'gainers', label: 'المرتفعة' },
              { key: 'losers', label: 'المنخفضة' },
              { key: 'active', label: 'الأكثر نشاطاً' }            ].map(mode => (
              <Button
                key={mode.key}
                variant={viewMode === mode.key ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode(mode.key as 'all' | 'gainers' | 'losers' | 'active')}
              >
                {mode.label}
              </Button>
            ))}
          </div>

          {/* أزرار الترتيب */}
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'change_percent', label: 'نسبة التغير' },
              { key: 'volume', label: 'الحجم' },
              { key: 'turnover', label: 'القيمة المتداولة' },
              { key: 'symbol', label: 'الرمز' }
            ].map(sort => (
              <Button
                key={sort.key}
                variant={sortBy === sort.key ? "default" : "outline"}
                size="sm"                onClick={() => {
                  if (sortBy === sort.key) {
                    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
                  } else {
                    setSortBy(sort.key as 'change_percent' | 'volume' | 'turnover' | 'symbol');
                    setSortOrder('desc');
                  }
                }}
              >
                {sort.label}
                {sortBy === sort.key && (
                  <span className="ml-1">
                    {sortOrder === 'desc' ? '↓' : '↑'}
                  </span>
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* جدول البيانات */}
      <Card>
        <CardHeader>
          <div className="grid grid-cols-6 gap-4 text-sm font-medium text-gray-500">
            <div>السهم</div>
            <div>السعر</div>
            <div>التغير %</div>
            <div>الحجم</div>
            <div>القيمة المتداولة</div>
            <div>آخر تحديث</div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                <span className="mr-2">جاري تحميل البيانات...</span>
              </div>
            ) : filteredStocks.length === 0 ? (
              <div className="text-center p-8 text-gray-500">
                لا توجد بيانات متاحة
              </div>
            ) : (
              <div className="space-y-0">
                {filteredStocks.map((stock) => (
                  <StockRow key={stock.symbol} stock={stock} />
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* معلومات إضافية */}
      <div className="text-center text-sm text-gray-500">
        عدد الأسهم المعروضة: {filteredStocks.length} من أصل {stocks?.length || 0}
      </div>
    </div>
  );
};

export default EnhancedLiveTradingScreen;
