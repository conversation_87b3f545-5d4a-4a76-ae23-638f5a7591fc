
import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Activity, BarChart3, Users } from 'lucide-react';
import { useMarketOverview } from '@/hooks/useMarketOverview';

const MarketOverview = () => {
  const { data: marketData, isLoading, error } = useMarketOverview();

  const isVisible = true; // always true (could animate in, omitted for brevity)

  const formatNumber = (num: number | null | undefined) => {
    if (num === null || num === undefined) return '--';
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  const formatLargeNumber = (num: number | null | undefined) => {
    if (num == null) return '--';
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  if (isLoading) {
    return (
      <div className="mb-8">
        <Card>
          <CardContent className="p-8 text-center text-blue-800">جاري التحميل...</CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8">
        <Card>
          <CardContent className="p-8 text-center text-red-600">حدث خطأ أثناء تحميل بيانات السوق</CardContent>
        </Card>
      </div>
    );
  }

  const cards = [
    {
      title: "EGX 30",
      icon: BarChart3,
      value: marketData?.egx30?.current_value ?? '--',
      change: marketData?.egx30?.change_amount,
      changePercent: marketData?.egx30?.change_percent,
      gradient: "egx-gradient",
      textColor: "text-white",
      description: "المؤشر الرئيسي"
    },
    {
      title: "EGX 70",
      icon: Activity,
      value: marketData?.egx70?.current_value ?? '--',
      change: marketData?.egx70?.change_amount,
      changePercent: marketData?.egx70?.change_percent,
      gradient: "bg-gradient-to-br from-egx-blue-600 to-egx-blue-800",
      textColor: "text-white",
      description: "الشركات المتوسطة"
    },
    {
      title: "حجم التداول",
      icon: Activity,
      value: marketData?.totalVolume ? `${formatNumber(marketData.totalVolume)}B` : '--',
      subValue: marketData?.totalValue !== null && marketData?.totalValue !== undefined ? `القيمة: ${formatNumber(marketData.totalValue)}M EGP` : '--',
      gradient: "gold-gradient",
      textColor: "text-white",
      description: "إجمالي الحجم"
    },
    {
      title: "مؤشر السوق",
      icon: Users,
      gradient: "bg-gradient-to-br from-gray-700 to-gray-900",
      textColor: "text-white",
      description: "توزيع الأسهم",
      isComposite: true
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {cards.map((card, index) => (
        <Card
          key={card.title}
          className={`${card.gradient} text-white border-none hover:shadow-elevated transition-all duration-500 card-hover overflow-hidden relative group
            ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          style={{
            animationDelay: `${index * 150}ms`,
            animation: isVisible ? 'slide-up 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards' : 'none'
          }}
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 70% 20%, rgba(255,255,255,0.2) 0%, transparent 50%)`
            }}></div>
          </div>
          {/* Shine Effect */}
          <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-100%] transition-transform duration-1000"></div>
          </div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
            <div>
              <CardTitle className={`text-sm font-medium ${card.textColor}/90`}>
                {card.title}
              </CardTitle>
              <p className={`text-xs ${card.textColor}/70 mt-1`}>{card.description}</p>
            </div>
            <card.icon className={`h-5 w-5 ${card.textColor}/80`} />
          </CardHeader>
          <CardContent className="relative z-10">
            {card.isComposite ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center justify-between p-2 bg-white/10 rounded-lg backdrop-blur-sm">
                    <span className="text-green-300 font-medium">صاعد</span>
                    <Badge className="bg-market-green hover:bg-market-green/80 text-white font-bold">
                      {marketData?.gainers ?? '--'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-white/10 rounded-lg backdrop-blur-sm">
                    <span className="text-red-300 font-medium">هابط</span>
                    <Badge className="bg-market-red hover:bg-market-red/80 text-white font-bold">
                      {marketData?.losers ?? '--'}
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/10 rounded-lg backdrop-blur-sm">
                  <span className="text-gray-300 font-medium">ثابت</span>
                  <Badge variant="secondary" className="bg-gray-500 hover:bg-gray-600 text-white font-bold">
                    {marketData?.unchanged ?? '--'}
                  </Badge>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="text-3xl font-bold mb-2">
                  {typeof card.value === 'string' ? card.value : formatNumber(card.value)}
                </div>
                {card.change !== undefined && card.change !== null && (
                  <div className="flex items-center text-sm">
                    {card.change >= 0 ? (
                      <TrendingUp className="h-4 w-4 mr-1 text-green-300" />
                    ) : (
                      <TrendingDown className="h-4 w-4 mr-1 text-red-300" />
                    )}
                    <span className={card.change >= 0 ? 'text-green-300' : 'text-red-300'}>
                      {formatNumber(Math.abs(card.change))} ({formatNumber(Math.abs(card.changePercent))}%)
                    </span>
                  </div>
                )}
                {card.subValue && (
                  <div className={`text-sm ${card.textColor}/80`}>
                    {card.subValue}
                  </div>
                )}
                {/* Progress indicator for volume */}
                {card.title === "حجم التداول" && (
                  <div className="mt-3">
                    <div className="w-full bg-white/20 h-1.5 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-white/60 rounded-full transition-all duration-1000"
                        style={{
                          width: `${
                            marketData?.totalVolume && marketData?.totalVolume > 0
                              ? Math.min(100, (marketData.totalVolume / 5) * 100)
                              : 0
                          }%`
                        }}
                      ></div>
                    </div>
                    <div className="text-xs text-white/70 mt-1">من إجمالي 5B</div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default MarketOverview;
