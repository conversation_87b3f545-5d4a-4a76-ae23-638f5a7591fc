from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from ..database import get_db
from ..models.realtime import StockRealtime
from ..models.stocks import StockMaster
from ..models.schemas import ResponseBase

router = APIRouter()


# Pydantic models for realtime data
from pydantic import BaseModel, Field
from decimal import Decimal
from datetime import datetime


class RealtimeData(BaseModel):
    """Realtime stock data model"""
    symbol: str
    name_ar: str
    name_en: Optional[str] = None
    sector: Optional[str] = None
    
    # Price data
    open_price: Optional[Decimal] = None
    high_price: Optional[Decimal] = None
    low_price: Optional[Decimal] = None
    current_price: Optional[Decimal] = None
    previous_close: Optional[Decimal] = None
    change_amount: Optional[Decimal] = None
    change_percent: Optional[Decimal] = None
    
    # Volume data
    volume: Optional[int] = None
    turnover: Optional[Decimal] = None
    trades_count: Optional[int] = None
    
    # Market data
    market_cap: Optional[Decimal] = None
    pe_ratio: Optional[Decimal] = None
    dividend_yield: Optional[Decimal] = None
    
    # Technical indicators
    ma20: Optional[Decimal] = None
    ma50: Optional[Decimal] = None
    
    last_update: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class RealtimeListResponse(ResponseBase):
    """Realtime data list response"""
    data: Optional[List[RealtimeData]] = None
    total: Optional[int] = None


class RealtimeResponse(ResponseBase):
    """Single realtime data response"""
    data: Optional[RealtimeData] = None


@router.get("/", response_model=RealtimeListResponse)
async def get_realtime_data(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=200, description="Items per page"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    db: Session = Depends(get_db)
):
    """Get current market data for all stocks"""
    try:
        # Build query with join
        query = db.query(
            StockRealtime,
            StockMaster.name_ar,
            StockMaster.name_en,
            StockMaster.sector
        ).join(
            StockMaster, StockRealtime.symbol == StockMaster.symbol
        ).filter(
            StockMaster.is_active == True
        )
        
        # Apply sector filter
        if sector:
            query = query.filter(StockMaster.sector == sector)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        results = query.offset(offset).limit(page_size).all()
        
        # Convert to response format
        realtime_list = []
        for realtime, name_ar, name_en, stock_sector in results:
            realtime_list.append(RealtimeData(
                symbol=realtime.symbol,
                name_ar=name_ar,
                name_en=name_en,
                sector=stock_sector,
                open_price=realtime.open_price,
                high_price=realtime.high_price,
                low_price=realtime.low_price,
                current_price=realtime.current_price,
                previous_close=realtime.previous_close,
                change_amount=realtime.change_amount,
                change_percent=realtime.change_percent,
                volume=realtime.volume,
                turnover=realtime.turnover,
                trades_count=realtime.trades_count,
                market_cap=realtime.market_cap,
                pe_ratio=realtime.pe_ratio,
                dividend_yield=realtime.dividend_yield,
                ma20=realtime.ma20,
                ma50=realtime.ma50,
                last_update=realtime.last_update
            ))
        
        return RealtimeListResponse(
            success=True,
            data=realtime_list,
            total=total
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching realtime data: {str(e)}")


@router.get("/{symbol}", response_model=RealtimeResponse)
async def get_stock_realtime(symbol: str, db: Session = Depends(get_db)):
    """Get realtime data for a specific stock"""
    try:
        # Query with join
        result = db.query(
            StockRealtime,
            StockMaster.name_ar,
            StockMaster.name_en,
            StockMaster.sector
        ).join(
            StockMaster, StockRealtime.symbol == StockMaster.symbol
        ).filter(
            StockRealtime.symbol == symbol.upper()
        ).first()
        
        if not result:
            raise HTTPException(status_code=404, detail=f"Realtime data for {symbol} not found")
        
        realtime, name_ar, name_en, stock_sector = result
        
        realtime_data = RealtimeData(
            symbol=realtime.symbol,
            name_ar=name_ar,
            name_en=name_en,
            sector=stock_sector,
            open_price=realtime.open_price,
            high_price=realtime.high_price,
            low_price=realtime.low_price,
            current_price=realtime.current_price,
            previous_close=realtime.previous_close,
            change_amount=realtime.change_amount,
            change_percent=realtime.change_percent,
            volume=realtime.volume,
            turnover=realtime.turnover,
            trades_count=realtime.trades_count,
            market_cap=realtime.market_cap,
            pe_ratio=realtime.pe_ratio,
            dividend_yield=realtime.dividend_yield,
            ma20=realtime.ma20,
            ma50=realtime.ma50,
            last_update=realtime.last_update
        )
        
        return RealtimeResponse(
            success=True,
            data=realtime_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching realtime data: {str(e)}")


@router.get("/top-gainers", response_model=RealtimeListResponse)
async def get_top_gainers(
    limit: int = Query(20, ge=1, le=50, description="Number of top gainers"),
    db: Session = Depends(get_db)
):
    """Get top gaining stocks"""
    try:
        # Query top gainers
        results = db.query(
            StockRealtime,
            StockMaster.name_ar,
            StockMaster.name_en,
            StockMaster.sector
        ).join(
            StockMaster, StockRealtime.symbol == StockMaster.symbol
        ).filter(
            StockMaster.is_active == True,
            StockRealtime.change_percent.isnot(None),
            StockRealtime.change_percent > 0
        ).order_by(
            desc(StockRealtime.change_percent)
        ).limit(limit).all()
        
        # Convert to response format
        gainers_list = []
        for realtime, name_ar, name_en, stock_sector in results:
            gainers_list.append(RealtimeData(
                symbol=realtime.symbol,
                name_ar=name_ar,
                name_en=name_en,
                sector=stock_sector,
                current_price=realtime.current_price,
                change_amount=realtime.change_amount,
                change_percent=realtime.change_percent,
                volume=realtime.volume,
                last_update=realtime.last_update
            ))
        
        return RealtimeListResponse(
            success=True,
            data=gainers_list,
            total=len(gainers_list)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching top gainers: {str(e)}")


@router.get("/top-losers", response_model=RealtimeListResponse)
async def get_top_losers(
    limit: int = Query(20, ge=1, le=50, description="Number of top losers"),
    db: Session = Depends(get_db)
):
    """Get top losing stocks"""
    try:
        # Query top losers
        results = db.query(
            StockRealtime,
            StockMaster.name_ar,
            StockMaster.name_en,
            StockMaster.sector
        ).join(
            StockMaster, StockRealtime.symbol == StockMaster.symbol
        ).filter(
            StockMaster.is_active == True,
            StockRealtime.change_percent.isnot(None),
            StockRealtime.change_percent < 0
        ).order_by(
            StockRealtime.change_percent
        ).limit(limit).all()
        
        # Convert to response format
        losers_list = []
        for realtime, name_ar, name_en, stock_sector in results:
            losers_list.append(RealtimeData(
                symbol=realtime.symbol,
                name_ar=name_ar,
                name_en=name_en,
                sector=stock_sector,
                current_price=realtime.current_price,
                change_amount=realtime.change_amount,
                change_percent=realtime.change_percent,
                volume=realtime.volume,
                last_update=realtime.last_update
            ))
        
        return RealtimeListResponse(
            success=True,
            data=losers_list,
            total=len(losers_list)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching top losers: {str(e)}")


@router.get("/most-active", response_model=RealtimeListResponse)
async def get_most_active(
    limit: int = Query(20, ge=1, le=50, description="Number of most active stocks"),
    db: Session = Depends(get_db)
):
    """Get most active stocks by volume"""
    try:
        # Query most active stocks
        results = db.query(
            StockRealtime,
            StockMaster.name_ar,
            StockMaster.name_en,
            StockMaster.sector
        ).join(
            StockMaster, StockRealtime.symbol == StockMaster.symbol
        ).filter(
            StockMaster.is_active == True,
            StockRealtime.volume.isnot(None),
            StockRealtime.volume > 0
        ).order_by(
            desc(StockRealtime.volume)
        ).limit(limit).all()
        
        # Convert to response format
        active_list = []
        for realtime, name_ar, name_en, stock_sector in results:
            active_list.append(RealtimeData(
                symbol=realtime.symbol,
                name_ar=name_ar,
                name_en=name_en,
                sector=stock_sector,
                current_price=realtime.current_price,
                change_amount=realtime.change_amount,
                change_percent=realtime.change_percent,
                volume=realtime.volume,
                turnover=realtime.turnover,
                last_update=realtime.last_update
            ))
        
        return RealtimeListResponse(
            success=True,
            data=active_list,
            total=len(active_list)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching most active stocks: {str(e)}")
