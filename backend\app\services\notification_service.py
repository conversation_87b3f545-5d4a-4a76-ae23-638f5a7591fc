# Notification Service for Web and Mobile Notifications
import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class Notification:
    """Notification data structure"""
    id: str
    type: str
    title: str
    message: str
    data: Dict[str, Any]
    priority: str  # low, medium, high, urgent
    created_at: datetime
    read: bool = False

class NotificationService:
    """
    Service for managing web notifications, push notifications, and email alerts
    """
    
    def __init__(self):
        self.active_notifications = {}
        self.subscribers = {}  # WebSocket connections or push endpoints
        
    async def send_signal_notification(self, notification_data: Dict[str, Any]):
        """Send trading signal notification to all subscribed users"""
        try:
            notification = self._create_notification(
                type=notification_data.get('type', 'signal'),
                title=self._get_signal_title(notification_data),
                message=notification_data.get('message', ''),
                data=notification_data.get('data', {}),
                priority='high'
            )
            
            # Send via WebSocket
            await self._send_websocket_notification(notification)
            
            # Send push notification
            await self._send_push_notification(notification)
            
            # Store notification
            self.active_notifications[notification.id] = notification
            
            logger.info(f"Signal notification sent: {notification.id}")
            
        except Exception as e:
            logger.error(f"Error sending signal notification: {str(e)}")
    
    async def send_price_alert(self, stock_code: str, current_price: float, 
                              target_price: float, alert_type: str):
        """Send price alert notification"""
        try:
            title = f"تنبيه سعر - {stock_code}"
            
            if alert_type == 'above':
                message = f"وصل سعر {stock_code} إلى {current_price:.2f} (فوق {target_price:.2f})"
            elif alert_type == 'below':
                message = f"انخفض سعر {stock_code} إلى {current_price:.2f} (تحت {target_price:.2f})"
            else:
                message = f"تنبيه سعر {stock_code}: {current_price:.2f}"
            
            notification = self._create_notification(
                type='price_alert',
                title=title,
                message=message,
                data={
                    'stock_code': stock_code,
                    'current_price': current_price,
                    'target_price': target_price,
                    'alert_type': alert_type
                },
                priority='medium'
            )
            
            await self._send_websocket_notification(notification)
            await self._send_push_notification(notification)
            
            self.active_notifications[notification.id] = notification
            
        except Exception as e:
            logger.error(f"Error sending price alert: {str(e)}")
    
    async def send_technical_alert(self, stock_code: str, indicator: str, 
                                  signal: str, details: Dict[str, Any]):
        """Send technical analysis alert"""
        try:
            indicator_names = {
                'rsi': 'مؤشر القوة النسبية',
                'macd': 'الماكد',
                'ma': 'المتوسط المتحرك',
                'bb': 'نطاقات بولينجر',
                'volume': 'حجم التداول'
            }
            
            indicator_name = indicator_names.get(indicator, indicator)
            title = f"تنبيه فني - {stock_code}"
            message = f"{indicator_name}: {signal}"
            
            notification = self._create_notification(
                type='technical_alert',
                title=title,
                message=message,
                data={
                    'stock_code': stock_code,
                    'indicator': indicator,
                    'signal': signal,
                    **details
                },
                priority='medium'
            )
            
            await self._send_websocket_notification(notification)
            await self._send_push_notification(notification)
            
            self.active_notifications[notification.id] = notification
            
        except Exception as e:
            logger.error(f"Error sending technical alert: {str(e)}")
    
    async def send_news_alert(self, title: str, content: str, impact: str, 
                             affected_stocks: List[str]):
        """Send news-based market alert"""
        try:
            impact_levels = {
                'high': 'تأثير عالي',
                'medium': 'تأثير متوسط',
                'low': 'تأثير منخفض'
            }
            
            impact_text = impact_levels.get(impact, impact)
            notification_title = f"تنبيه إخباري - {impact_text}"
            
            notification = self._create_notification(
                type='news_alert',
                title=notification_title,
                message=title,
                data={
                    'content': content,
                    'impact': impact,
                    'affected_stocks': affected_stocks,
                    'source': 'news'
                },
                priority='medium' if impact == 'high' else 'low'
            )
            
            await self._send_websocket_notification(notification)
            
            if impact == 'high':
                await self._send_push_notification(notification)
            
            self.active_notifications[notification.id] = notification
            
        except Exception as e:
            logger.error(f"Error sending news alert: {str(e)}")
    
    async def send_system_alert(self, message: str, alert_type: str = 'info'):
        """Send system-level alert"""
        try:
            alert_types = {
                'info': ('معلومات النظام', 'low'),
                'warning': ('تحذير النظام', 'medium'),
                'error': ('خطأ في النظام', 'high'),
                'maintenance': ('صيانة النظام', 'medium')
            }
            
            title, priority = alert_types.get(alert_type, ('تنبيه النظام', 'low'))
            
            notification = self._create_notification(
                type='system_alert',
                title=title,
                message=message,
                data={'alert_type': alert_type},
                priority=priority
            )
            
            await self._send_websocket_notification(notification)
            
            if priority in ['high', 'urgent']:
                await self._send_push_notification(notification)
            
            self.active_notifications[notification.id] = notification
            
        except Exception as e:
            logger.error(f"Error sending system alert: {str(e)}")
    
    def _create_notification(self, type: str, title: str, message: str, 
                           data: Dict[str, Any], priority: str) -> Notification:
        """Create notification object"""
        import uuid
        
        return Notification(
            id=str(uuid.uuid4()),
            type=type,
            title=title,
            message=message,
            data=data,
            priority=priority,
            created_at=datetime.utcnow()
        )
    
    def _get_signal_title(self, notification_data: Dict[str, Any]) -> str:
        """Generate appropriate title for signal notification"""
        signal_type = notification_data.get('type', '')
        stock_code = notification_data.get('data', {}).get('stock_code', '')
        
        titles = {
            'buy_signal': f'إشارة شراء - {stock_code}',
            'sell_signal': f'إشارة بيع - {stock_code}',
            'tp_signal': f'تحقق هدف - {stock_code}',
            'sl_signal': f'وقف خسارة - {stock_code}'
        }
        
        return titles.get(signal_type, f'إشارة تداول - {stock_code}')
    
    async def _send_websocket_notification(self, notification: Notification):
        """Send notification via WebSocket"""
        try:
            # Import here to avoid circular imports
            from .websocket_manager import signal_manager
            
            websocket_message = {
                'type': 'notification',
                'notification': {
                    'id': notification.id,
                    'type': notification.type,
                    'title': notification.title,
                    'message': notification.message,
                    'data': notification.data,
                    'priority': notification.priority,
                    'created_at': notification.created_at.isoformat()
                }
            }
            
            await signal_manager.broadcast_to_channel(
                json.dumps(websocket_message, ensure_ascii=False),
                'notifications'
            )
            
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {str(e)}")
    
    async def _send_push_notification(self, notification: Notification):
        """Send push notification to mobile devices"""
        try:
            # This would integrate with push notification services
            # like Firebase Cloud Messaging (FCM), Apple Push Notification (APN)
            
            push_payload = {
                'title': notification.title,
                'body': notification.message,
                'data': notification.data,
                'priority': notification.priority
            }
            
            # Placeholder for push notification implementation
            logger.info(f"Push notification would be sent: {push_payload}")
            
            # TODO: Implement actual push notification sending
            # await self._send_fcm_notification(push_payload)
            # await self._send_apn_notification(push_payload)
            
        except Exception as e:
            logger.error(f"Error sending push notification: {str(e)}")
    
    async def get_user_notifications(self, user_id: str, unread_only: bool = False) -> List[Dict]:
        """Get notifications for a specific user"""
        try:
            notifications = []
            
            for notification in self.active_notifications.values():
                # Filter by user preferences, subscription, etc.
                if unread_only and notification.read:
                    continue
                
                notifications.append({
                    'id': notification.id,
                    'type': notification.type,
                    'title': notification.title,
                    'message': notification.message,
                    'data': notification.data,
                    'priority': notification.priority,
                    'created_at': notification.created_at.isoformat(),
                    'read': notification.read
                })
            
            # Sort by creation time (newest first)
            notifications.sort(key=lambda x: x['created_at'], reverse=True)
            
            return notifications
            
        except Exception as e:
            logger.error(f"Error getting user notifications: {str(e)}")
            return []
    
    async def mark_notification_read(self, notification_id: str) -> bool:
        """Mark notification as read"""
        try:
            if notification_id in self.active_notifications:
                self.active_notifications[notification_id].read = True
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error marking notification as read: {str(e)}")
            return False
    
    async def clear_old_notifications(self, days: int = 7):
        """Clear notifications older than specified days"""
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            to_remove = []
            for notification_id, notification in self.active_notifications.items():
                if notification.created_at < cutoff_date:
                    to_remove.append(notification_id)
            
            for notification_id in to_remove:
                del self.active_notifications[notification_id]
            
            logger.info(f"Cleared {len(to_remove)} old notifications")
            
        except Exception as e:
            logger.error(f"Error clearing old notifications: {str(e)}")

# Global notification service instance
notification_service = NotificationService()
