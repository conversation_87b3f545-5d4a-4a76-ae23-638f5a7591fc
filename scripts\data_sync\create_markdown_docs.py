#!/usr/bin/env python3
"""
Create Markdown Documentation from Schema JSON
"""

import json
from datetime import datetime

def create_markdown_from_json():
    """Create markdown documentation from the JSON schema file"""
    
    # Load the JSON schema
    with open('database_schema_reference.json', 'r', encoding='utf-8') as f:
        schema_doc = json.load(f)
    
    md_content = []
    md_content.append("# EGX Stock AI Oracle - Database Schema Reference")
    md_content.append(f"\n**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    md_content.append(f"\n**Database:** {schema_doc['database_url']}")
    md_content.append("\n---\n")
    
    # Summary
    md_content.append("## 📊 Summary")
    summary = schema_doc['inspection_summary']
    md_content.append(f"- **Total Tables:** {summary['total_tables_inspected']}")
    md_content.append(f"- **Total Records:** {summary['total_records_across_main_tables']:,}")
    md_content.append(f"- **Total Columns:** {summary['total_columns_across_main_tables']}")
    md_content.append("")
    
    # Main Tables
    md_content.append("## 📋 Main Stock Tables")
    for table_name, table_info in schema_doc['main_tables'].items():
        md_content.append(f"\n### 🗂️ {table_name}")
        md_content.append(f"- **Records:** {table_info['total_records']:,}")
        md_content.append(f"- **Columns:** {table_info['total_columns']}")
        md_content.append(f"- **Last Updated:** {table_info.get('latest_update', 'N/A')}")
        
        md_content.append("\n**Columns:**")
        for col_name in sorted(table_info['column_names']):
            col_info = table_info['columns'][col_name]
            md_content.append(f"- `{col_name}` ({col_info['data_type']})")
    
    # Additional Tables
    if schema_doc.get('additional_tables'):
        md_content.append("\n## 🔧 Additional Tables")
        for table_name, table_info in schema_doc['additional_tables'].items():
            md_content.append(f"\n### 🗂️ {table_name}")
            md_content.append(f"- **Records:** {table_info['total_records']:,}")
            md_content.append(f"- **Columns:** {table_info['total_columns']}")
            
            md_content.append("\n**Columns:**")
            for col_name in sorted(table_info['column_names']):
                col_info = table_info['columns'][col_name]
                md_content.append(f"- `{col_name}` ({col_info['data_type']})")
    
    # Relationships
    md_content.append("\n## 🔗 Table Relationships")
    for rel_name, rel_info in schema_doc['table_relationships'].items():
        tables = rel_name.replace('_to_', ' ↔ ')
        md_content.append(f"\n### {tables}")
        md_content.append(f"- **Common Symbols:** {rel_info['common_symbols']}")
        md_content.append(f"- **Coverage:** {rel_info['coverage_percentage']}%")
        if rel_info['sample_common_symbols']:
            md_content.append(f"- **Sample Symbols:** {', '.join(rel_info['sample_common_symbols'][:5])}")
    
    # Usage Guide
    if 'frontend_usage_guide' in schema_doc:
        guide = schema_doc['frontend_usage_guide']
        
        md_content.append("\n## 🚀 Frontend Development Guide")
        
        # Common Queries
        if 'common_queries' in guide:
            md_content.append("\n### 📊 Common Query Examples")
            for query_name, query_info in guide['common_queries'].items():
                md_content.append(f"\n#### {query_name.replace('_', ' ').title()}")
                md_content.append(f"**Description:** {query_info['description']}")
                md_content.append("```typescript")
                md_content.append("const { data, error } = await supabase")
                md_content.append(f"  .from('{query_info['table']}')")
                md_content.append(f"  .select('{query_info.get('select', '*')}')")
                if 'filter' in query_info:
                    md_content.append(f"  .{query_info['filter'].replace(' ', '(').replace('>', ', ')})")
                if 'order' in query_info:
                    order_parts = query_info['order'].split()
                    col = order_parts[0]
                    direction = order_parts[1].lower() if len(order_parts) > 1 else 'asc'
                    md_content.append(f"  .order('{col}', {{ ascending: {'true' if direction == 'asc' else 'false'} }})")
                if 'limit' in query_info:
                    md_content.append(f"  .limit({query_info['limit']})")
                md_content.append("```")
        
        # Column Categories
        if 'column_categories' in guide:
            md_content.append("\n### 📋 Column Categories (stocks_realtime)")
            for category, columns in guide['column_categories'].get('stocks_realtime', {}).items():
                if columns:
                    md_content.append(f"\n#### {category.replace('_', ' ').title()}")
                    for col in columns:
                        md_content.append(f"- `{col}`")
    
    # Technical Information
    md_content.append("\n## 🔧 Technical Information")
    md_content.append("\n### Database Connection")
    md_content.append("```typescript")
    md_content.append("import { createClient } from '@supabase/supabase-js'")
    md_content.append("")
    md_content.append("const supabase = createClient(")
    md_content.append("  process.env.NEXT_PUBLIC_SUPABASE_URL!,")
    md_content.append("  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!")
    md_content.append(")")
    md_content.append("```")
    
    md_content.append("\n### Recommended Filters")
    md_content.append("```typescript")
    md_content.append("// Get only active stocks")
    md_content.append(".gt('volume', 0)")
    md_content.append("")
    md_content.append("// Exclude suspended stocks")
    md_content.append(".not('current_price', 'is', null)")
    md_content.append("")
    md_content.append("// High volume stocks")
    md_content.append(".gt('volume', 100000)")
    md_content.append("")
    md_content.append("// Recent data only")
    md_content.append(".gte('updated_at', new Date(Date.now() - 24*60*60*1000).toISOString())")
    md_content.append("```")
    
    # Write to file
    with open('DATABASE_SCHEMA.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(md_content))
    
    print("✅ Markdown documentation created: DATABASE_SCHEMA.md")

if __name__ == "__main__":
    create_markdown_from_json()
