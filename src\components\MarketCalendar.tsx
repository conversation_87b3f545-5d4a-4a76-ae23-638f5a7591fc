
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, DollarSign, TrendingUp, Clock, Bell, AlertCircle } from 'lucide-react';

interface CalendarEvent {
  id: string;
  title: string;
  type: 'earnings' | 'dividend' | 'meeting' | 'holiday' | 'ipo' | 'report';
  date: Date;
  time?: string;
  relatedStock?: string;
  importance: 'high' | 'medium' | 'low';
  description: string;
  expectedImpact?: string;
}

const MarketCalendar = () => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [filter, setFilter] = useState<string>('all');

  useEffect(() => {
    // Mock calendar events
    const mockEvents: CalendarEvent[] = [
      {
        id: '1',
        title: 'إعلان أرباح البنك التجاري الدولي',
        type: 'earnings',
        date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        time: '10:00',
        relatedStock: 'COMI',
        importance: 'high',
        description: 'إعلان النتائج المالية للربع الثالث من العام الحالي',
        expectedImpact: 'تأثير إيجابي متوقع بناءً على الأداء السابق'
      },
      {
        id: '2',
        title: 'توزيع أرباح شركة طلعت مصطفى',
        type: 'dividend',
        date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        time: '12:00',
        relatedStock: 'TALAAT',
        importance: 'medium',
        description: 'توزيع أرباح نقدية بقيمة 1.5 جنيه للسهم الواحد',
        expectedImpact: 'دعم سعر السهم قصير المدى'
      },
      {
        id: '3',
        title: 'اجتماع الجمعية العامة لأوراسكوم',
        type: 'meeting',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        time: '14:00',
        relatedStock: 'OTMT',
        importance: 'medium',
        description: 'اجتماع الجمعية العامة العادية لمناقشة خطط التوسع',
        expectedImpact: 'قرارات استراتيجية مهمة متوقعة'
      },
      {
        id: '4',
        title: 'عطلة رسمية - البورصة مغلقة',
        type: 'holiday',
        date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
        importance: 'low',
        description: 'البورصة المصرية مغلقة بمناسبة العطلة الرسمية',
        expectedImpact: 'لا يوجد تداول'
      },
      {
        id: '5',
        title: 'طرح أولي لشركة تكنولوجية جديدة',
        type: 'ipo',
        date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        time: '09:30',
        importance: 'high',
        description: 'طرح أولي لشركة متخصصة في الذكاء الاصطناعي',
        expectedImpact: 'اهتمام كبير من المستثمرين متوقع'
      },
      {
        id: '6',
        title: 'تقرير التضخم الشهري',
        type: 'report',
        date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        time: '11:00',
        importance: 'high',
        description: 'إعلان معدل التضخم للشهر الماضي من البنك المركزي',
        expectedImpact: 'تأثير على السياسة النقدية والأسعار'
      }
    ];

    setEvents(mockEvents);
  }, []);

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'earnings': return 'bg-green-100 text-green-800 border-green-200';
      case 'dividend': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'meeting': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'holiday': return 'bg-red-100 text-red-800 border-red-200';
      case 'ipo': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'report': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEventTypeLabel = (type: string) => {
    switch (type) {
      case 'earnings': return 'أرباح';
      case 'dividend': return 'توزيعات';
      case 'meeting': return 'اجتماع';
      case 'holiday': return 'عطلة';
      case 'ipo': return 'طرح أولي';
      case 'report': return 'تقرير';
      default: return type;
    }
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'earnings': return <TrendingUp className="h-4 w-4" />;
      case 'dividend': return <DollarSign className="h-4 w-4" />;
      case 'meeting': return <Calendar className="h-4 w-4" />;
      case 'holiday': return <AlertCircle className="h-4 w-4" />;
      case 'ipo': return <TrendingUp className="h-4 w-4" />;
      case 'report': return <Clock className="h-4 w-4" />;
      default: return <Calendar className="h-4 w-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateShort = (date: Date) => {
    return date.toLocaleDateString('ar-EG', {
      month: 'short',
      day: 'numeric'
    });
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isTomorrow = (date: Date) => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return date.toDateString() === tomorrow.toDateString();
  };

  const filteredEvents = filter === 'all' ? events : events.filter(event => event.type === filter);

  const upcomingEvents = filteredEvents
    .filter(event => event.date >= new Date())
    .sort((a, b) => a.date.getTime() - b.date.getTime())
    .slice(0, 10);

  const filterOptions = [
    { value: 'all', label: 'جميع الأحداث' },
    { value: 'earnings', label: 'الأرباح' },
    { value: 'dividend', label: 'التوزيعات' },
    { value: 'meeting', label: 'الاجتماعات' },
    { value: 'ipo', label: 'الطروحات الأولية' },
    { value: 'report', label: 'التقارير' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800 arabic" dir="rtl">
            <Calendar className="h-5 w-5" />
            التقويم المالي
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {filterOptions.map(option => (
              <Button
                key={option.value}
                variant={filter === option.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter(option.value)}
                className="arabic"
              >
                {option.label}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Events List */}
      <div className="space-y-4">
        {upcomingEvents.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-muted-foreground mb-2">لا توجد أحداث</h3>
              <p className="text-muted-foreground">لا توجد أحداث مجدولة للفترة القادمة</p>
            </CardContent>
          </Card>
        ) : (
          upcomingEvents.map(event => (
            <Card key={event.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={`p-2 rounded-lg ${getEventTypeColor(event.type).replace('text-', 'bg-').replace('800', '100')}`}>
                        {getEventIcon(event.type)}
                      </div>
                      <div>
                        <h3 className="font-bold text-lg arabic" dir="rtl">{event.title}</h3>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span className="arabic">
                            {isToday(event.date) ? 'اليوم' : 
                             isTomorrow(event.date) ? 'غداً' : 
                             formatDate(event.date)}
                          </span>
                          {event.time && (
                            <>
                              <span>•</span>
                              <span>{event.time}</span>
                            </>
                          )}
                          {event.relatedStock && (
                            <>
                              <span>•</span>
                              <Badge variant="outline" className="text-blue-600 border-blue-200">
                                {event.relatedStock}
                              </Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <p className="text-muted-foreground text-right arabic mb-3" dir="rtl">
                      {event.description}
                    </p>

                    {event.expectedImpact && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-sm text-blue-800 arabic" dir="rtl">
                          <strong>التأثير المتوقع:</strong> {event.expectedImpact}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col gap-2 items-end">
                    <Badge className={`${getEventTypeColor(event.type)} border`}>
                      {getEventTypeLabel(event.type)}
                    </Badge>
                    
                    <div className="flex items-center gap-1">
                      <AlertCircle className={`h-3 w-3 ${getImportanceColor(event.importance)}`} />
                      <span className={`text-xs ${getImportanceColor(event.importance)}`}>
                        {event.importance === 'high' ? 'مهم جداً' : 
                         event.importance === 'medium' ? 'متوسط' : 'منخفض'}
                      </span>
                    </div>

                    <Button variant="outline" size="sm">
                      <Bell className="h-3 w-3 mr-1" />
                      تذكير
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default MarketCalendar;
