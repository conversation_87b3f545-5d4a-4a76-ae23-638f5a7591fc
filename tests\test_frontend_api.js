// Test script to verify API connectivity from frontend perspective
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tbzbrujqjwpatbzffmwq.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiemJydWpxandwYXRiemZmbXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTE1MDAsImV4cCI6MjA2NTQ4NzUwMH0.Uye7DdCX2xJ38_ApaZFp8TFm1KuyX2ZlzbJ8QUt3ORw";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testMarketOverviewAPI() {
    console.log("🔍 Testing Market Overview API (simulating frontend)...\n");
    
    try {
        // This mimics the exact query from useMarketOverview hook
        const { data: indices, error: indicesError } = await supabase
            .from('market_indices')
            .select('*')
            .in('symbol', ['EGX30', 'EGX70']);

        if (indicesError) {
            console.error("❌ Error fetching indices:", indicesError);
            return;
        }

        console.log("✅ Raw indices data:", JSON.stringify(indices, null, 2));

        // Process the data like the hook does
        const findIndexData = (symbol) => (indices || []).find(idx => idx.symbol === symbol) || null;
        
        const egx30 = findIndexData('EGX30');
        const egx70 = findIndexData('EGX70');

        console.log("\n📊 Processed data:");
        console.log("EGX30:", egx30 ? {
            symbol: egx30.symbol,
            name: egx30.name,
            current_value: Number(egx30.current_value || 0),
            change_amount: Number(egx30.change_amount || 0),
            change_percent: Number(egx30.change_percent || 0)
        } : null);

        console.log("EGX70:", egx70 ? {
            symbol: egx70.symbol,
            name: egx70.name,
            current_value: Number(egx70.current_value || 0),
            change_amount: Number(egx70.change_amount || 0),
            change_percent: Number(egx70.change_percent || 0)
        } : null);

        // Test volume/turnover data
        const totalVolume = egx30?.volume ? Number(egx30.volume) : null;
        const totalValue = egx30?.turnover ? Number(egx30.turnover) : null;

        console.log("\n💰 Volume/Value data:");
        console.log("Total Volume:", totalVolume);
        console.log("Total Value:", totalValue);

        // Test distribution stats
        const gainers = egx30?.constituents_count ? Math.round((egx30.constituents_count || 0) * 0.6) : null;
        const losers = egx30?.constituents_count ? Math.round((egx30.constituents_count || 0) * 0.3) : null;
        const unchanged = egx30?.constituents_count ? (egx30.constituents_count - gainers - losers) : null;

        console.log("\n📈 Market Distribution:");
        console.log("Gainers:", gainers);
        console.log("Losers:", losers);
        console.log("Unchanged:", unchanged);

        console.log("\n✅ API Test Complete - Data looks good!");

    } catch (error) {
        console.error("❌ API Test Failed:", error);
    }
}

// Also test real-time stocks to see if they're accessible
async function testRealtimeStocks() {
    console.log("\n🔄 Testing Real-time Stocks Access...\n");
    
    try {
        const { data: stocks, error } = await supabase
            .from('stocks_realtime')
            .select('symbol, current_price, change_amount, change_percent, volume')
            .limit(5);

        if (error) {
            console.error("❌ Error fetching real-time stocks:", error);
            return;
        }

        console.log("✅ Sample real-time stocks:");
        console.table(stocks);

    } catch (error) {
        console.error("❌ Real-time stocks test failed:", error);
    }
}

// Run tests
async function runTests() {
    await testMarketOverviewAPI();
    await testRealtimeStocks();
}

runTests();
