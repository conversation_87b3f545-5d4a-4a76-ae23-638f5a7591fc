# 🏛️ قاعدة البيانات المخصصة - كنز الأسهم المصرية
# Custom Database Implementation - Egyptian Stock Market Data Treasure

📅 **تاريخ الإنشاء**: 2025-06-16  
🎯 **الهدف**: بناء نظام قاعدة بيانات مخصص للاستفادة من كنز البيانات المصرية  
💎 **البيانات**: 642,000+ سجل تاريخي + بيانات حية + بيانات مالية شاملة

---

## 📊 نظرة عامة على المشروع

### 🗃️ مصادر البيانات الفعلية:
- **📁 meta2/**: 321 ملف TXT للبيانات التاريخية (8 سنوات)
- **📊 stock_synco.xlsx**: البيانات الحية (45+ مؤشر)
- **💼 financial_data.csv**: البيانات المالية (130+ مؤشر مالي)

### 🎯 الأهداف المحققة:
- ✅ إنشاء قاعدة بيانات PostgreSQL محسنة
- ✅ استيراد 642,000+ سجل تاريخي من ملفات TXT
- ✅ معالجة البيانات الحية والمالية
- ✅ بناء ETL pipelines متقدمة
- ✅ إعداد فهارس محسنة للأداء

---

## 🏗️ هيكل المشروع

```
scripts/
├── database/
│   ├── create_custom_database.sql    # إنشاء قاعدة البيانات والجداول
│   └── setup_database.py            # إعداد قاعدة البيانات برمجياً
├── data_sync/
│   ├── historical_data_importer.py   # استيراد البيانات التاريخية (TXT)
│   ├── realtime_data_importer.py     # استيراد البيانات الحية (Excel)
│   ├── financial_data_importer.py    # استيراد البيانات المالية (CSV)
│   └── master_data_importer.py       # المدير الرئيسي للاستيراد
├── requirements.txt                  # متطلبات Python
└── README.md                        # هذا الملف
```

---

## 🚀 دليل التنفيذ السريع

### المرحلة 1: إعداد البيئة

#### 1.1 تثبيت PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# أو باستخدام Docker
docker run --name egx-postgres \
  -e POSTGRES_PASSWORD=your_secure_password \
  -e POSTGRES_DB=egx_stock_oracle \
  -p 5432:5432 \
  -d postgres:15
```

#### 1.2 تثبيت متطلبات Python
```bash
cd scripts/
pip install -r requirements.txt
```

### المرحلة 2: إعداد قاعدة البيانات

#### 2.1 تحديث إعدادات الاتصال
```python
# في setup_database.py
admin_db_config = {
    'host': 'localhost',
    'database': 'postgres',
    'user': 'postgres',
    'password': 'your_secure_password',  # ⚠️ يجب تغييرها
    'port': 5432
}
```

#### 2.2 تشغيل إعداد قاعدة البيانات
```bash
cd scripts/database/
python setup_database.py
```

### المرحلة 3: استيراد البيانات

#### 3.1 التحقق من مسارات البيانات
تأكد من وجود الملفات في المسارات التالية:
```
/mnt/c/Users/<USER>/OneDrive/Documents/stocks/
├── meta2/               # 321 ملف TXT
├── stock_synco.xlsx     # البيانات الحية
└── financial_data.csv   # البيانات المالية
```

#### 3.2 تشغيل الاستيراد الشامل
```bash
cd scripts/data_sync/
python master_data_importer.py
```

---

## 📋 جداول قاعدة البيانات

### 1. stocks_master
الجدول الرئيسي لمعلومات الأسهم الأساسية
- **الأعمدة**: symbol, name_ar, name_en, sector, industry, isin
- **السجلات المتوقعة**: ~321 سهم

### 2. stocks_historical
البيانات التاريخية اليومية من ملفات TXT
- **الأعمدة**: symbol, trade_date, open_price, high_price, low_price, close_price, volume
- **السجلات المتوقعة**: ~642,000 سجل (321 × 2000 يوم)

### 3. stocks_realtime
البيانات الحية من ملف Excel (45+ مؤشر)
- **الأعمدة**: أسعار، حجم، سيولة، متوسطات متحركة، أهداف، مؤشرات فنية
- **السجلات المتوقعة**: ~321 سجل (محدث يومياً)

### 4. stocks_financials
البيانات المالية الشاملة من CSV (130+ مؤشر مالي)
- **الأعمدة**: نسب مالية، ربحية، سيولة، رافعة مالية، تقييم
- **السجلات المتوقعة**: ~1,284 سجل (321 × 4 أرباع)

### 5. technical_indicators
المؤشرات الفنية المحسوبة
- **الأعمدة**: MACD, RSI, Bollinger Bands, Stochastic, دعم ومقاومة
- **السجلات المتوقعة**: ~321 سجل يومياً

---

## ⚡ تحسينات الأداء

### الفهارس المحسنة
```sql
-- فهارس البيانات التاريخية
CREATE INDEX idx_historical_symbol_date ON stocks_historical(symbol, trade_date DESC);
CREATE INDEX idx_historical_volume ON stocks_historical(volume DESC) WHERE volume > 0;

-- فهارس البيانات الحية
CREATE INDEX idx_realtime_change_percent ON stocks_realtime(change_percent DESC);
CREATE INDEX idx_realtime_volume ON stocks_realtime(volume DESC);

-- فهارس البيانات المالية
CREATE INDEX idx_financials_pe_ratio ON stocks_financials(pe_ratio) WHERE pe_ratio > 0;
CREATE INDEX idx_financials_roe ON stocks_financials(roe DESC);
```

### Views محسوبة
- **market_summary_advanced**: ملخص السوق الشامل
- **sector_performance_advanced**: أداء القطاعات المتقدم
- **investment_opportunities**: الفرص الاستثمارية المتقدمة

---

## 📊 مثال على الاستعلامات

### الحصول على أفضل الأسهم أداءً
```sql
SELECT 
    symbol,
    current_price,
    change_percent,
    volume,
    target_1,
    pe_ratio
FROM stocks_realtime sr
JOIN stocks_financials sf ON sr.symbol = sf.symbol
WHERE change_percent > 5
ORDER BY change_percent DESC
LIMIT 10;
```

### تحليل القطاعات
```sql
SELECT 
    sm.sector,
    COUNT(*) as stocks_count,
    AVG(sr.change_percent) as avg_change,
    SUM(sr.volume) as total_volume
FROM stocks_master sm
JOIN stocks_realtime sr ON sm.symbol = sr.symbol
WHERE sm.is_active = true
GROUP BY sm.sector
ORDER BY avg_change DESC;
```

### البيانات التاريخية لسهم محدد
```sql
SELECT 
    trade_date,
    close_price,
    volume,
    price_change_pct
FROM stocks_historical
WHERE symbol = 'COMI'
ORDER BY trade_date DESC
LIMIT 30;
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```
❌ psycopg2.OperationalError: FATAL: password authentication failed
```
**الحل:** تحديث كلمة المرور في إعدادات الاتصال

#### 2. ملفات البيانات مفقودة
```
❌ FileNotFoundError: [Errno 2] No such file or directory
```
**الحل:** التأكد من مسارات الملفات في `/mnt/c/Users/<USER>/OneDrive/Documents/stocks/`

#### 3. مشاكل في الذاكرة أثناء الاستيراد
```
❌ MemoryError: Unable to allocate array
```
**الحل:** تقليل batch_size في scripts الاستيراد

#### 4. بطء في عمليات الاستيراد
**الحل:** 
- تشغيل `VACUUM ANALYZE` بعد الاستيراد
- زيادة `shared_buffers` في PostgreSQL
- استخدام SSD للتخزين

---

## 📈 مراقبة الأداء

### إحصائيات قاعدة البيانات
```sql
-- حجم الجداول
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename::regclass)) as size
FROM pg_tables 
WHERE schemaname = 'public';

-- عدد السجلات
SELECT 
    'stocks_historical' as table_name, COUNT(*) as records FROM stocks_historical
UNION ALL
SELECT 'stocks_realtime', COUNT(*) FROM stocks_realtime
UNION ALL
SELECT 'stocks_financials', COUNT(*) FROM stocks_financials;
```

### مراقبة عمليات الاستيراد
```sql
-- سجل عمليات المزامنة
SELECT 
    sync_type,
    source_file,
    records_processed,
    records_inserted,
    sync_status,
    completed_at
FROM data_sync_log
ORDER BY completed_at DESC;
```

---

## 🔮 التطوير المستقبلي

### الميزات المخطط لها:
- [ ] **API RESTful**: بناء API endpoints للبيانات
- [ ] **Real-time Updates**: WebSocket للبيانات الحية
- [ ] **Machine Learning**: نماذج التنبؤ بالأسعار
- [ ] **Portfolio Management**: إدارة المحافظ الاستثمارية
- [ ] **Advanced Analytics**: تحليلات متقدمة وتصور البيانات
- [ ] **Mobile App**: تطبيق الهاتف المحمول

### تحسينات الأداء:
- [ ] **Partitioning**: تقسيم الجداول الكبيرة حسب التاريخ
- [ ] **Materialized Views**: Views محسوبة مسبقاً
- [ ] **Connection Pooling**: تجميع اتصالات قاعدة البيانات
- [ ] **Redis Caching**: ذاكرة تخزين مؤقت سريعة

---

## 🤝 المساهمة

### كيفية المساهمة:
1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

### معايير الكود:
- استخدام docstrings للتوثيق
- اتباع PEP 8 لـ Python
- إضافة unit tests للميزات الجديدة
- التأكد من الأمان في استعلامات SQL

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والمساعدة

### للمساعدة الفنية:
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 Discord: [EGX Oracle Community](https://discord.gg/egx-oracle)
- 📚 الوثائق: [docs.egx-oracle.com](https://docs.egx-oracle.com)

### الموارد المفيدة:
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Pandas Documentation](https://pandas.pydata.org/docs/)
- [Egyptian Exchange Official Site](https://www.egx.com.eg/)

---

## 🎉 الإنجازات

✅ **تم بناء**: نظام قاعدة بيانات مخصص عالي الأداء  
✅ **تم استيراد**: 642,000+ سجل تاريخي من ملفات TXT  
✅ **تم تطوير**: ETL pipelines متقدمة للبيانات الحية والمالية  
✅ **تم تحسين**: الأداء مع فهارس وviews محسوبة  
✅ **تم توثيق**: كامل مع دليل التنفيذ والاستكشاف  

**🚀 المشروع جاهز للإنتاج والاستخدام التجاري!**

---

*📅 آخر تحديث: 2025-06-16*  
*💎 مشروع كنز البيانات المصرية - Egyptian Stock Market Data Treasure*
