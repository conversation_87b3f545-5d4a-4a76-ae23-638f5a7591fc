import { useState, useEffect, useRef, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';

export interface LiveStockData {
  symbol: string;
  name?: string;
  sector?: string;
  current_price: number;
  previous_close: number;
  change_amount: number;
  change_percent: number;
  volume: number;
  turnover: number;
  trades_count?: number;
  high_price?: number;
  low_price?: number;
  open_price?: number;
  bid_price?: number;
  ask_price?: number;
  market_cap?: number;
  updated_at: string;
  
  // حالات التغيير للتأثيرات البصرية
  priceDirection?: 'up' | 'down' | 'neutral';
  volumeChange?: 'up' | 'down' | 'neutral';
  isNewUpdate?: boolean;
  flashColor?: 'green' | 'red' | 'none';
}

export interface LiveMarketStats {
  totalStocks: number;
  activeStocks: number;
  totalVolume: number;
  totalTurnover: number;
  totalTrades: number;
  gainers: number;
  losers: number;
  unchanged: number;
  topGainers: LiveStockData[];
  topLosers: LiveStockData[];
  mostActive: LiveStockData[];
  lastUpdate: string;
}

export const useLiveMarketData = () => {
  const [previousData, setPreviousData] = useState<Map<string, LiveStockData>>(new Map());
  const [marketStats, setMarketStats] = useState<LiveMarketStats | null>(null);
  const { toast } = useToast();
  const isFirstLoad = useRef(true);

  // استعلام البيانات مع تحديث سريع جداً
  const {
    data: rawStocks,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['live_market_data'],
    queryFn: async () => {
      try {
        console.log('🔄 Fetching live market data...');
        
        // جلب جميع البيانات اللحظية مع معلومات إضافية
        const { data: stocks, error: stocksError } = await supabase
          .from('stocks_realtime')
          .select(`
            symbol,
            current_price,
            previous_close,
            change_amount,
            change_percent,
            volume,
            turnover,
            trades_count,
            high_price,
            low_price,
            open_price,
            bid_price,
            ask_price,
            market_cap,
            updated_at
          `)
          .not('symbol', 'like', '*EGX*') // استبعاد المؤشرات
          .not('symbol', 'like', '*INDEX*')
          .gt('current_price', 0) // فقط الأسهم النشطة
          .order('turnover', { ascending: false })
          .limit(300); // أهم 300 سهم

        if (stocksError) {
          console.error('❌ Error fetching stocks:', stocksError);
          throw stocksError;
        }

        // جلب معلومات إضافية من stocks_master
        const symbols = stocks?.map(s => s.symbol) || [];
        const { data: masterData } = await supabase
          .from('stocks_master')
          .select('symbol, name, name_ar, sector, sector_ar')
          .in('symbol', symbols);

        // دمج البيانات
        const enrichedStocks = stocks?.map(stock => {
          const master = masterData?.find(m => m.symbol === stock.symbol);
          return {
            ...stock,
            name: master?.name_ar || master?.name || stock.symbol,
            sector: master?.sector_ar || master?.sector,
          };
        }) || [];

        console.log(`✅ Loaded ${enrichedStocks.length} stocks`);
        return enrichedStocks;
      } catch (error) {
        console.error('❌ Error in live data fetch:', error);
        throw error;
      }
    },
    refetchInterval: 10000, // تحديث كل 10 ثوانِ
    staleTime: 8000, // البيانات صالحة لمدة 8 ثوانِ
    retry: 3,
    retryDelay: 2000,
  });
  // معالجة البيانات وإضافة التأثيرات البصرية
  const processedStocks = useMemo(() => {
    return rawStocks?.map(stock => {
      const previous = previousData.get(stock.symbol);
      let priceDirection: 'up' | 'down' | 'neutral' = 'neutral';
      let volumeChange: 'up' | 'down' | 'neutral' = 'neutral';
      let flashColor: 'green' | 'red' | 'none' = 'none';
      let isNewUpdate = false;

      if (previous && !isFirstLoad.current) {
        // مقارنة الأسعار
        if (stock.current_price > previous.current_price) {
          priceDirection = 'up';
          flashColor = 'green';
          isNewUpdate = true;
        } else if (stock.current_price < previous.current_price) {
          priceDirection = 'down';
          flashColor = 'red';
          isNewUpdate = true;
        }

        // مقارنة الحجم
        if (stock.volume > previous.volume) {
          volumeChange = 'up';
        } else if (stock.volume < previous.volume) {
          volumeChange = 'down';
        }

        // إشعار للتغييرات الكبيرة
        if (Math.abs(stock.change_percent) > 5 && isNewUpdate) {
          toast({
            title: `🚀 تحرك كبير في ${stock.name || stock.symbol}`,
            description: `${stock.change_percent > 0 ? '⬆️' : '⬇️'} ${stock.change_percent.toFixed(2)}%`,
            duration: 3000,
          });
        }
      }

      return {
        ...stock,
        priceDirection,
        volumeChange,
        flashColor,
        isNewUpdate,
      };
    }) || [];
  }, [rawStocks, previousData, toast]);

  // تحديث البيانات السابقة
  useEffect(() => {
    if (processedStocks.length > 0) {
      const newPreviousData = new Map();
      processedStocks.forEach(stock => {
        newPreviousData.set(stock.symbol, stock);
      });
      setPreviousData(newPreviousData);
      
      // حساب إحصائيات السوق
      const stats = calculateMarketStats(processedStocks);
      setMarketStats(stats);
      
      if (isFirstLoad.current) {
        isFirstLoad.current = false;
      }
    }
  }, [processedStocks]);

  return {
    stocks: processedStocks,
    marketStats,
    isLoading,
    error,
    refetch,
    lastUpdate: new Date().toLocaleTimeString('ar-EG'),
  };
};

// حساب إحصائيات السوق
function calculateMarketStats(stocks: LiveStockData[]): LiveMarketStats {
  const activeStocks = stocks.filter(s => s.volume > 0);
  
  const totalVolume = stocks.reduce((sum, s) => sum + (Number(s.volume) || 0), 0);
  const totalTurnover = stocks.reduce((sum, s) => sum + (Number(s.turnover) || 0), 0);
  const totalTrades = stocks.reduce((sum, s) => sum + (Number(s.trades_count) || 0), 0);
  
  const gainers = stocks.filter(s => s.change_percent > 0).length;
  const losers = stocks.filter(s => s.change_percent < 0).length;
  const unchanged = stocks.filter(s => s.change_percent === 0).length;
  
  // أفضل وأسوأ أداء
  const sortedByChange = [...stocks].sort((a, b) => b.change_percent - a.change_percent);
  const topGainers = sortedByChange.slice(0, 10);
  const topLosers = sortedByChange.slice(-10).reverse();
  
  // الأكثر نشاطاً
  const mostActive = [...stocks]
    .sort((a, b) => (b.turnover || 0) - (a.turnover || 0))
    .slice(0, 10);

  return {
    totalStocks: stocks.length,
    activeStocks: activeStocks.length,
    totalVolume,
    totalTurnover,
    totalTrades,
    gainers,
    losers,
    unchanged,
    topGainers,
    topLosers,
    mostActive,
    lastUpdate: new Date().toISOString(),
  };
}
